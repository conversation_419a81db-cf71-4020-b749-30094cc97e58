#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Smart Friend Application Launcher

Simple launcher to start the Smart Friend application.
By default, runs with OpenManus enhancements enabled.
"""

import sys
import os
import argparse
import subprocess

def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(description='Smart Friend Application Launcher')
    parser.add_argument('--mode', choices=['standard', 'openmanus'], 
                       default='openmanus', help='Application mode (default: openmanus)')
    parser.add_argument('--port', type=int, default=8003, help='Port to run on (default: 8003)')
    
    args = parser.parse_args()
    
    print("🚀 Smart Friend Application Launcher")
    print("=" * 50)
    
    # Determine which script to run
    if args.mode == 'standard':
        script = 'main_standard.py'
        if not os.path.exists(script):
            print(f"❌ {script} not found. Using main.py instead.")
            script = 'main.py'
    else:  # openmanus mode
        script = 'main_openmanus.py'
        if not os.path.exists(script):
            print(f"ℹ️ Using main.py for OpenManus mode.")
            script = 'main.py'
    
    print(f"📋 Mode: {args.mode}")
    print(f"📍 Port: {args.port}")
    print(f"🔧 Script: {script}")
    print("=" * 50)
    
    try:
        # Run the selected script
        subprocess.run([sys.executable, script], check=True)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running {script}: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"❌ Script {script} not found")
        sys.exit(1)

if __name__ == "__main__":
    main()
