#!/usr/bin/env python3
"""
Body Detection Module
This module provides pose detection and desktop analysis functionality.

Imports the main classes from posedetection.py to maintain compatibility
with existing code that expects to import from body_detection.
"""

# Import the main classes from posedetection module
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from posedetection import StandalonePoseAnalyzer

# Make the class available at module level
__all__ = ['StandalonePoseAnalyzer']

# Version info
__version__ = '1.0.0'
__author__ = 'Smart Friend Team'
__description__ = 'Body detection and pose analysis module'
