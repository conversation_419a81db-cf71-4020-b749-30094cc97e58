#!/usr/bin/env python3
"""
Desktop Detection Module
This module provides desktop segmentation and layout analysis functionality.

Imports the desktop analysis classes from posedetection.py to maintain compatibility
with existing code that expects to import from body_detection.desk.
"""

# Import the desktop analysis classes from posedetection module
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from posedetection import DesktopSegmentationDetector, DesktopLayoutAnalyzer, QualityLevel, DesktopReport

# Make the classes available at module level
__all__ = [
    'DesktopSegmentationDetector', 
    'DesktopLayoutAnalyzer', 
    'QualityLevel', 
    'DesktopReport'
]

# Version info
__version__ = '1.0.0'
__author__ = 'Smart Friend Team'
__description__ = 'Desktop detection and layout analysis module'
