#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Web语音识别线程模块

提供基于Web界面的实时语音识别功能，集成豆包(火山引擎)ASR API
支持实时语音转文字，并将结果传递给main.py的voice_thread函数
"""

import os
import sys
import time
import threading
import queue
import json
import base64
import logging
import struct
import numpy as np
from datetime import datetime
from typing import Optional, Callable, Dict, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Flask和SocketIO相关导入
from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO, emit

# 导入ASR客户端
from backend.utils.volcano_asr_client import VolcanoASRClient

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_child.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局变量
app = Flask(__name__,
            template_folder=os.path.join(project_root, 'static'),
            static_folder=os.path.join(project_root, 'static'))
app.config['SECRET_KEY'] = 'voice_recognition_secret_key_2024'
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# ASR相关全局变量
asr_results = []
asr_results_lock = threading.Lock()

def asr_result_callback(text: str, is_final: bool):
    """ASR识别结果回调函数"""
    global asr_results

    timestamp = time.time()
    result = {
        "text": text,
        "is_final": is_final,
        "timestamp": timestamp
    }

    with asr_results_lock:
        asr_results.append(result)
        # 保留最近100个结果
        if len(asr_results) > 100:
            asr_results = asr_results[-100:]

    logger.info(f"ASR识别结果: {text} (final: {is_final})")

    # 通过Socket.IO发送结果到前端
    socketio.emit('asr_result', {
        'text': text,
        'is_final': is_final,
        'timestamp': timestamp
    })

# 语音识别结果队列，用于传递给main.py
voice_results_queue = queue.Queue()

# ASR客户端实例
asr_client = None
asr_connected = False

# 豆包(火山引擎)语音识别API凭据
ASR_APP_KEY = "5311525929"
ASR_ACCESS_KEY = "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23"

# 静音检测相关全局变量
last_speech_time = time.time()
silence_timeout = 4 #4秒静音检测
recognition_start_time = None
is_listening = False
last_recognized_text = ""  # 记录最后识别的文本，避免重复处理


class WebVoiceRecognitionService:
    """Web语音识别服务类"""

    def __init__(self):
        """初始化Web语音识别服务"""
        self.is_running = False
        self.current_session_id = None
        self.recognition_active = False

        logger.info("Web语音识别服务初始化完成")

    def start_service(self, host='0.0.0.0', port=5000):
        """启动Web语音识别服务"""
        logger.info(f"启动Web语音识别服务，监听 {host}:{port}")
        self.is_running = True

        try:
            socketio.run(app, host=host, port=port, debug=False, allow_unsafe_werkzeug=True)
        except Exception as e:
            logger.error(f"启动Web服务失败: {e}")
            self.is_running = False

    def stop_service(self):
        """停止Web语音识别服务"""
        logger.info("停止Web语音识别服务")
        print("停止Web语音识别服务")
        self.is_running = False

        # 断开ASR连接
        global asr_client, asr_connected
        if asr_client and asr_connected:
            try:
                asr_client.stop_recognition()
                asr_client.disconnect()
                asr_connected = False
                logger.info("ASR连接已断开")
            except Exception as e:
                logger.error(f"断开ASR连接时出错: {e}")


# 全局服务实例
web_voice_service = WebVoiceRecognitionService()


def get_voice_results():
    """
    获取语音识别结果

    Returns:
        list: 语音识别结果列表，如果没有新结果则返回空列表
    """
    results = []
    try:
        while not voice_results_queue.empty():
            result = voice_results_queue.get_nowait()
            results.append(result)
    except queue.Empty:
        pass

    return results


def is_voice_service_active():
    """
    检查语音识别服务是否活跃

    Returns:
        bool: 服务是否正在运行
    """
    return web_voice_service.is_running


def start_web_voice_service(host='0.0.0.0', port=5000):
    """
    启动Web语音识别服务的入口函数

    Args:
        host (str): 服务监听的主机地址
        port (int): 服务监听的端口
    """
    web_voice_service.start_service(host, port)


# 导入main.py中的函数
try:
    # 临时改变工作目录到项目根目录
    original_cwd = os.getcwd()
    os.chdir(project_root)
    from main import call_voice_model, get_daily_task
    os.chdir(original_cwd)
    logger.info("成功导入main.py中的函数")
except ImportError as e:
    logger.warning(f"导入main.py函数失败: {e}")
    call_voice_model = None
    get_daily_task = None
except Exception as e:
    logger.warning(f"导入main.py函数时出现其他错误: {e}")
    call_voice_model = None
    get_daily_task = None


# ASR结果回调函数
def on_asr_result(text: str, is_final: bool):
    """
    ASR识别结果回调函数

    Args:
        text (str): 识别的文本
        is_final (bool): 是否为最终结果
    """
    global last_speech_time, is_listening, last_recognized_text

    try:
        timestamp = datetime.now().isoformat()
        current_time = time.time()

        # 只有当文本真正发生变化时才更新语音时间
        if text and text.strip():
            # 检查文本是否与上次不同
            if text != last_recognized_text:
                last_speech_time = current_time
                last_recognized_text = text
                logger.info(f"检测到新语音内容: {text}, 更新最后语音时间")
            else:
                logger.debug(f"重复的语音内容: {text}, 不更新语音时间")
        else:
            # 即使没有文本，如果不是最终结果，也说明有语音活动
            if not is_final:
                last_speech_time = current_time
                logger.debug(f"检测到语音活动（临时结果），更新最后语音时间")

        # 发送结果到前端
        socketio.emit('recognition_result', {
            'text': text,
            'is_final': is_final,
            'timestamp': timestamp
        })

        # 如果是最终结果，添加到队列供main.py使用，并调用main.py的函数
        if is_final and text.strip():
            voice_results_queue.put({
                'text': text,
                'timestamp': timestamp,
                'source': 'web_voice_recognition'
            })
            logger.info(f"语音识别最终结果: {text}")

            # 调用main.py中的voice_thread逻辑
            if call_voice_model and get_daily_task:
                try:
                    logger.info("语音线程正在调用语音大模型接口...")
                    result = call_voice_model()
                    if result and isinstance(result, str) and result.strip():
                        logger.info(f"语音大模型返回结果: {result}")
                        logger.info("触发创建当日任务...")
                        daily_task = get_daily_task()
                        logger.info(f"当日任务: {daily_task}")

                        # 将结果也发送到前端显示
                        socketio.emit('task_created', {
                            'voice_result': result,
                            'daily_task': str(daily_task),
                            'timestamp': timestamp
                        })

                except Exception as e:
                    logger.error(f"调用main.py函数时出错: {e}")

    except Exception as e:
        logger.error(f"处理ASR结果时出错: {e}")


def check_silence_timeout():
    """
    检查静音超时的线程函数
    """
    global last_speech_time, silence_timeout, is_listening, asr_client, asr_connected

    logger.info("静音检测线程已启动")
    last_debug_time = 0

    while True:
        try:
            current_time = time.time()

            # 只有在监听状态下才进行详细检测，减少无用日志
            if is_listening and asr_connected and asr_client and web_voice_service.recognition_active:
                silence_duration = current_time - last_speech_time
                recognition_duration = current_time - recognition_start_time if recognition_start_time else 0

                # 只在每10秒打印一次状态信息，大幅减少日志频率
                if current_time - last_debug_time >= 10.0:
                    logger.info(f"静音检测状态 - 识别时长: {recognition_duration:.1f}s, 静音时长: {silence_duration:.1f}s")
                    last_debug_time = current_time

                # 只有在识别开始至少3秒后才开始检测静音，并且静音时间超过阈值
                if recognition_duration >= 3.0 and silence_duration >= silence_timeout:
                    logger.info(f"触发自动停止条件 - 识别时长: {recognition_duration:.1f}s >= 3.0s, 静音时长: {silence_duration:.1f}s >= {silence_timeout}s")

                    # 停止语音识别
                    try:
                        asr_client.stop_recognition()
                        is_listening = False
                        web_voice_service.recognition_active = False

                        # 通知前端识别已停止
                        socketio.emit('recognition_stopped', {
                            'message': f'检测到 {silence_timeout} 秒静音，自动停止识别',
                            'reason': 'silence_timeout'
                        })

                        logger.info("语音识别已因静音超时自动停止")

                    except Exception as e:
                        logger.error(f"自动停止语音识别时出错: {e}")

            time.sleep(0.1)  # 每100ms检查一次

        except Exception as e:
            logger.error(f"静音检测线程出错: {e}")
            time.sleep(1.0)


# Flask路由
@app.route('/')
def index():
    """主页路由"""
    return render_template('aiChild.html')

# ASR API路由
@app.route('/api/v1/asr/connect', methods=['POST'])
def connect_asr():
    """连接ASR服务"""
    global asr_client, asr_connected

    try:
        if asr_connected:
            return jsonify({
                "success": True,
                "message": "已经连接到ASR服务",
                "connection_id": getattr(asr_client, 'connect_id', None)
            })

        # 创建ASR客户端
        asr_client = VolcanoASRClient(
            app_key="5311525929",
            access_key="DRNTjbbfC1QcfDrTndiSSBdTr23F0-23",
            model_name="bigmodel",
            sample_rate=16000,
            channels=1,
            callback=asr_result_callback
        )

        # 连接到ASR服务
        success = asr_client.connect()
        if success:
            asr_connected = True
            logger.info("成功连接到火山引擎ASR服务")

            return jsonify({
                "success": True,
                "message": "成功连接到语音识别服务",
                "connection_id": getattr(asr_client, 'connect_id', None),
                "model_info": {
                    "model_name": "bigmodel",
                    "sample_rate": 16000,
                    "channels": 1
                }
            })
        else:
            return jsonify({
                "success": False,
                "message": "连接到语音识别服务失败"
            }), 503

    except Exception as e:
        logger.error(f"连接ASR服务时出错: {e}")
        return jsonify({
            "success": False,
            "message": f"连接失败: {str(e)}"
        }), 500

@app.route('/api/v1/asr/start', methods=['POST'])
def start_asr():
    """启动语音识别"""
    global asr_client, asr_connected, recognition_active

    try:
        if not asr_connected or not asr_client:
            return jsonify({
                "success": False,
                "message": "请先连接到语音识别服务"
            }), 400

        # 启动语音识别
        success = asr_client.start_recognition(callback=asr_result_callback)
        if success:
            recognition_active = True
            logger.info("语音识别已启动")
            return jsonify({
                "success": True,
                "message": "语音识别已启动"
            })
        else:
            return jsonify({
                "success": False,
                "message": "启动语音识别失败"
            }), 500

    except Exception as e:
        logger.error(f"启动语音识别时出错: {e}")
        return jsonify({
            "success": False,
            "message": f"启动语音识别失败: {str(e)}"
        }), 500

@app.route('/api/v1/asr/stop', methods=['POST'])
def stop_asr():
    """停止语音识别"""
    global asr_client, recognition_active

    try:
        if asr_client:
            asr_client.stop_recognition()
            recognition_active = False
            logger.info("语音识别已停止")
            return jsonify({
                "success": True,
                "message": "语音识别已停止"
            })
        else:
            return jsonify({
                "success": True,
                "message": "语音识别服务未运行"
            })

    except Exception as e:
        logger.error(f"停止语音识别时出错: {e}")
        return jsonify({
            "success": False,
            "message": f"停止语音识别失败: {str(e)}"
        }), 500

@app.route('/api/v1/asr/status', methods=['GET'])
def get_asr_status():
    """获取ASR服务状态"""
    try:
        status_info = {
            "connected": asr_connected,
            "client_exists": asr_client is not None,
            "recognition_active": recognition_active,
            "web_service_active": True,
            "model_name": getattr(asr_client, 'model_name', None),
            "sample_rate": getattr(asr_client, 'sample_rate', None),
            "channels": getattr(asr_client, 'channels', None),
            "connection_id": getattr(asr_client, 'connect_id', None),
            "last_activity": time.strftime('%Y-%m-%d %H:%M:%S')
        }

        return jsonify({
            "is_healthy": asr_connected and asr_client is not None,
            "status": status_info
        })

    except Exception as e:
        logger.error(f"获取ASR状态时出错: {e}")
        return jsonify({
            "is_healthy": False,
            "status": {
                "connected": False,
                "client_exists": False,
                "recognition_active": False
            },
            "error": str(e)
        })


# 旧的API路由已移除，使用新的/api/v1/asr/*路由


# SocketIO事件处理
@socketio.on('connect')
def handle_connect():
    """客户端连接事件"""
    logger.info(f"客户端已连接: {request.sid}")
    emit('status', {'message': '已连接到Web语音识别服务'})


@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接事件"""
    logger.info(f"客户端已断开连接: {request.sid}")


@socketio.on('start_recognition')
def handle_start_recognition():
    """开始语音识别事件"""
    global asr_client, asr_connected, last_speech_time, is_listening, recognition_start_time, last_recognized_text

    try:
        if not asr_connected or not asr_client:
            emit('error', {'message': '请先连接到语音识别服务'})
            return

        # 启动语音识别
        success = asr_client.start_recognition()
        if success:
            web_voice_service.recognition_active = True
            is_listening = True
            current_time = time.time()
            last_speech_time = current_time  # 重置最后语音时间
            recognition_start_time = current_time  # 记录识别开始时间
            last_recognized_text = ""  # 重置最后识别的文本
            logger.info("语音识别已启动，开始4秒静音检测")
            emit('recognition_started', {
                'message': '语音识别已启动，将在4秒静音后自动停止',
                'silence_timeout': silence_timeout
            })
        else:
            logger.error("启动语音识别失败")
            emit('error', {'message': '启动语音识别失败'})

    except Exception as e:
        logger.error(f"启动语音识别时出错: {e}")
        emit('error', {'message': f'启动语音识别失败: {str(e)}'})


@socketio.on('stop_recognition')
def handle_stop_recognition():
    """停止语音识别事件"""
    global asr_client, is_listening, recognition_start_time

    try:
        if asr_client and web_voice_service.recognition_active:
            asr_client.stop_recognition()
            web_voice_service.recognition_active = False
            is_listening = False
            recognition_start_time = None  # 重置识别开始时间
            logger.info("语音识别已停止")
            emit('recognition_stopped', {'message': '语音识别已停止'})
        else:
            emit('error', {'message': '当前没有进行语音识别'})

    except Exception as e:
        logger.error(f"停止语音识别时出错: {e}")
        emit('error', {'message': f'停止语音识别失败: {str(e)}'})


@socketio.on('audio_data')
def handle_audio_data(data):
    """处理音频数据事件"""
    global asr_client, last_speech_time

    try:
        if not asr_client or not web_voice_service.recognition_active:
            return

        # 解码base64音频数据
        audio_base64 = data.get('audio', '')
        if audio_base64:
            audio_bytes = base64.b64decode(audio_base64)

            # 检测音频数据是否包含语音活动
            # 简单的音量检测：如果音频数据不全是静音，就更新语音时间
            if len(audio_bytes) > 0:
                # 每2个字节组成一个16位整数
                if len(audio_bytes) >= 2:
                    try:
                        # 转换为16位整数数组
                        audio_samples = struct.unpack(f'<{len(audio_bytes)//2}h', audio_bytes[:len(audio_bytes)//2*2])

                        # 计算音频的RMS值
                        if audio_samples:
                            rms = np.sqrt(np.mean(np.square(audio_samples)))

                            # 降低音量阈值，使其更容易检测到语音活动
                            volume_threshold = 100  # 降低阈值
                            if rms > volume_threshold:
                                last_speech_time = time.time()
                                logger.debug(f"检测到音频活动，RMS: {rms:.2f}, 更新语音时间")
                            else:
                                logger.debug(f"音频音量较低，RMS: {rms:.2f}, 阈值: {volume_threshold}")
                    except Exception as audio_error:
                        # 如果音频处理出错，仍然更新语音时间（保守策略）
                        last_speech_time = time.time()
                        logger.debug(f"音频处理出错但更新语音时间: {audio_error}")

            # 发送音频数据到ASR服务
            asr_client.send_audio(audio_bytes)

    except Exception as e:
        logger.error(f"处理音频数据时出错: {e}")


if __name__ == "__main__":
    """直接运行时启动Web服务"""
    logger.info("启动Web语音识别服务...")

    # 启动静音检测线程
    silence_thread = threading.Thread(target=check_silence_timeout, daemon=True)
    silence_thread.start()
    logger.info("静音检测线程已启动")

    start_web_voice_service(host='0.0.0.0', port=5000)






