# 豆包模型API相关的Pydantic数据模型
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum


class MessageRole(str, Enum):
    """消息角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: MessageRole = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")
    
    class Config:
        use_enum_values = True


class DoubaoConfig(BaseModel):
    """豆包模型配置"""
    api_key: str = Field(..., description="API密钥")
    base_url: str = Field(default="https://ark.cn-beijing.volces.com/api/v3", description="API基础URL")
    model_name: str = Field(default="doubao-1-5-vision-pro-32k-250115", description="模型名称")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="温度参数")
    top_p: float = Field(default=0.9, ge=0.0, le=1.0, description="Top-p参数")
    max_tokens: int = Field(default=4000, ge=1, le=32000, description="最大token数")
    timeout: int = Field(default=180, ge=1, le=300, description="请求超时时间(秒)")

    class Config:
        protected_namespaces = ()


class ChatCompletionRequest(BaseModel):
    """聊天补全请求模型"""
    messages: List[ChatMessage] = Field(..., description="对话消息列表")
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0, description="温度参数")
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0, description="Top-p参数")
    max_tokens: Optional[int] = Field(None, ge=1, le=32000, description="最大token数")
    stream: Optional[bool] = Field(False, description="是否流式输出")
    
    @validator('messages')
    def validate_messages(cls, v):
        if not v:
            raise ValueError("消息列表不能为空")
        return v


class SimpleChatRequest(BaseModel):
    """简单聊天请求模型"""
    prompt: str = Field(..., description="用户输入的prompt")
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0, description="温度参数")
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0, description="Top-p参数")
    max_tokens: Optional[int] = Field(None, ge=1, le=32000, description="最大token数")
    
    @validator('prompt')
    def validate_prompt(cls, v):
        if not v.strip():
            raise ValueError("Prompt不能为空")
        return v.strip()


class TokenUsage(BaseModel):
    """Token使用情况"""
    prompt_tokens: Optional[int] = Field(None, description="输入token数")
    completion_tokens: Optional[int] = Field(None, description="输出token数")
    total_tokens: Optional[int] = Field(None, description="总token数")


class ChatCompletionResponse(BaseModel):
    """聊天补全响应模型"""
    success: bool = Field(..., description="请求是否成功")
    response_text: str = Field(default="", description="模型回复内容")
    model: str = Field(..., description="使用的模型名称")
    usage: Optional[TokenUsage] = Field(None, description="Token使用情况")
    timestamp: str = Field(..., description="响应时间戳")
    error: Optional[str] = Field(None, description="错误信息")
    data: Optional[Dict[str, Any]] = Field(None, description="原始API响应数据")


class ModelInfo(BaseModel):
    """模型信息"""
    model_name: str = Field(..., description="模型名称")
    api_key: Optional[str] = Field(None, description="API密钥(脱敏)")
    base_url: str = Field(..., description="API基础URL")

    class Config:
        protected_namespaces = ()


class ConnectionStatus(BaseModel):
    """连接状态"""
    is_connected: bool = Field(..., description="是否连接成功")
    message: str = Field(..., description="状态消息")
    timestamp: str = Field(..., description="检测时间")


class BatchChatRequest(BaseModel):
    """批量聊天请求模型"""
    requests: List[Union[ChatCompletionRequest, SimpleChatRequest]] = Field(..., description="批量请求列表")
    max_concurrent: int = Field(default=3, ge=1, le=10, description="最大并发数")
    
    @validator('requests')
    def validate_requests(cls, v):
        if not v:
            raise ValueError("请求列表不能为空")
        if len(v) > 50:
            raise ValueError("批量请求数量不能超过50个")
        return v


class BatchChatResponse(BaseModel):
    """批量聊天响应模型"""
    results: List[ChatCompletionResponse] = Field(..., description="批量响应结果")
    total_requests: int = Field(..., description="总请求数")
    successful_requests: int = Field(..., description="成功请求数")
    failed_requests: int = Field(..., description="失败请求数")
    total_usage: Optional[TokenUsage] = Field(None, description="总Token使用情况")
    processing_time: float = Field(..., description="处理时间(秒)")


class DoubaoServiceStatus(BaseModel):
    """豆包服务状态"""
    service_name: str = Field(default="DoubaoService", description="服务名称")
    is_healthy: bool = Field(..., description="服务是否健康")
    model_info: ModelInfo = Field(..., description="模型信息")
    connection_status: ConnectionStatus = Field(..., description="连接状态")
    last_check_time: str = Field(..., description="最后检查时间")

    class Config:
        protected_namespaces = ()


# 错误响应模型
class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="错误时间")
    request_id: Optional[str] = Field(None, description="请求ID")


# API响应包装模型
class APIResponse(BaseModel):
    """通用API响应包装"""
    success: bool = Field(..., description="请求是否成功")
    data: Optional[Any] = Field(None, description="响应数据")
    message: str = Field(default="", description="响应消息")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="响应时间")
    
    class Config:
        arbitrary_types_allowed = True
