# 每日学习数据服务
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from influxdb_client import Point

from core.planning.database.influxdb_connection import get_influxdb_manager

logger = logging.getLogger(__name__)


class DailyLearningService:
    """每日学习数据服务类"""
    
    def __init__(self):
        self.influxdb = get_influxdb_manager()
        self.measurement = "daily_learning"
        logger.info("DailyLearningService 初始化完成")
        logger.debug(f"使用 measurement: {self.measurement}")
    
    def add_learning_record(self, child_id: int, subject: str,
                          learning_data: Dict[str, Any]) -> bool:
        """添加每日学习记录

        Args:
            child_id: 孩子ID
            subject: 学科
            learning_data: 学习数据，包含各种学习指标

        Returns:
            bool: 是否成功添加
        """
        start_time = datetime.now(timezone.utc)
        logger.info(f"开始添加学习记录 - 学生ID: {child_id}, 学科: {subject}, 活动类型: {learning_data.get('activity_type', 'N/A')}")
        logger.debug(f"学习数据详情: {learning_data}")

        try:
            # 准备标签（用于索引和分组）
            tags = {
                "child_id": str(child_id),
                "subject": subject,
                "date": datetime.now(timezone.utc).strftime("%Y-%m-%d")
            }
            
            # 添加可选标签
            if "activity_type" in learning_data:
                tags["activity_type"] = learning_data["activity_type"]
            if "difficulty_level" in learning_data:
                tags["difficulty_level"] = str(learning_data["difficulty_level"])
            
            # 准备字段（实际的数值数据）
            fields = {}

            # === 专注度相关 ===
            if "concentration_level" in learning_data:
                fields["concentration_level"] = int(learning_data["concentration_level"])
            if "internal_interruptions" in learning_data:
                fields["internal_interruptions"] = int(learning_data["internal_interruptions"])
            if "desk_leaving_times" in learning_data:
                fields["desk_leaving_times"] = int(learning_data["desk_leaving_times"])

            # === 作业完成情况 ===
            if "homework_completion_rate" in learning_data:
                fields["homework_completion_rate"] = float(learning_data["homework_completion_rate"])
            if "completion_rate" in learning_data:
                fields["completion_rate"] = float(learning_data["completion_rate"])
            if "accuracy_rate" in learning_data:
                fields["accuracy_rate"] = float(learning_data["accuracy_rate"])

            # === 完成耗时 ===
            if "total_duration_minutes" in learning_data:
                fields["total_duration_minutes"] = float(learning_data["total_duration_minutes"])
            if "subject_duration_minutes" in learning_data:
                fields["subject_duration_minutes"] = float(learning_data["subject_duration_minutes"])
            if "task_duration_minutes" in learning_data:
                fields["task_duration_minutes"] = float(learning_data["task_duration_minutes"])
            if "study_duration_minutes" in learning_data:
                fields["study_duration_minutes"] = float(learning_data["study_duration_minutes"])

            # === 时间管理 ===
            if "is_ahead_schedule" in learning_data:
                fields["is_ahead_schedule"] = bool(learning_data["is_ahead_schedule"])
            if "is_behind_schedule" in learning_data:
                fields["is_behind_schedule"] = bool(learning_data["is_behind_schedule"])
            if "schedule_deviation_minutes" in learning_data:
                fields["schedule_deviation_minutes"] = float(learning_data["schedule_deviation_minutes"])

            # === 积分奖励 ===
            if "points_earned" in learning_data:
                fields["points_earned"] = int(learning_data["points_earned"])
            if "bonus_points" in learning_data:
                fields["bonus_points"] = int(learning_data["bonus_points"])

            # === 学科强弱势 ===
            if "is_weak_subject" in learning_data:
                fields["is_weak_subject"] = bool(learning_data["is_weak_subject"])
            if "is_strong_subject" in learning_data:
                fields["is_strong_subject"] = bool(learning_data["is_strong_subject"])
            if "subject_performance_level" in learning_data:
                fields["subject_performance_level"] = int(learning_data["subject_performance_level"])

            # === 评分数据 ===
            if "score" in learning_data:
                fields["score"] = float(learning_data["score"])
            if "max_score" in learning_data:
                fields["max_score"] = float(learning_data["max_score"])

            # === 情感和态度评价 ===
            if "enjoyment_rating" in learning_data:
                fields["enjoyment_rating"] = int(learning_data["enjoyment_rating"])
            if "difficulty_rating" in learning_data:
                fields["difficulty_rating"] = int(learning_data["difficulty_rating"])
            if "motivation_level" in learning_data:
                fields["motivation_level"] = int(learning_data["motivation_level"])

            # === 学习行为指标 ===
            if "questions_asked" in learning_data:
                fields["questions_asked"] = int(learning_data["questions_asked"])
            if "help_requests" in learning_data:
                fields["help_requests"] = int(learning_data["help_requests"])
            if "breaks_taken" in learning_data:
                fields["breaks_taken"] = int(learning_data["breaks_taken"])

            # === 文本字段 ===
            if "notes" in learning_data:
                fields["notes"] = str(learning_data["notes"])
            if "feedback" in learning_data:
                fields["feedback"] = str(learning_data["feedback"])
            if "schedule_summary" in learning_data:
                fields["schedule_summary"] = str(learning_data["schedule_summary"])
            
            # 设置时间戳
            timestamp = learning_data.get("timestamp")
            if timestamp and isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            elif not timestamp:
                timestamp = datetime.now(timezone.utc)
            
            # 写入数据
            success = self.influxdb.write_point(
                measurement=self.measurement,
                tags=tags,
                fields=fields,
                timestamp=timestamp
            )
            
            # 计算操作耗时
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000

            if success:
                logger.info(f"✅ 成功添加学习记录 - 学生ID: {child_id}, 学科: {subject}, 活动类型: {learning_data.get('activity_type', 'N/A')}, 耗时: {duration_ms:.2f}ms")
            else:
                logger.error(f"❌ 添加学习记录失败 - 学生ID: {child_id}, 学科: {subject}, 耗时: {duration_ms:.2f}ms")

            return success

        except Exception as e:
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000
            logger.error(f"❌ 添加学习记录时发生异常 - 学生ID: {child_id}, 学科: {subject}, 错误: {e}, 耗时: {duration_ms:.2f}ms")
            return False
    
    def get_learning_records(self, child_id: int, subject: Optional[str] = None,
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None,
                           limit: int = 100) -> List[Dict[str, Any]]:
        """获取学习记录
        Args:
            child_id: 孩子ID
            subject: 学科（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
            limit: 返回记录数限制

        Returns:
            List[Dict]: 学习记录列表
        """
        start_time = datetime.now(timezone.utc)
        logger.info(f"开始获取学习记录 - 学生ID: {child_id}, 学科: {subject}, 限制: {limit}")
        logger.debug(f"查询参数 - 开始日期: {start_date}, 结束日期: {end_date}")

        try:
            # 构建Flux查询
            query_parts = [
                f'from(bucket: "{self.influxdb.client._bucket if hasattr(self.influxdb.client, "_bucket") else "daily_learning"}")',
                f'|> range(start: {start_date.isoformat() if start_date else "-30d"}, stop: {end_date.isoformat() if end_date else "now()"})',
                f'|> filter(fn: (r) => r._measurement == "{self.measurement}")',
                f'|> filter(fn: (r) => r.child_id == "{child_id}")'
            ]
            
            if subject:
                query_parts.append(f'|> filter(fn: (r) => r.subject == "{subject}")')
            
            query_parts.extend([
                '|> sort(columns: ["_time"], desc: true)',
                f'|> limit(n: {limit})'
            ])
            
            flux_query = '\n  '.join(query_parts)
            
            # 执行查询
            raw_data = self.influxdb.query_data(flux_query)
            
            # 处理查询结果
            records = self._process_query_results(raw_data)

            # 计算操作耗时
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000

            logger.info(f"✅ 成功获取学习记录 - 学生ID: {child_id}, 返回记录数: {len(records)}, 耗时: {duration_ms:.2f}ms")
            return records

        except Exception as e:
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000
            logger.error(f"❌ 获取学习记录时发生异常 - 学生ID: {child_id}, 错误: {e}, 耗时: {duration_ms:.2f}ms")
            return []
    
    def delete_learning_records(self, child_id: int, 
                              start_date: datetime, 
                              end_date: datetime,
                              subject: Optional[str] = None) -> bool:
        """删除指定时间范围的学习记录
        
        Args:
            child_id: 孩子ID
            start_date: 开始日期
            end_date: 结束日期
            subject: 学科（可选）
            
        Returns:
            bool: 是否成功删除
        """
        try:
            # 构建删除条件
            predicate_parts = [f'child_id="{child_id}"']
            
            if subject:
                predicate_parts.append(f'subject="{subject}"')
            
            predicate = ' AND '.join(predicate_parts)
            
            # 执行删除
            success = self.influxdb.delete_data(
                start=start_date,
                stop=end_date,
                predicate=predicate
            )
            
            if success:
                logger.info(f"成功删除孩子{child_id}在{start_date}到{end_date}期间的学习记录")
            else:
                logger.error(f"删除孩子{child_id}的学习记录失败")
            
            return success
            
        except Exception as e:
            logger.error(f"删除学习记录时发生错误: {e}")
            return False
    
    def get_learning_statistics(self, child_id: int, 
                              days: int = 7,
                              subject: Optional[str] = None) -> Dict[str, Any]:
        """获取学习统计信息
        
        Args:
            child_id: 孩子ID
            days: 统计天数
            subject: 学科（可选）
            
        Returns:
            Dict: 统计信息
        """
        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days)
            
            records = self.get_learning_records(
                child_id=child_id,
                subject=subject,
                start_date=start_date,
                end_date=end_date,
                limit=1000
            )
            
            if not records:
                return {
                    "total_records": 0,
                    "total_study_time": 0,
                    "average_completion_rate": 0,
                    "average_accuracy_rate": 0,
                    "average_enjoyment": 0,
                    "subjects_studied": [],
                    "period_days": days
                }
            
            # 计算统计信息
            total_study_time = sum(r.get("study_duration_minutes", 0) for r in records)
            completion_rates = [r.get("completion_rate", 0) for r in records if "completion_rate" in r]
            accuracy_rates = [r.get("accuracy_rate", 0) for r in records if "accuracy_rate" in r]
            enjoyment_ratings = [r.get("enjoyment_rating", 0) for r in records if "enjoyment_rating" in r]
            subjects = list(set(r.get("subject", "") for r in records))
            
            return {
                "total_records": len(records),
                "total_study_time": total_study_time,
                "average_completion_rate": sum(completion_rates) / len(completion_rates) if completion_rates else 0,
                "average_accuracy_rate": sum(accuracy_rates) / len(accuracy_rates) if accuracy_rates else 0,
                "average_enjoyment": sum(enjoyment_ratings) / len(enjoyment_ratings) if enjoyment_ratings else 0,
                "subjects_studied": subjects,
                "period_days": days
            }
            
        except Exception as e:
            logger.error(f"获取学习统计信息时发生错误: {e}")
            return {}
    
    def _process_query_results(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理查询结果，将InfluxDB的原始数据转换为更友好的格式"""
        processed_records = {}
        
        for record in raw_data:
            time_key = record['time'].isoformat() if record['time'] else 'unknown'
            
            if time_key not in processed_records:
                processed_records[time_key] = {
                    'timestamp': record['time'],
                    'child_id': record['tags'].get('child_id'),
                    'subject': record['tags'].get('subject'),
                    'date': record['tags'].get('date'),
                    'activity_type': record['tags'].get('activity_type'),
                    'difficulty_level': record['tags'].get('difficulty_level')
                }
            
            # 添加字段值
            field_name = record['field']
            field_value = record['value']
            processed_records[time_key][field_name] = field_value
        
        return list(processed_records.values())
