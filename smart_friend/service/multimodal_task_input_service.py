# -*- coding: utf-8 -*-
"""
多模态任务输入处理服务
支持base64、bytes、PIL三种图片格式，直接使用多模态模型处理
"""

import logging
import json
import base64
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timezone
from io import BytesIO

# 可选依赖处理
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from core.prompt_generation.prompt_template.task_input_template import (
    TASK_INPUT_PARSING_TEMPLATE, 
    TASK_INPUT_TYPES,
    SUBJECT_MAPPING
)
from service.doubao_service import get_doubao_service
from service.daily_task_service import DailyTaskService

logger = logging.getLogger(__name__)


class MultimodalTaskInputService:
    """多模态任务输入处理服务"""
    
    def __init__(self):
        """初始化服务"""
        self.doubao_service = get_doubao_service()
        self.daily_task_service = DailyTaskService()
    
    async def process_text_input(self, child_id: int, text_content: str) -> Dict[str, Any]:
        """
        处理文本输入
        
        Args:
            child_id: 学生ID
            text_content: 文本内容
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理学生{child_id}的文本输入")
            
            return await self._process_text_input(
                child_id=child_id,
                input_type="text",
                input_content=text_content
            )
            
        except Exception as e:
            logger.error(f"处理文本输入时发生错误: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"处理文本输入失败: {str(e)}",
                "child_id": child_id,
                "input_type": "text",
                "tasks": []
            }
    
    async def process_voice_input(self, child_id: int, voice_text: str) -> Dict[str, Any]:
        """
        处理语音输入（已转换为文本）
        
        Args:
            child_id: 学生ID
            voice_text: 语音转文字结果
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理学生{child_id}的语音输入")
            
            return await self._process_text_input(
                child_id=child_id,
                input_type="voice",
                input_content=voice_text
            )
            
        except Exception as e:
            logger.error(f"处理语音输入时发生错误: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"处理语音输入失败: {str(e)}",
                "child_id": child_id,
                "input_type": "voice",
                "tasks": []
            }
    
    async def process_image_input(self, child_id: int, 
                                image_data: Union[str, bytes, 'Image.Image'],
                                image_format: str = "auto") -> Dict[str, Any]:
        """
        处理图片输入（多模态）
        
        Args:
            child_id: 学生ID
            image_data: 图片数据（base64字符串、字节数据或PIL Image对象）
            image_format: 图片格式（base64/bytes/pil/auto）
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理学生{child_id}的图片输入，格式: {image_format}")
            
            # 转换图片为base64格式
            base64_image = self._convert_to_base64(image_data, image_format)
            
            if not base64_image:
                return {
                    "success": False,
                    "message": "图片格式转换失败",
                    "child_id": child_id,
                    "input_type": "image",
                    "tasks": []
                }
            
            return await self._process_multimodal_input(
                child_id=child_id,
                image_base64=base64_image
            )
            
        except Exception as e:
            logger.error(f"处理图片输入时发生错误: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"处理图片输入失败: {str(e)}",
                "child_id": child_id,
                "input_type": "image",
                "tasks": []
            }

    async def process_image_input_changeTask(self, child_id: int, 
                                image_data: Union[str, bytes, 'Image.Image'],
                                image_format: str = "auto") -> Dict[str, Any]:
        """
        处理图片输入（多模态）
        
        Args:
            child_id: 学生ID
            image_data: 图片数据（base64字符串、字节数据或PIL Image对象）
            image_format: 图片格式（base64/bytes/pil/auto）
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理学生{child_id}的图片输入，格式: {image_format}")
            
            # 转换图片为base64格式
            base64_image = self._convert_to_base64(image_data, image_format)
            
            if not base64_image:
                return {
                    "success": False,
                    "message": "图片格式转换失败",
                    "child_id": child_id,
                    "input_type": "image",
                    "tasks": []
                }
            
            return await self._process_multimodal_input_changeTask(
                child_id=child_id,
                image_base64=base64_image
            )
            
        except Exception as e:
            logger.error(f"处理图片输入时发生错误: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"处理图片输入失败: {str(e)}",
                "child_id": child_id,
                "input_type": "image",
                "tasks": []
            }
    
    def _convert_to_base64(self, image_data: Union[str, bytes, 'Image.Image'], 
                          image_format: str = "auto") -> Optional[str]:
        """
        将不同格式的图片转换为base64
        
        Args:
            image_data: 图片数据
            image_format: 图片格式
            
        Returns:
            Optional[str]: base64编码的图片，失败返回None
        """
        try:
            # 自动检测格式
            if image_format == "auto":
                if isinstance(image_data, str):
                    image_format = "base64"
                elif isinstance(image_data, bytes):
                    image_format = "bytes"
                elif PIL_AVAILABLE and isinstance(image_data, Image.Image):
                    image_format = "pil"
                else:
                    logger.error(f"无法识别图片格式: {type(image_data)}")
                    return None
            
            # 处理base64格式
            if image_format == "base64":
                if isinstance(image_data, str):
                    # 移除data:image前缀（如果有）
                    if image_data.startswith('data:image'):
                        return image_data.split(',')[1]
                    return image_data
                else:
                    logger.error("base64格式要求字符串输入")
                    return None
            
            # 处理bytes格式
            elif image_format == "bytes":
                if isinstance(image_data, bytes):
                    return base64.b64encode(image_data).decode('utf-8')
                else:
                    logger.error("bytes格式要求字节输入")
                    return None
            
            # 处理PIL格式
            elif image_format == "pil":
                if not PIL_AVAILABLE:
                    logger.error("PIL库未安装，无法处理PIL格式")
                    return None
                
                if isinstance(image_data, Image.Image):
                    # 将PIL Image转换为bytes再转base64
                    buffer = BytesIO()
                    # 确保图片格式
                    if image_data.mode in ('RGBA', 'LA', 'P'):
                        image_data = image_data.convert('RGB')
                    image_data.save(buffer, format='JPEG', quality=85)
                    image_bytes = buffer.getvalue()
                    return base64.b64encode(image_bytes).decode('utf-8')
                else:
                    logger.error("PIL格式要求PIL.Image对象")
                    return None
            
            else:
                logger.error(f"不支持的图片格式: {image_format}")
                return None
                
        except Exception as e:
            logger.error(f"图片格式转换失败: {e}")
            return None
    
    async def _process_text_input(self, child_id: int, input_type: str, input_content: str) -> Dict[str, Any]:
        """
        处理文本输入的通用方法
        
        Args:
            child_id: 学生ID
            input_type: 输入类型
            input_content: 输入内容
            
        Returns:
            Dict: 处理结果
        """
        try:
            # 生成解析prompt
            parsing_prompt = TASK_INPUT_PARSING_TEMPLATE.format(
                input_type=TASK_INPUT_TYPES.get(input_type, input_type),
                input_content=input_content
            )
            
            logger.info(f"生成任务解析prompt，输入类型: {input_type}")
            
            # 调用豆包模型解析
            ai_result = self.doubao_service.simple_chat(
                prompt=parsing_prompt,
                temperature=0.3,
                max_tokens=3000
            )
            
            if not ai_result.get("success", False):
                return {
                    "success": False,
                    "message": f"AI模型调用失败: {ai_result.get('error', '未知错误')}",
                    "child_id": child_id,
                    "input_type": input_type,
                    "input_content": input_content,
                    "tasks": []
                }
            
            logger.info("AI模型成功解析任务输入")
            
            # 解析AI响应
            ai_response = ai_result.get("response_text", "")
            parsed_tasks = self._parse_ai_response(ai_response)

            # 如果AI解析失败，使用备用解析方法
            if not parsed_tasks:
                logger.warning("AI解析失败，尝试使用备用解析方法")
                parsed_tasks = self._fallback_parse_text(input_content)

                if not parsed_tasks:
                    return {
                        "success": False,
                        "message": "AI解析和备用解析都失败，无法提取有效的任务数据",
                        "child_id": child_id,
                        "input_type": input_type,
                        "input_content": input_content,
                        "ai_response": ai_response,
                        "tasks": []
                    }
            
            logger.info(f"成功解析出{len(parsed_tasks)}个任务")
            
            # 存储到今日任务表
            stored_task_ids = await self._store_tasks(child_id, parsed_tasks, input_type)
            
            return {
                "success": True,
                "message": f"成功处理{input_type}输入，解析并存储{len(stored_task_ids)}个任务",
                "child_id": child_id,
                "input_type": input_type,
                "input_content": input_content,
                "tasks": parsed_tasks,
                "stored_task_ids": stored_task_ids,
                "total_tasks": len(parsed_tasks),
                "stored_tasks": len(stored_task_ids),
                "parsing_prompt": parsing_prompt,
                "ai_response": ai_response,
                "processed_at": datetime.now(timezone.utc)
            }
            
        except Exception as e:
            logger.error(f"处理文本输入时发生错误: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"处理输入失败: {str(e)}",
                "child_id": child_id,
                "input_type": input_type,
                "input_content": input_content,
                "tasks": []
            }

    async def _process_multimodal_input(self, child_id: int, image_base64: str) -> Dict[str, Any]:
        """
        处理多模态输入（图片+文本）

        Args:
            child_id: 学生ID
            image_base64: base64编码的图片

        Returns:
            Dict: 处理结果
        """
        try:
            # 生成多模态解析prompt
            multimodal_prompt = """
Please analyze this image carefully and extract any homework, study tasks, or learning activities you can see.

TASK REQUIREMENTS:
1. 📷 Look for all text content in the image (handwritten or printed)
2. 📝 Identify homework assignments, study tasks, and time schedules
3. 🏫 Classify tasks by subject (Math, Chinese, English, Science, etc.)
4. 📋 Break down complex tasks into specific sub-tasks
5. ⏰ Extract time requirements and deadlines
6. 🎯 Estimate difficulty level (1-5 scale)

OUTPUT FORMAT:
Please return the learning tasks in JSON format with these fields:

```json
[
  {
    "task_name": "Task name (concise and clear)",
    "subject": "Subject name",
    "description": "Detailed task description",
    "sub_tasks": [
      {
        "task": "Specific sub-task description",
        "source": "Task source (e.g., school homework, parent request)"
      }
    ],
    "estimated_duration": 30,
    "difficulty_level": 3,
    "materials_needed": "Required materials and tools",
    "time_requirement": "Time requirement (if any)",
    "priority": "Priority (high/medium/low)",
    "notes": "Additional notes"
  }
]
```

IMPORTANT NOTES:
- If you see Chinese text, please translate and interpret it accurately
- If it's handwritten, try your best to recognize it
- If it's in table or list format, parse it in order
- If there are dates, include them in the notes
- If the image is unclear, explain the specific issues

Please analyze the image content and output structured learning task data:
"""

            logger.info("调用多模态模型解析图片任务")

            # 调用多模态模型
            ai_result = self.doubao_service.multimodal_chat(
                text_prompt=multimodal_prompt,
                image_data=image_base64,
                temperature=0.3,
                max_tokens=3000
            )

            if not ai_result.get("success", False):
                return {
                    "success": False,
                    "message": f"多模态AI模型调用失败: {ai_result.get('error', '未知错误')}",
                    "child_id": child_id,
                    "input_type": "image",
                    "tasks": []
                }

            logger.info("多模态AI模型成功解析图片任务")

            # 解析AI响应
            ai_response = ai_result.get("response_text", "")
            parsed_tasks = self._parse_ai_response(ai_response)

            if not parsed_tasks:
                return {
                    "success": False,
                    "message": "解析多模态AI响应失败，无法提取有效的任务数据",
                    "child_id": child_id,
                    "input_type": "image",
                    "ai_response": ai_response,
                    "tasks": []
                }

            logger.info(f"成功解析出{len(parsed_tasks)}个任务")

            # 存储到今日任务表
            stored_task_ids = await self._store_tasks(child_id, parsed_tasks, "image")

            return {
                "success": True,
                "message": f"成功处理图片输入，解析并存储{len(stored_task_ids)}个任务",
                "child_id": child_id,
                "input_type": "image",
                "tasks": parsed_tasks,
                "stored_task_ids": stored_task_ids,
                "total_tasks": len(parsed_tasks),
                "stored_tasks": len(stored_task_ids),
                "multimodal_prompt": multimodal_prompt,
                "ai_response": ai_response,
                "processed_at": datetime.now(timezone.utc)
            }

        except Exception as e:
            logger.error(f"处理多模态输入时发生错误: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"处理多模态输入失败: {str(e)}",
                "child_id": child_id,
                "input_type": "image",
                "tasks": []
            }

    async def _process_multimodal_input_changeTask(self, child_id: int, image_base64: str) -> Dict[str, Any]:
        """
        处理多模态输入（图片+文本）

        Args:
            child_id: 学生ID
            image_base64: base64编码的图片

        Returns:
            Dict: 处理结果
        """
        try:
            # 生成多模态解析prompt
            multimodal_prompt = """
你是一个专业的学习任务解析助手，需要分析图片中的学习任务内容并转换为结构化数据。

【任务要求】：
1. 📷 仔细观察图片中的所有文字内容（包括手写字、印刷字）
2. 📝 识别出所有的学习任务、作业要求、时间安排
3. 🏫 将任务正确分类到对应学科（数学、语文、英语、科学、音乐、美术、体育等）
4. 📋 将复杂任务拆解为具体的子任务
5. ⏰ 提取时间要求和截止日期信息
6. 🎯 根据任务内容评估难度等级（1-5级）

【输出格式】：
请将图片中识别的学习任务解析为JSON格式，包含以下字段：

```json
[
  {
    "task_name": "任务名称（简洁明确）",
    "subject": "学科名称",
    "description": "任务详细描述",
    "sub_tasks": [
      {
        "task": "具体子任务描述",
        "source": "任务来源（如：学校作业、家长要求等）"
      }
    ],
    "estimated_duration": 30,
    "difficulty_level": 3,
    "materials_needed": "所需材料和工具",
    "time_requirement": "时间要求（如果有）",
    "priority": "优先级（高/中/低）",
    "notes": "备注信息"
  }
]
```

【特别注意】：
- 如果是手写字迹，请尽量准确识别
- 如果是表格或列表形式，请按顺序解析
- 如果有日期信息，请在notes中标注
- 如果图片不清晰或无法识别，请说明具体问题

请仔细分析图片内容并输出结构化的学习任务数据：
"""

            logger.info("调用多模态模型解析图片任务用于添加任务")

            # 调用多模态模型
            ai_result = self.doubao_service.multimodal_chat(
                text_prompt=multimodal_prompt,
                image_data=image_base64,
                temperature=0.3,
                max_tokens=3000
            )

            if not ai_result.get("success", False):
                return {
                    "success": False,
                    "message": f"多模态AI模型调用失败: {ai_result.get('error', '未知错误')}",
                    "child_id": child_id,
                    "input_type": "image",
                    "tasks": []
                }

            logger.info("多模态AI模型成功解析图片任务")

            # 解析AI响应
            ai_response = ai_result.get("response_text", "")
            parsed_tasks = self._parse_ai_response(ai_response)

            if not parsed_tasks:
                return {
                    "success": False,
                    "message": "解析多模态AI响应失败，无法提取有效的任务数据",
                    "child_id": child_id,
                    "input_type": "image",
                    "ai_response": ai_response,
                    "tasks": []
                }

            logger.info(f"成功解析出{len(parsed_tasks)}个任务用于修改任务")

            # # 存储到今日任务表
            # stored_task_ids = await self._store_tasks(child_id, parsed_tasks, "image")

            return {
                "success": True,
                "message": f"成功处理图片输入，",
                "child_id": child_id,
                "input_type": "image",
                "tasks": parsed_tasks,
                "total_tasks": len(parsed_tasks),
                "multimodal_prompt": multimodal_prompt,
                "ai_response": ai_response,
                "processed_at": datetime.now(timezone.utc)
            }

        except Exception as e:
            logger.error(f"处理多模态输入时发生错误: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"处理多模态输入失败: {str(e)}",
                "child_id": child_id,
                "input_type": "image",
                "tasks": []
            }

    def _parse_ai_response(self, ai_response: str) -> List[Dict[str, Any]]:
        """
        解析AI响应，提取任务数据

        Args:
            ai_response: AI响应文本

        Returns:
            List[Dict]: 解析后的任务列表
        """
        try:
            logger.info("开始解析AI响应")

            # 尝试提取JSON部分
            json_start = ai_response.find('[')
            json_end = ai_response.rfind(']') + 1

            if json_start == -1 or json_end == 0:
                # 如果没有找到数组格式，尝试查找对象格式
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1

                if json_start == -1 or json_end == 0:
                    logger.error("AI响应中未找到有效的JSON格式")
                    return []

            json_str = ai_response[json_start:json_end]

            # 清理JSON字符串
            json_str = self._clean_json_string(json_str)

            # 解析JSON
            parsed_data = json.loads(json_str)

            # 如果是单个对象，转换为列表
            if isinstance(parsed_data, dict):
                parsed_data = [parsed_data]

            # 验证和标准化任务数据
            validated_tasks = []
            for task in parsed_data:
                validated_task = self._validate_task_data(task)
                if validated_task:
                    validated_tasks.append(validated_task)

            logger.info(f"成功解析出{len(validated_tasks)}个有效任务")
            return validated_tasks

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            logger.error(f"原始响应: {ai_response}")
            return []
        except Exception as e:
            logger.error(f"解析AI响应时发生错误: {e}")
            return []

    def _fallback_parse_text(self, text_content: str) -> List[Dict[str, Any]]:
        """
        备用文本解析方法，当AI解析失败时使用简单规则解析

        Args:
            text_content: 原始文本内容

        Returns:
            List[Dict]: 解析出的任务列表
        """
        try:
            # 简单的关键词匹配来推断学科
            subject = "其他"
            if any(keyword in text_content for keyword in ["数学", "算", "计算", "题", "练习册"]):
                subject = "数学"
            elif any(keyword in text_content for keyword in ["语文", "作文", "阅读", "写字", "汉字"]):
                subject = "语文"
            elif any(keyword in text_content for keyword in ["英语", "English", "单词", "英文"]):
                subject = "英语"
            elif any(keyword in text_content for keyword in ["科学", "实验", "观察"]):
                subject = "科学"

            # 估算时长
            estimated_duration = 30
            if "页" in text_content:
                # 尝试提取页数
                import re
                page_match = re.search(r'(\d+)页', text_content)
                if page_match:
                    pages = int(page_match.group(1))
                    estimated_duration = max(pages * 10, 15)  # 每页约10分钟

            # 创建任务
            task = {
                "task_name": text_content[:50],  # 取前50个字符作为任务名
                "subject": subject,
                "description": text_content,
                "estimated_duration": estimated_duration,
                "difficulty_level": 3,
                "materials_needed": "练习册、笔",
                "time_requirement": f"约{estimated_duration}分钟",
                "priority": "中",
                "notes": "通过简单规则解析生成",
                "sub_tasks": []
            }

            logger.info(f"使用备用解析方法生成任务: {task['task_name']}")
            return [task]

        except Exception as e:
            logger.error(f"备用解析方法失败: {e}")
            return []

    def _clean_json_string(self, json_str: str) -> str:
        """清理JSON字符串，移除可能的格式问题"""
        import re

        # 移除markdown代码块标记
        json_str = json_str.replace('```json', '').replace('```', '')

        # 移除多余的空白字符
        json_str = json_str.strip()

        # 替换可能的中文标点
        json_str = json_str.replace('"', '"').replace('"', '"')
        json_str = json_str.replace(''', "'").replace(''', "'")

        # 处理数学表达式，如 "20 * 60" -> 1200
        def replace_math_expr(match):
            try:
                expr = match.group(1)
                # 安全地计算简单的数学表达式
                if re.match(r'^[\d\s\+\-\*/\(\)]+$', expr):
                    result = eval(expr)
                    return str(result)
                else:
                    return expr
            except:
                return match.group(1)

        # 查找并替换数学表达式
        json_str = re.sub(r':\s*([0-9\s\+\-\*/\(\)]+),', lambda m: f': {replace_math_expr(m)},', json_str)
        json_str = re.sub(r':\s*([0-9\s\+\-\*/\(\)]+)\s*}', lambda m: f': {replace_math_expr(m)}}}', json_str)

        # 处理字符串中的连字符问题，如 "Self - study" -> "Self-study"
        json_str = re.sub(r'"([^"]*)\s-\s([^"]*)"', r'"\1-\2"', json_str)

        return json_str

    def _validate_task_data(self, task: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        验证和标准化任务数据

        Args:
            task: 原始任务数据

        Returns:
            Dict: 验证后的任务数据，无效则返回None
        """
        try:
            # 必需字段检查
            task_name = task.get("task_name", "").strip()
            subject = task.get("subject", "").strip()

            # 如果任务名称或学科为空，或者包含"未知"、"乱码"等关键词，说明AI解析失败
            description = str(task.get("description", ""))
            if (not task_name or not subject or
                "未知" in task_name or "未知" in subject or
                "乱码" in description or "无法" in description or
                task_name == "未知任务" or subject == "未知"):
                logger.warning(f"AI解析失败，任务数据无效，将被过滤: {task}")
                return None

            # 标准化学科名称
            standardized_subject = SUBJECT_MAPPING.get(subject, subject)

            # 如果学科仍然是"未知"，尝试从任务名称推断
            if standardized_subject == "未知" or not standardized_subject:
                if "数学" in task_name or "算" in task_name:
                    standardized_subject = "数学"
                elif "语文" in task_name or "作文" in task_name or "阅读" in task_name:
                    standardized_subject = "语文"
                elif "英语" in task_name or "English" in task_name:
                    standardized_subject = "英语"
                else:
                    standardized_subject = "其他"

            # 安全地处理数值字段
            estimated_duration = task.get("estimated_duration")
            if estimated_duration is None or not isinstance(estimated_duration, (int, float)):
                estimated_duration = 30
            estimated_duration = max(int(estimated_duration), 5)  # 最少5分钟

            difficulty_level = task.get("difficulty_level")
            if difficulty_level is None or not isinstance(difficulty_level, (int, float)):
                difficulty_level = 3
            difficulty_level = min(max(int(difficulty_level), 1), 5)  # 1-5级

            # 标准化任务数据
            validated_task = {
                "task_name": task_name,
                "subject": standardized_subject,
                "description": str(task.get("description", "")).strip(),
                "estimated_duration": estimated_duration,
                "difficulty_level": difficulty_level,
                "materials_needed": str(task.get("materials_needed", "")).strip(),
                "time_requirement": str(task.get("time_requirement", "")).strip(),
                "priority": task.get("priority", "中"),
                "notes": str(task.get("notes", "")).strip(),
                "sub_tasks": []
            }

            # 处理子任务
            sub_tasks = task.get("sub_tasks", [])
            if isinstance(sub_tasks, list):
                for sub_task in sub_tasks:
                    if isinstance(sub_task, str):
                        validated_task["sub_tasks"].append({
                            "task": sub_task.strip(),
                            "source": "用户输入"
                        })
                    elif isinstance(sub_task, dict):
                        validated_task["sub_tasks"].append({
                            "task": str(sub_task.get("task", "")).strip(),
                            "source": str(sub_task.get("source", "用户输入"))
                        })

            return validated_task

        except Exception as e:
            logger.error(f"验证任务数据时发生错误: {e}")
            return None

    async def _store_tasks(self, child_id: int, tasks: List[Dict[str, Any]], input_type: str) -> List[int]:
        """
        批量存储任务到今日任务表

        Args:
            child_id: 学生ID
            tasks: 任务列表
            input_type: 输入类型

        Returns:
            List[int]: 成功存储的任务ID列表
        """
        stored_task_ids = []

        for task in tasks:
            try:
                # 构建今日任务数据，匹配DailyTaskService.create_task的参数格式
                task_name = task["task_name"]
                base_description = task["description"]

                # 将子任务信息添加到description中
                description_parts = [base_description] if base_description else []

                sub_tasks = task.get("sub_tasks", [])
                if sub_tasks:
                    description_parts.append("\n【子任务】:")
                    for i, sub_task in enumerate(sub_tasks, 1):
                        if isinstance(sub_task, dict):
                            task_content = sub_task.get("task", "")
                            task_source = sub_task.get("source", "")
                            if task_content:
                                description_parts.append(f"{i}. {task_content}")
                                if task_source and task_source != "用户输入":
                                    description_parts[-1] += f" (来源: {task_source})"
                        elif isinstance(sub_task, str) and sub_task.strip():
                            description_parts.append(f"{i}. {sub_task.strip()}")

                # 合并描述
                description = "\n".join(description_parts)

                # 构建额外参数
                kwargs = {
                    "subject": task["subject"],
                    "estimated_duration": task["estimated_duration"],
                    "task_date": datetime.now(timezone.utc),
                    "status": "pending",  # 默认状态为待完成
                    "notes": f"来源: {input_type}输入. {task.get('notes', '')}",
                    "task_type": "homework",  # 默认为作业类型
                    "confidence_index": 3,  # 默认信心指数
                    "customization": f"优先级: {task.get('priority', '中')}. 材料: {task.get('materials_needed', '')}",
                    "difficulty": f"难度等级: {task.get('difficulty_level', 3)}/5",
                    "solution": task.get("time_requirement", ""),
                }

                # 调用今日任务服务存储
                result = self.daily_task_service.create_task(
                    child_id=child_id,
                    task_name=task_name,
                    description=description,
                    **kwargs
                )

                if result and isinstance(result, dict):
                    task_id = result.get("id")
                    if task_id:
                        stored_task_ids.append(task_id)
                        logger.info(f"成功存储任务: {task['task_name']} (ID: {task_id})")
                    else:
                        logger.error(f"存储任务失败，未获取到任务ID: {task['task_name']}")
                else:
                    logger.error(f"存储任务失败: {task['task_name']}")

            except Exception as e:
                logger.error(f"存储任务时发生错误: {e}")

        logger.info(f"成功存储{len(stored_task_ids)}个任务到今日任务表")
        return stored_task_ids


# 全局服务实例
_multimodal_task_input_service_instance = None


def get_multimodal_task_input_service() -> MultimodalTaskInputService:
    """
    获取多模态任务输入服务实例（单例模式）

    Returns:
        MultimodalTaskInputService: 多模态任务输入服务实例
    """
    global _multimodal_task_input_service_instance
    if _multimodal_task_input_service_instance is None:
        _multimodal_task_input_service_instance = MultimodalTaskInputService()
    return _multimodal_task_input_service_instance
