#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OpenManus Service Layer

Provides centralized OpenManus functionality for the entire wxysmart project.
Handles all OpenManus operations with error handling and fallback mechanisms.
"""

import logging
import time
from typing import Dict, Any, Optional
from openmanus import OpenManusPlanner

logger = logging.getLogger(__name__)

class OpenManusService:
    """
    Centralized OpenManus service for the wxysmart project
    """
    
    def __init__(self):
        """Initialize OpenManus service"""
        self.planner = None
        self.is_available = False
        self._initialize_planner()
    
    def _initialize_planner(self):
        """Initialize OpenManus planner with error handling"""
        try:
            logger.info("🧠 Initializing OpenManus planner...")
            self.planner = OpenManusPlanner()
            self.is_available = True
            logger.info("✅ OpenManus service initialized successfully")
            
            # Test basic functionality
            test_result = self.planner.classify_user_intent("Hello, test message")
            if test_result.get("success"):
                logger.info("✅ OpenManus service test successful")
            else:
                logger.warning("⚠️ OpenManus service test failed")
                
        except Exception as e:
            logger.error(f"❌ OpenManus service initialization failed: {e}")
            self.is_available = False
    
    def is_ready(self) -> bool:
        """Check if OpenManus service is ready"""
        return self.is_available and self.planner is not None
    
    def classify_intent(self, text: str) -> Dict[str, Any]:
        """
        Classify user intent with fallback
        
        Args:
            text: User input text
            
        Returns:
            Dictionary containing classification results
        """
        if not self.is_ready():
            return {
                "success": False,
                "error": "OpenManus service not available",
                "output": {
                    "predicted_class": 0,
                    "predicted_intention": "daily_chat",
                    "confidence": 0.0,
                    "similar_examples": []
                },
                "fallback": True
            }
        
        try:
            return self.planner.classify_user_intent(text)
        except Exception as e:
            logger.error(f"Intent classification failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "output": {
                    "predicted_class": 0,
                    "predicted_intention": "daily_chat",
                    "confidence": 0.0,
                    "similar_examples": []
                },
                "fallback": True
            }
    
    def create_plan(self, objective: str, resources: str = "", context: str = "", child_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Create task plan with fallback
        
        Args:
            objective: Learning objective
            resources: Available resources
            context: Additional context
            child_id: Optional child ID
            
        Returns:
            Dictionary containing the generated plan
        """
        if not self.is_ready():
            return {
                "success": False,
                "error": "OpenManus service not available",
                "plan": {
                    "objective": objective,
                    "detailed_plan": "OpenManus service not available. Please use standard planning features.",
                    "context_used": f"Resources: {resources}\nContext: {context}",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                },
                "status": "fallback",
                "fallback": True
            }
        
        try:
            return self.planner.create_task_plan(objective, resources, context, child_id)
        except Exception as e:
            logger.error(f"Task plan creation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "plan": {
                    "objective": objective,
                    "detailed_plan": f"Error creating plan: {str(e)}",
                    "context_used": f"Resources: {resources}\nContext: {context}",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                },
                "status": "error",
                "fallback": True
            }
    
    def process_user_input(self, user_input: str, child_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Process user input with comprehensive AI analysis
        
        Args:
            user_input: User's input text
            child_id: Optional child ID for personalization
            
        Returns:
            Dictionary containing processed results
        """
        if not self.is_ready():
            return {
                "success": False,
                "error": "OpenManus service not available",
                "final_response": "I'm here to help you learn! What would you like to study today?",
                "intent": {
                    "predicted_class": 0,
                    "predicted_intention": "daily_chat",
                    "confidence": 0.0
                },
                "metadata": {
                    "fallback": True,
                    "child_id": child_id
                }
            }
        
        try:
            return self.planner.process_user_input(user_input, child_id)
        except Exception as e:
            logger.error(f"User input processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "final_response": "I encountered an error, but I'm still here to help you learn!",
                "intent": {
                    "predicted_class": 0,
                    "predicted_intention": "daily_chat",
                    "confidence": 0.0
                },
                "metadata": {
                    "error": str(e),
                    "child_id": child_id
                }
            }
    
    def generate_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate learning report
        
        Args:
            data: Dictionary containing learning data
            
        Returns:
            Dictionary containing the generated report
        """
        if not self.is_ready():
            return {
                "success": False,
                "error": "OpenManus service not available",
                "output": {
                    "report_text": "Report generation not available - OpenManus service offline",
                    "summary": "Service unavailable"
                },
                "fallback": True
            }
        
        try:
            return self.planner.generate_simple_report(data)
        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "output": {
                    "report_text": f"Report generation failed: {str(e)}",
                    "summary": "Error occurred"
                },
                "fallback": True
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get OpenManus service status"""
        if not self.is_ready():
            return {
                "status": "unavailable",
                "ready": False,
                "message": "OpenManus service not available"
            }
        
        try:
            return self.planner.get_status()
        except Exception as e:
            return {
                "status": "error",
                "ready": False,
                "message": f"Status check failed: {str(e)}"
            }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        if not self.is_ready():
            return {"error": "Service not available"}
        
        try:
            return self.planner.get_performance_metrics()
        except Exception as e:
            return {"error": str(e)}

# Global service instance
openmanus_service = OpenManusService()
