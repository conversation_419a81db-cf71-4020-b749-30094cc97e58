# 豆包模型API服务
import logging
import requests
from typing import Dict, Any, Optional, List
from datetime import datetime
import json

from config.config import settings

logger = logging.getLogger(__name__)


class DoubaoAPIException(Exception):
    """豆包API异常类"""
    pass


class DoubaoService:
    """豆包模型API服务类"""
    
    def __init__(self, api_key: str = None, base_url: str = None, model_name: str = None):
        """
        初始化豆包服务
        
        Args:
            api_key: API密钥
            base_url: API基础URL
            model_name: 模型名称
        """
        # 使用传入参数或配置文件中的默认值
        self.api_key = api_key or settings.DOUBAO_API_KEY
        self.base_url = base_url or settings.DOUBAO_BASE_URL
        self.model_name = model_name or settings.DOUBAO_MODEL_NAME
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 默认参数（从配置文件读取）
        self.default_params = {
            "temperature": settings.DOUBAO_TEMPERATURE,
            "top_p": settings.DOUBAO_TOP_P,
            "max_tokens": settings.DOUBAO_MAX_TOKENS,
            "stream": False
        }

        # 请求超时时间
        self.timeout = settings.DOUBAO_TIMEOUT
    
    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stream: Optional[bool] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        调用豆包模型进行对话补全
        
        Args:
            messages: 对话消息列表，格式为 [{"role": "user", "content": "..."}]
            temperature: 温度参数，控制随机性
            top_p: Top-p参数，控制多样性
            max_tokens: 最大token数
            stream: 是否流式输出
            **kwargs: 其他参数
            
        Returns:
            Dict: API响应结果
            
        Raises:
            DoubaoAPIException: API调用失败时抛出
        """
        try:
            url = f"{self.base_url}/chat/completions"
            
            # 构建请求参数
            payload = {
                "model": self.model_name,
                "messages": messages,
                "temperature": temperature or self.default_params["temperature"],
                "top_p": top_p or self.default_params["top_p"],
                "max_tokens": max_tokens or self.default_params["max_tokens"],
                "stream": stream if stream is not None else self.default_params["stream"]
            }
            
            # 添加其他参数
            payload.update(kwargs)
            
            logger.info(f"调用豆包模型API: {url}")
            logger.debug(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
            
            response = requests.post(
                url,
                headers=self.headers,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("豆包模型API调用成功")
                
                return {
                    "success": True,
                    "data": result,
                    "usage": result.get("usage", {}),
                    "response_text": result["choices"][0]["message"]["content"] if result.get("choices") else "",
                    "model": self.model_name,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                logger.error(f"豆包模型API调用失败: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "response_text": "",
                    "model": self.model_name,
                    "timestamp": datetime.now().isoformat()
                }
                
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求异常: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "response_text": "",
                "model": self.model_name,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            error_msg = f"API调用异常: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "response_text": "",
                "model": self.model_name,
                "timestamp": datetime.now().isoformat()
            }
    
    def simple_chat(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        简单对话接口，直接传入prompt

        Args:
            prompt: 用户输入的prompt
            **kwargs: 其他参数

        Returns:
            Dict: API响应结果
        """
        messages = [{"role": "user", "content": prompt}]
        return self.chat_completion(messages, **kwargs)

    def multimodal_chat(self, text_prompt: str, image_data: str,
                       image_format: str = "base64", **kwargs) -> Dict[str, Any]:
        """
        多模态对话接口，支持文本+图片

        Args:
            text_prompt: 文本prompt
            image_data: 图片数据（base64编码）
            image_format: 图片格式，默认base64
            **kwargs: 其他参数

        Returns:
            Dict: API响应结果
        """
        try:
            # 构建多模态消息
            content = [
                {
                    "type": "text",
                    "text": text_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{image_data}" if not image_data.startswith('data:') else image_data
                    }
                }
            ]

            messages = [
                {
                    "role": "user",
                    "content": content
                }
            ]

            # 使用多模态模型
            return self.chat_completion(messages, **kwargs)

        except Exception as e:
            logger.error(f"多模态对话调用失败: {e}")
            return {
                "success": False,
                "error": f"多模态对话失败: {str(e)}",
                "response_text": "",
                "model": self.model_name,
                "timestamp": datetime.now().isoformat()
            }
    
    def get_model_info(self) -> Dict[str, str]:
        """
        获取当前模型信息
        
        Returns:
            Dict: 模型信息
        """
        return {
            "model_name": self.model_name,
            "api_key": self.api_key[:10] + "..." if self.api_key else None,
            "base_url": self.base_url
        }
    
    def validate_connection(self) -> bool:
        """
        验证API连接是否正常
        
        Returns:
            bool: 连接是否正常
        """
        try:
            test_result = self.simple_chat("测试连接", max_tokens=10)
            return test_result.get("success", False)
        except Exception as e:
            logger.error(f"连接验证失败: {e}")
            return False


# 全局服务实例
_doubao_service_instance = None


def get_doubao_service() -> DoubaoService:
    """
    获取豆包服务实例（单例模式）
    
    Returns:
        DoubaoService: 豆包服务实例
    """
    global _doubao_service_instance
    if _doubao_service_instance is None:
        _doubao_service_instance = DoubaoService()
    return _doubao_service_instance


def create_doubao_service(api_key: str = None, base_url: str = None, model_name: str = None) -> DoubaoService:
    """
    创建新的豆包服务实例
    
    Args:
        api_key: API密钥
        base_url: API基础URL
        model_name: 模型名称
        
    Returns:
        DoubaoService: 豆包服务实例
    """
    return DoubaoService(api_key=api_key, base_url=base_url, model_name=model_name)
