# 计划表修改服务
import json
import re
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
import logging

from core.prompt_generation.prompt_template.task_prompt_template import PLAN_MODIFICATION_TEMPLATE
from core.prompt_generation.schemas import PlanModificationRequest, PlanModificationResponse
from service.doubao_service import get_doubao_service
from service.planning_service import PlanningService

logger = logging.getLogger(__name__)


class PlanModificationService:
    """计划表修改服务 - 基于原有计划表和用户修改意见重新生成计划表"""
    
    def __init__(self):
        self.doubao_service = get_doubao_service()
        self.planning_service = PlanningService()
        logger.info("PlanModificationService 初始化完成")
        logger.debug("已初始化豆包服务和规划服务")
    
    async def modify_study_plan(self, request: PlanModificationRequest) -> PlanModificationResponse:
        """
        修改学习计划的完整流程
        
        1. 生成计划表修改prompt
        2. 调用豆包模型生成修改后的计划
        3. 解析AI响应
        4. 可选：保存到时序数据库
        
        Args:
            request: 计划表修改请求
            
        Returns:
            PlanModificationResponse: 完整的修改响应结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始为学生{request.child_id}修改学习计划")
            
            # 步骤1: 生成修改prompt
            logger.info("生成计划表修改prompt")
            
            modification_prompt = self._generate_modification_prompt(request)
            
            if not modification_prompt:
                return PlanModificationResponse(
                    child_id=request.child_id,
                    generated_at=datetime.now(timezone.utc),
                    modification_type=request.modification_type,
                    original_plan=request.original_plan,
                    modification_request=request.modification_request,
                    modified_plan=[],
                    modification_prompt="",
                    success=False,
                    message="生成修改prompt失败"
                )
            
            logger.info("成功生成计划表修改prompt")
            
            # 步骤2: 调用豆包模型
            logger.info("调用豆包模型生成修改后的学习计划")
            
            messages = [
                {
                    "role": "user",
                    "content": modification_prompt
                }
            ]
            
            ai_result = self.doubao_service.chat_completion(
                messages=messages,
                temperature=0.7,
                max_tokens=4000
            )
            
            if not ai_result.get("success", False):
                return PlanModificationResponse(
                    child_id=request.child_id,
                    generated_at=datetime.now(timezone.utc),
                    modification_type=request.modification_type,
                    original_plan=request.original_plan,
                    modification_request=request.modification_request,
                    modified_plan=[],
                    modification_prompt=modification_prompt,
                    ai_response=ai_result.get("response_text", ""),
                    success=False,
                    message="AI模型调用失败",
                    error_details=ai_result.get("error", "未知错误")
                )
            
            ai_response_text = ai_result.get("response_text", "")
            logger.info("豆包模型调用成功，开始解析响应")
            
            # 步骤3: 解析AI响应
            modified_plans = self._parse_ai_response(ai_response_text)
            
            if not modified_plans:
                return PlanModificationResponse(
                    child_id=request.child_id,
                    generated_at=datetime.now(timezone.utc),
                    modification_type=request.modification_type,
                    original_plan=request.original_plan,
                    modification_request=request.modification_request,
                    modified_plan=[],
                    modification_prompt=modification_prompt,
                    ai_response=ai_response_text,
                    success=False,
                    message="AI响应解析失败，无法提取有效的计划数据",
                    error_details="JSON解析失败或格式不正确"
                )
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            logger.info(f"成功为学生{request.child_id}修改学习计划，共{len(modified_plans)}个任务，耗时{processing_time:.2f}秒")
            
            return PlanModificationResponse(
                child_id=request.child_id,
                generated_at=datetime.now(timezone.utc),
                modification_type=request.modification_type,
                original_plan=request.original_plan,
                modification_request=request.modification_request,
                modified_plan=modified_plans,
                modification_prompt=modification_prompt,
                ai_response=ai_response_text,
                success=True,
                message=f"成功修改学习计划，共{len(modified_plans)}个任务"
            )
            
        except Exception as e:
            logger.error(f"修改学习计划时发生错误: {e}", exc_info=True)
            return PlanModificationResponse(
                child_id=request.child_id,
                generated_at=datetime.now(timezone.utc),
                modification_type=request.modification_type,
                original_plan=request.original_plan,
                modification_request=request.modification_request,
                modified_plan=[],
                modification_prompt="",
                success=False,
                message=f"修改学习计划失败: {str(e)}",
                error_details=str(e)
            )
    
    def _generate_modification_prompt(self, request: PlanModificationRequest) -> str:
        """
        生成计划表修改的prompt
        
        Args:
            request: 修改请求
            
        Returns:
            str: 格式化的prompt
        """
        try:
            # 将原有计划转换为JSON字符串
            original_plan_json = json.dumps(request.original_plan, ensure_ascii=False, indent=2)
            
            # 使用模板生成prompt
            prompt = PLAN_MODIFICATION_TEMPLATE.format(
                original_plan=original_plan_json,
                modification_request=request.modification_request,
                modification_type=request.modification_type
            )
            
            return prompt
            
        except Exception as e:
            logger.error(f"生成修改prompt时发生错误: {e}")
            return ""
    
    def _parse_ai_response(self, response_text: str) -> List[Dict[str, Any]]:
        """
        解析AI响应，提取计划数据
        
        Args:
            response_text: AI响应文本
            
        Returns:
            List[Dict]: 解析后的计划列表
        """
        try:
            # 尝试提取JSON内容
            json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
            if json_match:
                json_content = json_match.group(1)
            else:
                # 如果没有找到```json```标记，尝试直接解析整个响应
                json_content = response_text.strip()
            
            # 解析JSON
            plans = json.loads(json_content)
            
            # 验证数据格式
            if isinstance(plans, list):
                return plans
            else:
                logger.warning("AI响应不是列表格式")
                return []
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            logger.debug(f"原始响应: {response_text}")
            return []
        except Exception as e:
            logger.error(f"解析AI响应时发生错误: {e}")
            return []


# 全局服务实例
_plan_modification_service_instance = None


def get_plan_modification_service() -> PlanModificationService:
    """
    获取计划表修改服务实例（单例模式）
    
    Returns:
        PlanModificationService: 计划表修改服务实例
    """
    global _plan_modification_service_instance
    if _plan_modification_service_instance is None:
        _plan_modification_service_instance = PlanModificationService()
    return _plan_modification_service_instance


# 依赖注入函数
def get_plan_modification_service_dep() -> PlanModificationService:
    """
    FastAPI依赖注入函数
    
    Returns:
        PlanModificationService: 计划表修改服务实例
    """
    return get_plan_modification_service()
