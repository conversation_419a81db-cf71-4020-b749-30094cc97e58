#!/usr/bin/env python3
"""
wxysmart + OpenManus Integration Demo

This script demonstrates the seamless integration between wxysmart functionality
and OpenManus enhancements, showing how the system provides backward compatibility
while offering advanced AI capabilities.
"""

import requests
import json
import time
from datetime import datetime

def demo_header():
    """Display demo header"""
    print("🚀 wxysmart + OpenManus Integration Demo")
    print("=" * 60)
    print("This demo shows how wxysmart functionality has been enhanced")
    print("with OpenManus AI capabilities while maintaining compatibility.")
    print("=" * 60)

def demo_basic_health_check():
    """Demo 1: Basic health check"""
    print("\n📋 Demo 1: Health Check & System Status")
    print("-" * 40)
    
    try:
        # Health check
        response = requests.get("http://localhost:8003/api/v1/smart-agent/health", timeout=5)
        if response.status_code == 200:
            health = response.json()
            print(f"✅ System Health: {health.get('status', 'unknown')}")
            print(f"🧠 OpenManus Ready: {health.get('openmanus_ready', False)}")
            print(f"🔗 wxysmart Compatible: {health.get('wxysmart_compatible', False)}")
        
        # System status
        response = requests.get("http://localhost:8003/api/v1/smart-agent/status", timeout=10)
        if response.status_code == 200:
            status = response.json()
            print(f"🛠️ Available Tools: {len(status.get('available_tools', []))}")
            print(f"📊 System Components: {list(status.get('system_components', {}).keys())}")
            
    except Exception as e:
        print(f"❌ Health check failed: {e}")

def demo_intent_comparison():
    """Demo 2: Intent classification comparison"""
    print("\n🎯 Demo 2: Intent Classification Enhancement")
    print("-" * 50)
    print("wxysmart (5 categories) → OpenManus (99 categories)")
    
    test_phrases = [
        "帮我制定学习计划",
        "我想问数学问题",
        "检查我的作业进度",
        "设置提醒时间",
        "我完成了英语练习"
    ]
    
    for i, phrase in enumerate(test_phrases, 1):
        try:
            response = requests.post(
                "http://localhost:8003/api/v1/smart-agent/analyze-intent",
                json={"text": phrase, "return_top_matches": 3},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"{i}. '{phrase}'")
                print(f"   🎯 Intent: {result.get('intent', 'unknown')}")
                print(f"   📊 Confidence: {result.get('confidence', 0.0):.3f}")
                print(f"   🔧 Method: {result.get('classification_method', 'unknown')}")
                print(f"   📏 Embedding Dim: {result.get('embedding_dimension', 0)}")
                
        except Exception as e:
            print(f"{i}. ❌ Failed to analyze: {phrase}")

def demo_voice_processing():
    """Demo 3: Voice processing with OpenManus"""
    print("\n🎤 Demo 3: Voice Processing (wxysmart-compatible)")
    print("-" * 55)
    print("Shows how voice input gets enhanced AI processing")
    
    test_cases = [
        {
            "voice_text": "我需要帮助制定今天的学习计划",
            "description": "Study planning request"
        },
        {
            "voice_text": "我想学习Python编程，从哪里开始？",
            "description": "Programming learning inquiry"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['description']}")
        print(f"   Input: '{case['voice_text']}'")
        print("   Processing...", end="", flush=True)
        
        start_time = time.time()
        
        try:
            response = requests.post(
                "http://localhost:8003/api/v1/smart-agent/process-voice",
                json={
                    "voice_text": case['voice_text'],
                    "child_id": 12345,
                    "context": {"source": "demo", "demo_case": i}
                },
                timeout=30
            )
            
            processing_time = time.time() - start_time
            print(f" ✅ ({processing_time:.1f}s)")
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"   🎯 Intent: {result.get('intent_info', {}).get('intent', 'unknown')}")
                print(f"   📊 Confidence: {result.get('intent_info', {}).get('confidence', 0.0):.3f}")
                print(f"   🛠️ Tool: {result.get('intent_info', {}).get('tool_used', 'unknown')}")
                print(f"   ⚡ Processing: {processing_time:.2f}s")
                
                # Show response preview
                message = result.get('message', '')
                if message:
                    preview = message[:120] + "..." if len(message) > 120 else message
                    print(f"   💬 Response: {preview}")
                
            else:
                print(f"   ❌ Failed: HTTP {response.status_code}")
                
        except Exception as e:
            print(f" ❌ Error: {e}")

def demo_available_tools():
    """Demo 4: Available tools showcase"""
    print("\n🛠️ Demo 4: Available Tools & Capabilities")
    print("-" * 45)
    
    try:
        response = requests.get("http://localhost:8003/api/v1/smart-agent/tools", timeout=10)
        if response.status_code == 200:
            tools_data = response.json()
            if tools_data.get('success'):
                tools = tools_data.get('tools', {})
                print(f"Total Available Tools: {len(tools)}")
                print()
                
                for tool_name, tool_info in tools.items():
                    print(f"🔧 {tool_name}")
                    description = tool_info.get('description', 'No description available')
                    print(f"   {description[:80]}{'...' if len(description) > 80 else ''}")
                    print()
            else:
                print("❌ Failed to retrieve tools")
        else:
            print(f"❌ Tools request failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Tools request error: {e}")

def demo_performance_comparison():
    """Demo 5: Performance comparison"""
    print("\n📈 Demo 5: Performance & Capability Comparison")
    print("-" * 50)
    
    comparison_data = {
        "Intent Categories": {"wxysmart": 5, "OpenManus": 99},
        "Embedding Dimension": {"wxysmart": "Basic", "OpenManus": "384D Jina v4"},
        "Response Quality": {"wxysmart": "Template-based", "OpenManus": "AI-powered"},
        "Tool Integration": {"wxysmart": "None", "OpenManus": "6 specialized tools"},
        "Real-time Processing": {"wxysmart": "Basic", "OpenManus": "Advanced with metrics"},
        "API Compatibility": {"wxysmart": "✅", "OpenManus": "✅ (backward compatible)"}
    }
    
    print("Feature Comparison:")
    print("=" * 50)
    for feature, values in comparison_data.items():
        wxysmart_val = values["wxysmart"]
        openmanus_val = values["OpenManus"]
        print(f"{feature:20} | {wxysmart_val:15} → {openmanus_val}")
    
    print("\n🎯 Key Improvements:")
    print("• 1,980% increase in intent categories (5 → 99)")
    print("• Advanced semantic understanding with Jina embeddings")
    print("• AI-powered responses using Doubao API")
    print("• Real-time performance metrics and monitoring")
    print("• Seamless backward compatibility with wxysmart")

def demo_conclusion():
    """Demo conclusion"""
    print("\n🎉 Demo Conclusion")
    print("-" * 30)
    print("✅ wxysmart functionality preserved")
    print("✅ OpenManus enhancements integrated")
    print("✅ 99 intent categories available")
    print("✅ Real-time voice processing")
    print("✅ Advanced AI-powered responses")
    print("✅ Production-ready API endpoints")
    
    print("\n🌐 Access the system:")
    print("• Main App: http://localhost:8003")
    print("• API Docs: http://localhost:8003/docs")
    print("• Frontend: http://localhost:8003/static/aiChild.html")
    print("• Smart Agent API: http://localhost:8003/api/v1/smart-agent/")
    
    print(f"\n📅 Demo completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """Run the complete demo"""
    demo_header()
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8003/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server not running. Please start with: python main.py")
            return
    except:
        print("❌ Server not accessible. Please start with: python main.py")
        return
    
    # Run demo sections
    demo_basic_health_check()
    demo_intent_comparison()
    demo_voice_processing()
    demo_available_tools()
    demo_performance_comparison()
    demo_conclusion()

if __name__ == "__main__":
    main()
