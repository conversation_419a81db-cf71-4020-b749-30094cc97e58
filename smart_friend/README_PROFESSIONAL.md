# 🚀 Smart Friend - Professional AI Learning Assistant

A sophisticated AI-powered educational companion for children, featuring both standard and OpenManus-enhanced modes.

## 🎯 Quick Start

### Option 1: Interactive Launcher (Recommended)
```bash
python launcher.py
```
Select your preferred mode interactively.

### Option 2: Direct Mode Selection
```bash
# Standard Mode
python launcher.py --mode standard

# OpenManus Enhanced Mode  
python launcher.py --mode openmanus
```

### Option 3: Direct Script Execution
```bash
# Standard Mode
python main_standard.py

# OpenManus Enhanced Mode
python main_openmanus.py
```

## 📋 Application Modes

### 🔧 Standard Mode
**File**: `main_standard.py`  
**Port**: 8003

**Features:**
- Core AI services (Doubao, ASR, TTS)
- User management and learning analytics
- Task planning and daily learning tracking
- Voice interaction and multimodal input
- Real-time communication via Socket.IO

**Use Case**: Basic AI learning assistant functionality

### 🧠 OpenManus Enhanced Mode
**File**: `main_openmanus.py`  
**Port**: 8003

**Features:**
- All standard features PLUS:
- OpenManus AI orchestration framework
- Jina embeddings for semantic understanding (384D vectors)
- Intent classification with confidence scoring
- Multi-step reasoning and task planning
- Context-aware conversation management
- Educational content personalization

**Use Case**: Advanced AI tutoring with intelligent conversation

## 🌐 Access Points

Once started, access your application at:

- **Main Application**: http://localhost:8003
- **API Documentation**: http://localhost:8003/docs
- **Web Interface**: http://localhost:8003/static/aiChild.html
- **Health Check**: http://localhost:8003/health

## 🔧 Configuration

The application uses environment-based configuration:

### Standard Mode Environment
```bash
APP_MODE=standard
ENABLE_OPENMANUS=false
```

### OpenManus Mode Environment
```bash
APP_MODE=openmanus
ENABLE_OPENMANUS=true
OPENMANUS_INTENT_CLASSIFICATION=true
OPENMANUS_SEMANTIC_SEARCH=true
OPENMANUS_CONTEXT_AWARENESS=true
```

## 📊 API Endpoints

### Core Endpoints (Both Modes)
- `POST /api/v1/doubao/simple-chat` - Basic chat with Doubao
- `POST /api/v1/asr/connect` - ASR service connection
- `POST /api/v1/tts/play` - Text-to-speech conversion
- `POST /api/v1/voice-interaction/interact` - Voice interaction
- `GET /health` - Application health check

### OpenManus Enhanced Endpoints (OpenManus Mode Only)
- `POST /api/v1/openmanus/chat` - Complete intelligent conversation
- `POST /api/v1/openmanus/classify-intent` - Intent classification
- `GET /api/v1/openmanus/health` - OpenManus system health
- `GET /api/v1/openmanus/stats` - Performance analytics

## 🧪 Testing

### Test Standard Mode
```bash
curl -X POST "http://localhost:8003/api/v1/doubao/simple-chat" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "Hello, how are you?"}'
```

### Test OpenManus Mode
```bash
curl -X POST "http://localhost:8003/api/v1/openmanus/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Help me study math"}'
```

## 🏗️ Architecture

### Standard Mode Architecture
```
User Input → FastAPI → Service Selection → Direct API Call → Response
```

### OpenManus Enhanced Architecture
```
User Input → FastAPI → OpenManus Framework → Intent Classification (Jina) → 
Task Planning → Multi-Step Execution → Context Integration → 
Doubao Response → Intelligent Output
```

## 📁 Project Structure

```
smart_friend/
├── launcher.py              # Professional application launcher
├── main_standard.py         # Standard mode entry point
├── main_openmanus.py        # OpenManus enhanced entry point
├── app_factory.py           # Application factory for both modes
├── config/
│   └── app_config.py        # Configuration management
├── openmanus.py             # OpenManus framework integration
├── api/                     # API endpoints
├── service/                 # Core services
├── backend/                 # Backend utilities
└── templates/               # Frontend templates
```

## 🔍 Monitoring

### Health Checks
- **Application**: `GET /health`
- **OpenManus**: `GET /api/v1/openmanus/health` (OpenManus mode only)

### Logs
Both modes provide comprehensive logging with different log levels:
- INFO: General application information
- WARNING: Non-critical issues
- ERROR: Critical errors requiring attention

## 🚀 Production Deployment

### Standard Mode Production
```bash
# Set production environment
export APP_MODE=standard
export ENABLE_OPENMANUS=false
export DEBUG=false

# Start application
python main_standard.py
```

### OpenManus Mode Production
```bash
# Set production environment
export APP_MODE=openmanus
export ENABLE_OPENMANUS=true
export DEBUG=false

# Start application
python main_openmanus.py
```

## 🎓 Educational Features

### Standard Mode
- Basic conversational AI
- Voice interaction support
- Learning progress tracking
- Task management

### OpenManus Enhanced Mode
- **Intent Understanding**: Semantic analysis of user goals
- **Personalized Learning**: Age-appropriate, subject-specific responses
- **Context Awareness**: Memory and conversation continuity
- **Multi-step Reasoning**: Complex educational task planning
- **Adaptive Content**: Dynamic difficulty and content adjustment

## 🔧 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using port 8003
   lsof -i :8003
   
   # Kill the process if needed
   kill -9 <PID>
   ```

2. **OpenManus Initialization Failed**
   - Check if Jina embeddings are properly installed
   - Verify dataset files are present
   - Fall back to standard mode if needed

3. **ASR Service Connection Failed**
   - Check network connectivity
   - Verify ASR service is running
   - Review ASR configuration

### Support
For technical support or questions about the Smart Friend application, please refer to the API documentation at `/docs` when the application is running.

---

**Smart Friend** - Transforming education through intelligent AI assistance 🎓✨
