#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Application Factory

This module provides factory functions to create FastAPI applications
in different configurations (standard vs OpenManus-enhanced).
"""

import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

from config.app_config import get_app_config, is_openmanus_enabled, get_app_description, get_app_tags
from backend.utils.logging import setup_logger

# Import routers
from core.planning.endpoints import daily_learning, planning, plan_modification_api
from core.planning.endpoints.task_input_api import router as task_input_router
from core.user_management.api import user_management_router
from core.prompt_generation.api import prompt_generation_router
from core.task_input.api import router as task_inputs_router
from api.v1.endpoints.doubao import router as doubao_router
from api.v1.endpoints.asr_api import router as asr_router
from api.v1.endpoints.asr_socketio_api import router as asr_socketio_router
from api.v1.endpoints.tts_api import router as tts_router
from api.v1.endpoints.voice_interaction_api import router as voice_interaction_router
from api.v1.endpoints.daily_tasks_api import router as daily_tasks_router
from api.v1.endpoints.multimodal_task_input_api import router as multimodal_task_input_router
from api.v1.endpoints.user_plan_actions_api import router as user_plan_actions_router
from api.v1.endpoints.detection_api import router as detection_router
from backend.utils.camera_api import router as camera_router


def create_app() -> FastAPI:
    """
    Create FastAPI application based on configuration
    
    Returns:
        FastAPI: Configured application instance
    """
    config = get_app_config()
    
    # Initialize logging
    logger = setup_logger('app_factory')
    logger.info(f"🚀 Creating Smart Friend application in {config.APP_MODE.value} mode")
    
    # Create FastAPI app
    app_kwargs = {
        "title": config.PROJECT_NAME,
        "description": get_app_description(),
        "version": config.VERSION,
        "docs_url": "/docs",
        "redoc_url": "/redoc",
        "openapi_url": "/openapi.json"
    }
    
    # Add tags metadata
    app_kwargs.update(get_app_tags())
    
    app = FastAPI(**app_kwargs)
    
    # Setup CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include core routers (always available)
    _include_core_routers(app, config)
    
    # Include OpenManus routers if enabled
    if is_openmanus_enabled():
        _include_openmanus_routers(app, config)
        logger.info("✅ OpenManus features enabled")
    else:
        logger.info("📋 Running in standard mode")
    
    # Setup static files
    _setup_static_files(app)
    
    # Setup root endpoint
    _setup_root_endpoint(app)
    
    logger.info("📋 Smart Friend FastAPI application created successfully")
    return app


def _include_core_routers(app: FastAPI, config) -> None:
    """Include core application routers"""
    
    # User management
    app.include_router(
        user_management_router,
        prefix=f"{config.API_V1_STR}/user-management",
        tags=["user-management"]
    )
    
    # Daily learning data
    app.include_router(
        daily_learning.router,
        prefix=f"{config.API_V1_STR}/daily-learning",
        tags=["daily-learning"]
    )
    
    # Planning
    app.include_router(
        planning.router,
        prefix=f"{config.API_V1_STR}/planning",
        tags=["planning"]
    )
    
    # Plan modification
    app.include_router(
        plan_modification_api.router,
        prefix=f"{config.API_V1_STR}/plan-modification",
        tags=["planning"]
    )
    
    # Task input
    app.include_router(
        task_input_router,
        prefix=f"{config.API_V1_STR}/task-input",
        tags=["planning"]
    )
    
    # Prompt generation
    app.include_router(
        prompt_generation_router,
        prefix=f"{config.API_V1_STR}/prompt-generation",
        tags=["prompt-generation"]
    )
    
    # Task inputs
    app.include_router(
        task_inputs_router,
        prefix=f"{config.API_V1_STR}/task-inputs",
        tags=["task-inputs"]
    )
    
    # Doubao model
    app.include_router(
        doubao_router,
        prefix=f"{config.API_V1_STR}",
        tags=["doubao"]
    )
    
    # ASR
    app.include_router(
        asr_router,
        prefix=f"{config.API_V1_STR}",
        tags=["asr"]
    )
    
    # ASR Socket.IO
    app.include_router(
        asr_socketio_router,
        prefix=f"{config.API_V1_STR}",
        tags=["asr-socketio"]
    )
    
    # TTS
    app.include_router(
        tts_router,
        prefix=f"{config.API_V1_STR}",
        tags=["tts"]
    )
    
    # Voice interaction
    app.include_router(
        voice_interaction_router,
        prefix=f"{config.API_V1_STR}",
        tags=["voice-interaction"]
    )
    
    # Daily tasks
    app.include_router(
        daily_tasks_router,
        prefix=f"{config.API_V1_STR}",
        tags=["daily-tasks"]
    )
    
    # Multimodal task input
    app.include_router(
        multimodal_task_input_router,
        prefix=f"{config.API_V1_STR}",
        tags=["multimodal"]
    )
    
    # User plan actions
    app.include_router(
        user_plan_actions_router,
        prefix=f"{config.API_V1_STR}",
        tags=["user-plan-actions"]
    )
    
    # Detection
    app.include_router(
        detection_router,
        prefix=f"{config.API_V1_STR}",
        tags=["detection"]
    )
    
    # Camera
    app.include_router(
        camera_router,
        prefix=f"{config.API_V1_STR}",
        tags=["camera"]
    )


def _include_openmanus_routers(app: FastAPI, config) -> None:
    """Include OpenManus-specific routers"""
    try:
        from api.v1.endpoints.openmanus_api import router as openmanus_router
        
        app.include_router(
            openmanus_router,
            prefix=f"{config.API_V1_STR}",
            tags=["openmanus"]
        )
        
    except ImportError as e:
        logger = logging.getLogger('app_factory')
        logger.warning(f"OpenManus router not available: {e}")


def _setup_static_files(app: FastAPI) -> None:
    """Setup static file serving"""
    try:
        app.mount("/static", StaticFiles(directory="templates"), name="static")
    except Exception as e:
        logger = logging.getLogger('app_factory')
        logger.warning(f"Static files setup failed: {e}")


def _setup_root_endpoint(app: FastAPI) -> None:
    """Setup root endpoint"""
    @app.get("/", include_in_schema=False)
    async def root():
        """Root endpoint - redirect to docs"""
        return FileResponse("templates/aiChild.html")
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        config = get_app_config()

        # Check OpenManus status if enabled
        openmanus_status = "not_enabled"
        if is_openmanus_enabled():
            try:
                # Try to import and check OpenManus status
                from main import get_openmanus_planner
                planner = get_openmanus_planner()
                openmanus_status = "ready" if planner is not None else "not_initialized"
            except Exception:
                openmanus_status = "error"

        return {
            "status": "healthy",
            "mode": config.APP_MODE.value,
            "openmanus_enabled": is_openmanus_enabled(),
            "openmanus_status": openmanus_status,
            "version": config.VERSION,
            "server": "running"
        }
