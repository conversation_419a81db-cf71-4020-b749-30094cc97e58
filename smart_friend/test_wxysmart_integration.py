#!/usr/bin/env python3
"""
Test script for wxysmart integration with OpenManus
This script demonstrates how the enhanced openmanus.py provides
wxysmart-compatible input/output while maintaining OpenManus performance.
"""

import json
import time
from datetime import datetime
from openmanus import OpenManusPlanner

def test_wxysmart_integration():
    """Test the wxysmart integration functionality"""
    
    print("🚀 Testing wxysmart Integration with OpenManus")
    print("=" * 60)
    
    # Initialize OpenManus planner
    print("📋 Initializing OpenManus planner...")
    planner = OpenManusPlanner()
    
    # Test cases that mirror wxysmart functionality
    test_cases = [
        {
            "voice_text": "帮我制定今天的学习计划",
            "child_id": 12345,
            "expected_intent": "学习任务_创建计划"
        },
        {
            "voice_text": "我想问一个数学问题",
            "child_id": 12345,
            "expected_intent": "日常聊天"
        },
        {
            "voice_text": "把数学作业时间改为14:00-15:00",
            "child_id": 12345,
            "expected_intent": "学习任务_修改计划"
        },
        {
            "voice_text": "我完成了英语作业",
            "child_id": 12345,
            "expected_intent": "学习任务_提交作业"
        }
    ]
    
    print(f"🧪 Running {len(test_cases)} test cases...")
    print()
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test Case {i}: {test_case['voice_text']}")
        print("-" * 40)
        
        start_time = time.time()
        
        # Test wxysmart-compatible voice input processing
        result = planner.process_voice_input(
            voice_text=test_case['voice_text'],
            child_id=test_case['child_id']
        )
        
        processing_time = time.time() - start_time
        
        # Display results
        print(f"✅ Success: {result.get('success', False)}")
        print(f"🎯 Intent: {result.get('intent_info', {}).get('intent', 'unknown')}")
        print(f"📊 Confidence: {result.get('intent_info', {}).get('confidence', 0.0):.3f}")
        print(f"⚡ Processing Time: {processing_time:.2f}s")
        print(f"💬 Response: {result.get('message', 'No message')[:100]}...")
        
        # Check if intent matches expected
        detected_intent = result.get('intent_info', {}).get('intent', '')
        expected_intent = test_case['expected_intent']
        intent_match = expected_intent.lower() in detected_intent.lower() or detected_intent.lower() in expected_intent.lower()
        
        print(f"🎯 Intent Match: {'✅' if intent_match else '❌'} (Expected: {expected_intent})")
        
        results.append({
            'test_case': i,
            'input': test_case['voice_text'],
            'success': result.get('success', False),
            'intent': detected_intent,
            'expected_intent': expected_intent,
            'intent_match': intent_match,
            'confidence': result.get('intent_info', {}).get('confidence', 0.0),
            'processing_time': processing_time,
            'response_length': len(result.get('message', ''))
        })
        
        print()
    
    # Performance Summary
    print("📊 Performance Summary")
    print("=" * 60)
    
    performance_summary = planner.get_performance_summary()
    
    print(f"🔧 System Status:")
    for component, status in performance_summary['system_status'].items():
        print(f"   {component}: {status}")
    
    print(f"\n📈 Integration Info:")
    for key, value in performance_summary['integration_info'].items():
        print(f"   {key}: {value}")
    
    print(f"\n⚡ Test Results:")
    successful_tests = sum(1 for r in results if r['success'])
    intent_matches = sum(1 for r in results if r['intent_match'])
    avg_processing_time = sum(r['processing_time'] for r in results) / len(results)
    avg_confidence = sum(r['confidence'] for r in results) / len(results)
    
    print(f"   Successful Tests: {successful_tests}/{len(results)} ({successful_tests/len(results)*100:.1f}%)")
    print(f"   Intent Accuracy: {intent_matches}/{len(results)} ({intent_matches/len(results)*100:.1f}%)")
    print(f"   Average Processing Time: {avg_processing_time:.2f}s")
    print(f"   Average Confidence: {avg_confidence:.3f}")
    
    # Test wxysmart-compatible tools
    print(f"\n🛠️ Available wxysmart-compatible Tools:")
    tools = planner.get_wxysmart_compatible_tools()
    for tool_name, tool_info in tools.items():
        print(f"   {tool_name}: {tool_info['description']}")
    
    # Test intent analysis in wxysmart format
    print(f"\n🎯 Intent Analysis Test (wxysmart format):")
    test_text = "我想学习Python编程"
    intent_analysis = planner.analyze_intent_with_wxysmart_format(test_text)
    print(f"   Input: {test_text}")
    print(f"   Intent: {intent_analysis.get('intent', 'unknown')}")
    print(f"   Confidence: {intent_analysis.get('confidence', 0.0):.3f}")
    print(f"   Method: {intent_analysis.get('classification_method', 'unknown')}")
    
    print(f"\n🎉 wxysmart Integration Test Completed!")
    print(f"📅 Timestamp: {datetime.now().isoformat()}")
    
    return results, performance_summary

def compare_with_original_openmanus():
    """Compare performance with original OpenManus method"""
    
    print("\n🔄 Comparing with Original OpenManus Method")
    print("=" * 60)
    
    planner = OpenManusPlanner()
    test_input = "帮我制定学习计划"
    
    # Test original method
    print("🔧 Testing original process_user_input...")
    start_time = time.time()
    original_result = planner.process_user_input(test_input)
    original_time = time.time() - start_time
    
    # Test wxysmart method
    print("🎤 Testing wxysmart process_voice_input...")
    start_time = time.time()
    wxysmart_result = planner.process_voice_input(test_input, child_id=12345)
    wxysmart_time = time.time() - start_time
    
    print(f"\n📊 Comparison Results:")
    print(f"   Original Method:")
    print(f"     Success: {original_result.get('success', False)}")
    print(f"     Processing Time: {original_time:.2f}s")
    print(f"     Response Type: {type(original_result.get('final_response', ''))}")
    
    print(f"   wxysmart Method:")
    print(f"     Success: {wxysmart_result.get('success', False)}")
    print(f"     Processing Time: {wxysmart_time:.2f}s")
    print(f"     Response Type: {type(wxysmart_result.get('message', ''))}")
    print(f"     Child Context: {'Yes' if wxysmart_result.get('child_context') else 'No'}")
    
    print(f"\n⚡ Performance Comparison:")
    print(f"   Time Difference: {abs(wxysmart_time - original_time):.2f}s")
    print(f"   wxysmart Overhead: {((wxysmart_time - original_time) / original_time * 100):.1f}%")

if __name__ == "__main__":
    try:
        # Run main integration test
        results, performance = test_wxysmart_integration()
        
        # Run comparison test
        compare_with_original_openmanus()
        
        print(f"\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
