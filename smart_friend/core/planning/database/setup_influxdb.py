#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
InfluxDB设置脚本 - 创建bucket和基础配置
"""

import logging
from influxdb_client import InfluxDBClient
from influxdb_client.client.exceptions import InfluxDBError
from config.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def setup_influxdb():
    """设置InfluxDB bucket和基础配置"""
    
    if not settings.INFLUXDB_ENABLED:
        logger.warning("InfluxDB未启用，跳过设置")
        return False
    
    try:
        # 创建InfluxDB客户端
        client = InfluxDBClient(
            url=settings.INFLUXDB_URL,
            token=settings.INFLUXDB_TOKEN,
            org=settings.INFLUXDB_ORG
        )
        
        # 检查连接
        health = client.health()
        if health.status != "pass":
            logger.error(f"InfluxDB健康检查失败: {health.status}")
            return False
        
        logger.info("InfluxDB连接成功")
        
        # 获取buckets API
        buckets_api = client.buckets_api()
        
        # 检查bucket是否已存在
        try:
            existing_bucket = buckets_api.find_bucket_by_name(settings.INFLUXDB_BUCKET)
            if existing_bucket:
                logger.info(f"Bucket '{settings.INFLUXDB_BUCKET}' 已存在")
                return True
        except Exception:
            # Bucket不存在，继续创建
            pass
        
        # 获取组织信息
        orgs_api = client.organizations_api()
        try:
            # 先列出所有组织
            orgs = orgs_api.find_organizations()
            logger.info(f"可用组织: {[org.name for org in orgs]}")
            
            # 尝试使用第一个组织
            if orgs:
                org = orgs[0]
                logger.info(f"使用组织: {org.name} (ID: {org.id})")
            else:
                logger.error("没有找到可用的组织")
                return False
        except Exception as e:
            logger.error(f"获取组织信息失败: {e}")
            return False
        
        # 创建bucket
        try:
            bucket = buckets_api.create_bucket(
                bucket_name=settings.INFLUXDB_BUCKET,
                org=org,
                retention_rules=[],  # 无限期保留
                description="学伴1.0每日学习数据存储"
            )
            logger.info(f"成功创建bucket: {bucket.name}")
            return True
            
        except InfluxDBError as e:
            if "already exists" in str(e).lower():
                logger.info(f"Bucket '{settings.INFLUXDB_BUCKET}' 已存在")
                return True
            else:
                logger.error(f"创建bucket失败: {e}")
                return False
        
    except Exception as e:
        logger.error(f"设置InfluxDB失败: {e}")
        return False
    
    finally:
        if 'client' in locals():
            client.close()


def test_bucket_access():
    """测试bucket访问权限"""
    try:
        from core.planning.database.influxdb_connection import get_influxdb_manager
        
        influxdb = get_influxdb_manager()
        
        if not influxdb.check_connection():
            logger.error("InfluxDB连接失败")
            return False
        
        # 尝试写入测试数据
        test_success = influxdb.write_point(
            measurement="test",
            tags={"test": "true"},
            fields={"value": 1}
        )
        
        if test_success:
            logger.info("测试写入成功")
            
            # 尝试删除测试数据
            from datetime import datetime, timezone, timedelta
            now = datetime.now(timezone.utc)
            delete_success = influxdb.delete_data(
                start=now - timedelta(minutes=1),
                stop=now + timedelta(minutes=1),
                predicate='test="true"'
            )
            
            if delete_success:
                logger.info("测试删除成功")
            else:
                logger.warning("测试删除失败，但这不影响正常使用")
            
            return True
        else:
            logger.error("测试写入失败")
            return False
            
    except Exception as e:
        logger.error(f"测试bucket访问失败: {e}")
        return False


def main():
    """主函数"""
    print("开始设置InfluxDB...")
    print("=" * 50)
    
    # 显示配置信息
    print(f"InfluxDB URL: {settings.INFLUXDB_URL}")
    print(f"组织: {settings.INFLUXDB_ORG}")
    print(f"Bucket: {settings.INFLUXDB_BUCKET}")
    print(f"启用状态: {settings.INFLUXDB_ENABLED}")
    print("=" * 50)
    
    # 设置bucket
    if setup_influxdb():
        print("✓ InfluxDB设置成功")
        
        # 测试访问
        print("\n测试bucket访问权限...")
        if test_bucket_access():
            print("✓ Bucket访问测试成功")
            print("\n🎉 InfluxDB设置完成！现在可以使用API了。")
        else:
            print("✗ Bucket访问测试失败")
            print("请检查Token权限设置")
    else:
        print("✗ InfluxDB设置失败")
        print("请检查配置信息和网络连接")


if __name__ == "__main__":
    main()
