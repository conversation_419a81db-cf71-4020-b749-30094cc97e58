# Pydantic模型 (请求/响应格式)
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class TaskCreate(BaseModel):
    """创建任务的请求模型"""
    title: str
    description: str
    subject: str
    difficulty_level: int

class TaskResponse(BaseModel):
    """任务响应模型"""
    id: int
    title: str
    description: str
    subject: str
    difficulty_level: int
    created_at: datetime

class PlanCreate(BaseModel):
    """创建计划的请求模型"""
    task_id: int
    duration_days: int
    daily_hours: int

class PlanResponse(BaseModel):
    """计划响应模型"""
    id: int
    task_id: int
    duration_days: int
    daily_hours: int
    created_at: datetime
    status: str

class FeedbackCreate(BaseModel):
    """创建反馈的请求模型"""
    plan_id: int
    rating: int
    comment: Optional[str] = None

class FeedbackResponse(BaseModel):
    """反馈响应模型"""
    id: int
    plan_id: int
    rating: int
    comment: Optional[str]
    created_at: datetime


# InfluxDB每日学习数据模型
class DailyLearningCreate(BaseModel):
    """创建每日学习记录的请求模型"""
    child_id: int = Field(..., description="孩子ID")
    subject: str = Field(..., description="学科名称")
    activity_type: Optional[str] = Field(None, description="活动类型：homework, lesson, exercise, test, project")
    difficulty_level: Optional[int] = Field(None, ge=1, le=5, description="难度等级 1-5")

    # === 专注度相关 ===
    concentration_level: Optional[int] = Field(None, ge=1, le=5, description="专注程度 1-5")
    internal_interruptions: Optional[int] = Field(None, ge=0, description="内部中断次数（分心）")
    desk_leaving_times: Optional[int] = Field(None, ge=0, description="离开课桌次数")

    # === 作业完成情况 ===
    homework_completion_rate: Optional[float] = Field(None, ge=0, le=100, description="作业完成率（百分比）")
    completion_rate: Optional[float] = Field(None, ge=0, le=100, description="总体完成率（百分比）")
    accuracy_rate: Optional[float] = Field(None, ge=0, le=100, description="正确率（百分比）")

    # === 完成耗时 ===
    total_duration_minutes: Optional[float] = Field(None, ge=0, description="总耗时（分钟）")
    subject_duration_minutes: Optional[float] = Field(None, ge=0, description="单科耗时（分钟）")
    task_duration_minutes: Optional[float] = Field(None, ge=0, description="任务耗时（分钟）")
    study_duration_minutes: Optional[float] = Field(None, ge=0, description="学习时长（分钟）")

    # === 时间管理 ===
    is_ahead_schedule: Optional[bool] = Field(None, description="是否提前完成")
    is_behind_schedule: Optional[bool] = Field(None, description="是否延迟完成")
    schedule_deviation_minutes: Optional[float] = Field(None, description="与计划时间偏差（分钟，正数为延迟，负数为提前）")

    # === 积分奖励 ===
    points_earned: Optional[int] = Field(None, ge=0, description="获得积分")
    bonus_points: Optional[int] = Field(None, ge=0, description="奖励积分")

    # === 学科强弱势 ===
    is_weak_subject: Optional[bool] = Field(None, description="是否为薄弱学科")
    is_strong_subject: Optional[bool] = Field(None, description="是否为强势学科")
    subject_performance_level: Optional[int] = Field(None, ge=1, le=5, description="学科表现等级 1-5")

    # === 评分数据 ===
    score: Optional[float] = Field(None, ge=0, description="得分")
    max_score: Optional[float] = Field(None, ge=0, description="满分")

    # === 情感和态度评价 ===
    enjoyment_rating: Optional[int] = Field(None, ge=1, le=5, description="享受程度 1-5")
    difficulty_rating: Optional[int] = Field(None, ge=1, le=5, description="难度感受 1-5")
    motivation_level: Optional[int] = Field(None, ge=1, le=5, description="学习动机 1-5")

    # === 学习行为 ===
    questions_asked: Optional[int] = Field(None, ge=0, description="提问次数")
    help_requests: Optional[int] = Field(None, ge=0, description="求助次数")
    breaks_taken: Optional[int] = Field(None, ge=0, description="休息次数")

    # === 文本信息 ===
    notes: Optional[str] = Field(None, description="学习笔记")
    feedback: Optional[str] = Field(None, description="反馈信息")
    schedule_summary: Optional[str] = Field(None, description="作业提前/延迟总结")

    # === 时间戳 ===
    timestamp: Optional[datetime] = Field(None, description="记录时间戳")


class DailyLearningResponse(BaseModel):
    """每日学习记录响应模型"""
    timestamp: datetime
    child_id: str
    subject: str
    date: str
    activity_type: Optional[str] = None
    difficulty_level: Optional[str] = None

    # === 专注度相关 ===
    concentration_level: Optional[int] = None
    internal_interruptions: Optional[int] = None
    desk_leaving_times: Optional[int] = None

    # === 作业完成情况 ===
    homework_completion_rate: Optional[float] = None
    completion_rate: Optional[float] = None
    accuracy_rate: Optional[float] = None

    # === 完成耗时 ===
    total_duration_minutes: Optional[float] = None
    subject_duration_minutes: Optional[float] = None
    task_duration_minutes: Optional[float] = None
    study_duration_minutes: Optional[float] = None

    # === 时间管理 ===
    is_ahead_schedule: Optional[bool] = None
    is_behind_schedule: Optional[bool] = None
    schedule_deviation_minutes: Optional[float] = None

    # === 积分奖励 ===
    points_earned: Optional[int] = None
    bonus_points: Optional[int] = None

    # === 学科强弱势 ===
    is_weak_subject: Optional[bool] = None
    is_strong_subject: Optional[bool] = None
    subject_performance_level: Optional[int] = None

    # === 评分数据 ===
    score: Optional[float] = None
    max_score: Optional[float] = None

    # === 情感和态度评价 ===
    enjoyment_rating: Optional[int] = None
    difficulty_rating: Optional[int] = None
    motivation_level: Optional[int] = None

    # === 学习行为 ===
    questions_asked: Optional[int] = None
    help_requests: Optional[int] = None
    breaks_taken: Optional[int] = None

    # === 文本信息 ===
    notes: Optional[str] = None
    feedback: Optional[str] = None
    schedule_summary: Optional[str] = None


class LearningStatistics(BaseModel):
    """学习统计信息模型"""
    total_records: int = Field(..., description="总记录数")
    total_study_time: float = Field(..., description="总学习时间（分钟）")
    average_completion_rate: float = Field(..., description="平均完成率")
    average_accuracy_rate: float = Field(..., description="平均正确率")
    average_enjoyment: float = Field(..., description="平均享受程度")
    subjects_studied: List[str] = Field(..., description="学习的学科列表")
    period_days: int = Field(..., description="统计周期（天数）")


class DeleteLearningRequest(BaseModel):
    """删除学习记录请求模型"""
    child_id: int = Field(..., description="孩子ID")
    start_date: datetime = Field(..., description="开始日期")
    end_date: datetime = Field(..., description="结束日期")
    subject: Optional[str] = Field(None, description="学科（可选）")


# ==================== 计划管理相关模型 ====================

class SubTask(BaseModel):
    """子任务模型"""
    task: str = Field(..., description="任务内容")
    source: str = Field(..., description="任务来源")

class StudyPlanCreate(BaseModel):
    """创建学习计划的请求模型"""
    child_id: int = Field(..., description="学生ID")
    task_name: str = Field(..., description="任务名称")
    time_slot: str = Field(..., description="时段，格式：HH:MM - HH:MM")
    subject: str = Field(..., description="学科")
    sub_tasks: List[SubTask] = Field(..., description="子任务列表")
    customization: Optional[str] = Field(None, description="定制说明")
    difficulty: Optional[str] = Field(None, description="难点")
    solution: Optional[str] = Field(None, description="解决方案")
    confidence_index: Optional[int] = Field(None, ge=1, le=5, description="执行信心指数(1-5)")
    plan_date: Optional[datetime] = Field(None, description="计划日期")
    status: Optional[str] = Field("pending", description="计划状态")
    notes: Optional[str] = Field(None, description="备注")

class StudyPlanUpdate(BaseModel):
    """更新学习计划的请求模型"""
    task_name: Optional[str] = Field(None, description="任务名称")
    time_slot: Optional[str] = Field(None, description="时段")
    subject: Optional[str] = Field(None, description="学科")
    sub_tasks: Optional[List[SubTask]] = Field(None, description="子任务列表")
    customization: Optional[str] = Field(None, description="定制说明")
    difficulty: Optional[str] = Field(None, description="难点")
    solution: Optional[str] = Field(None, description="解决方案")
    confidence_index: Optional[int] = Field(None, ge=1, le=5, description="执行信心指数(1-5)")
    status: Optional[str] = Field(None, description="计划状态")
    notes: Optional[str] = Field(None, description="备注")

class StudyPlanResponse(BaseModel):
    """学习计划响应模型"""
    plan_id: str = Field(..., description="计划ID")
    child_id: int = Field(..., description="学生ID")
    task_name: str = Field(..., description="任务名称")
    time_slot: str = Field(..., description="时段")
    subject: str = Field(..., description="学科")
    sub_tasks: List[SubTask] = Field(..., description="子任务列表")
    customization: Optional[str] = Field(None, description="定制说明")
    difficulty: Optional[str] = Field(None, description="难点")
    solution: Optional[str] = Field(None, description="解决方案")
    confidence_index: Optional[int] = Field(None, description="执行信心指数")
    plan_date: datetime = Field(..., description="计划日期")
    status: str = Field(..., description="计划状态")
    notes: Optional[str] = Field(None, description="备注")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

class PlanStatistics(BaseModel):
    """计划统计信息模型"""
    total_plans: int = Field(..., description="总计划数")
    completed_plans: int = Field(..., description="已完成计划数")
    pending_plans: int = Field(..., description="待执行计划数")
    in_progress_plans: int = Field(..., description="进行中计划数")
    completion_rate: float = Field(..., description="完成率")
    subjects_distribution: Dict[str, int] = Field(..., description="学科分布")
    average_confidence: float = Field(..., description="平均信心指数")
    period_days: int = Field(..., description="统计周期天数")

class DeletePlanRequest(BaseModel):
    """删除计划的请求模型"""
    child_id: int = Field(..., description="学生ID")
    plan_id: Optional[str] = Field(None, description="计划ID（可选）")
    start_date: Optional[datetime] = Field(None, description="开始日期（可选）")
    end_date: Optional[datetime] = Field(None, description="结束日期（可选）")
    subject: Optional[str] = Field(None, description="学科筛选（可选）")
