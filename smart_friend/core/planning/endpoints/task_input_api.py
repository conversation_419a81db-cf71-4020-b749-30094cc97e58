# -*- coding: utf-8 -*-
"""
任务输入API接口
支持语音、图片、文本三种输入方式的任务创建
"""

import logging
from fastapi import APIRouter, HTTPException, Depends, File, UploadFile, Form
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

from service.task_input_service import get_task_input_service, TaskInputService

logger = logging.getLogger(__name__)

router = APIRouter()


class TextTaskRequest(BaseModel):
    """文本任务输入请求模型"""
    child_id: int = Field(..., description="学生ID")
    text_content: str = Field(..., description="文本内容")


class VoiceTaskRequest(BaseModel):
    """语音任务输入请求模型"""
    child_id: int = Field(..., description="学生ID")
    voice_text: str = Field(..., description="语音转文字结果")


class TaskInputResponse(BaseModel):
    """任务输入响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    child_id: int = Field(..., description="学生ID")
    input_type: str = Field(..., description="输入类型")
    input_content: str = Field(..., description="输入内容")
    tasks: List[Dict[str, Any]] = Field(..., description="解析的任务列表")
    stored_task_ids: Optional[List[int]] = Field(None, description="存储的任务ID列表")
    total_tasks: int = Field(0, description="总任务数")
    stored_tasks: int = Field(0, description="成功存储的任务数")
    processed_at: Optional[datetime] = Field(None, description="处理时间")


@router.post("/text", response_model=TaskInputResponse)
async def process_text_task_input(
    request: TextTaskRequest,
    service: TaskInputService = Depends(get_task_input_service)
):
    """
    处理文本输入的任务
    
    将用户输入的文本内容解析为结构化的今日任务，并存储到数据库。
    
    **请求参数：**
    - **child_id**: 学生ID（必填）
    - **text_content**: 文本内容（必填）
    
    **示例：**
    ```json
    {
        "child_id": 1,
        "text_content": "今天数学作业：完成练习册第15-16页，重点是分数加减法。语文要背诵《静夜思》。"
    }
    ```
    
    **返回结果：**
    - 解析出的结构化任务列表
    - 存储到数据库的任务ID列表
    - 处理统计信息
    """
    try:
        logger.info(f"收到学生{request.child_id}的文本任务输入请求")
        
        # 验证学生ID
        if request.child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 验证文本内容
        if not request.text_content.strip():
            raise HTTPException(
                status_code=400,
                detail="文本内容不能为空"
            )
        
        # 调用服务处理文本输入
        result = await service.process_text_input(
            child_id=request.child_id,
            text_content=request.text_content
        )
        
        if result["success"]:
            logger.info(f"成功处理学生{request.child_id}的文本输入，解析出{result['total_tasks']}个任务")
        else:
            logger.warning(f"学生{request.child_id}的文本输入处理失败: {result['message']}")
        
        return TaskInputResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理文本任务输入请求时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"处理文本输入失败: {str(e)}"
        )


@router.post("/voice", response_model=TaskInputResponse)
async def process_voice_task_input(
    request: VoiceTaskRequest,
    service: TaskInputService = Depends(get_task_input_service)
):
    """
    处理语音输入的任务
    
    将语音转文字的结果解析为结构化的今日任务，并存储到数据库。
    
    **请求参数：**
    - **child_id**: 学生ID（必填）
    - **voice_text**: 语音转文字结果（必填）
    
    **示例：**
    ```json
    {
        "child_id": 1,
        "voice_text": "嗯，今天老师说要做英语听力练习，大概二十分钟吧，还有那个科学实验报告也要写完。"
    }
    ```
    
    **返回结果：**
    - 解析出的结构化任务列表
    - 存储到数据库的任务ID列表
    - 处理统计信息
    """
    try:
        logger.info(f"收到学生{request.child_id}的语音任务输入请求")
        
        # 验证学生ID
        if request.child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 验证语音文字内容
        if not request.voice_text.strip():
            raise HTTPException(
                status_code=400,
                detail="语音文字内容不能为空"
            )
        
        # 调用服务处理语音输入
        result = await service.process_voice_input(
            child_id=request.child_id,
            voice_text=request.voice_text
        )
        
        if result["success"]:
            logger.info(f"成功处理学生{request.child_id}的语音输入，解析出{result['total_tasks']}个任务")
        else:
            logger.warning(f"学生{request.child_id}的语音输入处理失败: {result['message']}")
        
        return TaskInputResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理语音任务输入请求时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"处理语音输入失败: {str(e)}"
        )


@router.post("/image", response_model=TaskInputResponse)
async def process_image_task_input(
    child_id: int = Form(..., description="学生ID"),
    image_file: UploadFile = File(..., description="图片文件"),
    service: TaskInputService = Depends(get_task_input_service)
):
    """
    处理图片输入的任务
    
    从上传的图片中提取文字，解析为结构化的今日任务，并存储到数据库。
    
    **请求参数：**
    - **child_id**: 学生ID（必填）
    - **image_file**: 图片文件（必填，支持jpg、png、gif等格式）
    
    **使用方式：**
    ```bash
    curl -X POST "http://localhost:8000/api/v1/planning/task-input/image" \
      -F "child_id=1" \
      -F "image_file=@homework.jpg"
    ```
    
    **返回结果：**
    - 从图片中识别的文字内容
    - 解析出的结构化任务列表
    - 存储到数据库的任务ID列表
    - 处理统计信息
    """
    try:
        logger.info(f"收到学生{child_id}的图片任务输入请求")
        
        # 验证学生ID
        if child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 验证图片文件
        if not image_file:
            raise HTTPException(
                status_code=400,
                detail="图片文件不能为空"
            )
        
        # 检查文件类型
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp"]
        if image_file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的图片格式: {image_file.content_type}，支持的格式: {', '.join(allowed_types)}"
            )
        
        # 读取图片数据
        image_data = await image_file.read()
        
        if len(image_data) == 0:
            raise HTTPException(
                status_code=400,
                detail="图片文件为空"
            )
        
        # 调用服务处理图片输入
        result = await service.process_image_input(
            child_id=child_id,
            image_data=image_data
        )
        
        if result["success"]:
            logger.info(f"成功处理学生{child_id}的图片输入，解析出{result['total_tasks']}个任务")
        else:
            logger.warning(f"学生{child_id}的图片输入处理失败: {result['message']}")
        
        return TaskInputResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理图片任务输入请求时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"处理图片输入失败: {str(e)}"
        )


@router.post("/image-base64", response_model=TaskInputResponse)
async def process_image_base64_task_input(
    child_id: int = Form(..., description="学生ID"),
    image_base64: str = Form(..., description="Base64编码的图片数据"),
    service: TaskInputService = Depends(get_task_input_service)
):
    """
    处理Base64编码图片输入的任务

    从Base64编码的图片中提取文字，解析为结构化的今日任务，并存储到数据库。
    适用于前端直接传递图片数据的场景。

    **请求参数：**
    - **child_id**: 学生ID（必填）
    - **image_base64**: Base64编码的图片数据（必填）

    **示例：**
    ```json
    {
        "child_id": 1,
        "image_base64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
    }
    ```
    """
    try:
        logger.info(f"收到学生{child_id}的Base64图片任务输入请求")

        # 验证学生ID
        if child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )

        # 验证Base64数据
        if not image_base64.strip():
            raise HTTPException(
                status_code=400,
                detail="Base64图片数据不能为空"
            )

        # 调用服务处理图片输入
        result = await service.process_image_input(
            child_id=child_id,
            image_data=image_base64
        )

        if result["success"]:
            logger.info(f"成功处理学生{child_id}的Base64图片输入，解析出{result['total_tasks']}个任务")
        else:
            logger.warning(f"学生{child_id}的Base64图片输入处理失败: {result['message']}")

        return TaskInputResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理Base64图片任务输入请求时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"处理Base64图片输入失败: {str(e)}"
        )


@router.get("/health")
async def health_check():
    """
    健康检查接口

    检查任务输入服务的状态和依赖服务的连接情况。
    """
    try:
        service = get_task_input_service()

        # 检查豆包服务连接
        doubao_status = service.doubao_service.validate_connection()

        # 检查今日任务服务
        daily_task_status = True  # 简单检查，可以扩展

        return {
            "status": "healthy" if doubao_status and daily_task_status else "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "doubao_service": "connected" if doubao_status else "disconnected",
                "daily_task_service": "connected" if daily_task_status else "disconnected"
            },
            "supported_input_types": ["text", "voice", "image"],
            "supported_image_formats": ["jpeg", "jpg", "png", "gif", "bmp"]
        }

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }


@router.get("/input-types")
async def get_supported_input_types():
    """
    获取支持的输入类型

    返回系统支持的所有输入类型和相关信息。
    """
    return {
        "supported_types": [
            {
                "type": "text",
                "name": "文本输入",
                "description": "直接输入文本内容",
                "endpoint": "/text",
                "method": "POST",
                "example": {
                    "child_id": 1,
                    "text_content": "今天数学作业：完成练习册第15-16页"
                }
            },
            {
                "type": "voice",
                "name": "语音输入",
                "description": "语音转文字后的内容",
                "endpoint": "/voice",
                "method": "POST",
                "example": {
                    "child_id": 1,
                    "voice_text": "今天老师说要做英语听力练习"
                }
            },
            {
                "type": "image",
                "name": "图片输入",
                "description": "上传图片文件，自动识别文字",
                "endpoint": "/image",
                "method": "POST",
                "content_type": "multipart/form-data",
                "supported_formats": ["jpeg", "jpg", "png", "gif", "bmp"]
            },
            {
                "type": "image_base64",
                "name": "Base64图片输入",
                "description": "Base64编码的图片数据",
                "endpoint": "/image-base64",
                "method": "POST",
                "example": {
                    "child_id": 1,
                    "image_base64": "data:image/jpeg;base64,..."
                }
            }
        ],
        "common_fields": {
            "child_id": "学生ID（必填）",
            "response_format": "统一的TaskInputResponse格式"
        }
    }
