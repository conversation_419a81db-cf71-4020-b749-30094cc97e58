# 计划表修改API端点
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime, date
import logging

from core.prompt_generation.schemas import PlanModificationRequest, PlanModificationResponse
from service.plan_modification_service import get_plan_modification_service_dep, PlanModificationService

logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["plan-modification"],
    responses={404: {"description": "Not found"}}
)


@router.post("/modify", response_model=PlanModificationResponse)
async def modify_study_plan(
    request: PlanModificationRequest,
    service: PlanModificationService = Depends(get_plan_modification_service_dep)
):
    """
    修改学习计划
    
    基于原有计划表和用户修改意见，重新生成优化后的学习计划。
    
    **请求参数：**
    - **child_id**: 学生ID（必填）
    - **original_plan**: 原有任务计划表JSON数据（必填）
    - **modification_request**: 用户的修改意见和要求（必填）
    - **modification_type**: 修改类型（可选，默认为"other"）
        - add_task: 添加任务
        - remove_task: 删除任务
        - modify_task: 修改任务
        - adjust_time: 调整时间
        - other: 其他修改
    - **plan_date**: 计划日期（可选）
    
    **响应内容：**
    - **success**: 修改是否成功
    - **message**: 处理结果消息
    - **original_plan**: 原有计划表
    - **modified_plan**: 修改后的计划表
    - **modification_prompt**: 用于修改的完整prompt
    - **ai_response**: AI原始响应
    
    **示例请求：**
    ```json
    {
        "child_id": 1,
        "original_plan": [
            {
                "task_name": "数学作业",
                "time_slot": "18:00 - 18:45",
                "subject": "数学",
                "sub_tasks": [
                    {
                        "task_content": "练习册第9页",
                        "time_slot": "18:00-18:15",
                        "estimated_minutes": 15,
                        "order_index": 1
                    }
                ],
                "customization": "针对乘法表记忆困难",
                "difficulty": "乘法表记忆",
                "solution": "听乘法口诀歌",
                "confidence_index": 3
            }
        ],
        "modification_request": "在数学作业中增加两道应用题练习",
        "modification_type": "add_task"
    }
    ```
    """
    try:
        logger.info(f"收到学生{request.child_id}的计划表修改请求，修改类型: {request.modification_type}")
        
        # 调用服务修改计划
        result = await service.modify_study_plan(request)
        
        if result.success:
            logger.info(f"成功为学生{request.child_id}修改学习计划，共{len(result.modified_plan)}个任务")
        else:
            logger.warning(f"学生{request.child_id}的学习计划修改失败: {result.message}")
        
        return result
        
    except Exception as e:
        logger.error(f"处理计划表修改请求时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"修改学习计划失败: {str(e)}"
        )


class SimplePlanModificationRequest(BaseModel):
    """简化的计划表修改请求"""
    child_id: int = Field(..., description="学生ID")
    original_plan: List[Dict[str, Any]] = Field(..., description="原有任务计划表JSON数据")
    modification_request: str = Field(..., description="用户的修改意见和要求")
    modification_type: str = Field(default="other", description="修改类型")
    plan_date: Optional[date] = Field(None, description="计划日期(YYYY-MM-DD)")


@router.post("/modify-simple")
async def modify_study_plan_simple(
    request: SimplePlanModificationRequest,
    service: PlanModificationService = Depends(get_plan_modification_service_dep)
):
    """
    简化的计划表修改接口
    
    通过表单参数快速修改学习计划，适用于简单的调用场景。
    
    **参数说明：**
    - **child_id**: 学生ID
    - **original_plan**: 原有计划表JSON数据
    - **modification_request**: 修改要求
    - **modification_type**: 修改类型（add_task/remove_task/modify_task/adjust_time/other）
    - **plan_date**: 计划日期（可选）
    
    **示例：**
    ```
    POST /api/v1/planning/plan-modification/modify-simple
    Content-Type: application/json
    
    {
        "child_id": 1,
        "original_plan": [...],
        "modification_request": "增加英语听力练习15分钟",
        "modification_type": "add_task"
    }
    ```
    """
    try:
        # 转换日期格式
        plan_datetime = None
        if request.plan_date:
            plan_datetime = datetime.combine(request.plan_date, datetime.min.time())

        # 构建请求对象
        full_request = PlanModificationRequest(
            child_id=request.child_id,
            original_plan=request.original_plan,
            modification_request=request.modification_request,
            modification_type=request.modification_type,
            plan_date=plan_datetime
        )

        logger.info(f"收到简化计划表修改请求，学生ID: {request.child_id}")
        
        # 调用服务修改计划
        result = await service.modify_study_plan(full_request)

        if result.success:
            logger.info(f"成功为学生{request.child_id}修改学习计划")
        else:
            logger.warning(f"学生{request.child_id}的学习计划修改失败: {result.message}")
        
        return result
        
    except Exception as e:
        logger.error(f"处理简化计划表修改请求时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"修改学习计划失败: {str(e)}"
        )


@router.post("/batch-modify")
async def modify_study_plans_batch(
    requests: List[PlanModificationRequest],
    service: PlanModificationService = Depends(get_plan_modification_service_dep)
):
    """
    批量修改学习计划
    
    为多个学生或多个计划同时进行修改。
    
    **请求体：**
    - **requests**: 修改请求列表
    
    **响应：**
    - 返回每个修改请求的处理结果
    
    **示例：**
    ```json
    [
        {
            "child_id": 1,
            "original_plan": [...],
            "modification_request": "增加数学练习",
            "modification_type": "add_task"
        },
        {
            "child_id": 2,
            "original_plan": [...],
            "modification_request": "调整作业时间",
            "modification_type": "adjust_time"
        }
    ]
    ```
    """
    try:
        logger.info(f"收到批量计划表修改请求，数量: {len(requests)}")
        
        results = []
        
        for request in requests:
            try:
                # 调用服务修改计划
                result = await service.modify_study_plan(request)
                results.append({
                    "child_id": request.child_id,
                    "result": result
                })
                
                logger.info(f"学生{request.child_id}的计划修改完成，成功: {result.success}")
                
            except Exception as e:
                logger.error(f"为学生{request.child_id}修改计划时发生错误: {e}")
                results.append({
                    "child_id": request.child_id,
                    "result": PlanModificationResponse(
                        child_id=request.child_id,
                        generated_at=datetime.now(),
                        modification_type=request.modification_type,
                        original_plan=request.original_plan,
                        modification_request=request.modification_request,
                        modified_plan=[],
                        modification_prompt="",
                        success=False,
                        message=f"修改失败: {str(e)}",
                        error_details=str(e)
                    )
                })
        
        success_count = sum(1 for r in results if r["result"].success)
        logger.info(f"批量计划表修改完成，成功: {success_count}/{len(requests)}")
        
        return {
            "total_requests": len(requests),
            "successful_modifications": success_count,
            "failed_modifications": len(requests) - success_count,
            "results": results
        }
        
    except Exception as e:
        logger.error(f"处理批量计划表修改请求时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"批量修改学习计划失败: {str(e)}"
        )
