# Pydantic模型 - 每日任务请求/响应格式
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from enum import Enum


class TaskStatusEnum(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    OVERDUE = "overdue"


class TaskTypeEnum(str, Enum):
    """任务类型枚举"""
    HOMEWORK = "homework"
    READING = "reading"
    EXERCISE = "exercise"
    PROJECT = "project"
    REVIEW = "review"
    PREVIEW = "preview"
    OTHER = "other"


class TaskItemStatusEnum(str, Enum):
    """任务子项状态枚举"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"


class TaskItemCreate(BaseModel):
    """创建子任务的请求模型"""
    task_content: str = Field(..., description="子任务内容")
    task_source: Optional[str] = Field(None, description="任务来源")
    time_slot: Optional[str] = Field(None, description="时间段，格式：HH:MM-HH:MM")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    order_index: Optional[int] = Field(None, description="执行顺序")
    estimated_minutes: Optional[int] = Field(None, ge=0, description="预计用时(分钟)")


class TaskItemUpdate(BaseModel):
    """更新子任务的请求模型"""
    id: Optional[int] = Field(None, description="子任务ID，新建时不填")
    task_content: Optional[str] = Field(None, description="子任务内容")
    task_source: Optional[str] = Field(None, description="任务来源")
    time_slot: Optional[str] = Field(None, description="时间段，格式：HH:MM-HH:MM")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    order_index: Optional[int] = Field(None, description="执行顺序")
    is_completed: Optional[bool] = Field(None, description="是否完成")
    completion_time: Optional[datetime] = Field(None, description="完成时间")
    estimated_minutes: Optional[int] = Field(None, ge=0, description="预计用时(分钟)")
    actual_minutes: Optional[int] = Field(None, ge=0, description="实际用时(分钟)")
    difficulty_rating: Optional[int] = Field(None, ge=1, le=5, description="难度评分(1-5)")
    quality_rating: Optional[int] = Field(None, ge=1, le=5, description="质量评分(1-5)")
    notes: Optional[str] = Field(None, description="备注")
    points_earned: Optional[int] = Field(None, ge=0, description="获得积分")
    bonus_points: Optional[int] = Field(None, ge=0, description="奖励积分")
    points_reason: Optional[str] = Field(None, description="积分说明")


class TaskItemResponse(BaseModel):
    """子任务响应模型"""
    id: int
    daily_task_id: int
    task_content: str
    task_source: Optional[str] = None
    time_slot: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    order_index: Optional[int] = None
    status: str
    is_completed: bool
    completion_time: Optional[datetime] = None
    estimated_minutes: Optional[int] = None
    actual_minutes: Optional[int] = None
    difficulty_rating: Optional[int] = None
    quality_rating: Optional[int] = None
    notes: Optional[str] = None
    points_earned: Optional[int] = None
    bonus_points: Optional[int] = None
    points_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class DailyTaskCreateSimple(BaseModel):
    """创建每日任务的简化请求模型（只需三个必填字段）"""
    child_id: int = Field(..., description="学生ID")
    task_name: str = Field(..., description="任务名称")
    description: Optional[str] = Field("", description="任务描述")


class DailyTaskCreate(BaseModel):
    """创建每日任务的完整请求模型"""
    child_id: int = Field(..., description="学生ID")
    task_name: str = Field(..., description="任务名称")
    description: Optional[str] = Field("", description="任务描述")

    # 可选字段
    task_date: Optional[datetime] = Field(None, description="任务日期，默认当前时间")
    time_slot: Optional[str] = Field(None, description="时段，格式：HH:MM-HH:MM")
    estimated_duration: Optional[int] = Field(None, ge=0, description="预计用时(分钟)")
    subject: Optional[str] = Field("其他", description="学科，默认'其他'")
    task_type: Optional[TaskTypeEnum] = Field(TaskTypeEnum.HOMEWORK, description="任务类型")
    customization: Optional[str] = Field(None, description="定制说明")
    difficulty: Optional[str] = Field(None, description="预期难点")
    solution: Optional[str] = Field(None, description="解决方案")
    confidence_index: Optional[int] = Field(None, ge=1, le=5, description="执行信心指数(1-5)")
    status: Optional[TaskStatusEnum] = Field(TaskStatusEnum.PENDING, description="任务状态")
    notes: Optional[str] = Field(None, description="备注")
    sub_tasks: Optional[List[TaskItemCreate]] = Field([], description="子任务列表")


class DailyTaskUpdate(BaseModel):
    """更新每日任务的请求模型"""
    task_name: Optional[str] = Field(None, description="任务名称")
    task_date: Optional[datetime] = Field(None, description="任务日期")
    time_slot: Optional[str] = Field(None, description="时段，格式：HH:MM-HH:MM")
    estimated_duration: Optional[int] = Field(None, ge=0, description="预计用时(分钟)")
    actual_duration: Optional[int] = Field(None, ge=0, description="实际用时(分钟)")
    subject: Optional[str] = Field(None, description="学科")
    task_type: Optional[TaskTypeEnum] = Field(None, description="任务类型")
    description: Optional[str] = Field(None, description="任务描述")
    customization: Optional[str] = Field(None, description="定制说明")
    difficulty: Optional[str] = Field(None, description="预期难点")
    solution: Optional[str] = Field(None, description="解决方案")
    confidence_index: Optional[int] = Field(None, ge=1, le=5, description="执行信心指数(1-5)")
    status: Optional[TaskStatusEnum] = Field(None, description="任务状态")
    completion_percentage: Optional[float] = Field(None, ge=0, le=100, description="完成百分比")
    difficulty_rating: Optional[int] = Field(None, ge=1, le=5, description="实际难度评分(1-5)")
    satisfaction_rating: Optional[int] = Field(None, ge=1, le=5, description="满意度评分(1-5)")
    notes: Optional[str] = Field(None, description="备注")
    total_points: Optional[int] = Field(None, ge=0, description="总积分")
    bonus_points: Optional[int] = Field(None, ge=0, description="奖励积分")
    points_reason: Optional[str] = Field(None, description="积分说明")
    sub_tasks: Optional[List[TaskItemUpdate]] = Field(None, description="子任务列表")


class DailyTaskResponse(BaseModel):
    """每日任务响应模型"""
    id: int
    child_id: int
    task_name: str
    task_date: datetime
    time_slot: Optional[str] = None
    estimated_duration: Optional[int] = None
    actual_duration: Optional[int] = None
    subject: str
    task_type: Optional[str] = None
    description: Optional[str] = None
    customization: Optional[str] = None
    difficulty: Optional[str] = None
    solution: Optional[str] = None
    confidence_index: Optional[int] = None
    status: str
    completion_percentage: Optional[float] = None
    difficulty_rating: Optional[int] = None
    satisfaction_rating: Optional[int] = None
    notes: Optional[str] = None
    total_points: Optional[int] = None
    bonus_points: Optional[int] = None
    points_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    task_items: List[TaskItemResponse] = []


class TaskStatusUpdate(BaseModel):
    """任务状态更新请求模型"""
    status: TaskStatusEnum = Field(..., description="新状态")
    completion_percentage: Optional[float] = Field(None, ge=0, le=100, description="完成百分比")


class TaskStatistics(BaseModel):
    """任务统计响应模型"""
    total_tasks: int = Field(..., description="总任务数")
    completed_tasks: int = Field(..., description="已完成任务数")
    pending_tasks: int = Field(..., description="待执行任务数")
    in_progress_tasks: int = Field(..., description="进行中任务数")
    completion_rate: float = Field(..., description="完成率(%)")
    average_completion_percentage: float = Field(..., description="平均完成百分比")
    subjects_distribution: Dict[str, int] = Field(..., description="学科分布")
    period_days: int = Field(..., description="统计周期(天)")


class YesterdayTaskSummary(BaseModel):
    """昨日任务摘要（用于prompt4）"""
    subject: str = Field(..., description="学科")
    task_name: str = Field(..., description="任务名称")
    completion_status: str = Field(..., description="完成状态")
    completion_percentage: float = Field(..., description="完成百分比")
    time_spent: int = Field(..., description="用时(分钟)")
    difficulties: Optional[str] = Field(None, description="遇到的困难")
    notes: Optional[str] = Field(None, description="备注")


class DeleteTaskRequest(BaseModel):
    """删除任务请求模型"""
    task_id: int = Field(..., description="要删除的任务ID")
    reason: Optional[str] = Field(None, description="删除原因")


class BatchTaskCreate(BaseModel):
    """批量创建任务请求模型"""
    child_id: int = Field(..., description="学生ID")
    task_date: date = Field(..., description="任务日期")
    tasks: List[Dict[str, Any]] = Field(..., description="任务列表")


class TaskQueryParams(BaseModel):
    """任务查询参数模型"""
    child_id: int = Field(..., description="学生ID")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    subject: Optional[str] = Field(None, description="学科筛选")
    status: Optional[TaskStatusEnum] = Field(None, description="状态筛选")
    task_type: Optional[TaskTypeEnum] = Field(None, description="任务类型筛选")
    limit: Optional[int] = Field(100, ge=1, le=1000, description="返回记录数限制")


# 用于与现有prompt系统兼容的模型
class PromptTaskSummary(BaseModel):
    """用于prompt生成的任务摘要"""
    task_name: str = Field(..., description="任务名称")
    time_slot: str = Field(..., description="时段")
    subject: str = Field(..., description="学科")
    sub_tasks: List[str] = Field(..., description="子任务列表")
    customization: Optional[str] = Field(None, description="定制说明")
    difficulty: Optional[str] = Field(None, description="难点")
    solution: Optional[str] = Field(None, description="解决方案")
    confidence_index: Optional[int] = Field(None, description="执行信心指数(1-5)")
    plan_date: Optional[date] = Field(None, description="计划日期")
    status: Optional[str] = Field("pending", description="状态")
    notes: Optional[str] = Field(None, description="备注")
