# 小孩管理API端点
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
import logging

from ..service import user_management_service, UserManagementService
from ..schemas import (
    ChildCreate, ChildUpdate, ChildResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


def get_user_management_service() -> UserManagementService:
    """获取用户管理服务依赖"""
    return user_management_service


@router.post("/", response_model=ChildResponse, status_code=status.HTTP_201_CREATED)
async def create_child(
    child_data: ChildCreate,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    创建小孩
    
    - **name**: 小孩姓名（必填）
    - **nickname**: 昵称（可选）
    - **gender**: 性别（可选）
    - **birth_date**: 出生日期（可选）
    - **age**: 年龄（可选，0-18岁）
    - **academic_level**: 学业等级（可选）
    - **school_name**: 学校名称（可选）
    - **height**: 身高（可选，单位：cm）
    - **weight**: 体重（可选，单位：kg）
    - **overall_health_status**: 整体健康状况（可选）
    - **personality_traits**: 性格特点（可选）
    - **learning_style**: 学习风格（可选）
    - **behavior_level**: 行为表现等级（可选）
    - **attention_span_minutes**: 注意力持续时间（可选，单位：分钟）
    - **favorite_subjects**: 喜欢的学科（可选）
    - **notes**: 备注信息（可选）
    """
    try:
        child = service.create_child(child_data)
        if not child:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建小孩失败，请检查输入数据"
            )
        
        return child
        
    except Exception as e:
        logger.error(f"创建小孩时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建小孩失败: {str(e)}"
        )


@router.get("/{child_id}", response_model=ChildResponse)
async def get_child(
    child_id: int,
    include_relations: bool = Query(False, description="是否包含关联信息"),
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    根据ID获取小孩信息
    
    - **child_id**: 小孩ID
    - **include_relations**: 是否包含关联的家长和学业记录信息
    """
    try:
        child = service.get_child(child_id, include_relations=include_relations)
        if not child:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到ID为{child_id}的小孩"
            )
        
        return child
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取小孩信息时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取小孩信息失败: {str(e)}"
        )


@router.get("/", response_model=List[ChildResponse])
async def get_children(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    is_active: bool = Query(True, description="是否只返回激活的小孩"),
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    获取小孩列表
    
    - **skip**: 跳过的记录数（分页用）
    - **limit**: 返回的记录数（1-1000）
    - **is_active**: 是否只返回激活的小孩
    """
    try:
        children = service.get_children(skip=skip, limit=limit, is_active=is_active)
        return children
        
    except Exception as e:
        logger.error(f"获取小孩列表时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取小孩列表失败: {str(e)}"
        )


@router.put("/{child_id}", response_model=ChildResponse)
async def update_child(
    child_id: int,
    child_data: ChildUpdate,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    更新小孩信息
    
    - **child_id**: 小孩ID
    - 其他字段均为可选，只更新提供的字段
    """
    try:
        child = service.update_child(child_id, child_data)
        if not child:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到ID为{child_id}的小孩或更新失败"
            )
        
        return child
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新小孩信息时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新小孩信息失败: {str(e)}"
        )


@router.delete("/{child_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_child(
    child_id: int,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    软删除小孩（将is_active设为False）
    
    - **child_id**: 小孩ID
    """
    try:
        success = service.delete_child(child_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到ID为{child_id}的小孩或删除失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除小孩时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除小孩失败: {str(e)}"
        )


@router.get("/{child_id}/parents", response_model=List[dict])
async def get_child_parents(
    child_id: int,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    获取小孩的所有家长
    
    - **child_id**: 小孩ID
    """
    try:
        parents = service.get_child_parents(child_id)
        return parents
        
    except Exception as e:
        logger.error(f"获取小孩家长信息时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取小孩家长信息失败: {str(e)}"
        )


@router.get("/{child_id}/academic-records", response_model=List[dict])
async def get_child_academic_records(
    child_id: int,
    subject: str = Query(None, description="学科筛选"),
    academic_year: str = Query(None, description="学年筛选"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    获取小孩的学业记录
    
    - **child_id**: 小孩ID
    - **subject**: 学科筛选（可选）
    - **academic_year**: 学年筛选（可选）
    - **limit**: 返回的记录数（1-1000）
    """
    try:
        records = service.get_child_academic_records(
            child_id=child_id,
            subject=subject,
            academic_year=academic_year,
            limit=limit
        )
        return [record.model_dump() for record in records]
        
    except Exception as e:
        logger.error(f"获取小孩学业记录时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取小孩学业记录失败: {str(e)}"
        )
