# 用户信息管理API路由汇总
from fastapi import APIRouter

from .endpoints import parents, children, relationships

# 创建用户管理API路由器
user_management_router = APIRouter()

# 包含各个模块的路由
user_management_router.include_router(
    parents.router, 
    prefix="/parents", 
    tags=["家长管理"]
)

user_management_router.include_router(
    children.router, 
    prefix="/children", 
    tags=["小孩管理"]
)

user_management_router.include_router(
    relationships.router, 
    prefix="/relationships", 
    tags=["关系管理"]
)
