#!/usr/bin/env python3
# 用户管理数据库初始化脚本
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.exc import SQLAlchemyError
import logging
from core.user_management.database.connection import get_sqlite_manager
from core.user_management.models.user_models import Base

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_tables():
    """创建所有表"""
    try:
        sqlite_manager = get_sqlite_manager()
        
        if not sqlite_manager.engine:
            logger.error("数据库引擎未初始化")
            return False
        
        # 创建所有表
        Base.metadata.create_all(bind=sqlite_manager.engine)
        logger.info("成功创建所有数据表")
        
        # 验证表是否创建成功
        table_names = sqlite_manager.get_table_names()
        expected_tables = ['parents', 'children', 'child_parent_relationships', 'child_academic_records', 'user_task_inputs']
        
        for table in expected_tables:
            if table in table_names:
                logger.info(f"✓ 表 '{table}' 创建成功")
            else:
                logger.error(f"✗ 表 '{table}' 创建失败")
                return False
        
        return True
        
    except SQLAlchemyError as e:
        logger.error(f"创建表失败: {e}")
        return False


def drop_tables():
    """删除所有表"""
    try:
        sqlite_manager = get_sqlite_manager()
        
        if not sqlite_manager.engine:
            logger.error("数据库引擎未初始化")
            return False
        
        # 删除所有表
        Base.metadata.drop_all(bind=sqlite_manager.engine)
        logger.info("成功删除所有数据表")
        return True
        
    except SQLAlchemyError as e:
        logger.error(f"删除表失败: {e}")
        return False


def reset_database():
    """重置数据库（删除后重新创建）"""
    logger.info("开始重置数据库...")
    
    # 删除现有表
    if not drop_tables():
        logger.error("删除表失败，重置中止")
        return False
    
    # 重新创建表
    if not create_tables():
        logger.error("创建表失败，重置中止")
        return False
    
    logger.info("数据库重置完成")
    return True


def check_database():
    """检查数据库状态"""
    try:
        sqlite_manager = get_sqlite_manager()
        
        # 检查连接
        if not sqlite_manager.check_connection():
            logger.error("数据库连接失败")
            return False
        
        logger.info("数据库连接正常")
        
        # 检查表
        table_names = sqlite_manager.get_table_names()
        expected_tables = ['parents', 'children', 'child_parent_relationships', 'child_academic_records', 'user_task_inputs']
        
        logger.info(f"数据库中的表: {table_names}")
        
        missing_tables = []
        for table in expected_tables:
            if table not in table_names:
                missing_tables.append(table)
        
        if missing_tables:
            logger.warning(f"缺少表: {missing_tables}")
            return False
        else:
            logger.info("所有必需的表都存在")
            return True
            
    except Exception as e:
        logger.error(f"检查数据库失败: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python init_db.py create    # 创建表")
        print("  python init_db.py drop      # 删除表")
        print("  python init_db.py reset     # 重置数据库")
        print("  python init_db.py check     # 检查数据库状态")
        return
    
    command = sys.argv[1].lower()
    
    if command == "create":
        if create_tables():
            print("✅ 数据表创建成功")
        else:
            print("❌ 数据表创建失败")
            sys.exit(1)
    
    elif command == "drop":
        confirm = input("确定要删除所有表吗？这将清除所有数据！(yes/no): ")
        if confirm.lower() == 'yes':
            if drop_tables():
                print("✅ 数据表删除成功")
            else:
                print("❌ 数据表删除失败")
                sys.exit(1)
        else:
            print("操作已取消")
    
    elif command == "reset":
        confirm = input("确定要重置数据库吗？这将清除所有数据！(yes/no): ")
        if confirm.lower() == 'yes':
            if reset_database():
                print("✅ 数据库重置成功")
            else:
                print("❌ 数据库重置失败")
                sys.exit(1)
        else:
            print("操作已取消")
    
    elif command == "check":
        if check_database():
            print("✅ 数据库状态正常")
        else:
            print("❌ 数据库状态异常")
            sys.exit(1)
    
    else:
        print(f"未知命令: {command}")
        print("支持的命令: create, drop, reset, check")
        sys.exit(1)


if __name__ == "__main__":
    main()
