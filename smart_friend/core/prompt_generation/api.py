# Prompt生成模块API路由汇总
from fastapi import APIRouter

# 导入prompt生成相关的端点
from .endpoints.prompt_api import router as prompt_router
from .endpoints.task_prompt_api import router as task_prompt_router

# 创建prompt生成API路由器
prompt_generation_router = APIRouter()

# 包含基础prompt生成路由
prompt_generation_router.include_router(
    prompt_router,
    prefix="",
    tags=["prompt-generation"]
)

# 包含任务prompt生成路由
prompt_generation_router.include_router(
    task_prompt_router,
    prefix="",
    tags=["prompt-generation"]
)
