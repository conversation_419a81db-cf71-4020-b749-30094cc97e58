# Prompt生成服务
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone, timedelta

from .schemas import (
    ChildProfileSummary, TodayHomeworkSummary, RecentHomeworkCompletion,
    GeneratedPrompt, PromptGenerationRequest, TaskPromptRequest, TaskPromptResponse
)
from .template_manager import PromptTemplateManager
from core.user_management.service import UserManagementService
from service.planning_service import PlanningService
from service.daily_learning_service import DailyLearningService
from service.daily_task_service import DailyTaskService

logger = logging.getLogger(__name__)


class PromptGenerationService:
    """Prompt生成服务类"""
    
    def __init__(self):
        self.user_service = UserManagementService()
        self.planning_service = PlanningService()
        self.daily_learning_service = DailyLearningService()
        self.daily_task_service = DailyTaskService()
        self.template_manager = PromptTemplateManager()
    
    def generate_prompt(self, request: PromptGenerationRequest) -> Optional[GeneratedPrompt]:
        """生成综合prompt"""
        try:
            # 1. 获取儿童个人档案
            child_profile = self._get_child_profile(request.child_id)
            if not child_profile:
                logger.error(f"未找到儿童ID {request.child_id} 的档案信息")
                return None
            
            # 2. 获取今日作业/学习计划
            today_homework = []
            if request.include_today_homework:
                today_homework = self._get_today_homework(request.child_id, request.subject_filter)
            
            # 3. 获取近期作业完成情况
            recent_completion = []
            if request.include_recent_completion:
                recent_completion = self._get_recent_completion(
                    request.child_id, 
                    request.days_back, 
                    request.subject_filter
                )
            
            # 4. 生成结构化prompt
            structured_prompt = self._generate_structured_prompt(
                child_profile, today_homework, recent_completion, request.prompt_template
            )
            
            # 5. 生成prompt各部分内容
            prompt_sections = self._generate_prompt_sections(
                child_profile, today_homework, recent_completion
            )
            
            # 6. 生成数据统计摘要
            data_summary = self._generate_data_summary(
                child_profile, today_homework, recent_completion
            )
            
            return GeneratedPrompt(
                child_id=request.child_id,
                generated_at=datetime.now(timezone.utc),
                child_profile=child_profile,
                today_homework=today_homework,
                recent_completion=recent_completion,
                structured_prompt=structured_prompt,
                prompt_sections=prompt_sections,
                data_summary=data_summary
            )
            
        except Exception as e:
            logger.error(f"生成prompt时发生错误: {e}")
            return None
    
    def _get_child_profile(self, child_id: int) -> Optional[ChildProfileSummary]:
        """获取儿童个人档案摘要"""
        try:
            child = self.user_service.get_child(child_id, include_relations=False)
            if not child:
                return None
            
            return ChildProfileSummary(
                id=child.id,
                name=child.name,
                nickname=child.nickname,
                age=child.age,
                academic_level=child.academic_level,
                school_name=child.school_name,
                learning_style=child.learning_style,
                attention_span_minutes=child.attention_span_minutes,
                personality_traits=child.personality_traits,
                favorite_subjects=child.favorite_subjects,
                disliked_subjects=child.disliked_subjects,
                good_at_subjects=child.good_at_subjects,
                weak_at_subjects=child.weak_at_subjects
            )
            
        except Exception as e:
            logger.error(f"获取儿童档案时发生错误: {e}")
            return None
    
    def _get_today_homework(self, child_id: int, subject_filter: Optional[str] = None) -> List[TodayHomeworkSummary]:
        """获取今日作业/学习计划"""
        try:
            # 优先从每日任务数据库获取今日任务
            today_tasks = self.daily_task_service.get_today_tasks(child_id)

            # 如果没有今日任务，检查是否需要创建示例数据
            if not today_tasks:
                logger.info(f"儿童{child_id}没有今日任务数据，尝试创建示例数据")
                # 检查是否已经有昨日任务，如果没有则创建完整示例数据
                yesterday_tasks = self.daily_task_service.get_yesterday_tasks(child_id)
                if not yesterday_tasks:
                    # 创建完整的示例数据（只包括昨日）
                    success = self.daily_task_service.create_sample_tasks(child_id)
                    if success:
                        today_tasks = self.daily_task_service.get_today_tasks(child_id)
                        logger.info(f"成功创建示例数据，获取到{len(today_tasks)}个今日任务")
                else:
                    # 不再自动创建今日任务，让用户通过任务输入功能添加
                    logger.info(f"儿童{child_id}没有今日任务，请通过任务输入功能添加任务")

            homework_list = []

            # 如果有今日任务数据，转换为TodayHomeworkSummary格式
            if today_tasks:
                for task in today_tasks:
                    # 应用学科筛选
                    if subject_filter and task.get('subject') != subject_filter:
                        continue

                    # 处理子任务 - 转换为字符串列表
                    sub_tasks = []
                    if task.get('description'):
                        # 将描述拆分为子任务字符串列表
                        desc_parts = task['description'].split('，')
                        sub_tasks = [part.strip() for part in desc_parts if part.strip()]

                    homework = TodayHomeworkSummary(
                        plan_id=str(task.get("id", "")),
                        child_id=task.get("child_id", child_id),
                        task_name=task.get("task_name", ""),
                        time_slot=task.get("time_slot", ""),
                        subject=task.get("subject", ""),
                        sub_tasks=sub_tasks,
                        customization=task.get("customization"),
                        difficulty=task.get("difficulty"),
                        solution=task.get("solution"),
                        confidence_index=task.get("confidence_index"),
                        plan_date=task.get("task_date"),
                        status=task.get("status", "pending"),
                        notes=task.get("notes")
                    )
                    homework_list.append(homework)

                return homework_list

            # 如果没有今日任务数据，从planning_service获取（兼容性处理）
            today = datetime.now(timezone.utc).date()
            start_date = datetime.combine(today, datetime.min.time()).replace(tzinfo=timezone.utc)
            end_date = start_date + timedelta(days=1)

            plans = self.planning_service.get_plans(
                child_id=child_id,
                subject=subject_filter,
                start_date=start_date,
                end_date=end_date,
                limit=50
            )

            for plan in plans:
                # 处理子任务列表
                sub_tasks = plan.get("sub_tasks", [])
                if sub_tasks and isinstance(sub_tasks[0], dict):
                    # 如果是字典格式，直接使用
                    processed_sub_tasks = sub_tasks
                else:
                    # 如果是字符串列表，转换为字典格式
                    processed_sub_tasks = [{"task": task, "source": ""} for task in sub_tasks] if sub_tasks else []

                homework = TodayHomeworkSummary(
                    plan_id=plan.get("plan_id"),
                    child_id=plan.get("child_id", child_id),
                    task_name=plan.get("task_name", ""),
                    time_slot=plan.get("time_slot", ""),
                    subject=plan.get("subject", ""),
                    sub_tasks=processed_sub_tasks,
                    customization=plan.get("customization"),
                    difficulty=plan.get("difficulty"),
                    solution=plan.get("solution"),
                    confidence_index=plan.get("confidence_index"),
                    plan_date=plan.get("plan_date"),
                    status=plan.get("status", "pending"),
                    notes=plan.get("notes")
                )
                homework_list.append(homework)

            return homework_list

        except Exception as e:
            logger.error(f"获取今日作业时发生错误: {e}")
            return []
    
    def _get_recent_completion(self, child_id: int, days_back: int, subject_filter: Optional[str] = None) -> List[RecentHomeworkCompletion]:
        """获取近期作业完成情况"""
        try:
            # 计算日期范围
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days_back)
            
            # 获取学习记录
            records = self.daily_learning_service.get_learning_records(
                child_id=child_id,
                subject=subject_filter,
                start_date=start_date,
                end_date=end_date,
                limit=100
            )
            
            completion_list = []
            for record in records:
                completion = RecentHomeworkCompletion(
                    date=record.get("date", ""),
                    subject=record.get("subject", ""),
                    activity_type=record.get("activity_type"),
                    completion_rate=record.get("completion_rate"),
                    accuracy_rate=record.get("accuracy_rate"),
                    homework_completion_rate=record.get("homework_completion_rate"),
                    total_duration_minutes=record.get("total_duration_minutes"),
                    subject_duration_minutes=record.get("subject_duration_minutes"),
                    is_behind_schedule=record.get("is_behind_schedule"),
                    concentration_level=record.get("concentration_level"),
                    internal_interruptions=record.get("internal_interruptions"),
                    desk_leaving_times=record.get("desk_leaving_times"),
                    enjoyment_rating=record.get("enjoyment_rating"),
                    difficulty_rating=record.get("difficulty_rating"),
                    motivation_level=record.get("motivation_level")
                )
                completion_list.append(completion)
            
            return completion_list
            
        except Exception as e:
            logger.error(f"获取近期完成情况时发生错误: {e}")
            return []

    def _generate_structured_prompt(self, child_profile: ChildProfileSummary,
                                  today_homework: List[TodayHomeworkSummary],
                                  recent_completion: List[RecentHomeworkCompletion],
                                  custom_template: Optional[str] = None) -> str:
        """生成结构化prompt文本"""
        try:
            if custom_template:
                # 使用自定义模板
                return self._apply_custom_template(custom_template, child_profile, today_homework, recent_completion)

            # 使用默认模板
            prompt_parts = []

            # 1. 儿童基本信息
            prompt_parts.append("## 学生基本信息")
            prompt_parts.append(f"姓名: {child_profile.name}")
            if child_profile.nickname:
                prompt_parts.append(f"昵称: {child_profile.nickname}")
            if child_profile.age:
                prompt_parts.append(f"年龄: {child_profile.age}岁")
            if child_profile.academic_level:
                prompt_parts.append(f"学业等级: {child_profile.academic_level}")
            if child_profile.school_name:
                prompt_parts.append(f"学校: {child_profile.school_name}")

            # 2. 学习特征
            prompt_parts.append("\n## 学习特征")
            if child_profile.learning_style:
                prompt_parts.append(f"学习风格: {child_profile.learning_style}")
            if child_profile.attention_span_minutes:
                prompt_parts.append(f"注意力持续时间: {child_profile.attention_span_minutes}分钟")
            if child_profile.personality_traits:
                prompt_parts.append(f"性格特点: {child_profile.personality_traits}")

            # 3. 学科偏好和能力
            prompt_parts.append("\n## 学科偏好和能力")
            if child_profile.good_at_subjects:
                prompt_parts.append(f"擅长科目: {child_profile.good_at_subjects}")
            if child_profile.weak_at_subjects:
                prompt_parts.append(f"薄弱科目: {child_profile.weak_at_subjects}")
            if child_profile.favorite_subjects:
                prompt_parts.append(f"喜欢的科目: {child_profile.favorite_subjects}")
            if child_profile.disliked_subjects:
                prompt_parts.append(f"不喜欢的科目: {child_profile.disliked_subjects}")

            # 4. 今日学习任务
            if today_homework:
                prompt_parts.append("\n## 今日学习任务")
                for i, homework in enumerate(today_homework, 1):
                    prompt_parts.append(f"{i}. {homework.subject}")
                    if homework.task_name:
                        prompt_parts.append(f"   任务: {homework.task_name}")
                    if homework.time_slot:
                        prompt_parts.append(f"   时间: {homework.time_slot}")
                    if homework.difficulty:
                        prompt_parts.append(f"   难点: {homework.difficulty}")
                    if homework.confidence_index:
                        prompt_parts.append(f"   信心指数: {homework.confidence_index}/5")
                    if homework.sub_tasks:
                        prompt_parts.append(f"   子任务: {', '.join(homework.sub_tasks)}")

            # 5. 近期学习表现
            if recent_completion:
                prompt_parts.append("\n## 近期学习表现")

                # 按学科分组统计
                subject_stats = {}
                for record in recent_completion:
                    subject = record.subject
                    if subject not in subject_stats:
                        subject_stats[subject] = {
                            'count': 0,
                            'avg_completion': 0,
                            'avg_accuracy': 0,
                            'avg_concentration': 0,
                            'avg_enjoyment': 0,
                            'total_time': 0
                        }

                    stats = subject_stats[subject]
                    stats['count'] += 1
                    if record.completion_rate:
                        stats['avg_completion'] += record.completion_rate
                    if record.accuracy_rate:
                        stats['avg_accuracy'] += record.accuracy_rate
                    if record.concentration_level:
                        stats['avg_concentration'] += record.concentration_level
                    if record.enjoyment_rating:
                        stats['avg_enjoyment'] += record.enjoyment_rating
                    if record.total_duration_minutes:
                        stats['total_time'] += record.total_duration_minutes

                # 计算平均值并输出
                for subject, stats in subject_stats.items():
                    count = stats['count']
                    prompt_parts.append(f"\n{subject} (共{count}次学习):")
                    if stats['avg_completion'] > 0:
                        prompt_parts.append(f"  平均完成率: {stats['avg_completion']/count:.1f}%")
                    if stats['avg_accuracy'] > 0:
                        prompt_parts.append(f"  平均正确率: {stats['avg_accuracy']/count:.1f}%")
                    if stats['avg_concentration'] > 0:
                        prompt_parts.append(f"  平均专注度: {stats['avg_concentration']/count:.1f}/5")
                    if stats['avg_enjoyment'] > 0:
                        prompt_parts.append(f"  平均享受程度: {stats['avg_enjoyment']/count:.1f}/5")
                    if stats['total_time'] > 0:
                        prompt_parts.append(f"  总学习时间: {stats['total_time']:.0f}分钟")

            return "\n".join(prompt_parts)

        except Exception as e:
            logger.error(f"生成结构化prompt时发生错误: {e}")
            return f"生成prompt时发生错误: {str(e)}"

    def _apply_custom_template(self, template: str, child_profile: ChildProfileSummary,
                             today_homework: List[TodayHomeworkSummary],
                             recent_completion: List[RecentHomeworkCompletion]) -> str:
        """应用自定义模板"""
        try:
            # 准备模板变量
            template_vars = {
                'child_name': child_profile.name,
                'child_nickname': child_profile.nickname or '',
                'child_age': child_profile.age or '',
                'academic_level': child_profile.academic_level or '',
                'school_name': child_profile.school_name or '',
                'learning_style': child_profile.learning_style or '',
                'attention_span': child_profile.attention_span_minutes or '',
                'personality_traits': child_profile.personality_traits or '',
                'favorite_subjects': child_profile.favorite_subjects or '',
                'disliked_subjects': child_profile.disliked_subjects or '',
                'good_at_subjects': child_profile.good_at_subjects or '',
                'weak_at_subjects': child_profile.weak_at_subjects or '',
                'today_homework_count': len(today_homework),
                'recent_records_count': len(recent_completion)
            }

            # 应用模板变量
            result = template
            for key, value in template_vars.items():
                result = result.replace(f"{{{key}}}", str(value))

            return result

        except Exception as e:
            logger.error(f"应用自定义模板时发生错误: {e}")
            return template

    def _generate_prompt_sections(self, child_profile: ChildProfileSummary,
                                today_homework: List[TodayHomeworkSummary],
                                recent_completion: List[RecentHomeworkCompletion]) -> Dict[str, Any]:
        """生成prompt各部分内容"""
        try:
            sections = {
                'basic_info': {
                    'name': child_profile.name,
                    'nickname': child_profile.nickname,
                    'age': child_profile.age,
                    'academic_level': child_profile.academic_level,
                    'school_name': child_profile.school_name
                },
                'learning_characteristics': {
                    'learning_style': child_profile.learning_style,
                    'attention_span_minutes': child_profile.attention_span_minutes,
                    'personality_traits': child_profile.personality_traits
                },
                'subject_preferences': {
                    'good_at_subjects': child_profile.good_at_subjects,
                    'weak_at_subjects': child_profile.weak_at_subjects,
                    'favorite_subjects': child_profile.favorite_subjects,
                    'disliked_subjects': child_profile.disliked_subjects
                },
                'today_tasks': [homework.model_dump() for homework in today_homework],
                'recent_performance': [completion.model_dump() for completion in recent_completion]
            }

            return sections

        except Exception as e:
            logger.error(f"生成prompt部分内容时发生错误: {e}")
            return {}

    def _generate_data_summary(self, child_profile: ChildProfileSummary,
                             today_homework: List[TodayHomeworkSummary],
                             recent_completion: List[RecentHomeworkCompletion]) -> Dict[str, Any]:
        """生成数据统计摘要"""
        try:
            # 基本统计
            summary = {
                'child_name': child_profile.name,
                'today_homework_count': len(today_homework),
                'recent_records_count': len(recent_completion),
                'data_date_range': None,
                'subjects_covered': set(),
                'avg_completion_rate': 0,
                'avg_accuracy_rate': 0,
                'avg_concentration_level': 0,
                'total_study_time': 0
            }

            if recent_completion:
                # 日期范围
                dates = [record.date for record in recent_completion if record.date]
                if dates:
                    summary['data_date_range'] = f"{min(dates)} 到 {max(dates)}"

                # 学科统计
                subjects = {record.subject for record in recent_completion if record.subject}
                summary['subjects_covered'] = list(subjects)

                # 性能统计
                completion_rates = [r.completion_rate for r in recent_completion if r.completion_rate is not None]
                accuracy_rates = [r.accuracy_rate for r in recent_completion if r.accuracy_rate is not None]
                concentration_levels = [r.concentration_level for r in recent_completion if r.concentration_level is not None]
                study_times = [r.total_duration_minutes for r in recent_completion if r.total_duration_minutes is not None]

                if completion_rates:
                    summary['avg_completion_rate'] = sum(completion_rates) / len(completion_rates)
                if accuracy_rates:
                    summary['avg_accuracy_rate'] = sum(accuracy_rates) / len(accuracy_rates)
                if concentration_levels:
                    summary['avg_concentration_level'] = sum(concentration_levels) / len(concentration_levels)
                if study_times:
                    summary['total_study_time'] = sum(study_times)

            # 今日任务统计
            if today_homework:
                today_subjects = {homework.subject for homework in today_homework if homework.subject}
                summary['today_subjects'] = list(today_subjects)

                confidence_indices = [h.confidence_index for h in today_homework if h.confidence_index is not None]
                if confidence_indices:
                    summary['avg_confidence_index'] = sum(confidence_indices) / len(confidence_indices)

            return summary

        except Exception as e:
            logger.error(f"生成数据摘要时发生错误: {e}")
            return {}

    def _get_yesterday_tasks(self, child_id: int, subject_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取昨日任务情况"""
        try:
            yesterday_tasks = self.daily_task_service.get_yesterday_tasks(child_id)

            # 如果没有昨日任务，创建示例数据
            if not yesterday_tasks:
                logger.info(f"儿童{child_id}没有昨日任务数据，创建示例数据")
                success = self.daily_task_service.create_sample_tasks(child_id)
                if success:
                    yesterday_tasks = self.daily_task_service.get_yesterday_tasks(child_id)

            # 应用学科筛选
            if subject_filter and yesterday_tasks:
                yesterday_tasks = [task for task in yesterday_tasks if task.get('subject') == subject_filter]

            return yesterday_tasks

        except Exception as e:
            logger.error(f"获取昨日任务时发生错误: {e}")
            return []
    def generate_task_prompt(self, request: TaskPromptRequest) -> Optional[TaskPromptResponse]:
        """生成任务计划表的prompt"""
        try:
            # 1. 获取儿童个人档案
            child_profile = self._get_child_profile(request.child_id)
            if not child_profile:
                logger.error(f"未找到儿童ID {request.child_id} 的档案信息")
                return None

            # 2. 获取今日作业/学习计划
            today_homework = self._get_today_homework(request.child_id, request.subject_filter)

            # 3. 获取近期作业完成情况
            recent_completion = self._get_recent_completion(
                request.child_id,
                request.days_back,
                request.subject_filter
            )

            # 4. 获取昨日任务情况
            yesterday_tasks = None
            if request.include_yesterday_tasks:
                yesterday_tasks = self._get_yesterday_tasks(request.child_id, request.subject_filter)

            # 5. 使用模板管理器生成最终prompt
            final_prompt = self.template_manager.generate_task_prompt(
                child_profile=child_profile,
                recent_completion=recent_completion,
                yesterday_tasks=yesterday_tasks,
                today_homework=today_homework,
                template_type=request.template_type
            )

            # 6. 生成各个prompt部分
            prompt1 = self.template_manager._generate_prompt1(child_profile)
            prompt2 = self.template_manager._generate_prompt2(recent_completion)
            prompt3 = self.template_manager._generate_prompt3(yesterday_tasks)
            prompt4 = self.template_manager._generate_prompt4(today_homework)

            return TaskPromptResponse(
                child_id=request.child_id,
                generated_at=datetime.now(timezone.utc),
                template_type=request.template_type,
                prompt1=prompt1,
                prompt2=prompt2,
                prompt3=prompt3,
                prompt4=prompt4,
                final_prompt=final_prompt,
                child_profile=child_profile,
                today_homework=today_homework,
                recent_completion=recent_completion,
                yesterday_tasks=yesterday_tasks
            )

        except Exception as e:
            logger.error(f"生成任务prompt时发生错误: {e}")
            return None


