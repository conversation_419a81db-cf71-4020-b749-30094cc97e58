# SQLAlchemy ORM 模型 - 用户任务输入记录表
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime
from enum import Enum as PyEnum
from typing import Optional, List, Dict, Any
import logging

# 使用现有的Base
from core.user_management.models.user_models import Base

# 设置日志
logger = logging.getLogger(__name__)


class OperatorTypeEnum(PyEnum):
    """操作用户类型枚举"""
    CHILD = 1    # 孩子操作
    PARENT = 2   # 家长操作


class InputMethodEnum(PyEnum):
    """输入方式枚举"""
    TEXT = 1     # 文本输入
    VOICE = 2    # 语音输入
    IMAGE = 3    # 图片输入


class UserTaskInput(Base):
    """用户任务输入记录表"""
    __tablename__ = "user_task_inputs"

    # 基本信息
    id = Column(Integer, primary_key=True, autoincrement=True, comment="唯一标识记录，无业务含义")
    operator_type = Column(Integer, nullable=False, comment="区分操作用户类型（1=孩子，2=家长）")
    operator_id = Column(Integer, nullable=False, comment="关联用户表的自增ID（孩子/家长表通用，靠operator_type区分表）")
    child_id = Column(Integer, nullable=False, comment="明确任务归属的孩子（即使家长操作，也需关联孩子）")

    # 输入内容
    content = Column(Text, nullable=False, comment="存储用户输入的原始内容（文本格式）")
    input_method = Column(Integer, nullable=False, comment="输入方式枚举（1=文本，2=语音，3=图片）")
    input_time = Column(DateTime, nullable=False, default=func.now(), comment="记录输入时间，用于时序分析")
    
    # 系统信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    notes = Column(Text, nullable=True, comment="备注信息")

    @classmethod
    def create_input_record(cls, session, operator_type: int, operator_id: int, child_id: int,
                           content: str, input_method: int, **kwargs) -> Optional['UserTaskInput']:
        """创建用户任务输入记录"""
        try:
            # 验证操作用户类型
            if operator_type not in [OperatorTypeEnum.CHILD.value, OperatorTypeEnum.PARENT.value]:
                logger.error(f"无效的操作用户类型: {operator_type}")
                return None
            
            # 验证输入方式
            if input_method not in [InputMethodEnum.TEXT.value, InputMethodEnum.VOICE.value, InputMethodEnum.IMAGE.value]:
                logger.error(f"无效的输入方式: {input_method}")
                return None
            
            # 创建记录数据
            record_data = {
                'operator_type': operator_type,
                'operator_id': operator_id,
                'child_id': child_id,
                'content': content,
                'input_method': input_method,
                'input_time': kwargs.get('input_time', datetime.now()),
                **kwargs
            }

            record = cls(**record_data)
            session.add(record)
            session.flush()  # 获取ID但不提交
            return record
        except SQLAlchemyError as e:
            logger.error(f"创建用户任务输入记录失败: {e}")
            session.rollback()
            return None

    @classmethod
    def get_by_id(cls, session, record_id: int) -> Optional['UserTaskInput']:
        """根据ID获取记录"""
        try:
            return session.query(cls).filter(cls.id == record_id, cls.is_active == True).first()
        except SQLAlchemyError as e:
            logger.error(f"获取记录失败: {e}")
            return None

    @classmethod
    def get_by_child_id(cls, session, child_id: int, limit: int = 100) -> List['UserTaskInput']:
        """根据孩子ID获取输入记录列表"""
        try:
            return session.query(cls).filter(
                cls.child_id == child_id,
                cls.is_active == True
            ).order_by(cls.input_time.desc()).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"获取孩子输入记录失败: {e}")
            return []

    @classmethod
    def get_by_operator(cls, session, operator_type: int, operator_id: int, limit: int = 100) -> List['UserTaskInput']:
        """根据操作者获取输入记录列表"""
        try:
            return session.query(cls).filter(
                cls.operator_type == operator_type,
                cls.operator_id == operator_id,
                cls.is_active == True
            ).order_by(cls.input_time.desc()).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"获取操作者输入记录失败: {e}")
            return []

    @classmethod
    def get_by_time_range(cls, session, child_id: int, start_time: datetime, end_time: datetime) -> List['UserTaskInput']:
        """根据时间范围获取输入记录"""
        try:
            return session.query(cls).filter(
                cls.child_id == child_id,
                cls.input_time >= start_time,
                cls.input_time <= end_time,
                cls.is_active == True
            ).order_by(cls.input_time.desc()).all()
        except SQLAlchemyError as e:
            logger.error(f"获取时间范围内输入记录失败: {e}")
            return []

    @classmethod
    def get_by_input_method(cls, session, child_id: int, input_method: int, limit: int = 100) -> List['UserTaskInput']:
        """根据输入方式获取记录"""
        try:
            return session.query(cls).filter(
                cls.child_id == child_id,
                cls.input_method == input_method,
                cls.is_active == True
            ).order_by(cls.input_time.desc()).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"获取指定输入方式记录失败: {e}")
            return []

    def update_record(self, session, **kwargs) -> bool:
        """更新记录信息"""
        try:
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            self.updated_at = datetime.now()
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"更新记录失败: {e}")
            session.rollback()
            return False

    def soft_delete(self, session) -> bool:
        """软删除记录"""
        try:
            self.is_active = False
            self.updated_at = datetime.now()
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"删除记录失败: {e}")
            session.rollback()
            return False

    def get_operator_info(self, session) -> Optional[Dict[str, Any]]:
        """获取操作者信息（根据operator_type决定查询哪个表）"""
        try:
            if self.operator_type == OperatorTypeEnum.CHILD.value:
                # 查询children表
                from core.user_management.models.user_models import Child
                child = session.query(Child).filter(Child.id == self.operator_id).first()
                if child:
                    return {
                        'type': 'child',
                        'id': child.id,
                        'name': child.name,
                        'nickname': child.nickname
                    }
            elif self.operator_type == OperatorTypeEnum.PARENT.value:
                # 查询parents表
                from core.user_management.models.user_models import Parent
                parent = session.query(Parent).filter(Parent.id == self.operator_id).first()
                if parent:
                    return {
                        'type': 'parent',
                        'id': parent.id,
                        'name': parent.name
                    }
            return None
        except SQLAlchemyError as e:
            logger.error(f"获取操作者信息失败: {e}")
            return None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'operator_type': self.operator_type,
            'operator_id': self.operator_id,
            'child_id': self.child_id,
            'content': self.content,
            'input_method': self.input_method,
            'input_time': self.input_time.isoformat() if self.input_time else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'notes': self.notes
        }
