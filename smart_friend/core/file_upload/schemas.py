# 文件上传相关数据模型
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class FileConflictStrategy(str, Enum):
    """文件冲突处理策略"""
    RENAME = "rename"      # 自动重命名
    REPLACE = "replace"    # 覆盖替换
    CANCEL = "cancel"      # 取消上传


class ExistingFileInfo(BaseModel):
    """现有文件信息模型"""
    file_path: str = Field(..., description="文件路径")
    file_name: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小（字节）")
    created_time: datetime = Field(..., description="创建时间")
    modified_time: datetime = Field(..., description="修改时间")
    file_hash: Optional[str] = Field(None, description="文件哈希值")
    is_identical: Optional[bool] = Field(None, description="是否与新文件内容相同")


class FileConflictInfo(BaseModel):
    """文件冲突信息模型"""
    conflict_detected: bool = Field(..., description="是否检测到冲突")
    existing_file: Optional[ExistingFileInfo] = Field(None, description="现有文件信息")
    suggested_strategy: Optional[FileConflictStrategy] = Field(None, description="建议的处理策略")
    suggested_filename: Optional[str] = Field(None, description="建议的新文件名")
    conflict_reason: Optional[str] = Field(None, description="冲突原因")


class FileUploadRequest(BaseModel):
    """文件上传请求模型"""
    upload_path: str = Field("uploads/temp", description="上传路径")
    user_id: Optional[int] = Field(None, description="用户ID")
    max_size: Optional[int] = Field(None, description="最大文件大小（字节）")
    allowed_types: Optional[List[str]] = Field(None, description="允许的文件类型")
    conflict_strategy: FileConflictStrategy = Field(FileConflictStrategy.RENAME, description="冲突处理策略")
    create_backup: bool = Field(False, description="是否在覆盖前创建备份")
    check_content: bool = Field(True, description="是否检查文件内容相同性")


class FileUploadResponse(BaseModel):
    """文件上传响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    file_info: Optional[Dict[str, Any]] = Field(None, description="文件信息")
    file_url: Optional[str] = Field(None, description="文件访问URL")
    conflict_info: Optional[FileConflictInfo] = Field(None, description="冲突处理信息")
    backup_info: Optional[Dict[str, str]] = Field(None, description="备份文件信息")
    operation_log: Optional[str] = Field(None, description="操作日志")


class FileConflictCheckRequest(BaseModel):
    """文件冲突检查请求模型"""
    filename: str = Field(..., description="文件名")
    upload_path: str = Field(..., description="上传路径")
    file_size: Optional[int] = Field(None, description="文件大小")
    file_hash: Optional[str] = Field(None, description="文件哈希值")


class FileConflictCheckResponse(BaseModel):
    """文件冲突检查响应模型"""
    conflict_info: FileConflictInfo = Field(..., description="冲突信息")
    recommendations: List[str] = Field(..., description="处理建议")
    available_strategies: List[FileConflictStrategy] = Field(..., description="可用的处理策略")
