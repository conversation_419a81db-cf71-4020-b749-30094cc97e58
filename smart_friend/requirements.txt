# 补充依赖: fastapi, sqlmodel, pydantic
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
sqlalchemy>=2.0.23
pydantic>=2.5.0
python-multipart>=0.0.6
python-dotenv>=1.0.0
# 用户信息管理API依赖
email-validator>=2.0.0

# TTS相关依赖
pygame>=2.5.0
pydub>=0.25.0
websockets>=12.0
aiofiles>=23.0.0

# ASR和VAD相关依赖
webrtcvad>=2.0.10

# Enhanced OpenManus dependencies
requests>=2.31.0
numpy>=1.24.0
scikit-learn>=1.3.0
sentence-transformers>=2.2.0

# Jina embeddings support
torch>=1.11.0
