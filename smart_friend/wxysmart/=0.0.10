Looking in indexes: https://mirrors.ivolces.com/pypi/simple/
Collecting langchain
  Using cached https://mirrors.ivolces.com/pypi/packages/f1/f2/c09a2e383283e3af1db669ab037ac05a45814f4b9c472c48dc24c0cef039/langchain-0.3.26-py3-none-any.whl (1.0 MB)
Requirement already satisfied: langchain-core in /root/miniforge3/lib/python3.12/site-packages (0.3.63)
Collecting langchain-community
  Downloading https://mirrors.ivolces.com/pypi/packages/c8/bc/f8c7dae8321d37ed39ac9d7896617c4203248240a4835b136e3724b3bb62/langchain_community-0.3.27-py3-none-any.whl (2.5 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.5/2.5 MB 59.3 MB/s eta 0:00:00
Collecting langchain-core
  Downloading https://mirrors.ivolces.com/pypi/packages/f9/da/c89be0a272993bfcb762b2a356b9f55de507784c2755ad63caec25d183bf/langchain_core-0.3.68-py3-none-any.whl (441 kB)
Collecting langchain-text-splitters<1.0.0,>=0.3.8 (from langchain)
  Downloading https://mirrors.ivolces.com/pypi/packages/8b/a3/3696ff2444658053c01b6b7443e761f28bb71217d82bb89137a978c5f66f/langchain_text_splitters-0.3.8-py3-none-any.whl (32 kB)
Requirement already satisfied: langsmith>=0.1.17 in /root/miniforge3/lib/python3.12/site-packages (from langchain) (0.3.44)
Collecting pydantic<3.0.0,>=2.7.4 (from langchain)
  Using cached https://mirrors.ivolces.com/pypi/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl (444 kB)
Requirement already satisfied: SQLAlchemy<3,>=1.4 in /root/miniforge3/lib/python3.12/site-packages (from langchain) (2.0.23)
Requirement already satisfied: requests<3,>=2 in /root/miniforge3/lib/python3.12/site-packages (from langchain) (2.32.3)
Requirement already satisfied: PyYAML>=5.3 in /root/miniforge3/lib/python3.12/site-packages (from langchain) (6.0.2)
Collecting langsmith>=0.1.17 (from langchain)
  Downloading https://mirrors.ivolces.com/pypi/packages/c8/10/ad3107b666c3203b7938d10ea6b8746b9735c399cf737a51386d58e41d34/langsmith-0.4.5-py3-none-any.whl (367 kB)
Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in /root/miniforge3/lib/python3.12/site-packages (from langchain-core) (9.0.0)
Requirement already satisfied: jsonpatch<2.0,>=1.33 in /root/miniforge3/lib/python3.12/site-packages (from langchain-core) (1.33)
Requirement already satisfied: packaging<25,>=23.2 in /root/miniforge3/lib/python3.12/site-packages (from langchain-core) (23.2)
Requirement already satisfied: typing-extensions>=4.7 in /root/miniforge3/lib/python3.12/site-packages (from langchain-core) (4.13.2)
Requirement already satisfied: jsonpointer>=1.9 in /root/miniforge3/lib/python3.12/site-packages (from jsonpatch<2.0,>=1.33->langchain-core) (3.0.0)
Requirement already satisfied: annotated-types>=0.6.0 in /root/miniforge3/lib/python3.12/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)
Collecting pydantic-core==2.33.2 (from pydantic<3.0.0,>=2.7.4->langchain)
  Using cached https://mirrors.ivolces.com/pypi/packages/f9/41/4b043778cf9c4285d59742281a769eac371b9e47e35f98ad321349cc5d61/pydantic_core-2.33.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)
Requirement already satisfied: typing-inspection>=0.4.0 in /root/miniforge3/lib/python3.12/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.4.0)
Requirement already satisfied: charset_normalizer<4,>=2 in /root/miniforge3/lib/python3.12/site-packages (from requests<3,>=2->langchain) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /root/miniforge3/lib/python3.12/site-packages (from requests<3,>=2->langchain) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /root/miniforge3/lib/python3.12/site-packages (from requests<3,>=2->langchain) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in /root/miniforge3/lib/python3.12/site-packages (from requests<3,>=2->langchain) (2025.1.31)
Requirement already satisfied: greenlet!=0.4.17 in /root/miniforge3/lib/python3.12/site-packages (from SQLAlchemy<3,>=1.4->langchain) (3.2.1)
Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /root/miniforge3/lib/python3.12/site-packages (from langchain-community) (3.11.18)
Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in /root/miniforge3/lib/python3.12/site-packages (from langchain-community) (0.6.7)
Requirement already satisfied: pydantic-settings<3.0.0,>=2.4.0 in /root/miniforge3/lib/python3.12/site-packages (from langchain-community) (2.9.1)
Requirement already satisfied: httpx-sse<1.0.0,>=0.4.0 in /root/miniforge3/lib/python3.12/site-packages (from langchain-community) (0.4.0)
Requirement already satisfied: numpy>=1.26.2 in /root/miniforge3/lib/python3.12/site-packages (from langchain-community) (1.26.4)
Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /root/miniforge3/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (2.6.1)
Requirement already satisfied: aiosignal>=1.1.2 in /root/miniforge3/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.2)
Requirement already satisfied: attrs>=17.3.0 in /root/miniforge3/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in /root/miniforge3/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.6.0)
Requirement already satisfied: multidict<7.0,>=4.5 in /root/miniforge3/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.4.3)
Requirement already satisfied: propcache>=0.2.0 in /root/miniforge3/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (0.3.1)
Requirement already satisfied: yarl<2.0,>=1.17.0 in /root/miniforge3/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.20.0)
Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /root/miniforge3/lib/python3.12/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (3.26.1)
Requirement already satisfied: typing-inspect<1,>=0.4.0 in /root/miniforge3/lib/python3.12/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (0.9.0)
Requirement already satisfied: python-dotenv>=0.21.0 in /root/miniforge3/lib/python3.12/site-packages (from pydantic-settings<3.0.0,>=2.4.0->langchain-community) (1.0.0)
Requirement already satisfied: mypy-extensions>=0.3.0 in /root/miniforge3/lib/python3.12/site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community) (1.1.0)
Requirement already satisfied: httpx<1,>=0.23.0 in /root/miniforge3/lib/python3.12/site-packages (from langsmith>=0.1.17->langchain) (0.28.1)
Requirement already satisfied: orjson<4.0.0,>=3.9.14 in /root/miniforge3/lib/python3.12/site-packages (from langsmith>=0.1.17->langchain) (3.10.18)
Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /root/miniforge3/lib/python3.12/site-packages (from langsmith>=0.1.17->langchain) (1.0.0)
Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /root/miniforge3/lib/python3.12/site-packages (from langsmith>=0.1.17->langchain) (0.23.0)
Requirement already satisfied: anyio in /root/miniforge3/lib/python3.12/site-packages (from httpx<1,>=0.23.0->langsmith>=0.1.17->langchain) (3.7.1)
Requirement already satisfied: httpcore==1.* in /root/miniforge3/lib/python3.12/site-packages (from httpx<1,>=0.23.0->langsmith>=0.1.17->langchain) (1.0.9)
Requirement already satisfied: h11>=0.16 in /root/miniforge3/lib/python3.12/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith>=0.1.17->langchain) (0.16.0)
Requirement already satisfied: sniffio>=1.1 in /root/miniforge3/lib/python3.12/site-packages (from anyio->httpx<1,>=0.23.0->langsmith>=0.1.17->langchain) (1.3.1)
Installing collected packages: pydantic-core, pydantic, langsmith, langchain-core, langchain-text-splitters, langchain, langchain-community
  Attempting uninstall: pydantic-core
    Found existing installation: pydantic_core 2.14.1
    Uninstalling pydantic_core-2.14.1:
      Successfully uninstalled pydantic_core-2.14.1
  Attempting uninstall: pydantic
    Found existing installation: pydantic 2.5.0
    Uninstalling pydantic-2.5.0:
      Successfully uninstalled pydantic-2.5.0
  Attempting uninstall: langsmith
    Found existing installation: langsmith 0.3.44
    Uninstalling langsmith-0.3.44:
      Successfully uninstalled langsmith-0.3.44
  Attempting uninstall: langchain-core
    Found existing installation: langchain-core 0.3.63
    Uninstalling langchain-core-0.3.63:
      Successfully uninstalled langchain-core-0.3.63

Successfully installed langchain-0.3.26 langchain-community-0.3.27 langchain-core-0.3.68 langchain-text-splitters-0.3.8 langsmith-0.4.5 pydantic-2.11.7 pydantic-core-2.33.2
