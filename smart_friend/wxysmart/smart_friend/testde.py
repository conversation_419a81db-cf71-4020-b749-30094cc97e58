import cv2
from body_detection.desk import DesktopSegmentationDetector, DesktopLayoutAnalyzer

# 1. 初始化检测器和分析器
detector = DesktopSegmentationDetector(
    model_path="yolo11n-seg.pt",
    confidence_threshold=0.25
)
analyzer = DesktopLayoutAnalyzer()

# 2. 读取图像
frame = cv2.imread("1.jpg")

# 3. 执行分割检测
objects = detector.detect_objects_with_segmentation(frame)
print(f"检测到 {len(objects)} 个物体")

# 4. 执行布局分析
report = analyzer.evaluate_desktop_layout(objects, frame.shape[:2])

# 5. 查看评估结果
print(f"总体评分: {report.overall_score:.1f}/100")
print(f"质量等级: {report.quality_level.value}")
print(f"学习用品: {report.learning_objects}/{report.total_objects}")

# 6. 获取改进建议
for suggestion in report.priority_suggestions:
    print(f"💡 建议: {suggestion}")
