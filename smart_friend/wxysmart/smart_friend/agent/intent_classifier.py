#!/usr/bin/env python3
"""
RoBERTa意图识别服务
"""

import logging
import requests
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class IntentResult:
    """意图识别结果"""
    intent: str
    confidence: float
    text: str
    top_intents: Optional[list] = None


class IntentClassifier:
    """
    RoBERTa意图识别分类器
    
    支持的意图类型：
    - 0: 日常聊天 (解答作业问题、闲聊内容)
    - 1: 学习任务_创建计划 (制定学习计划)
    - 2: 学习任务_修改计划 (修改学习计划)
    - 3: 学习任务_提交作业 (拍照提交任务)
    - 4: 学习任务_生成报告 (生成学习总结报告)
    """
    
    def __init__(self, api_url: str = "http://localhost:8000/predict"):
        """
        初始化意图分类器
        
        Args:
            api_url: RoBERTa模型API地址
        """
        self.api_url = api_url
        self.intent_mapping = {
            "日常聊天": "smart_chat",
            "学习任务_创建计划": "generate_daily_tasks",
            "学习任务_修改计划": "modify_task_plan",
            "学习任务_提交作业": "submit_homework",
            "学习任务_生成报告": "generate_report"
        }
        
        # 测试API连接
        self._test_connection()
    
    def _test_connection(self) -> bool:
        """测试API连接"""
        try:
            response = requests.post(
                self.api_url,
                json={"text": "测试连接"},
                timeout=5
            )
            if response.status_code == 200:
                logger.info("RoBERTa意图识别API连接成功")
                return True
            else:
                logger.warning(f"RoBERTa API响应异常: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"RoBERTa API连接失败: {e}")
            return False
    
    def predict_intent(self, text: str) -> Optional[IntentResult]:
        """
        预测用户输入的意图
        
        Args:
            text: 用户输入文本
            
        Returns:
            IntentResult: 意图识别结果，失败时返回None
        """
        try:
            response = requests.post(
                self.api_url,
                json={"text": text},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                return IntentResult(
                    text=result.get("text", text),
                    intent=result.get("intent", "日常聊天"),
                    confidence=result.get("confidence", 0.0),
                    top_intents=result.get("top_intents")
                )
            else:
                logger.error(f"意图识别API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"意图识别异常: {e}")
            return None
    
    def get_tool_name(self, intent: str) -> str:
        """
        根据意图获取对应的工具名称
        
        Args:
            intent: 意图类型
            
        Returns:
            str: 工具名称
        """
        return self.intent_mapping.get(intent, "smart_chat")
    
    def analyze_intent_and_get_tool(self, text: str, confidence_threshold: float = 0.7) -> Dict[str, Any]:
        """
        分析意图并返回工具调用信息
        
        Args:
            text: 用户输入文本
            confidence_threshold: 置信度阈值
            
        Returns:
            Dict: 包含工具名称和参数的字典
        """
        # 预测意图
        intent_result = self.predict_intent(text)
        
        if not intent_result:
            logger.warning("意图识别失败，使用默认聊天工具")
            return {
                "tool_name": "smart_chat",
                "confidence": 0.0,
                "intent": "日常聊天",
                "fallback": True
            }
        
        # 检查置信度
        if intent_result.confidence < confidence_threshold:
            logger.info(f"意图置信度过低 ({intent_result.confidence:.3f} < {confidence_threshold})，使用聊天工具")
            return {
                "tool_name": "smart_chat",
                "confidence": intent_result.confidence,
                "intent": intent_result.intent,
                "fallback": True
            }
        
        # 获取对应工具
        tool_name = self.get_tool_name(intent_result.intent)
        
        logger.info(f"意图识别结果: {intent_result.intent} (置信度: {intent_result.confidence:.3f}) -> 工具: {tool_name}")
        
        return {
            "tool_name": tool_name,
            "confidence": intent_result.confidence,
            "intent": intent_result.intent,
            "fallback": False
        }
    
    def get_intent_description(self, intent: str) -> str:
        """获取意图描述"""
        descriptions = {
            "日常聊天": "解答作业问题、闲聊内容",
            "学习任务_创建计划": "制定学习计划",
            "学习任务_修改计划": "修改学习计划", 
            "学习任务_提交作业": "拍照提交任务",
            "学习任务_生成报告": "生成学习总结报告"
        }
        return descriptions.get(intent, "未知意图")


# 全局意图分类器实例
_intent_classifier: Optional[IntentClassifier] = None


def get_intent_classifier() -> IntentClassifier:
    """获取意图分类器实例（单例模式）"""
    global _intent_classifier
    if _intent_classifier is None:
        _intent_classifier = IntentClassifier()
    return _intent_classifier
