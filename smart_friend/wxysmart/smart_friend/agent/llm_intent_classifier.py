#!/usr/bin/env python3
"""
LLM意图识别服务
使用本地小参数LLM模型进行意图分类，替换RoBERTa
"""

import logging
import requests
import json
import re
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class IntentResult:
    """意图识别结果"""
    intent: str
    confidence: float
    text: str
    top_intents: Optional[list] = None


class LLMIntentClassifier:
    """
    LLM意图识别分类器
    
    支持的意图类型：
    - 日常聊天 (解答作业问题、闲聊内容)
    - 学习任务_创建计划 (制定学习计划)
    - 学习任务_修改计划 (修改学习计划)
    - 学习任务_提交作业 (拍照提交任务)
    - 学习任务_生成报告 (生成学习总结报告)
    """
    
    def __init__(self, api_url: str = "http://localhost:8001/generate"):
        """
        初始化LLM意图分类器
        
        Args:
            api_url: 本地LLM模型API地址
        """
        self.api_url = api_url
        self.intent_mapping = {
            "日常聊天": "smart_chat",
            "学习任务_创建计划": "generate_daily_tasks",
            "学习任务_修改计划": "modify_task_plan",
            "学习任务_提交作业": "submit_homework",
            "学习任务_生成报告": "generate_report"
        }
        
        # 意图分类提示词模板
        self.classification_prompt = """你是一个专业的意图识别助手。请根据用户输入，判断用户的意图类型。

支持的意图类型：
1. 日常聊天 - 普通对话、问候、闲聊、询问问题等
2. 学习任务_创建计划 - 要求制定学习计划、安排任务、生成今日任务等
3. 学习任务_修改计划 - 修改任务时间、调整任务安排、更改学习计划等
4. 学习任务_提交作业 - 提交作业、上传作业、完成任务等
5. 学习任务_生成报告 - 生成学习报告、总结学习情况、查看学习统计等

分类规则：
- 如果用户说"数学作业时间改为14:00-15:30"、"把英语作业调整到16:00-17:00"等，属于"学习任务_修改计划"
- 如果用户说"制定今日学习计划"、"安排今天的任务"等，属于"学习任务_创建计划"
- 如果用户说"提交作业"、"上传作业照片"等，属于"学习任务_提交作业"
- 如果用户说"生成学习报告"、"查看学习统计"等，属于"学习任务_生成报告"
- 其他情况都属于"日常聊天"

用户输入：{user_input}

请直接回答意图类型，只需要回答以下选项之一：
日常聊天
学习任务_创建计划
学习任务_修改计划
学习任务_提交作业
学习任务_生成报告"""
        
        # 测试API连接
        self._test_connection()
    
    def _test_connection(self) -> bool:
        """测试API连接"""
        try:
            response = requests.post(
                self.api_url,
                json={
                    "prompt": "测试连接",
                    "max_new_tokens": 10,
                    "temperature": 0.1
                },
                timeout=10
            )
            if response.status_code == 200:
                logger.info("LLM意图识别API连接成功")
                return True
            else:
                logger.warning(f"LLM API响应异常: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"LLM API连接失败: {e}")
            return False
    
    def predict_intent(self, text: str) -> Optional[IntentResult]:
        """
        预测用户输入的意图
        
        Args:
            text: 用户输入文本
            
        Returns:
            IntentResult: 意图识别结果，失败时返回None
        """
        try:
            # 构建分类提示词
            prompt = self.classification_prompt.format(user_input=text)
            
            # 调用LLM API
            response = requests.post(
                self.api_url,
                json={
                    "prompt": prompt,
                    "max_new_tokens": 50,
                    "temperature": 0.1  # 使用较低的温度确保结果稳定
                },
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result.get("generated_text", "").strip()
                
                # 解析LLM输出，提取意图
                intent, confidence = self._parse_llm_output(generated_text, text)
                
                logger.info(f"LLM意图识别: '{text}' -> {intent} (置信度: {confidence:.3f})")
                
                return IntentResult(
                    text=text,
                    intent=intent,
                    confidence=confidence,
                    top_intents=None
                )
            else:
                logger.error(f"LLM API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"LLM意图识别异常: {e}")
            return None
    
    def _parse_llm_output(self, llm_output: str, user_input: str) -> tuple[str, float]:
        """
        解析LLM输出，提取意图和置信度
        
        Args:
            llm_output: LLM生成的文本
            user_input: 用户原始输入
            
        Returns:
            tuple: (意图, 置信度)
        """
        # 定义意图关键词匹配
        intent_keywords = {
            "学习任务_修改计划": [
                "时间改为", "调整到", "修改为", "改成", "改时间", "换时间",
                "任务时间", "作业时间", "学习时间"
            ],
            "学习任务_创建计划": [
                "制定", "安排", "计划", "今日任务", "学习计划", "生成任务"
            ],
            "学习任务_提交作业": [
                "提交", "上传", "完成作业", "交作业", "作业完成"
            ],
            "学习任务_生成报告": [
                "报告", "统计", "总结", "学习情况", "学习报告"
            ]
        }
        
        # 首先尝试从LLM输出中直接匹配意图
        valid_intents = list(self.intent_mapping.keys())
        for intent in valid_intents:
            if intent in llm_output:
                # 基于关键词匹配计算置信度
                confidence = self._calculate_confidence(user_input, intent, intent_keywords)
                return intent, confidence
        
        # 如果LLM输出不包含有效意图，使用关键词匹配
        best_intent = "日常聊天"
        best_confidence = 0.5  # 默认置信度
        
        user_input_lower = user_input.lower()
        
        for intent, keywords in intent_keywords.items():
            for keyword in keywords:
                if keyword in user_input_lower:
                    confidence = self._calculate_confidence(user_input, intent, intent_keywords)
                    if confidence > best_confidence:
                        best_intent = intent
                        best_confidence = confidence
                    break
        
        return best_intent, best_confidence
    
    def _calculate_confidence(self, user_input: str, intent: str, intent_keywords: dict) -> float:
        """
        基于关键词匹配计算置信度
        
        Args:
            user_input: 用户输入
            intent: 识别的意图
            intent_keywords: 意图关键词字典
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        if intent not in intent_keywords:
            return 0.6  # 默认置信度
        
        user_input_lower = user_input.lower()
        keywords = intent_keywords[intent]
        
        # 计算匹配的关键词数量
        matched_keywords = 0
        for keyword in keywords:
            if keyword in user_input_lower:
                matched_keywords += 1
        
        # 基于匹配度计算置信度
        if matched_keywords == 0:
            return 0.5
        elif matched_keywords == 1:
            return 0.75
        elif matched_keywords == 2:
            return 0.85
        else:
            return 0.95
    
    def get_tool_name(self, intent: str) -> str:
        """
        根据意图获取对应的工具名称
        
        Args:
            intent: 意图类型
            
        Returns:
            str: 工具名称
        """
        return self.intent_mapping.get(intent, "smart_chat")
    
    def analyze_intent_and_get_tool(self, text: str, confidence_threshold: float = 0.7) -> Dict[str, Any]:
        """
        分析意图并返回工具调用信息
        
        Args:
            text: 用户输入文本
            confidence_threshold: 置信度阈值
            
        Returns:
            Dict: 包含工具名称和参数的字典
        """
        # 预测意图
        intent_result = self.predict_intent(text)
        
        if not intent_result:
            logger.warning("LLM意图识别失败，使用默认聊天工具")
            return {
                "tool_name": "smart_chat",
                "confidence": 0.0,
                "intent": "日常聊天",
                "fallback": True
            }
        
        # 检查置信度
        if intent_result.confidence < confidence_threshold:
            logger.info(f"意图置信度过低 ({intent_result.confidence:.3f} < {confidence_threshold})，使用聊天工具")
            return {
                "tool_name": "smart_chat",
                "confidence": intent_result.confidence,
                "intent": intent_result.intent,
                "fallback": True
            }
        
        # 获取对应工具
        tool_name = self.get_tool_name(intent_result.intent)
        
        logger.info(f"LLM意图识别结果: {intent_result.intent} (置信度: {intent_result.confidence:.3f}) -> 工具: {tool_name}")
        
        return {
            "tool_name": tool_name,
            "confidence": intent_result.confidence,
            "intent": intent_result.intent,
            "fallback": False
        }
    
    def get_intent_description(self, intent: str) -> str:
        """获取意图描述"""
        descriptions = {
            "日常聊天": "解答作业问题、闲聊内容",
            "学习任务_创建计划": "制定学习计划",
            "学习任务_修改计划": "修改学习计划", 
            "学习任务_提交作业": "拍照提交任务",
            "学习任务_生成报告": "生成学习总结报告"
        }
        return descriptions.get(intent, "未知意图")


# 全局LLM意图分类器实例
_llm_intent_classifier: Optional[LLMIntentClassifier] = None


def get_llm_intent_classifier() -> LLMIntentClassifier:
    """获取LLM意图分类器实例（单例模式）"""
    global _llm_intent_classifier
    if _llm_intent_classifier is None:
        _llm_intent_classifier = LLMIntentClassifier()
    return _llm_intent_classifier
