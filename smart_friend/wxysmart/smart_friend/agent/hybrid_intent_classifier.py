#!/usr/bin/env python3
"""
混合意图识别服务
优先使用LLM，失败时回退到关键词匹配，确保系统稳定性
"""

import logging
import requests
import json
import re
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class IntentResult:
    """意图识别结果"""
    intent: str
    confidence: float
    text: str
    top_intents: Optional[list] = None


class HybridIntentClassifier:
    """
    混合意图识别分类器
    
    优先级：
    1. LLM意图分类（如果可用）
    2. 关键词匹配（备选方案）
    
    支持的意图类型：
    - 日常聊天 (解答作业问题、闲聊内容)
    - 学习任务_创建计划 (制定学习计划)
    - 学习任务_修改计划 (修改学习计划)
    - 学习任务_提交作业 (拍照提交任务)
    - 学习任务_生成报告 (生成学习总结报告)
    """
    
    def __init__(self, llm_api_url: str = "http://localhost:8005/chat"):
        """
        初始化混合意图分类器

        Args:
            llm_api_url: 本地LLM模型API地址
        """
        self.llm_api_url = llm_api_url
        self.llm_available = False
        
        self.intent_mapping = {
            "日常聊天": "smart_chat",
            "学习任务_创建计划": "generate_daily_tasks",
            "学习任务_修改计划": "modify_task_plan",
            "学习任务_提交作业": "submit_homework",
            "学习任务_生成报告": "generate_report"
        }
        
        # 关键词匹配规则
        self.keyword_patterns = {
            "学习任务_修改计划": [
                # 任务时间修改
                r"(.+?)时间改为(\d{1,2}:\d{2}-\d{1,2}:\d{2})",
                r"把(.+?)调整到(\d{1,2}:\d{2}-\d{1,2}:\d{2})",
                r"(.+?)修改为(\d{1,2}:\d{2}-\d{1,2}:\d{2})",
                r"将(.+?)改成(\d{1,2}:\d{2}-\d{1,2}:\d{2})",
                # 一般修改关键词
                r"修改.*任务",
                r"调整.*时间",
                r"改.*作业",
                r"换.*时间"
            ],
            "学习任务_创建计划": [
                r"制定.*计划",
                r"安排.*任务",
                r"生成.*任务",
                r"创建.*计划",
                r"今日任务",
                r"学习计划"
            ],
            "学习任务_提交作业": [
                r"提交.*作业",
                r"上传.*作业",
                r"完成.*作业",
                r"交.*作业",
                r"作业完成"
            ],
            "学习任务_生成报告": [
                r"生成.*报告",
                r"学习.*统计",
                r"学习.*总结",
                r"学习.*情况",
                r"学习.*报告"
            ]
        }
        
        # LLM分类提示词模板 - 针对小参数模型优化
        self.classification_prompt = """意图分类：

选项：
A. 日常聊天
B. 学习任务_创建计划
C. 学习任务_修改计划
D. 学习任务_提交作业
E. 学习任务_生成报告

输入：{user_input}

规则：
- 修改时间/调整时间 → C
- 制定计划/安排任务 → B
- 提交作业/上传作业 → D
- 生成报告/查看统计 → E
- 其他 → A

答案："""
        
        # 测试LLM API连接
        self._test_llm_connection()
    
    def _test_llm_connection(self) -> bool:
        """测试LLM API连接"""
        try:
            response = requests.post(
                self.llm_api_url,
                json={
                    "message": "测试连接"
                },
                timeout=5
            )
            if response.status_code == 200:
                self.llm_available = True
                logger.info("LLM意图识别API连接成功")
                return True
            else:
                self.llm_available = False
                logger.warning(f"LLM API响应异常: {response.status_code}")
                return False
        except Exception as e:
            self.llm_available = False
            logger.warning(f"LLM API连接失败，将使用关键词匹配: {e}")
            return False
    
    def predict_intent(self, text: str) -> Optional[IntentResult]:
        """
        预测用户输入的意图
        
        Args:
            text: 用户输入文本
            
        Returns:
            IntentResult: 意图识别结果，失败时返回None
        """
        # 优先尝试LLM分类
        if self.llm_available:
            llm_result = self._predict_with_llm(text)
            if llm_result:
                return llm_result
            else:
                # LLM失败，标记为不可用并回退到关键词匹配
                self.llm_available = False
                logger.warning("LLM意图识别失败，回退到关键词匹配")
        
        # 使用关键词匹配作为备选方案
        return self._predict_with_keywords(text)
    
    def _predict_with_llm(self, text: str) -> Optional[IntentResult]:
        """
        使用LLM进行意图预测
        
        Args:
            text: 用户输入文本
            
        Returns:
            IntentResult: 意图识别结果，失败时返回None
        """
        try:
            # 构建分类提示词
            prompt = self.classification_prompt.format(user_input=text)

            # 调用LLM API
            response = requests.post(
                self.llm_api_url,
                json={
                    "message": prompt
                },
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                generated_text = result.get("response", "").strip()

                # 解析LLM输出，提取意图
                intent, confidence = self._parse_llm_output(generated_text, text)

                logger.info(f"LLM意图识别: '{text}' -> {intent} (置信度: {confidence:.3f})")

                return IntentResult(
                    text=text,
                    intent=intent,
                    confidence=confidence,
                    top_intents=None
                )
            else:
                logger.error(f"LLM API请求失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"LLM意图识别异常: {e}")
            return None
    
    def _predict_with_keywords(self, text: str) -> IntentResult:
        """
        使用关键词匹配进行意图预测
        
        Args:
            text: 用户输入文本
            
        Returns:
            IntentResult: 意图识别结果
        """
        text_lower = text.lower()
        best_intent = "日常聊天"
        best_confidence = 0.6  # 关键词匹配的基础置信度
        
        # 遍历所有意图的关键词模式
        for intent, patterns in self.keyword_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    # 根据匹配的复杂度调整置信度
                    if r"\d{1,2}:\d{2}-\d{1,2}:\d{2}" in pattern:
                        # 时间格式匹配，置信度更高
                        confidence = 0.9
                    else:
                        # 一般关键词匹配
                        confidence = 0.75
                    
                    if confidence > best_confidence:
                        best_intent = intent
                        best_confidence = confidence
                    break
        
        logger.info(f"关键词意图识别: '{text}' -> {best_intent} (置信度: {best_confidence:.3f})")
        
        return IntentResult(
            text=text,
            intent=best_intent,
            confidence=best_confidence,
            top_intents=None
        )
    
    def _parse_llm_output(self, llm_output: str, user_input: str) -> tuple[str, float]:
        """
        解析LLM输出，提取意图和置信度

        Args:
            llm_output: LLM生成的文本
            user_input: 用户原始输入

        Returns:
            tuple: (意图, 置信度)
        """
        # 选项映射
        option_mapping = {
            'A': "日常聊天",
            'B': "学习任务_创建计划",
            'C': "学习任务_修改计划",
            'D': "学习任务_提交作业",
            'E': "学习任务_生成报告"
        }

        # 首先尝试匹配选项格式（A、B、C、D、E）
        for option, intent in option_mapping.items():
            if option in llm_output.upper():
                confidence = self._calculate_confidence_from_keywords(user_input, intent)
                return intent, confidence

        # 然后尝试直接匹配意图名称
        valid_intents = list(self.intent_mapping.keys())
        for intent in valid_intents:
            if intent in llm_output:
                confidence = self._calculate_confidence_from_keywords(user_input, intent)
                return intent, confidence

        # 如果LLM输出不包含有效意图，使用关键词匹配
        return self._extract_intent_from_keywords(user_input)
    
    def _extract_intent_from_keywords(self, user_input: str) -> tuple[str, float]:
        """
        从用户输入中提取意图（基于关键词）
        
        Args:
            user_input: 用户输入
            
        Returns:
            tuple: (意图, 置信度)
        """
        text_lower = user_input.lower()
        
        # 检查任务时间修改的特殊模式
        time_patterns = [
            r"(.+?)时间改为(\d{1,2}:\d{2}-\d{1,2}:\d{2})",
            r"把(.+?)调整到(\d{1,2}:\d{2}-\d{1,2}:\d{2})",
            r"(.+?)修改为(\d{1,2}:\d{2}-\d{1,2}:\d{2})",
            r"将(.+?)改成(\d{1,2}:\d{2}-\d{1,2}:\d{2})"
        ]
        
        for pattern in time_patterns:
            if re.search(pattern, text_lower):
                return "学习任务_修改计划", 0.95
        
        # 检查其他关键词
        keyword_checks = {
            "学习任务_修改计划": ["修改", "调整", "改", "换"],
            "学习任务_创建计划": ["制定", "安排", "计划", "生成任务"],
            "学习任务_提交作业": ["提交", "上传", "完成作业", "交作业"],
            "学习任务_生成报告": ["报告", "统计", "总结", "学习情况"]
        }
        
        for intent, keywords in keyword_checks.items():
            for keyword in keywords:
                if keyword in text_lower:
                    return intent, 0.75
        
        return "日常聊天", 0.6
    
    def _calculate_confidence_from_keywords(self, user_input: str, intent: str) -> float:
        """
        基于关键词匹配计算置信度
        
        Args:
            user_input: 用户输入
            intent: 识别的意图
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        if intent not in self.keyword_patterns:
            return 0.8  # LLM识别的默认置信度
        
        text_lower = user_input.lower()
        patterns = self.keyword_patterns[intent]
        
        # 检查是否匹配高置信度模式（如时间格式）
        for pattern in patterns:
            if re.search(pattern, text_lower):
                if r"\d{1,2}:\d{2}-\d{1,2}:\d{2}" in pattern:
                    return 0.95
                else:
                    return 0.85
        
        return 0.8
    
    def get_tool_name(self, intent: str) -> str:
        """
        根据意图获取对应的工具名称
        
        Args:
            intent: 意图类型
            
        Returns:
            str: 工具名称
        """
        return self.intent_mapping.get(intent, "smart_chat")
    
    def get_intent_description(self, intent: str) -> str:
        """获取意图描述"""
        descriptions = {
            "日常聊天": "解答作业问题、闲聊内容",
            "学习任务_创建计划": "制定学习计划",
            "学习任务_修改计划": "修改学习计划", 
            "学习任务_提交作业": "拍照提交任务",
            "学习任务_生成报告": "生成学习总结报告"
        }
        return descriptions.get(intent, "未知意图")


# 全局混合意图分类器实例
_hybrid_intent_classifier: Optional[HybridIntentClassifier] = None


def get_hybrid_intent_classifier() -> HybridIntentClassifier:
    """获取混合意图分类器实例（单例模式）"""
    global _hybrid_intent_classifier
    if _hybrid_intent_classifier is None:
        _hybrid_intent_classifier = HybridIntentClassifier()
    return _hybrid_intent_classifier
