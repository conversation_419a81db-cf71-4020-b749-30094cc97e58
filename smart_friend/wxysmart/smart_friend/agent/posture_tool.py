#!/usr/bin/env python3
"""
坐姿检测工具 - 简化版本
专注于触发前端坐姿检测按钮
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class PostureTool:
    """坐姿检测工具类 - 简化版本"""

    def __init__(self):
        """初始化坐姿检测工具"""
        logger.info("坐姿检测工具初始化完成")
    
    def trigger_posture_detection(self) -> Dict[str, Any]:
        """
        触发前端坐姿检测流程

        Returns:
            Dict: 包含触发前端检测指令的响应
        """
        try:
            logger.info("Agent触发坐姿检测...")

            return {
                'success': True,
                'message': '🧘 正在启动坐姿检测...\n\n请保持坐姿端正，我将为你启动摄像头进行检测。\n\n📋 检测步骤：\n1️⃣ 启动坐姿摄像头\n2️⃣ 开始坐姿分析\n3️⃣ 生成检测报告\n\n请稍等片刻...',
                'posture_data': {
                    'status': 'camera_detection_requested',
                    'instruction': 'trigger_camera_detection',
                    'detection_type': 'posture_camera'
                }
            }

        except Exception as e:
            logger.error(f"坐姿检测触发异常: {e}")
            return {
                'success': False,
                'message': '坐姿检测启动失败，请稍后再试。',
                'error': str(e)
            }
    
    def get_posture_tips(self) -> Dict[str, Any]:
        """
        获取坐姿建议

        Returns:
            Dict: 坐姿建议
        """
        return {
            'success': True,
            'message': '''🧘 正确坐姿指南：

📏 **基本要求**：
1. 👤 身体挺直，不要弯腰驼背
2. 👀 眼睛与书本保持30-40cm距离
3. 🦵 双脚平放在地面上
4. 💺 臀部坐满椅子，背部贴靠椅背
5. 📖 书本与桌面成30-45度角

⚠️ **常见问题**：
- ❌ 趴在桌子上写字
- ❌ 歪着头看书
- ❌ 翘二郎腿
- ❌ 离书本太近

💡 **健康提醒**：
- 每30分钟起身活动一下
- 做做颈部和肩部伸展运动
- 眨眼休息，保护视力

保持良好坐姿不仅有利于学习效率，还能保护你的脊椎和视力健康！'''
        }
    
# 创建工具实例
posture_tool = PostureTool()


def register_posture_tools(agent):
    """
    向Agent注册坐姿相关工具

    Args:
        agent: SmartLearningAgent实例
    """

    async def check_posture_camera():
        """触发前端坐姿检测"""
        return posture_tool.trigger_posture_detection()

    async def get_posture_tips():
        """获取坐姿建议"""
        return posture_tool.get_posture_tips()

    # 注册工具
    agent.register_tool(
        'check_posture_camera',
        check_posture_camera,
        '启动坐姿摄像头检测学生的坐姿状态'
    )

    agent.register_tool(
        'posture_tips',
        get_posture_tips,
        '提供正确坐姿的指导建议'
    )

    logger.info("坐姿检测工具已注册到Agent")
