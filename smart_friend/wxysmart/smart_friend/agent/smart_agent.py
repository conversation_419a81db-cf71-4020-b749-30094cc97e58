#!/usr/bin/env python3
"""
智能学习陪伴Agent
专门为儿童学习场景设计的轻量级Agent系统
"""

import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime

from service.doubao_service import DoubaoService
from .memory_system import MemorySystem, MemoryType, MemoryImportance
from .hybrid_intent_classifier import get_hybrid_intent_classifier

logger = logging.getLogger(__name__)


class SmartLearningAgent:
    """
    智能学习陪伴Agent
    
    专门为儿童学习陪伴场景设计，具备：
    - 自主决策能力
    - 工具链整合
    - 语音交互
    - 学习状态监控
    """
    
    def __init__(self, memory_db_path: str = "agent_memory.db"):
        """初始化Agent"""
        self.doubao_service = DoubaoService()
        self.tools = {}  # 工具注册表
        self.conversation_history = []  # 对话历史
        self.child_context = {}  # 学生上下文信息

        # 初始化记忆系统
        self.memory_system = MemorySystem(db_path=memory_db_path)

        # 初始化混合意图分类器
        self.intent_classifier = get_hybrid_intent_classifier()

        # 注册基础工具
        self._register_basic_tools()

        logger.info("SmartLearningAgent初始化完成")
    
    def register_tool(self, name: str, func: Callable, description: str):
        """
        注册工具
        
        Args:
            name: 工具名称
            func: 工具函数
            description: 工具描述
        """
        self.tools[name] = {
            'function': func,
            'description': description
        }
        logger.info(f"工具已注册: {name}")
    
    def _register_basic_tools(self):
        """注册基础工具"""
        

        # 语音任务输入工具
        def voice_task_input(task_content: str, child_id: int = None):
            """将语音输入的任务存储到user_task_inputs表中"""
            try:
                from core.task_input.models.task_input_models import UserTaskInput, OperatorTypeEnum, InputMethodEnum
                from core.user_management.database.connection import get_db_session_context
                from datetime import datetime

                # 如果没有提供child_id，使用默认值
                if not child_id:
                    child_id = 4  # 默认孩子ID，与前端保持一致

                # 存储语音任务到数据库
                with get_db_session_context() as session:
                    record = UserTaskInput.create_input_record(
                        session=session,
                        operator_type=OperatorTypeEnum.CHILD.value,  # 孩子操作
                        operator_id=child_id,  # 操作者ID就是孩子ID
                        child_id=child_id,     # 任务归属孩子ID
                        content=task_content,  # 语音识别的文本内容
                        input_method=InputMethodEnum.VOICE.value,  # 语音输入
                        input_time=datetime.now(),
                        notes="Agent语音任务输入"
                    )

                    if record:
                        session.commit()
                        logger.info(f"语音任务已保存到数据库，记录ID: {record.id}")
                        return {
                            'success': True,
                            'message': f'✅ 任务已记录！\n\n📝 任务内容：{task_content}\n⏰ 记录时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n\n我会帮你跟踪这个任务的完成情况！',
                            'task_data': {
                                'record_id': record.id,
                                'content': task_content,
                                'input_time': record.input_time.isoformat()
                            }
                        }
                    else:
                        return {
                            'success': False,
                            'message': '❌ 任务记录失败，请稍后再试。'
                        }

            except Exception as e:
                logger.error(f"语音任务输入处理失败: {e}")
                return {
                    'success': False,
                    'message': f'❌ 任务记录失败：{str(e)}'
                }

        # 任务生成工具
        def generate_daily_tasks(child_id: int = None):
            """生成今日任务 - 触发前端任务生成功能"""
            try:
                if not child_id:
                    child_id = 4  # 默认孩子ID，与前端保持一致

                # 返回特殊标记，让前端知道需要触发任务生成
                return {
                    'success': True,
                    'message': '📝 正在为您生成今日学习任务...\n\n请稍等，我正在根据您的学习情况制定个性化的任务计划。',
                    'action': 'trigger_task_generation',  # 特殊标记
                    'task_data': {
                        'child_id': child_id,
                        'status': 'task_generation_requested'
                    }
                }

            except Exception as e:
                logger.error(f"任务生成调用失败: {e}")
                return {
                    'success': False,
                    'message': f'任务生成启动失败：{str(e)}'
                }

        # 修改任务计划工具
        def modify_task_plan(child_id: int = None):
            """修改学习任务计划"""
            try:
                if not child_id:
                    child_id = 4  # 默认孩子ID，与前端保持一致

                # 返回特殊标记，让前端知道需要触发任务修改功能
                return {
                    'success': True,
                    'message': '✏️ 正在为您修改学习任务计划...\n\n请告诉我您想要修改的具体内容。',
                    'action': 'trigger_task_modification',  # 特殊标记
                    'task_data': {
                        'child_id': child_id,
                        'status': 'task_modification_requested'
                    }
                }

            except Exception as e:
                logger.error(f"任务修改调用失败: {e}")
                return {
                    'success': False,
                    'message': f'任务修改启动失败：{str(e)}'
                }

        # 提交作业工具
        def submit_homework(child_id: int = None):
            """提交作业 - 触发拍照提交功能"""
            try:
                if not child_id:
                    child_id = 4  # 默认孩子ID，与前端保持一致

                # 返回特殊标记，让前端知道需要触发拍照提交功能
                return {
                    'success': True,
                    'message': '📸 正在启动拍照提交作业功能...\n\n请准备好您的作业，我将为您启动摄像头进行拍照提交。',
                    'action': 'trigger_homework_submission',  # 特殊标记
                    'task_data': {
                        'child_id': child_id,
                        'status': 'homework_submission_requested'
                    }
                }

            except Exception as e:
                logger.error(f"作业提交调用失败: {e}")
                return {
                    'success': False,
                    'message': f'作业提交启动失败：{str(e)}'
                }

        # 生成学习报告工具
        def generate_report(child_id: int = None):
            """生成学习总结报告"""
            try:
                if not child_id:
                    child_id = 4  # 默认孩子ID，与前端保持一致

                # 返回特殊标记，让前端知道需要触发报告生成功能
                return {
                    'success': True,
                    'message': '📊 正在为您生成学习总结报告...\n\n请稍等，我正在分析您的学习数据并生成详细报告。',
                    'action': 'trigger_report_generation',  # 特殊标记
                    'task_data': {
                        'child_id': child_id,
                        'status': 'report_generation_requested'
                    }
                }

            except Exception as e:
                logger.error(f"报告生成调用失败: {e}")
                return {
                    'success': False,
                    'message': f'报告生成启动失败：{str(e)}'
                }

        # 智能聊天工具（处理所有对话）
        def smart_chat(user_input: str, child_id: int = None):
            """处理所有类型的对话，包括问候、学习建议、鼓励、日常聊天等"""
            try:
                if not child_id:
                    child_id = 4  # 默认孩子ID，与前端保持一致

                # 获取相关记忆作为上下文
                relevant_memories = self.memory_system.retrieve_memories(
                    child_id=child_id,
                    query=user_input,
                    limit=3
                )

                # 构建记忆上下文
                memory_context = ""
                if relevant_memories:
                    memory_items = []
                    for memory in relevant_memories:
                        memory_items.append(f"- {memory.content}")
                    memory_context = f"\n\n相关记忆信息：\n" + "\n".join(memory_items)

                # 使用豆包模型生成智能回复
                chat_prompt = f"""你是一个友善、活泼的儿童学习陪伴AI助手，名叫"智能小伙伴"。

用户说：{user_input}{memory_context}

请根据用户的输入内容和相关记忆信息，智能地给出合适的回复：

1. 如果是问候（如"你好"、"hi"等），请热情地介绍自己和功能
2. 如果询问学习方法或遇到学习困难，请提供具体的学习建议
3. 如果需要鼓励或支持，请给出积极正面的鼓励
4. 如果是日常聊天，请自然地回应
5. 如果涉及坐姿相关问题，请说明需要使用坐姿检测功能
6. 如果有相关记忆信息，请适当地结合这些信息来回复

回复要求：
- 语言要适合儿童，简单易懂
- 保持积极正面的态度
- 可以适当使用emoji表情
- 回复要简洁，一般不超过150字
- 保持对话的连贯性和趣味性
- 如果是学习建议，可以给出具体的方法和步骤
- 如果有相关记忆，可以自然地提及，但不要过于突兀

请直接给出回复内容，不需要其他格式："""

                # 调用豆包模型
                response = self.doubao_service.chat_completion(
                    messages=[{"role": "user", "content": chat_prompt}],
                    temperature=0.8  # 增加一些随机性，让对话更自然
                )

                if response and response.get('success'):
                    chat_reply = response['response_text'].strip()

                    # 将对话添加到对话记忆（确保持久化）
                    self.memory_system.add_memory(
                        child_id=child_id,
                        content=f"用户说：{user_input}，我回复：{chat_reply}",
                        memory_type=MemoryType.CONVERSATION,
                        importance=MemoryImportance.MEDIUM,
                        context={"interaction_type": "chat", "timestamp": datetime.now().isoformat()},
                        tags=["对话", "聊天"]
                    )

                    return {
                        'success': True,
                        'message': chat_reply
                    }
                else:
                    return {
                        'success': True,
                        'message': '哈哈，我刚才走神了，你能再说一遍吗？😊'
                    }

            except Exception as e:
                logger.error(f"智能聊天处理失败: {e}")
                return {
                    'success': True,
                    'message': '我现在有点忙，不过很高兴和你聊天！有什么需要帮助的可以随时告诉我哦～😊'
                }

        # 记忆管理工具
        def remember_important_info(info_content: str, child_id: int = None, importance: str = "medium"):
            """记住重要信息"""
            try:
                if not child_id:
                    child_id = 4  # 默认孩子ID，与前端保持一致

                # 转换重要性级别
                importance_map = {
                    "low": MemoryImportance.LOW,
                    "medium": MemoryImportance.MEDIUM,
                    "high": MemoryImportance.HIGH,
                    "critical": MemoryImportance.CRITICAL
                }
                importance_level = importance_map.get(importance.lower(), MemoryImportance.MEDIUM)

                # 添加到长期记忆
                memory_id = self.memory_system.add_memory(
                    child_id=child_id,
                    content=info_content,
                    memory_type=MemoryType.LONG_TERM,
                    importance=importance_level,
                    context={"source": "user_request", "timestamp": datetime.now().isoformat()},
                    tags=["user_info", "important"]
                )

                if memory_id:
                    return {
                        'success': True,
                        'message': f'✅ 重要信息已记住！\n\n📝 内容：{info_content}\n🧠 重要性：{importance}\n🆔 记忆ID：{memory_id[:8]}...\n\n我会在需要时回忆起这个信息！'
                    }
                else:
                    return {
                        'success': False,
                        'message': '❌ 记忆存储失败，请稍后再试。'
                    }

            except Exception as e:
                logger.error(f"记忆存储失败: {e}")
                return {
                    'success': False,
                    'message': f'❌ 记忆存储失败：{str(e)}'
                }

        def recall_memories(query: str = "", child_id: int = None, limit: int = 5):
            """回忆相关记忆"""
            try:
                if not child_id:
                    child_id = 4  # 默认孩子ID，与前端保持一致

                memories = self.memory_system.retrieve_memories(
                    child_id=child_id,
                    query=query,
                    limit=limit
                )

                if not memories:
                    return {
                        'success': True,
                        'message': f'🤔 没有找到与"{query}"相关的记忆信息。'
                    }

                memory_list = []
                for i, memory in enumerate(memories, 1):
                    memory_info = f"{i}. {memory.content}"
                    if memory.tags:
                        memory_info += f" (标签: {', '.join(memory.tags)})"
                    memory_list.append(memory_info)

                response = f'🧠 找到 {len(memories)} 条相关记忆：\n\n' + '\n\n'.join(memory_list)

                return {
                    'success': True,
                    'message': response
                }

            except Exception as e:
                logger.error(f"记忆回忆失败: {e}")
                return {
                    'success': False,
                    'message': f'❌ 记忆回忆失败：{str(e)}'
                }

        def get_memory_summary(child_id: int = None):
            """获取记忆摘要"""
            try:
                if not child_id:
                    child_id = 4  # 默认孩子ID，与前端保持一致

                # 获取记忆统计
                stats = self.memory_system.get_memory_stats(child_id)

                # 获取上下文摘要
                context_summary = self.memory_system.get_context_summary(child_id)

                response = f"""🧠 记忆系统状态：

📊 记忆统计：
• 短期记忆：{stats.get('short_term_count', 0)} 条
• 工作记忆：{stats.get('working_memory_count', 0)} 条
• 长期记忆：{stats.get('long_term_count', 0)} 条
• 语义记忆：{stats.get('semantic_count', 0)} 条
• 总计：{stats.get('total_memories', 0)} 条

{context_summary}"""

                return {
                    'success': True,
                    'message': response
                }

            except Exception as e:
                logger.error(f"获取记忆摘要失败: {e}")
                return {
                    'success': False,
                    'message': f'❌ 获取记忆摘要失败：{str(e)}'
                }

        # 注册工具
        self.register_tool('voice_task_input', voice_task_input, '将语音输入的任务存储到数据库中')
        self.register_tool('generate_daily_tasks', generate_daily_tasks, '生成今日学习任务计划')
        self.register_tool('modify_task_plan', modify_task_plan, '修改学习任务计划')
        self.register_tool('submit_homework', submit_homework, '提交作业 - 触发拍照提交功能')
        self.register_tool('generate_report', generate_report, '生成学习总结报告')
        self.register_tool('remember_info', remember_important_info, '记住重要信息到长期记忆中')
        self.register_tool('recall_memories', recall_memories, '回忆相关的记忆信息')
        self.register_tool('memory_summary', get_memory_summary, '获取记忆系统状态摘要')
        self.register_tool('smart_chat', smart_chat, '处理所有类型的对话，包括问候、学习建议、鼓励、日常聊天等')
    
    async def process_voice_input(self, voice_text: str, child_id: int = None) -> Dict[str, Any]:
        """
        处理语音输入
        
        Args:
            voice_text: 语音识别的文本
            child_id: 学生ID
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"处理语音输入: {voice_text}")
            
            # 更新学生上下文
            if child_id:
                self.child_context['child_id'] = child_id
                self.child_context['last_interaction'] = datetime.now().isoformat()

            # 记录用户输入到对话记忆（确保持久化）
            if child_id:
                self.memory_system.add_memory(
                    child_id=child_id,
                    content=f"用户语音输入: {voice_text}",
                    memory_type=MemoryType.CONVERSATION,
                    importance=MemoryImportance.MEDIUM,
                    context={"input_type": "voice", "timestamp": datetime.now().isoformat()},
                    tags=["用户输入", "语音"]
                )

            # 添加到对话历史
            self.conversation_history.append({
                'type': 'user',
                'content': voice_text,
                'timestamp': datetime.now().isoformat(),
                'child_id': child_id
            })
            
            # 分析意图并选择合适的响应策略（使用混合意图分类器）
            response = await self._analyze_intent_with_roberta(voice_text, child_id)
            
            # 添加Agent响应到历史
            self.conversation_history.append({
                'type': 'agent',
                'content': response.get('message', ''),
                'timestamp': datetime.now().isoformat(),
                'child_id': child_id
            })

            # 记录Agent响应到对话记忆（确保持久化）
            if child_id and response.get('success'):
                self.memory_system.add_memory(
                    child_id=child_id,
                    content=f"我回复: {response.get('message', '')}",
                    memory_type=MemoryType.CONVERSATION,
                    importance=MemoryImportance.MEDIUM,
                    context={"response_type": response.get('tool_used', 'direct'),
                             "timestamp": datetime.now().isoformat()},
                    tags=["Agent响应"]
                )
            
            return response
            
        except Exception as e:
            logger.error(f"处理语音输入异常: {e}")
            return {
                'success': False,
                'message': '抱歉，我遇到了一些问题，请稍后再试。',
                'error': str(e)
            }
    
    def _get_tools_by_intent_category(self, intent: str) -> List[str]:
        """
        根据意图类别获取该类别下的所有工具

        Args:
            intent: 意图类别

        Returns:
            List[str]: 该类别下的工具名称列表
        """
        intent_tool_mapping = {
            "日常聊天": [
                "smart_chat",           # 智能聊天
                "remember_info",        # 记住信息
                "recall_memories",      # 回忆记忆
                "memory_summary" ,       # 记忆摘要
                "check_posture_camera" ,        #检查坐姿
                "generate_daily_tasks", # 生成今日任务
                "voice_task_input" ,  # 语音任务输入
                "modify_task_plan" ,  # 修改任务计划
                "modify_task_time" ,  # 修改任务时间
                "submit_homework" # 提交作业

            ],
            "学习任务_创建计划": [
                "generate_daily_tasks", # 生成今日任务
                "voice_task_input"      # 语音任务输入
            ],
            "学习任务_修改计划": [
                "modify_task_plan" ,   # 修改任务计划
                "modify_task_time" ,   # 修改任务时间
                "voice_task_input"      # 语音任务输入
            ],
            "学习任务_提交作业": [
                "submit_homework",     # 提交作业
   
            ],
            "学习任务_生成报告": [
                "generate_report"      # 生成报告

            ]
        }

        return intent_tool_mapping.get(intent, ["smart_chat"])

    def _get_tools_description_by_category(self, intent: str) -> str:
        """
        获取特定意图类别下工具的描述

        Args:
            intent: 意图类别

        Returns:
            str: 工具描述文本
        """
        category_tools = self._get_tools_by_intent_category(intent)
        descriptions = []

        for tool_name in category_tools:
            if tool_name in self.tools:
                descriptions.append(f"- {tool_name}: {self.tools[tool_name]['description']}")

        return "\n".join(descriptions)

    async def _analyze_intent_with_roberta(self, text: str, child_id: int = None) -> Dict[str, Any]:
        """
        使用混合意图分类器进行二级分类分析用户意图并生成响应

        第一级：混合意图分类器识别意图类别（优先LLM，备选关键词匹配）
        第二级：在该类别下选择具体工具

        Args:
            text: 用户输入文本
            child_id: 学生ID

        Returns:
            Dict: 响应结果
        """
        try:
            # 检查是否是意图确认回复
            clarification_result = self._handle_intent_clarification_response(text, child_id)
            if clarification_result:
                return clarification_result

            # 第一级分类：使用混合意图分类器进行意图识别
            intent_result = self.intent_classifier.predict_intent(text)

            if not intent_result:
                logger.warning("混合意图识别失败，使用默认聊天工具")
                return self.tools['smart_chat']['function'](user_input=text, child_id=child_id)

            intent = intent_result.intent
            confidence = intent_result.confidence

            logger.info(f"第一级分类 - 混合意图识别: {intent} (置信度: {confidence:.3f})")

            # 检查置信度阈值 - 如果低于0.8，让Agent自己判断工具
            if confidence < 0.7:
                logger.info(f"意图置信度较低 ({confidence:.3f} < 0.8)，让Agent自己判断最佳工具")
                return await self._agent_decide_tool_on_low_confidence(text, intent, confidence, child_id)

            # 检查置信度阈值 - 但对于日常聊天类别，即使置信度低也进行二级分类
            if confidence < 0.5 and intent != "日常聊天":
                logger.info(f"意图置信度过低 ({confidence:.3f} < 0.5)，使用聊天工具")
                return self.tools['smart_chat']['function'](user_input=text, child_id=child_id)

            # 第二级分类：在该意图类别下选择具体工具
            category_tools = self._get_tools_by_intent_category(intent)

            if len(category_tools) == 1:
                # 如果该类别只有一个工具，直接使用
                tool_name = category_tools[0]
                logger.info(f"第二级分类 - 直接选择唯一工具: {tool_name}")
            else:
                # 如果该类别有多个工具，使用豆包模型进行二级分类
                tool_name = await self._select_tool_in_category(text, intent, category_tools, child_id)
                logger.info(f"第二级分类 - 选择工具: {tool_name}")

            # 准备工具参数
            tool_params = self._prepare_tool_params(tool_name, text, child_id)

            # 调用对应工具
            if tool_name in self.tools:
                tool_result = self.tools[tool_name]['function'](**tool_params)

                # 添加意图识别信息到结果中
                tool_result['intent_info'] = {
                    'intent': intent,
                    'confidence': confidence,
                    'tool_used': tool_name,
                    'classification_level': 'two_level'
                }

                return tool_result
            else:
                logger.error(f"工具 {tool_name} 不存在")
                return {
                    'success': False,
                    'message': f'工具 {tool_name} 不存在'
                }

        except Exception as e:
            logger.error(f"混合意图分类异常: {e}")
            # 降级到智能聊天
            return self.tools['smart_chat']['function'](user_input=text, child_id=child_id)

    async def _select_tool_in_category(self, text: str, intent: str, category_tools: List[str], child_id: int = None) -> str:
        """
        在特定意图类别下选择具体工具（第二级分类）

        Args:
            text: 用户输入文本
            intent: 意图类别
            category_tools: 该类别下的工具列表
            child_id: 学生ID

        Returns:
            str: 选择的工具名称
        """
        try:
            # 构建第二级分类的系统提示
            tools_description = self._get_tools_description_by_category(intent)

            system_prompt = f"""你是一个专业的儿童学习陪伴AI助手工具选择器。

用户的意图类别已确定为：{intent}

在该类别下，当前可用的工具有：
{tools_description}

用户输入: {text}

请根据用户的具体需求，从上述工具中选择最合适的一个。

选择标准：
1. 如果用户输入包含"检查坐姿"、"坐姿检测"、"看看坐姿"、"立即检查坐姿"等关键词，必须选择check_posture_camera工具
2. 如果用户要求修改任务时间，包含"时间改为"、"调整到"、"修改为"、"改成"等关键词，必须选择modify_task_time工具
3. 如果用户在描述具体任务内容，选择voice_task_input
4. 如果用户要求记住信息，选择remember_info
5. 如果用户询问之前的记忆，选择recall_memories
6. 如果用户询问记忆状态，选择memory_summary
7. 对于其他情况，选择smart_chat工具

重要提醒：
- 用户说"检查坐姿"、"坐姿检测"等，必须选择check_posture_camera，不要选择smart_chat
- 用户说"数学作业时间改为14:00-15:30"、"把英语作业调整到16:00-17:00"等，必须选择modify_task_time
- 只有在用户明确要求坐姿检测时才选择check_posture_camera
- 只有在用户明确要求修改任务时间时才选择modify_task_time
- 其他日常对话选择smart_chat

请直接回复工具名称，不需要其他解释。"""

            # 调用豆包模型进行第二级分类
            result = self.doubao_service.simple_chat(system_prompt)

            if result.get('success', False):
                selected_tool = result.get('response_text', '').strip()

                # 验证选择的工具是否在可用工具列表中
                if selected_tool in category_tools:
                    return selected_tool
                else:
                    logger.warning(f"选择的工具 {selected_tool} 不在类别 {intent} 的工具列表中，使用默认工具")
                    return category_tools[0]  # 返回第一个工具作为默认
            else:
                logger.warning("第二级分类失败，使用默认工具")
                return category_tools[0]  # 返回第一个工具作为默认

        except Exception as e:
            logger.error(f"第二级工具选择异常: {e}")
            return category_tools[0] if category_tools else "smart_chat"

    def _prepare_tool_params(self, tool_name: str, text: str, child_id: int = None) -> Dict[str, Any]:
        """
        为工具准备参数

        Args:
            tool_name: 工具名称
            text: 用户输入文本
            child_id: 学生ID

        Returns:
            Dict: 工具参数
        """
        tool_params = {}

        # 根据工具类型设置参数
        if tool_name == 'smart_chat':
            tool_params = {'user_input': text, 'child_id': child_id}
        elif tool_name == 'voice_task_input':
            tool_params = {'task_content': text, 'child_id': child_id}
        elif tool_name in ['generate_daily_tasks', 'modify_task_plan', 'submit_homework', 'generate_report']:
            tool_params = {'child_id': child_id}
        elif tool_name == 'remember_info':
            tool_params = {'info_content': text, 'child_id': child_id, 'importance': 'medium'}
        elif tool_name == 'recall_memories':
            tool_params = {'query': text, 'child_id': child_id}
        elif tool_name == 'memory_summary':
            tool_params = {'child_id': child_id}
        elif tool_name == 'check_posture_camera':
            # 坐姿检测工具的参数
            tool_params = {}
        elif tool_name == 'modify_task_time':
            # 任务时间修改工具的参数
            tool_params = {'user_input': text, 'child_id': child_id}
        else:
            # 默认参数
            tool_params = {'child_id': child_id}

        return tool_params

    async def _analyze_and_respond(self, text: str, child_id: int = None) -> Dict[str, Any]:
        """
        分析用户输入并生成响应（使用豆包模型的原始方法，作为备用）

        Args:
            text: 用户输入文本
            child_id: 学生ID

        Returns:
            Dict: 响应结果
        """
        try:
            # 构建系统提示
            system_prompt = f"""你是一个专业的儿童学习陪伴AI助手，名叫"智能小伙伴"。

当前可用工具：
{self._get_tools_description()}

用户输入: {text}

请分析用户的需求，并选择合适的响应方式：
1. 如果要求检查坐姿、坐姿检测、看看坐姿等，使用check_posture_camera工具
2. 如果要求修改任务时间（如"数学作业时间改为14:00-15:30"、"把英语作业调整到16:00-17:00"等），使用modify_task_time工具
3. 如果用户在描述具体的任务、作业、学习计划等需要记录的内容，使用voice_task_input工具
4. 如果用户要求生成任务、创建任务、制定计划、安排学习等，使用generate_daily_tasks工具
5. 如果用户要求记住某些重要信息（如"记住我喜欢数学"、"帮我记住明天考试"等），使用remember_info工具
6. 如果用户询问之前的记忆或信息（如"你还记得我说过什么吗"、"回忆一下我的学习情况"等），使用recall_memories工具
7. 如果用户询问记忆状态或统计（如"我的记忆情况如何"、"记忆摘要"等），使用memory_summary工具
7. 对于所有其他类型的对话（包括问候、学习建议、鼓励、日常聊天等），使用smart_chat工具

识别标准：
- 任务输入：包含具体的学习任务描述、时间安排或计划、作业内容等
- 任务生成：用户要求生成、创建、制定任务计划，使用"生成任务"、"创建任务"、"制定计划"、"安排学习"、"今日任务"等词汇
- 记忆存储：用户明确要求记住某些信息，使用"记住"、"别忘了"等词汇
- 记忆回忆：用户询问之前的信息，使用"记得"、"回忆"、"之前说过"等词汇
- 记忆状态：用户询问记忆系统状态，使用"记忆情况"、"摘要"等词汇

请以JSON格式回复，包含：
- action: 要执行的动作（"use_tool"）
- tool_name: 工具名称
- tool_params: 工具参数

示例：
{{"action": "use_tool", "tool_name": "remember_info", "tool_params": {{"info_content": "{text}", "child_id": {child_id}, "importance": "medium"}}}}
或
{{"action": "use_tool", "tool_name": "recall_memories", "tool_params": {{"query": "{text}", "child_id": {child_id}}}}}
或
{{"action": "use_tool", "tool_name": "smart_chat", "tool_params": {{"user_input": "{text}", "child_id": {child_id}}}}}
"""
            
            # 调用豆包模型分析
            result = self.doubao_service.simple_chat(system_prompt)
            
            if not result.get('success', False):
                return {
                    'success': False,
                    'message': '抱歉，我现在无法理解你的问题，请稍后再试。'
                }
            
            # 解析LLM响应
            response_text = result.get('response_text', '')
            
            try:
                # 尝试解析JSON响应
                decision = json.loads(response_text)
                
                if decision.get('action') == 'use_tool':
                    tool_name = decision.get('tool_name')
                    tool_params = decision.get('tool_params', {})

                    # 确保需要child_id的工具都能获得正确的child_id
                    tools_need_child_id = ['voice_task_input', 'generate_daily_tasks', 'modify_task_plan', 'submit_homework', 'generate_report', 'remember_info', 'recall_memories', 'memory_summary', 'smart_chat']
                    if tool_name in tools_need_child_id and child_id:
                        tool_params['child_id'] = child_id

                    if tool_name in self.tools:
                        tool_result = self.tools[tool_name]['function'](**tool_params)
                        return tool_result
                    else:
                        return {
                            'success': False,
                            'message': f'工具 {tool_name} 不存在'
                        }
                
                elif decision.get('action') == 'direct_response':
                    return {
                        'success': True,
                        'message': decision.get('message', '我理解了你的问题。')
                    }
                
            except json.JSONDecodeError:
                # 如果无法解析JSON，直接使用LLM的回复
                return {
                    'success': True,
                    'message': response_text
                }
            
            return {
                'success': True,
                'message': '我收到了你的消息，让我想想如何帮助你。'
            }
            
        except Exception as e:
            logger.error(f"分析和响应异常: {e}")
            return {
                'success': False,
                'message': '抱歉，我遇到了一些问题，请稍后再试。',
                'error': str(e)
            }
    
    def _get_tools_description(self) -> str:
        """获取工具描述"""
        descriptions = []
        for name, tool in self.tools.items():
            descriptions.append(f"- {name}: {tool['description']}")
        return '\n'.join(descriptions)
    
    def get_conversation_history(self, child_id: int = None, limit: int = 10) -> List[Dict]:
        """
        获取对话历史
        
        Args:
            child_id: 学生ID，如果指定则只返回该学生的历史
            limit: 返回的历史记录数量限制
            
        Returns:
            List[Dict]: 对话历史列表
        """
        history = self.conversation_history
        
        if child_id:
            history = [h for h in history if h.get('child_id') == child_id]
        
        return history[-limit:] if limit > 0 else history
    
    def _ask_for_intent_clarification(self, text: str, predicted_intent: str, confidence: float, child_id: int) -> Dict:
        """
        当意图识别置信度较低时，询问用户具体意图

        Args:
            text: 用户输入文本
            predicted_intent: 预测的意图
            confidence: 置信度
            child_id: 学生ID

        Returns:
            Dict: 包含询问信息的响应
        """
        try:
            # 构建意图选项
            intent_options = {
                "日常聊天": "💬 日常聊天 - 普通对话交流",
                "学习任务_创建计划": "📝 创建学习计划 - 生成今日学习任务",
                "学习任务_修改计划": "✏️ 修改学习计划 - 调整现有任务安排",
                "学习任务_提交作业": "📸 提交作业 - 拍照上传作业内容",
                "学习任务_生成报告": "📊 生成学习报告 - 查看学习进度总结"
            }

            # 生成询问消息
            clarification_message = f"""🤔 我不太确定你的具体意图（当前理解为：{predicted_intent}，置信度：{confidence:.1%}）

请告诉我你想要做什么：

1️⃣ {intent_options.get("日常聊天", "日常聊天")}
2️⃣ {intent_options.get("学习任务_创建计划", "创建学习计划")}
3️⃣ {intent_options.get("学习任务_修改计划", "修改学习计划")}
4️⃣ {intent_options.get("学习任务_提交作业", "提交作业")}
5️⃣ {intent_options.get("学习任务_生成报告", "生成学习报告")}

你可以直接说出对应的数字（如"1"）或者重新描述你的需求。"""

            # 记录到记忆系统
            self.memory_system.add_memory(
                content=f"用户输入: {text}",
                memory_type="conversation",
                importance=MemoryImportance.NORMAL,
                child_id=child_id
            )

            self.memory_system.add_memory(
                content=f"Agent询问意图确认: 置信度{confidence:.1%}过低，需要用户澄清意图",
                memory_type="conversation",
                importance=MemoryImportance.NORMAL,
                child_id=child_id
            )

            return {
                'success': True,
                'message': clarification_message,
                'child_id': child_id,
                'timestamp': None,
                'data': {
                    'status': 'intent_clarification_requested',
                    'original_text': text,
                    'predicted_intent': predicted_intent,
                    'confidence': confidence,
                    'options': intent_options
                },
                'intent_info': {
                    'intent': 'intent_clarification',
                    'confidence': confidence,
                    'tool_used': 'intent_clarification',
                    'classification_level': 'clarification_needed'
                }
            }

        except Exception as e:
            logger.error(f"意图确认询问失败: {e}")
            # 降级到智能聊天
            return self.tools['smart_chat']['function'](user_input=text, child_id=child_id)

    def _handle_intent_clarification_response(self, text: str, child_id: int) -> Dict:
        """
        处理用户对意图确认的回复

        Args:
            text: 用户输入文本
            child_id: 学生ID

        Returns:
            Dict: 如果是意图确认回复则返回处理结果，否则返回None
        """
        try:
            # 检查是否是数字回复
            text_clean = text.strip()

            # 意图映射
            intent_mapping = {
                "1": "日常聊天",
                "2": "学习任务_创建计划",
                "3": "学习任务_修改计划",
                "4": "学习任务_提交作业",
                "5": "学习任务_生成报告"
            }

            # 关键词映射
            keyword_mapping = {
                "聊天": "日常聊天",
                "对话": "日常聊天",
                "创建": "学习任务_创建计划",
                "生成": "学习任务_创建计划",
                "计划": "学习任务_创建计划",
                "任务": "学习任务_创建计划",
                "修改": "学习任务_修改计划",
                "调整": "学习任务_修改计划",
                "提交": "学习任务_提交作业",
                "作业": "学习任务_提交作业",
                "拍照": "学习任务_提交作业",
                "报告": "学习任务_生成报告",
                "总结": "学习任务_生成报告",
                "进度": "学习任务_生成报告"
            }

            selected_intent = None

            # 检查数字回复
            if text_clean in intent_mapping:
                selected_intent = intent_mapping[text_clean]
                logger.info(f"用户选择意图: {text_clean} -> {selected_intent}")

            # 检查关键词
            elif not selected_intent:
                for keyword, intent in keyword_mapping.items():
                    if keyword in text_clean:
                        selected_intent = intent
                        logger.info(f"通过关键词'{keyword}'识别意图: {selected_intent}")
                        break

            # 如果识别到明确意图，直接处理
            if selected_intent:
                logger.info(f"意图确认成功: {selected_intent}")

                # 记录到记忆系统
                self.memory_system.add_memory(
                    content=f"用户确认意图: {selected_intent}",
                    memory_type="conversation",
                    importance=MemoryImportance.NORMAL,
                    child_id=child_id
                )

                # 直接处理该意图
                return self._process_confirmed_intent(text, selected_intent, child_id)

            # 如果不是意图确认回复，返回None继续正常流程
            return None

        except Exception as e:
            logger.error(f"处理意图确认回复失败: {e}")
            return None

    def _process_confirmed_intent(self, text: str, intent: str, child_id: int) -> Dict:
        """
        处理用户确认的意图

        Args:
            text: 用户输入文本
            intent: 确认的意图
            child_id: 学生ID

        Returns:
            Dict: 处理结果
        """
        try:
            # 第二级分类：在该意图类别下选择具体工具
            category_tools = self._get_tools_by_intent_category(intent)

            if len(category_tools) == 1:
                # 如果该类别只有一个工具，直接使用
                tool_name = category_tools[0]
                logger.info(f"确认意图后直接选择唯一工具: {tool_name}")
            else:
                # 如果有多个工具，使用豆包模型进行第二级分类
                tool_name = asyncio.run(self._select_tool_in_category(text, intent, category_tools, child_id))
                logger.info(f"确认意图后二级分类选择工具: {tool_name}")

            # 准备工具参数
            tool_params = self._prepare_tool_params(tool_name, text, child_id)

            # 执行工具
            result = self.tools[tool_name]['function'](**tool_params)

            # 添加意图信息
            if isinstance(result, dict):
                result['intent_info'] = {
                    'intent': intent,
                    'confidence': 1.0,  # 用户确认的意图置信度为1.0
                    'tool_used': tool_name,
                    'classification_level': 'user_confirmed'
                }

            return result

        except Exception as e:
            logger.error(f"处理确认意图失败: {e}")
            # 降级到智能聊天
            return self.tools['smart_chat']['function'](user_input=text, child_id=child_id)

    def clear_conversation_history(self, child_id: int = None):
        """
        清空对话历史

        Args:
            child_id: 如果指定，只清空该学生的历史
        """
        if child_id:
            self.conversation_history = [
                h for h in self.conversation_history 
                if h.get('child_id') != child_id
            ]
        else:
            self.conversation_history = []
        
        logger.info(f"对话历史已清空 (child_id: {child_id})")

    async def _agent_decide_tool_on_low_confidence(self, text: str, detected_intent: str, confidence: float, child_id: int = None) -> Dict[str, Any]:
        """
        当置信度低于0.8时，让Agent自己判断应该使用哪个工具

        Args:
            text: 用户输入文本
            detected_intent: 检测到的意图
            confidence: 置信度
            child_id: 学生ID

        Returns:
            Dict: 工具执行结果
        """
        try:
            logger.info(f"Agent开始自主判断工具 - 输入: '{text}', 检测意图: {detected_intent}, 置信度: {confidence:.3f}")

            # 构建Agent自主判断的提示词
            decision_prompt = f"""你是一个智能学习助手，需要根据用户输入判断应该使用哪个工具来最好地帮助用户。

用户输入："{text}"
初步检测意图：{detected_intent} (置信度: {confidence:.3f})

可用的工具及其功能：
1. modify_task_time - 修改任务时间（当用户说"作业时间改为XX:XX-XX:XX"、"调整到XX:XX-XX:XX"等）
2. voice_task_input - 语音任务输入（当用户描述具体任务内容、作业要求等）
3. generate_daily_tasks - 生成今日任务（当用户要求制定学习计划、安排任务等）
4. modify_task_plan - 修改任务计划（当用户要求修改学习安排、调整计划等）
5. submit_homework - 提交作业（当用户说要提交作业、上传作业等）
6. generate_report - 生成报告（当用户要求生成学习报告、查看统计等）
7. remember_info - 记住信息（当用户要求记住某些重要信息）
8. recall_memories - 回忆记忆（当用户询问之前的记忆或信息）
9. memory_summary - 记忆摘要（当用户询问记忆状态或统计）
10. check_posture_camera - 检查坐姿（当用户说"检查坐姿"、"坐姿检测"等）
11. smart_chat - 智能聊天（普通对话、问答、闲聊等）

分析规则：
- 如果用户输入包含时间格式（如"19:00-20:00"）且提到"改为"、"调整"、"修改"等词，优先选择 modify_task_time
- 如果用户在描述具体任务内容，选择 voice_task_input
- 如果用户要求生成或制定计划，选择 generate_daily_tasks
- 如果用户要求检查坐姿，选择 check_posture_camera
- 如果是普通对话或问答，选择 smart_chat

请直接回答最适合的工具名称，只需要回答工具名称，不要解释："""

            # 调用豆包模型进行判断
            response = self.doubao_service.chat_completion(
                messages=[{"role": "user", "content": decision_prompt}],
                temperature=0.1  # 使用较低温度确保结果稳定
            )

            if response and response.get('success'):
                decision_text = response['response_text'].strip()
                logger.info(f"Agent自主判断结果: {decision_text}")

                # 提取工具名称
                selected_tool = self._extract_tool_name_from_decision(decision_text)

                if selected_tool and selected_tool in self.tools:
                    logger.info(f"Agent选择工具: {selected_tool}")

                    # 准备工具参数
                    tool_params = self._prepare_tool_params(selected_tool, text, child_id)

                    # 执行选择的工具
                    result = await self.tools[selected_tool]['function'](**tool_params)

                    # 记录到记忆系统
                    await self.memory_system.add_memory(
                        child_id=child_id,
                        content=f"用户: {text}",
                        memory_type=MemoryType.CONVERSATION,
                        importance=MemoryImportance.MEDIUM
                    )

                    if isinstance(result, dict) and result.get('success'):
                        await self.memory_system.add_memory(
                            child_id=child_id,
                            content=f"助手: {result.get('message', '')}",
                            memory_type=MemoryType.CONVERSATION,
                            importance=MemoryImportance.MEDIUM
                        )

                    return result
                else:
                    logger.warning(f"Agent选择的工具无效: {selected_tool}，降级到智能聊天")
                    return await self.tools['smart_chat']['function'](user_input=text, child_id=child_id)
            else:
                logger.error("Agent自主判断失败，降级到智能聊天")
                return await self.tools['smart_chat']['function'](user_input=text, child_id=child_id)

        except Exception as e:
            logger.error(f"Agent自主判断异常: {e}")
            return await self.tools['smart_chat']['function'](user_input=text, child_id=child_id)

    def _extract_tool_name_from_decision(self, decision_text: str) -> Optional[str]:
        """
        从Agent的判断结果中提取工具名称

        Args:
            decision_text: Agent的判断结果文本

        Returns:
            str: 提取的工具名称，如果无法提取则返回None
        """
        # 定义所有可用工具
        available_tools = [
            'modify_task_time', 'voice_task_input', 'generate_daily_tasks',
            'modify_task_plan', 'submit_homework', 'generate_report',
            'remember_info', 'recall_memories', 'memory_summary',
            'check_posture_camera', 'smart_chat'
        ]

        # 直接匹配工具名称
        decision_lower = decision_text.lower()
        for tool in available_tools:
            if tool in decision_lower:
                return tool

        # 如果没有直接匹配，尝试关键词匹配
        keyword_mapping = {
            'modify_task_time': ['修改时间', '调整时间', '改时间', 'modify_task_time'],
            'voice_task_input': ['任务输入', '语音输入', 'voice_task_input'],
            'generate_daily_tasks': ['生成任务', '制定计划', 'generate_daily_tasks'],
            'modify_task_plan': ['修改计划', '调整计划', 'modify_task_plan'],
            'submit_homework': ['提交作业', '上传作业', 'submit_homework'],
            'generate_report': ['生成报告', '学习报告', 'generate_report'],
            'remember_info': ['记住信息', '记忆', 'remember_info'],
            'recall_memories': ['回忆', '记忆查询', 'recall_memories'],
            'memory_summary': ['记忆摘要', '记忆统计', 'memory_summary'],
            'check_posture_camera': ['检查坐姿', '坐姿检测', 'check_posture_camera'],
            'smart_chat': ['聊天', '对话', 'smart_chat']
        }

        for tool, keywords in keyword_mapping.items():
            for keyword in keywords:
                if keyword in decision_lower:
                    return tool

        return None


# 全局Agent实例
_agent_instance: Optional[SmartLearningAgent] = None


def get_agent() -> SmartLearningAgent:
    """获取Agent实例（单例模式）"""
    global _agent_instance
    if _agent_instance is None:
        _agent_instance = SmartLearningAgent()
    return _agent_instance
