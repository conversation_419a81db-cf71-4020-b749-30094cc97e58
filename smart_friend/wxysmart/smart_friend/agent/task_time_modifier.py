#!/usr/bin/env python3
"""
任务时间修改工具
让agent能够根据用户的语音指令自动修改任务时间
"""

import logging
import re
from typing import Dict, Any, Optional, List
from datetime import datetime, date, time
from backend.models import get_db_session_context, DailyTask, TaskItem

logger = logging.getLogger(__name__)


class TaskTimeModifier:
    """任务时间修改工具类"""
    
    def __init__(self):
        """初始化任务时间修改工具"""
        logger.info("任务时间修改工具初始化完成")
    
    def modify_task_time(self, child_id: int, task_description: str, new_time_slot: str) -> Dict[str, Any]:
        """
        修改任务时间
        
        Args:
            child_id: 学生ID
            task_description: 任务描述（用于匹配任务）
            new_time_slot: 新的时间段，格式：HH:MM-HH:MM
            
        Returns:
            Dict: 修改结果
        """
        try:
            logger.info(f"开始修改任务时间 - 学生ID: {child_id}, 任务: {task_description}, 新时间: {new_time_slot}")
            
            # 验证时间格式
            if not self._validate_time_slot(new_time_slot):
                return {
                    'success': False,
                    'message': f'时间格式不正确，请使用 HH:MM-HH:MM 格式，如：14:00-15:30'
                }
            
            # 查找匹配的任务
            matched_tasks = self._find_matching_tasks(child_id, task_description)
            
            if not matched_tasks:
                return {
                    'success': False,
                    'message': f'没有找到匹配的任务：{task_description}\n\n请检查任务名称是否正确，或者先查看今日任务列表。'
                }
            
            if len(matched_tasks) > 1:
                # 如果找到多个匹配的任务，返回列表让用户选择
                task_list = "\n".join([f"• {task['task_name']} ({task['time_slot'] or '未设置时间'})" for task in matched_tasks])
                return {
                    'success': False,
                    'message': f'找到多个匹配的任务，请更具体地描述：\n\n{task_list}\n\n请说明具体要修改哪个任务的时间。'
                }
            
            # 修改任务时间
            task = matched_tasks[0]
            result = self._update_task_time(task['id'], new_time_slot)
            
            if result['success']:
                return {
                    'success': True,
                    'message': f'✅ 任务时间修改成功！\n\n📝 任务：{task["task_name"]}\n⏰ 原时间：{task["time_slot"] or "未设置"}\n🕐 新时间：{new_time_slot}\n\n时间已更新，请按新时间安排学习。'
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"修改任务时间异常: {e}")
            return {
                'success': False,
                'message': f'修改任务时间失败：{str(e)}'
            }
    
    def _validate_time_slot(self, time_slot: str) -> bool:
        """
        验证时间段格式
        
        Args:
            time_slot: 时间段字符串
            
        Returns:
            bool: 是否有效
        """
        try:
            # 匹配格式：HH:MM-HH:MM
            pattern = r'^(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})$'
            match = re.match(pattern, time_slot.strip())
            
            if not match:
                return False
            
            start_hour, start_min, end_hour, end_min = map(int, match.groups())
            
            # 验证时间范围
            if not (0 <= start_hour <= 23 and 0 <= start_min <= 59):
                return False
            if not (0 <= end_hour <= 23 and 0 <= end_min <= 59):
                return False
            
            # 验证结束时间晚于开始时间
            start_total_min = start_hour * 60 + start_min
            end_total_min = end_hour * 60 + end_min
            
            if end_total_min <= start_total_min:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _find_matching_tasks(self, child_id: int, task_description: str) -> List[Dict[str, Any]]:
        """
        查找匹配的任务
        
        Args:
            child_id: 学生ID
            task_description: 任务描述
            
        Returns:
            List[Dict]: 匹配的任务列表
        """
        try:
            with get_db_session_context() as session:
                # 获取今日任务
                today = date.today()
                tasks = session.query(DailyTask).filter(
                    DailyTask.child_id == child_id,
                    DailyTask.task_date >= today,
                    DailyTask.is_active == True
                ).all()
                
                matched_tasks = []
                task_description_lower = task_description.lower()
                
                for task in tasks:
                    # 模糊匹配任务名称
                    if (task_description_lower in task.task_name.lower() or 
                        task.task_name.lower() in task_description_lower):
                        matched_tasks.append({
                            'id': task.id,
                            'task_name': task.task_name,
                            'time_slot': task.time_slot,
                            'subject': task.subject,
                            'task_date': task.task_date
                        })
                
                return matched_tasks
                
        except Exception as e:
            logger.error(f"查找匹配任务失败: {e}")
            return []
    
    def _update_task_time(self, task_id: int, new_time_slot: str) -> Dict[str, Any]:
        """
        更新任务时间
        
        Args:
            task_id: 任务ID
            new_time_slot: 新时间段
            
        Returns:
            Dict: 更新结果
        """
        try:
            with get_db_session_context() as session:
                task = session.query(DailyTask).filter(DailyTask.id == task_id).first()
                
                if not task:
                    return {
                        'success': False,
                        'message': '任务不存在'
                    }
                
                # 更新任务时间
                task.time_slot = new_time_slot
                task.updated_at = datetime.now()
                
                # 同时更新子任务的时间（如果有的话）
                sub_tasks = session.query(TaskItem).filter(TaskItem.daily_task_id == task_id).all()
                for sub_task in sub_tasks:
                    # 如果子任务没有单独设置时间，使用主任务时间
                    if not sub_task.time_slot:
                        sub_task.time_slot = new_time_slot
                        sub_task.updated_at = datetime.now()
                
                session.commit()
                
                return {
                    'success': True,
                    'message': '任务时间更新成功'
                }
                
        except Exception as e:
            logger.error(f"更新任务时间失败: {e}")
            return {
                'success': False,
                'message': f'更新任务时间失败：{str(e)}'
            }
    
    def parse_time_modification_request(self, user_input: str) -> Optional[Dict[str, str]]:
        """
        解析用户的时间修改请求
        
        Args:
            user_input: 用户输入
            
        Returns:
            Dict: 解析结果，包含task_description和new_time_slot
        """
        try:
            # 常见的时间修改模式
            patterns = [
                # "数学作业时间改为14:00-15:30"
                r'(.+?)时间改为(\d{1,2}:\d{2}-\d{1,2}:\d{2})',
                # "把英语作业调整到16:00-17:00"
                r'把(.+?)调整到(\d{1,2}:\d{2}-\d{1,2}:\d{2})',
                # "语文作业修改为18:00-19:00"
                r'(.+?)修改为(\d{1,2}:\d{2}-\d{1,2}:\d{2})',
                # "将物理作业改成20:00-21:00"
                r'将(.+?)改成(\d{1,2}:\d{2}-\d{1,2}:\d{2})',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, user_input)
                if match:
                    task_description = match.group(1).strip()
                    new_time_slot = match.group(2).strip()
                    
                    return {
                        'task_description': task_description,
                        'new_time_slot': new_time_slot
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"解析时间修改请求失败: {e}")
            return None


# 创建工具实例
task_time_modifier = TaskTimeModifier()


def register_task_time_modifier_tools(agent):
    """
    向Agent注册任务时间修改工具
    
    Args:
        agent: SmartLearningAgent实例
    """
    
    async def modify_task_time(user_input: str, child_id: int = None):
        """修改任务时间"""
        try:
            if not child_id:
                child_id = 4  # 默认孩子ID
            
            # 解析用户输入
            parsed_request = task_time_modifier.parse_time_modification_request(user_input)
            
            if not parsed_request:
                return {
                    'success': False,
                    'message': '无法理解您的时间修改要求。\n\n请按以下格式说明：\n• "数学作业时间改为14:00-15:30"\n• "把英语作业调整到16:00-17:00"\n• "语文作业修改为18:00-19:00"'
                }
            
            # 执行时间修改
            return task_time_modifier.modify_task_time(
                child_id=child_id,
                task_description=parsed_request['task_description'],
                new_time_slot=parsed_request['new_time_slot']
            )
            
        except Exception as e:
            logger.error(f"任务时间修改工具调用失败: {e}")
            return {
                'success': False,
                'message': f'任务时间修改失败：{str(e)}'
            }
    
    # 注册工具
    agent.register_tool(
        'modify_task_time',
        modify_task_time,
        '修改任务时间 - 根据用户指令自动修改指定任务的时间安排'
    )
    
    logger.info("任务时间修改工具已注册到Agent")
