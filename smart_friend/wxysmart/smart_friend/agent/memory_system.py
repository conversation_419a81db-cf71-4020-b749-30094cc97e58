#!/usr/bin/env python3
"""
Agent记忆系统
基于OpenManus思想设计的多层次记忆架构
"""

import json
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib

logger = logging.getLogger(__name__)


class MemoryType(Enum):
    """记忆类型枚举"""
    SHORT_TERM = "short_term"      # 短期记忆：当前会话
    WORKING = "working"            # 工作记忆：近期重要信息
    LONG_TERM = "long_term"        # 长期记忆：用户偏好、习惯
    SEMANTIC = "semantic"          # 语义记忆：知识图谱
    CONVERSATION = "conversation"  # 对话记忆：日常对话内容


class MemoryImportance(Enum):
    """记忆重要性枚举"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class MemoryItem:
    """记忆项数据结构"""
    id: str
    child_id: int
    memory_type: MemoryType
    content: str
    context: Dict[str, Any]
    importance: MemoryImportance
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    tags: List[str] = None
    related_memories: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.related_memories is None:
            self.related_memories = []


class MemorySystem:
    """
    Agent记忆系统
    
    基于OpenManus思想的多层次记忆架构：
    1. 短期记忆：当前对话会话中的上下文
    2. 工作记忆：近期交互的重要信息
    3. 长期记忆：用户偏好、学习习惯、重要事件
    4. 语义记忆：知识图谱和概念关联
    """
    
    def __init__(self, db_path: str = "agent_memory.db"):
        """初始化记忆系统"""
        self.db_path = db_path
        self.short_term_memory = {}  # child_id -> List[MemoryItem]
        self.working_memory = {}     # child_id -> List[MemoryItem]
        
        # 记忆容量限制
        self.short_term_limit = 10   # 短期记忆容量
        self.working_memory_limit = 50  # 工作记忆容量
        
        # 初始化数据库
        self._init_database()
        
        logger.info("记忆系统初始化完成")
    
    def _init_database(self):
        """初始化记忆数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建记忆表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS memories (
                        id TEXT PRIMARY KEY,
                        child_id INTEGER NOT NULL,
                        memory_type TEXT NOT NULL,
                        content TEXT NOT NULL,
                        context TEXT,
                        importance INTEGER NOT NULL,
                        created_at TEXT NOT NULL,
                        last_accessed TEXT NOT NULL,
                        access_count INTEGER DEFAULT 0,
                        tags TEXT,
                        related_memories TEXT
                    )
                """)
                
                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_child_id ON memories(child_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_memory_type ON memories(memory_type)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_importance ON memories(importance)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_created_at ON memories(created_at)")
                
                conn.commit()
                logger.info("记忆数据库初始化成功")
                
        except Exception as e:
            logger.error(f"记忆数据库初始化失败: {e}")
    
    def _generate_memory_id(self, content: str, child_id: int) -> str:
        """生成记忆ID"""
        data = f"{content}_{child_id}_{datetime.now().isoformat()}"
        return hashlib.md5(data.encode()).hexdigest()
    
    def add_memory(self, child_id: int, content: str, memory_type: MemoryType, 
                   importance: MemoryImportance, context: Dict[str, Any] = None,
                   tags: List[str] = None) -> str:
        """添加记忆"""
        try:
            if context is None:
                context = {}
            if tags is None:
                tags = []
            
            memory_id = self._generate_memory_id(content, child_id)
            now = datetime.now()
            
            memory_item = MemoryItem(
                id=memory_id,
                child_id=child_id,
                memory_type=memory_type,
                content=content,
                context=context,
                importance=importance,
                created_at=now,
                last_accessed=now,
                tags=tags
            )
            
            # 所有记忆类型都只保存到数据库中，不使用内存存储
            self._save_to_database(memory_item)
            
            logger.info(f"添加记忆成功: {memory_id} ({memory_type.value})")
            return memory_id
            
        except Exception as e:
            logger.error(f"添加记忆失败: {e}")
            return ""
    
    def _add_to_short_term(self, child_id: int, memory_item: MemoryItem):
        """添加到短期记忆"""
        if child_id not in self.short_term_memory:
            self.short_term_memory[child_id] = []
        
        memories = self.short_term_memory[child_id]
        memories.append(memory_item)
        
        # 超出容量限制时，移除最旧的记忆
        if len(memories) > self.short_term_limit:
            old_memory = memories.pop(0)
            # 将重要的短期记忆转移到工作记忆
            if old_memory.importance.value >= MemoryImportance.MEDIUM.value:
                old_memory.memory_type = MemoryType.WORKING
                self._add_to_working_memory(child_id, old_memory)
    
    def _add_to_working_memory(self, child_id: int, memory_item: MemoryItem):
        """添加到工作记忆"""
        if child_id not in self.working_memory:
            self.working_memory[child_id] = []
        
        memories = self.working_memory[child_id]
        memories.append(memory_item)
        
        # 超出容量限制时，将重要记忆保存到长期记忆
        if len(memories) > self.working_memory_limit:
            # 按重要性和访问频率排序
            memories.sort(key=lambda x: (x.importance.value, x.access_count), reverse=True)
            
            # 保留重要的记忆，其他转移到长期记忆
            keep_memories = memories[:self.working_memory_limit]
            archive_memories = memories[self.working_memory_limit:]
            
            for memory in archive_memories:
                if memory.importance.value >= MemoryImportance.MEDIUM.value:
                    memory.memory_type = MemoryType.LONG_TERM
                    self._save_to_database(memory)
            
            self.working_memory[child_id] = keep_memories
    
    def _save_to_database(self, memory_item: MemoryItem):
        """保存记忆到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO memories 
                    (id, child_id, memory_type, content, context, importance, 
                     created_at, last_accessed, access_count, tags, related_memories)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    memory_item.id,
                    memory_item.child_id,
                    memory_item.memory_type.value,
                    memory_item.content,
                    json.dumps(memory_item.context, ensure_ascii=False),
                    memory_item.importance.value,
                    memory_item.created_at.isoformat(),
                    memory_item.last_accessed.isoformat(),
                    memory_item.access_count,
                    json.dumps(memory_item.tags, ensure_ascii=False),
                    json.dumps(memory_item.related_memories, ensure_ascii=False)
                ))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"保存记忆到数据库失败: {e}")
    
    def retrieve_memories(self, child_id: int, query: str = "", 
                         memory_types: List[MemoryType] = None,
                         limit: int = 10) -> List[MemoryItem]:
        """检索记忆"""
        try:
            # 所有记忆都从数据库中检索
            all_memories = self._retrieve_from_database(child_id, query, memory_types, limit)
            
            # 过滤记忆类型
            if memory_types:
                all_memories = [m for m in all_memories if m.memory_type in memory_types]
            
            # 简单的相关性排序（基于关键词匹配）
            if query:
                scored_memories = []
                for memory in all_memories:
                    score = self._calculate_relevance_score(memory, query)
                    if score > 0:
                        scored_memories.append((memory, score))
                
                scored_memories.sort(key=lambda x: x[1], reverse=True)
                all_memories = [m[0] for m in scored_memories[:limit]]
            else:
                # 按时间和重要性排序
                all_memories.sort(key=lambda x: (x.importance.value, x.last_accessed), reverse=True)
                all_memories = all_memories[:limit]
            
            # 更新访问记录
            for memory in all_memories:
                memory.last_accessed = datetime.now()
                memory.access_count += 1
            
            return all_memories
            
        except Exception as e:
            logger.error(f"检索记忆失败: {e}")
            return []
    
    def _retrieve_from_database(self, child_id: int, query: str = "",
                               memory_types: List[MemoryType] = None,
                               limit: int = 50) -> List[MemoryItem]:
        """从数据库检索记忆"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                sql = "SELECT * FROM memories WHERE child_id = ?"
                params = [child_id]
                
                if memory_types:
                    type_placeholders = ",".join("?" * len(memory_types))
                    sql += f" AND memory_type IN ({type_placeholders})"
                    params.extend([mt.value for mt in memory_types])
                
                # 注释掉数据库级别的查询过滤，让应用程序级别的评分来处理
                # if query:
                #     sql += " AND (content LIKE ? OR tags LIKE ?)"
                #     params.extend([f"%{query}%", f"%{query}%"])
                
                sql += " ORDER BY importance DESC, last_accessed DESC LIMIT ?"
                params.append(limit)
                
                cursor.execute(sql, params)
                rows = cursor.fetchall()
                
                memories = []
                for row in rows:
                    memory = MemoryItem(
                        id=row[0],
                        child_id=row[1],
                        memory_type=MemoryType(row[2]),
                        content=row[3],
                        context=json.loads(row[4]) if row[4] else {},
                        importance=MemoryImportance(row[5]),
                        created_at=datetime.fromisoformat(row[6]),
                        last_accessed=datetime.fromisoformat(row[7]),
                        access_count=row[8],
                        tags=json.loads(row[9]) if row[9] else [],
                        related_memories=json.loads(row[10]) if row[10] else []
                    )
                    memories.append(memory)
                
                return memories
                
        except Exception as e:
            logger.error(f"从数据库检索记忆失败: {e}")
            return []
    
    def _calculate_relevance_score(self, memory: MemoryItem, query: str) -> float:
        """计算记忆相关性得分"""
        score = 0.0
        query_lower = query.lower()
        memory_content_lower = memory.content.lower()

        # 完整查询匹配
        if query_lower in memory_content_lower:
            score += 2.0

        # 关键词匹配 - 提取查询中的关键词
        keywords = self._extract_keywords(query_lower)
        for keyword in keywords:
            if keyword in memory_content_lower:
                score += 1.5

        # 标签匹配
        for tag in memory.tags:
            tag_lower = tag.lower()
            if query_lower in tag_lower:
                score += 1.5
            # 关键词与标签匹配
            for keyword in keywords:
                if keyword in tag_lower:
                    score += 1.0

        # 重要性加权
        score *= memory.importance.value / 4.0

        # 访问频率加权
        score *= (1 + memory.access_count * 0.1)

        return score

    def _extract_keywords(self, query: str) -> list:
        """从查询中提取关键词"""
        # 定义关键词映射
        keyword_map = {
            "颜色": ["颜色", "蓝色", "红色", "绿色", "黄色", "紫色", "橙色", "黑色", "白色"],
            "喜欢": ["喜欢", "最喜欢", "爱", "偏爱"],
            "明天": ["明天", "安排", "计划", "日程"],
            "科目": ["科目", "学科", "数学", "语文", "英语", "物理", "化学", "生物", "历史", "地理"],
            "考试": ["考试", "竞赛", "测试", "测验", "比赛"],
            "学习": ["学习", "作业", "功课", "练习"],
            "时间": ["时间", "点钟", "小时", "分钟"]
        }

        keywords = []

        # 直接关键词匹配
        for main_keyword, related_keywords in keyword_map.items():
            for keyword in related_keywords:
                if keyword in query:
                    keywords.append(keyword)
                    # 如果找到相关词，也添加主关键词
                    if main_keyword not in keywords:
                        keywords.append(main_keyword)

        # 如果没有找到预定义关键词，尝试提取重要的中文词汇
        if not keywords:
            # 简单的中文词汇提取（去除常见的助词、疑问词等）
            stop_words = ["你", "我", "他", "她", "它", "的", "了", "吗", "呢", "吧", "啊", "什么", "怎么", "为什么", "还", "记得", "知道"]
            words = []
            current_word = ""

            for char in query:
                if char.isalpha() or char in "一二三四五六七八九十":
                    current_word += char
                else:
                    if current_word and len(current_word) >= 2 and current_word not in stop_words:
                        words.append(current_word)
                    current_word = ""

            # 添加最后一个词
            if current_word and len(current_word) >= 2 and current_word not in stop_words:
                words.append(current_word)

            keywords.extend(words)

        return list(set(keywords))  # 去重
    
    def get_context_summary(self, child_id: int, limit: int = 5) -> str:
        """获取上下文摘要"""
        try:
            # 获取最近的重要记忆
            recent_memories = self.retrieve_memories(
                child_id=child_id,
                memory_types=[MemoryType.SHORT_TERM, MemoryType.WORKING],
                limit=limit
            )
            
            if not recent_memories:
                return "暂无相关记忆信息。"
            
            summary_parts = []
            for memory in recent_memories:
                summary_parts.append(f"- {memory.content}")
            
            return "最近的对话记忆：\n" + "\n".join(summary_parts)
            
        except Exception as e:
            logger.error(f"获取上下文摘要失败: {e}")
            return "获取记忆信息失败。"
    
    def clear_session_memory(self, child_id: int):
        """清除会话记忆"""
        if child_id in self.short_term_memory:
            # 将重要的短期记忆转移到工作记忆
            for memory in self.short_term_memory[child_id]:
                if memory.importance.value >= MemoryImportance.MEDIUM.value:
                    memory.memory_type = MemoryType.WORKING
                    self._add_to_working_memory(child_id, memory)
            
            del self.short_term_memory[child_id]
            logger.info(f"已清除用户 {child_id} 的会话记忆")
    
    def get_memory_stats(self, child_id: int) -> Dict[str, Any]:
        """获取记忆统计信息"""
        try:
            stats = {
                "short_term_count": 0,
                "working_memory_count": 0,
                "long_term_count": 0,
                "semantic_count": 0,
                "conversation_count": 0,
                "total_memories": 0
            }
            
            # 统计数据库中的记忆
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT memory_type, COUNT(*) 
                    FROM memories 
                    WHERE child_id = ? 
                    GROUP BY memory_type
                """, (child_id,))
                
                for memory_type, count in cursor.fetchall():
                    if memory_type == MemoryType.SHORT_TERM.value:
                        stats["short_term_count"] = count
                    elif memory_type == MemoryType.WORKING.value:
                        stats["working_memory_count"] = count
                    elif memory_type == MemoryType.LONG_TERM.value:
                        stats["long_term_count"] = count
                    elif memory_type == MemoryType.SEMANTIC.value:
                        stats["semantic_count"] = count
                    elif memory_type == MemoryType.CONVERSATION.value:
                        stats["conversation_count"] = count
            
            stats["total_memories"] = sum(stats.values())
            return stats
            
        except Exception as e:
            logger.error(f"获取记忆统计失败: {e}")
            return {}
