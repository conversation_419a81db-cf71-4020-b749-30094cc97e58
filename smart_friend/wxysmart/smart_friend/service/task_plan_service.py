# 任务计划服务
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from sqlalchemy.orm import Session

from core.user_management.database.connection import get_db_session_context
from core.daily_tasks.models.daily_task_models import DailyTask, TaskItem
from core.daily_tasks.schemas import DailyTaskResponse

logger = logging.getLogger(__name__)


class TaskPlanService:
    """任务计划服务类"""
    
    def __init__(self):
        logger.info("TaskPlanService 初始化完成")
    
    async def save_task_plan(self, child_id: int, task_plan: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        保存前端生成的任务计划到数据库
        
        Args:
            child_id: 学生ID
            task_plan: 任务计划数据
            
        Returns:
            Dict: 保存结果
        """
        try:
            logger.info(f"开始保存学生{child_id}的任务计划，共{len(task_plan)}个任务")
            
            with get_db_session_context() as session:
                # 1. 软删除之前的任务（将is_active设为0）
                soft_deleted_count = self._soft_delete_previous_tasks(session, child_id)
                logger.info(f"软删除了{soft_deleted_count}个之前的任务")
                
                # 2. 保存新的任务计划
                saved_task_ids = []
                today = date.today()
                
                for task_data in task_plan:
                    # 解析时间段
                    time_slot = task_data.get("time_slot", "")
                    start_time, end_time = self._parse_time_slot(time_slot)

                    # 将时间转换为datetime对象
                    start_datetime = datetime.combine(today, start_time) if start_time else None
                    end_datetime = datetime.combine(today, end_time) if end_time else None

                    # 创建主任务
                    task = DailyTask(
                        child_id=child_id,
                        task_name=task_data.get("task_name", ""),
                        subject=self._infer_subject_from_task_name(task_data.get("task_name", "")),
                        task_date=datetime.combine(today, datetime.min.time()),
                        time_slot=time_slot,
                        start_time=start_datetime,
                        end_time=end_datetime,
                        description=task_data.get("customization", ""),
                        customization=task_data.get("customization", ""),
                        difficulty=task_data.get("difficulty", ""),
                        solution=task_data.get("solution", ""),
                        confidence_index=task_data.get("confidence_index", 3),
                        status="pending",
                        is_active=True,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    
                    session.add(task)
                    session.flush()  # 获取任务ID
                    
                    saved_task_ids.append(task.id)
                    logger.info(f"保存主任务: {task.task_name} (ID: {task.id})")
                    
                    # 创建子任务
                    sub_tasks = task_data.get("sub_tasks", [])
                    for sub_task_data in sub_tasks:
                        sub_time_slot = sub_task_data.get("time_slot", "")
                        sub_start_time, sub_end_time = self._parse_time_slot(sub_time_slot)

                        # 将时间转换为datetime对象
                        sub_start_datetime = datetime.combine(today, sub_start_time) if sub_start_time else None
                        sub_end_datetime = datetime.combine(today, sub_end_time) if sub_end_time else None

                        sub_task = TaskItem(
                            daily_task_id=task.id,
                            task_content=sub_task_data.get("sub_task_name", ""),
                            time_slot=sub_time_slot,
                            start_time=sub_start_datetime,
                            end_time=sub_end_datetime,
                            task_source="前端任务计划",
                            is_active=True,
                            created_at=datetime.now(),
                            updated_at=datetime.now()
                        )

                        session.add(sub_task)
                        logger.info(f"保存子任务: {sub_task.task_content} (主任务ID: {task.id})")
                
                session.commit()
                
                logger.info(f"成功保存学生{child_id}的任务计划，共{len(saved_task_ids)}个任务")
                
                return {
                    "success": True,
                    "total_tasks": len(saved_task_ids),
                    "saved_task_ids": saved_task_ids,
                    "soft_deleted_count": soft_deleted_count
                }
                
        except Exception as e:
            logger.error(f"保存任务计划失败: {e}", exc_info=True)
            return {
                "success": False,
                "message": str(e)
            }
    
    def _soft_delete_previous_tasks(self, session: Session, child_id: int) -> int:
        """
        软删除指定学生的之前任务
        
        Args:
            session: 数据库会话
            child_id: 学生ID
            
        Returns:
            int: 软删除的任务数量
        """
        try:
            today = date.today()
            
            # 软删除今日的活跃任务
            updated_count = session.query(DailyTask).filter(
                DailyTask.child_id == child_id,
                DailyTask.task_date >= datetime.combine(today, datetime.min.time()),
                DailyTask.task_date < datetime.combine(today, datetime.max.time()),
                DailyTask.is_active == True
            ).update({
                "is_active": False,
                "updated_at": datetime.now()
            })
            
            # 软删除对应的子任务
            sub_task_ids = session.query(TaskItem.id).join(DailyTask).filter(
                DailyTask.child_id == child_id,
                DailyTask.task_date >= datetime.combine(today, datetime.min.time()),
                DailyTask.task_date < datetime.combine(today, datetime.max.time()),
                TaskItem.is_active == True
            ).all()

            if sub_task_ids:
                session.query(TaskItem).filter(
                    TaskItem.id.in_([st.id for st in sub_task_ids])
                ).update({
                    "is_active": False,
                    "updated_at": datetime.now()
                })
            
            return updated_count
            
        except Exception as e:
            logger.error(f"软删除之前任务失败: {e}")
            return 0
    
    def _parse_time_slot(self, time_slot: str) -> tuple:
        """
        解析时间段字符串
        
        Args:
            time_slot: 时间段字符串，如 "18:00 - 18:45"
            
        Returns:
            tuple: (开始时间, 结束时间)
        """
        try:
            if " - " in time_slot:
                start_str, end_str = time_slot.split(" - ")
                start_time = datetime.strptime(start_str.strip(), "%H:%M").time()
                end_time = datetime.strptime(end_str.strip(), "%H:%M").time()
                return start_time, end_time
            else:
                # 如果格式不正确，返回默认时间
                return datetime.strptime("18:00", "%H:%M").time(), datetime.strptime("19:00", "%H:%M").time()
        except Exception as e:
            logger.warning(f"解析时间段失败: {time_slot}, 错误: {e}")
            return datetime.strptime("18:00", "%H:%M").time(), datetime.strptime("19:00", "%H:%M").time()
    
    def _infer_subject_from_task_name(self, task_name: str) -> str:
        """
        从任务名称推断学科
        
        Args:
            task_name: 任务名称
            
        Returns:
            str: 学科名称
        """
        task_name_lower = task_name.lower()
        
        if any(keyword in task_name_lower for keyword in ["数学", "math", "算", "计算"]):
            return "数学"
        elif any(keyword in task_name_lower for keyword in ["语文", "chinese", "作文", "阅读"]):
            return "语文"
        elif any(keyword in task_name_lower for keyword in ["英语", "english", "单词"]):
            return "英语"
        elif any(keyword in task_name_lower for keyword in ["科学", "science", "实验"]):
            return "科学"
        elif any(keyword in task_name_lower for keyword in ["音乐", "music"]):
            return "音乐"
        elif any(keyword in task_name_lower for keyword in ["美术", "art", "画画"]):
            return "美术"
        elif any(keyword in task_name_lower for keyword in ["体育", "sports", "运动"]):
            return "体育"
        else:
            return "其他"
    
    async def get_today_task_plan(self, child_id: int) -> List[Dict[str, Any]]:
        """
        获取指定学生今日的任务计划
        
        Args:
            child_id: 学生ID
            
        Returns:
            List[Dict]: 任务计划列表
        """
        try:
            with get_db_session_context() as session:
                today = date.today()
                
                # 查询今日的活跃任务
                tasks = session.query(DailyTask).filter(
                    DailyTask.child_id == child_id,
                    DailyTask.task_date >= datetime.combine(today, datetime.min.time()),
                    DailyTask.task_date < datetime.combine(today, datetime.max.time()),
                    DailyTask.is_active == True
                ).order_by(DailyTask.start_time).all()
                
                result = []
                for task in tasks:
                    # 查询子任务
                    sub_tasks = session.query(TaskItem).filter(
                        TaskItem.daily_task_id == task.id,
                        TaskItem.is_active == True
                    ).order_by(TaskItem.start_time).all()

                    task_dict = {
                        "id": task.id,
                        "task_name": task.task_name,
                        "subject": task.subject,
                        "time_slot": task.time_slot,
                        "start_time": task.start_time.strftime("%H:%M") if task.start_time else "",
                        "end_time": task.end_time.strftime("%H:%M") if task.end_time else "",
                        "description": task.description,
                        "customization": task.customization,
                        "difficulty": task.difficulty,
                        "solution": task.solution,
                        "confidence_index": task.confidence_index,
                        "status": task.status,
                        "sub_tasks": [
                            {
                                "id": st.id,
                                "sub_task_name": st.task_content,
                                "time_slot": st.time_slot,
                                "start_time": st.start_time.strftime("%H:%M") if st.start_time else "",
                                "end_time": st.end_time.strftime("%H:%M") if st.end_time else "",
                                "description": st.notes or "",
                                "status": "completed" if st.is_completed else "pending"
                            }
                            for st in sub_tasks
                        ]
                    }
                    result.append(task_dict)
                
                return result
                
        except Exception as e:
            logger.error(f"获取今日任务计划失败: {e}")
            return []
    
    async def delete_task(self, task_id: int) -> Dict[str, Any]:
        """
        删除指定任务（软删除）
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 删除结果
        """
        try:
            with get_db_session_context() as session:
                # 查找任务
                task = session.query(DailyTask).filter(
                    DailyTask.id == task_id,
                    DailyTask.is_active == True
                ).first()
                
                if not task:
                    return {
                        "success": False,
                        "message": "任务不存在或已被删除"
                    }
                
                # 软删除任务
                task.is_active = False
                task.updated_at = datetime.now()

                # 软删除相关子任务
                sub_task_count = session.query(TaskItem).filter(
                    TaskItem.daily_task_id == task_id,
                    TaskItem.is_active == True
                ).update({
                    "is_active": False,
                    "updated_at": datetime.now()
                })

                session.commit()

                return {
                    "success": True,
                    "message": "任务删除成功",
                    "data": {
                        "task_id": task_id,
                        "task_name": task.task_name,
                        "deleted_subtasks": sub_task_count
                    }
                }
                
        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            return {
                "success": False,
                "message": str(e)
            }


# 依赖注入函数
def get_task_plan_service() -> TaskPlanService:
    """获取任务计划服务实例"""
    return TaskPlanService()
