#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TTS API 相关的 Pydantic 数据模型

定义文本转语音（TTS）服务的请求和响应模型
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum


class TTSPlayRequest(BaseModel):
    """TTS播放请求模型"""
    text: str = Field(..., description="要转换为语音的文本")
    speaker: Optional[str] = Field("zh_female_shuangkuaisisi_moon_bigtts", description="发音人")
    async_mode: Optional[bool] = Field(True, description="是否异步播放")
    return_audio: Optional[bool] = Field(False, description="是否返回音频数据")
    
    @validator('text')
    def validate_text(cls, v):
        if not v.strip():
            raise ValueError("文本内容不能为空")
        return v.strip()


class TTSPlayResponse(BaseModel):
    """TTS播放响应模型"""
    success: bool = Field(..., description="播放是否成功")
    message: str = Field(..., description="响应消息")
    audio_data: Optional[str] = Field(None, description="Base64编码的音频数据")
    audio_format: Optional[str] = Field(None, description="音频格式")
    audio_size: Optional[int] = Field(None, description="音频文件大小")
    file_path: Optional[str] = Field(None, description="音频文件路径")
    duration: Optional[float] = Field(None, description="处理耗时")


class TTSGenerateRequest(BaseModel):
    """TTS生成文件请求模型"""
    text: str = Field(..., description="要转换为语音的文本")
    output_path: Optional[str] = Field(None, description="输出文件路径")
    speaker: Optional[str] = Field("zh_female_shuangkuaisisi_moon_bigtts", description="发音人")
    audio_format: Optional[str] = Field("mp3", description="音频格式")
    
    @validator('text')
    def validate_text(cls, v):
        if not v.strip():
            raise ValueError("文本内容不能为空")
        return v.strip()


class TTSGenerateResponse(BaseModel):
    """TTS生成文件响应模型"""
    success: bool = Field(..., description="生成是否成功")
    message: str = Field(..., description="响应消息")
    file_path: Optional[str] = Field(None, description="生成的音频文件路径")
    file_size: Optional[int] = Field(None, description="文件大小")
    duration: Optional[float] = Field(None, description="处理耗时")


class TTSStatus(BaseModel):
    """TTS服务状态模型"""
    initialized: bool = Field(..., description="是否已初始化")
    tts_available: bool = Field(..., description="TTS服务是否可用")
    temp_audio_dir: Optional[str] = Field(None, description="临时音频目录")
    temp_files_count: Optional[int] = Field(None, description="临时文件数量")
    current_speaker: Optional[str] = Field(None, description="当前发音人")
    last_activity: Optional[str] = Field(None, description="最后活动时间")


class TTSHealthResponse(BaseModel):
    """TTS健康检查响应模型"""
    is_healthy: bool = Field(..., description="服务是否健康")
    status: TTSStatus = Field(..., description="详细状态信息")
    error: Optional[str] = Field(None, description="错误信息")


class TTSCleanupRequest(BaseModel):
    """TTS清理请求模型"""
    max_files: int = Field(10, description="保留的最大文件数")
    
    @validator('max_files')
    def validate_max_files(cls, v):
        if v < 0:
            raise ValueError("最大文件数不能为负数")
        return v


class TTSCleanupResponse(BaseModel):
    """TTS清理响应模型"""
    success: bool = Field(..., description="清理是否成功")
    message: str = Field(..., description="响应消息")
    files_removed: int = Field(0, description="删除的文件数")
    files_remaining: int = Field(0, description="剩余的文件数")


class TTSConfigRequest(BaseModel):
    """TTS配置请求模型"""
    app_id: Optional[str] = Field(None, description="应用ID")
    token: Optional[str] = Field(None, description="访问令牌")
    speaker: Optional[str] = Field(None, description="发音人")
    audio_format: Optional[str] = Field(None, description="音频格式")


class TTSAPIResponse(BaseModel):
    """TTS通用API响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="附加数据")


class TTSErrorResponse(BaseModel):
    """TTS错误响应模型"""
    error: str = Field(..., description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="错误时间")
    request_id: Optional[str] = Field(None, description="请求ID")


class TTSBatchRequest(BaseModel):
    """TTS批量处理请求模型"""
    texts: List[str] = Field(..., description="文本列表")
    speaker: Optional[str] = Field("zh_female_shuangkuaisisi_moon_bigtts", description="发音人")
    output_dir: Optional[str] = Field(None, description="输出目录")
    
    @validator('texts')
    def validate_texts(cls, v):
        if not v:
            raise ValueError("文本列表不能为空")
        for text in v:
            if not text.strip():
                raise ValueError("文本内容不能为空")
        return v


class TTSBatchResponse(BaseModel):
    """TTS批量处理响应模型"""
    success: bool = Field(..., description="批量处理是否成功")
    message: str = Field(..., description="响应消息")
    results: List[Dict[str, Any]] = Field(default_factory=list, description="处理结果列表")
    total_count: int = Field(0, description="总文本数")
    success_count: int = Field(0, description="成功处理数")
    failed_count: int = Field(0, description="失败处理数")
    duration: float = Field(0.0, description="处理耗时")


class TTSSpeakerInfo(BaseModel):
    """TTS发音人信息模型"""
    speaker_id: str = Field(..., description="发音人ID")
    speaker_name: str = Field(..., description="发音人名称")
    language: str = Field(..., description="支持的语言")
    gender: str = Field(..., description="性别")
    description: Optional[str] = Field(None, description="发音人描述")


class TTSSpeakersResponse(BaseModel):
    """TTS发音人列表响应模型"""
    success: bool = Field(..., description="请求是否成功")
    speakers: List[TTSSpeakerInfo] = Field(default_factory=list, description="发音人列表")
    total_count: int = Field(0, description="发音人总数")
