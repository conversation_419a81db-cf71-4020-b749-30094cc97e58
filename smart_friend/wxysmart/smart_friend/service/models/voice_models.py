#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
语音交互 API 相关的 Pydantic 数据模型

定义语音交互服务的请求和响应模型，整合ASR和TTS功能
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class VoiceInteractionRequest(BaseModel):
    """语音交互请求模型"""
    audio_data: str = Field(..., description="Base64编码的音频数据")
    return_audio: Optional[bool] = Field(True, description="是否返回音频回复")
    speaker: Optional[str] = Field("zh_female_shuangkuaisisi_moon_bigtts", description="TTS发音人")
    asr_model: Optional[str] = Field("bigmodel", description="ASR模型名称")
    ai_model: Optional[str] = Field("doubao", description="AI对话模型")
    
    @validator('audio_data')
    def validate_audio_data(cls, v):
        if not v.strip():
            raise ValueError("音频数据不能为空")
        return v.strip()


class VoiceInteractionResponse(BaseModel):
    """语音交互响应模型"""
    success: bool = Field(..., description="处理是否成功")
    recognized_text: Optional[str] = Field(None, description="识别的文本")
    response_text: Optional[str] = Field(None, description="AI回复文本")
    audio_data: Optional[str] = Field(None, description="Base64编码的回复音频")
    audio_format: Optional[str] = Field(None, description="音频格式")
    processing_time: Optional[float] = Field(None, description="总处理时间")
    asr_time: Optional[float] = Field(None, description="ASR处理时间")
    ai_time: Optional[float] = Field(None, description="AI处理时间")
    tts_time: Optional[float] = Field(None, description="TTS处理时间")
    message: str = Field(..., description="处理消息")
    error: Optional[str] = Field(None, description="错误信息")


class VoiceServiceStatus(BaseModel):
    """语音服务状态模型"""
    service_name: str = Field(..., description="服务名称")
    available: bool = Field(..., description="服务是否可用")
    healthy: bool = Field(..., description="服务是否健康")
    last_check: Optional[str] = Field(None, description="最后检查时间")
    error: Optional[str] = Field(None, description="错误信息")


class VoiceServicesStatusResponse(BaseModel):
    """语音服务状态响应模型"""
    success: bool = Field(..., description="检查是否成功")
    message: str = Field(..., description="响应消息")
    asr_status: VoiceServiceStatus = Field(..., description="ASR服务状态")
    tts_status: VoiceServiceStatus = Field(..., description="TTS服务状态")
    ai_status: VoiceServiceStatus = Field(..., description="AI服务状态")
    voice_interaction_available: bool = Field(..., description="语音交互是否可用")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="检查时间")


class VoiceConfigRequest(BaseModel):
    """语音配置请求模型"""
    asr_config: Optional[Dict[str, Any]] = Field(None, description="ASR配置")
    tts_config: Optional[Dict[str, Any]] = Field(None, description="TTS配置")
    ai_config: Optional[Dict[str, Any]] = Field(None, description="AI配置")
    default_speaker: Optional[str] = Field(None, description="默认发音人")
    default_asr_model: Optional[str] = Field(None, description="默认ASR模型")


class VoiceConfigResponse(BaseModel):
    """语音配置响应模型"""
    success: bool = Field(..., description="配置是否成功")
    message: str = Field(..., description="响应消息")
    current_config: Dict[str, Any] = Field(..., description="当前配置")


class VoiceBatchRequest(BaseModel):
    """语音批量处理请求模型"""
    audio_files: List[str] = Field(..., description="音频文件路径列表或Base64数据列表")
    return_audio: Optional[bool] = Field(False, description="是否返回音频回复")
    speaker: Optional[str] = Field("zh_female_shuangkuaisisi_moon_bigtts", description="TTS发音人")
    
    @validator('audio_files')
    def validate_audio_files(cls, v):
        if not v:
            raise ValueError("音频文件列表不能为空")
        return v


class VoiceBatchResponse(BaseModel):
    """语音批量处理响应模型"""
    success: bool = Field(..., description="批量处理是否成功")
    message: str = Field(..., description="响应消息")
    results: List[VoiceInteractionResponse] = Field(default_factory=list, description="处理结果列表")
    total_count: int = Field(0, description="总音频数")
    success_count: int = Field(0, description="成功处理数")
    failed_count: int = Field(0, description="失败处理数")
    total_processing_time: float = Field(0.0, description="总处理时间")


class VoiceHealthResponse(BaseModel):
    """语音服务健康检查响应模型"""
    is_healthy: bool = Field(..., description="整体服务是否健康")
    services: VoiceServicesStatusResponse = Field(..., description="各服务状态详情")
    recommendations: Optional[List[str]] = Field(None, description="改进建议")


class VoiceAPIResponse(BaseModel):
    """语音API通用响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="附加数据")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="响应时间")


class VoiceErrorResponse(BaseModel):
    """语音API错误响应模型"""
    error: str = Field(..., description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_type: Optional[str] = Field(None, description="错误类型")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="错误时间")
    request_id: Optional[str] = Field(None, description="请求ID")
    suggestions: Optional[List[str]] = Field(None, description="解决建议")
