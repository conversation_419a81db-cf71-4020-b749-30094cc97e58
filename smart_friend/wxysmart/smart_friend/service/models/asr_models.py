#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ASR API 相关的 Pydantic 数据模型

定义自动语音识别（ASR）服务的请求和响应模型
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum


class ASRConnectRequest(BaseModel):
    """ASR连接请求模型"""
    app_key: Optional[str] = Field(None, description="应用密钥")
    access_key: Optional[str] = Field(None, description="访问密钥")
    model_name: Optional[str] = Field("bigmodel", description="模型名称")
    sample_rate: Optional[int] = Field(16000, description="采样率")
    channels: Optional[int] = Field(1, description="通道数")


class ASRConnectResponse(BaseModel):
    """ASR连接响应模型"""
    success: bool = Field(..., description="连接是否成功")
    message: str = Field(..., description="响应消息")
    connection_id: Optional[str] = Field(None, description="连接ID")
    model_info: Optional[Dict[str, Any]] = Field(None, description="模型信息")


class ASRAudioRequest(BaseModel):
    """ASR音频数据请求模型"""
    audio_data: str = Field(..., description="Base64编码的音频数据")
    format: Optional[str] = Field("pcm", description="音频格式")
    
    @validator('audio_data')
    def validate_audio_data(cls, v):
        if not v.strip():
            raise ValueError("音频数据不能为空")
        return v.strip()


class ASRResult(BaseModel):
    """ASR识别结果模型"""
    text: str = Field(..., description="识别的文本")
    is_final: bool = Field(..., description="是否为最终结果")
    timestamp: float = Field(..., description="时间戳")
    confidence: Optional[float] = Field(None, description="置信度")


class ASRRecognitionResponse(BaseModel):
    """ASR识别响应模型"""
    success: bool = Field(..., description="识别是否成功")
    result: Optional[ASRResult] = Field(None, description="识别结果")
    message: str = Field(..., description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")


class ASRResultsResponse(BaseModel):
    """ASR识别结果列表响应模型"""
    success: bool = Field(..., description="请求是否成功")
    results: List[ASRResult] = Field(default_factory=list, description="识别结果列表")
    count: int = Field(0, description="结果数量")
    has_final: bool = Field(False, description="是否包含最终结果")


class ASRStatus(BaseModel):
    """ASR服务状态模型"""
    connected: bool = Field(..., description="是否已连接")
    client_exists: bool = Field(..., description="客户端是否存在")
    recognition_active: bool = Field(..., description="识别是否激活")
    web_service_active: bool = Field(False, description="Web服务是否激活")
    model_name: Optional[str] = Field(None, description="模型名称")
    sample_rate: Optional[int] = Field(None, description="采样率")
    channels: Optional[int] = Field(None, description="通道数")
    connection_id: Optional[str] = Field(None, description="连接ID")
    last_activity: Optional[str] = Field(None, description="最后活动时间")


class ASRHealthResponse(BaseModel):
    """ASR健康检查响应模型"""
    is_healthy: bool = Field(..., description="服务是否健康")
    status: ASRStatus = Field(..., description="详细状态信息")
    error: Optional[str] = Field(None, description="错误信息")


class ASRConfigRequest(BaseModel):
    """ASR配置请求模型"""
    app_key: Optional[str] = Field(None, description="应用密钥")
    access_key: Optional[str] = Field(None, description="访问密钥")
    model_name: Optional[str] = Field(None, description="模型名称")
    sample_rate: Optional[int] = Field(None, description="采样率")
    channels: Optional[int] = Field(None, description="通道数")


class ASRAPIResponse(BaseModel):
    """ASR通用API响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="附加数据")


class ASRErrorResponse(BaseModel):
    """ASR错误响应模型"""
    error: str = Field(..., description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="错误时间")
    request_id: Optional[str] = Field(None, description="请求ID")


class ASRBatchRequest(BaseModel):
    """ASR批量处理请求模型"""
    audio_files: List[str] = Field(..., description="音频文件路径列表")
    output_format: Optional[str] = Field("json", description="输出格式")
    
    @validator('audio_files')
    def validate_audio_files(cls, v):
        if not v:
            raise ValueError("音频文件列表不能为空")
        return v


class ASRBatchResponse(BaseModel):
    """ASR批量处理响应模型"""
    success: bool = Field(..., description="批量处理是否成功")
    results: List[Dict[str, Any]] = Field(default_factory=list, description="处理结果列表")
    total_count: int = Field(0, description="总文件数")
    success_count: int = Field(0, description="成功处理数")
    failed_count: int = Field(0, description="失败处理数")
    duration: float = Field(0.0, description="处理耗时")


class ASRModelInfo(BaseModel):
    """ASR模型信息模型"""
    model_id: str = Field(..., description="模型ID")
    model_name: str = Field(..., description="模型名称")
    language: str = Field(..., description="支持的语言")
    sample_rates: List[int] = Field(..., description="支持的采样率列表")
    description: Optional[str] = Field(None, description="模型描述")


class ASRModelsResponse(BaseModel):
    """ASR模型列表响应模型"""
    success: bool = Field(..., description="请求是否成功")
    models: List[ASRModelInfo] = Field(default_factory=list, description="模型列表")
    total_count: int = Field(0, description="模型总数")


class ASRStatisticsResponse(BaseModel):
    """ASR统计信息响应模型"""
    success: bool = Field(..., description="请求是否成功")
    total_requests: int = Field(0, description="总请求数")
    successful_requests: int = Field(0, description="成功请求数")
    failed_requests: int = Field(0, description="失败请求数")
    uptime: float = Field(0.0, description="运行时间（秒）")
