#!/usr/bin/env python3
"""
OpenManus Service for wxysmart integration
Provides OpenManus functionality as a service layer for the entire wxysmart project
"""

import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class OpenManusService:
    """
    OpenManus Service for wxysmart integration
    Provides enhanced AI capabilities throughout the wxysmart project
    """
    
    def __init__(self):
        """Initialize OpenManus Service"""
        self.openmanus_planner = None
        self.is_available = False
        self._initialize_openmanus()
    
    def _initialize_openmanus(self):
        """Initialize OpenManus planner"""
        try:
            from openmanus import OpenManusPlanner
            self.openmanus_planner = OpenManusPlanner()
            self.is_available = True
            logger.info("✅ OpenManus Service initialized successfully")
        except ImportError as e:
            logger.warning(f"⚠️ OpenManus not available: {e}")
            self.is_available = False
        except Exception as e:
            logger.error(f"❌ Failed to initialize OpenManus Service: {e}")
            self.is_available = False
    
    def is_openmanus_available(self) -> bool:
        """Check if OpenManus is available"""
        return self.is_available and self.openmanus_planner is not None
    
    def process_user_input(self, user_input: str, child_id: int = None) -> Dict[str, Any]:
        """
        Process user input with OpenManus enhancement
        
        Args:
            user_input: User's input text
            child_id: Optional child ID
            
        Returns:
            Dict containing response and metadata
        """
        if not self.is_openmanus_available():
            return {
                'success': False,
                'message': 'OpenManus service not available',
                'fallback_required': True
            }
        
        try:
            # Use OpenManus to process the input
            result = self.openmanus_planner.process_user_input(user_input)
            
            # Generate summary
            summary_data = {
                "user_input": user_input,
                "intent": result.get("intent", {}),
                "response": result.get("final_response", ""),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "child_id": child_id
            }
            
            summary_result = self.openmanus_planner.generate_simple_report(summary_data)
            summary_text = summary_result.get("output", {}).get("report_text", "")
            
            return {
                'success': True,
                'message': result.get("final_response", "Response generated successfully"),
                'summary': summary_text,
                'intent_info': result.get("intent", {}),
                'metadata': result.get("metadata", {}),
                'openmanus_enhanced': True
            }
            
        except Exception as e:
            logger.error(f"OpenManus processing failed: {e}")
            return {
                'success': False,
                'message': f'OpenManus processing failed: {str(e)}',
                'fallback_required': True
            }
    
    def classify_intent(self, text: str) -> Dict[str, Any]:
        """
        Classify user intent using OpenManus
        
        Args:
            text: Text to classify
            
        Returns:
            Dict containing intent classification results
        """
        if not self.is_openmanus_available():
            return {
                'success': False,
                'message': 'OpenManus service not available',
                'fallback_required': True
            }
        
        try:
            result = self.openmanus_planner.classify_user_intent(text)
            return {
                'success': True,
                'intent_info': result.get('output', {}),
                'confidence': result.get('output', {}).get('confidence', 0.0),
                'predicted_intention': result.get('output', {}).get('predicted_intention', 'unknown'),
                'openmanus_enhanced': True
            }
        except Exception as e:
            logger.error(f"OpenManus intent classification failed: {e}")
            return {
                'success': False,
                'message': f'Intent classification failed: {str(e)}',
                'fallback_required': True
            }
    
    def create_task_plan(self, objective: str, resources: str = "", context: str = "") -> Dict[str, Any]:
        """
        Create task plan using OpenManus
        
        Args:
            objective: Task objective
            resources: Available resources
            context: Additional context
            
        Returns:
            Dict containing task plan
        """
        if not self.is_openmanus_available():
            return {
                'success': False,
                'message': 'OpenManus service not available',
                'fallback_required': True
            }
        
        try:
            result = self.openmanus_planner.create_task_plan(
                objective=objective,
                resources=resources,
                existing_context=context
            )
            return {
                'success': True,
                'plan': result.get('output', {}),
                'status': result.get('status', 'unknown'),
                'openmanus_enhanced': True
            }
        except Exception as e:
            logger.error(f"OpenManus task planning failed: {e}")
            return {
                'success': False,
                'message': f'Task planning failed: {str(e)}',
                'fallback_required': True
            }
    
    def enhance_response(self, original_response: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Enhance existing response with OpenManus capabilities
        
        Args:
            original_response: Original response text
            context: Additional context information
            
        Returns:
            Dict containing enhanced response
        """
        if not self.is_openmanus_available():
            return {
                'success': True,
                'message': original_response,
                'enhanced': False
            }
        
        try:
            # Use OpenManus to enhance the response
            enhancement_prompt = f"Enhance this response for a child learning assistant: {original_response}"
            if context:
                enhancement_prompt += f"\nContext: {context}"
            
            result = self.openmanus_planner.process_user_input(enhancement_prompt)
            
            return {
                'success': True,
                'message': result.get("final_response", original_response),
                'enhanced': True,
                'original_response': original_response,
                'openmanus_enhanced': True
            }
            
        except Exception as e:
            logger.warning(f"Response enhancement failed, using original: {e}")
            return {
                'success': True,
                'message': original_response,
                'enhanced': False
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get OpenManus service status"""
        if not self.is_openmanus_available():
            return {
                'status': 'unavailable',
                'ready': False,
                'message': 'OpenManus service not initialized'
            }
        
        try:
            # Get performance metrics if available
            metrics = getattr(self.openmanus_planner, '_performance_metrics', {})
            
            return {
                'status': 'ready',
                'ready': True,
                'metrics': metrics,
                'message': 'OpenManus service is ready'
            }
        except Exception as e:
            return {
                'status': 'error',
                'ready': False,
                'message': f'Status check failed: {str(e)}'
            }

# Global OpenManus service instance
_openmanus_service = None

def get_openmanus_service() -> OpenManusService:
    """Get global OpenManus service instance"""
    global _openmanus_service
    if _openmanus_service is None:
        _openmanus_service = OpenManusService()
    return _openmanus_service

def is_openmanus_available() -> bool:
    """Check if OpenManus is available globally"""
    service = get_openmanus_service()
    return service.is_openmanus_available()
