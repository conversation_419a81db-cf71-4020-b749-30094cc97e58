# 文件上传服务
import os
import shutil
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from fastapi import UploadFile, HTTPException

from core.file_upload.validator import FileUploadValidator
from core.file_upload.config import FileUploadConfig
from core.file_upload.schemas import (
    FileConflictStrategy, FileConflictInfo, ExistingFileInfo,
    FileUploadResponse, FileConflictCheckRequest, FileConflictCheckResponse
)

logger = logging.getLogger(__name__)


class FileUploadService:
    """文件上传服务类"""
    
    def __init__(self):
        self.validator = FileUploadValidator()
        self.config = FileUploadConfig()
    
    async def upload_file(self, 
                         file: UploadFile,
                         upload_path: str = "uploads/temp",
                         user_id: Optional[int] = None,
                         max_size: Optional[int] = None,
                         allowed_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        上传文件
        
        Args:
            file: 上传的文件对象
            upload_path: 上传路径
            user_id: 用户ID（可选）
            max_size: 最大文件大小（可选）
            allowed_types: 允许的文件类型（可选）
            
        Returns:
            Dict: 上传结果信息
        """
        try:
            # 1. 验证文件
            validation_result = await self.validator.validate_file(
                file=file,
                upload_path=upload_path,
                max_size=max_size
            )
            
            if not validation_result['valid']:
                raise HTTPException(
                    status_code=400,
                    detail={
                        "message": "文件验证失败",
                        "errors": validation_result['errors']
                    }
                )
            
            # 2. 额外的类型检查
            if allowed_types:
                file_ext = os.path.splitext(validation_result['filename'])[1].lower()
                if file_ext not in allowed_types:
                    raise HTTPException(
                        status_code=400,
                        detail=f"不允许的文件类型: {file_ext}，允许的类型: {allowed_types}"
                    )
            
            # 3. 保存文件
            saved_file_info = await self._save_file(file, validation_result)
            
            # 4. 记录上传日志
            upload_record = {
                "user_id": user_id,
                "original_filename": validation_result['filename'],
                "saved_filename": validation_result['safe_filename'],
                "file_path": saved_file_info['file_path'],
                "file_size": validation_result['file_size'],
                "file_type": validation_result['file_type'],
                "mime_type": validation_result['mime_type'],
                "file_hash": validation_result['file_hash'],
                "upload_time": datetime.now(timezone.utc).isoformat(),
                "upload_ip": None,  # 可以从请求中获取
                "status": "success"
            }
            
            logger.info(f"文件上传成功: {validation_result['filename']} -> {saved_file_info['file_path']}")
            
            return {
                "success": True,
                "message": "文件上传成功",
                "file_info": upload_record,
                "file_url": saved_file_info.get('file_url'),
                "validation_info": validation_result
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"文件上传失败: {str(e)}"
            )
    
    async def _save_file(self, file: UploadFile, validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """保存文件到磁盘"""
        try:
            file_path = validation_result['full_path']
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存文件
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            # 验证文件是否保存成功
            if not os.path.exists(file_path):
                raise Exception("文件保存失败")
            
            # 验证文件大小
            saved_size = os.path.getsize(file_path)
            if saved_size != validation_result['file_size']:
                os.remove(file_path)  # 删除损坏的文件
                raise Exception("文件保存后大小不匹配")
            
            # 生成文件URL（相对路径）
            file_url = f"/{validation_result['relative_path']}"
            
            return {
                "file_path": file_path,
                "file_url": file_url,
                "saved_size": saved_size
            }
            
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            raise Exception(f"保存文件失败: {str(e)}")
    
    async def upload_multiple_files(self,
                                  files: List[UploadFile],
                                  upload_path: str = "uploads/temp",
                                  user_id: Optional[int] = None,
                                  max_size: Optional[int] = None,
                                  allowed_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        批量上传文件
        
        Args:
            files: 文件列表
            upload_path: 上传路径
            user_id: 用户ID
            max_size: 最大文件大小
            allowed_types: 允许的文件类型
            
        Returns:
            Dict: 批量上传结果
        """
        results = {
            "success_count": 0,
            "failed_count": 0,
            "total_count": len(files),
            "successful_uploads": [],
            "failed_uploads": [],
            "total_size": 0
        }
        
        for file in files:
            try:
                result = await self.upload_file(
                    file=file,
                    upload_path=upload_path,
                    user_id=user_id,
                    max_size=max_size,
                    allowed_types=allowed_types
                )
                
                results["successful_uploads"].append(result)
                results["success_count"] += 1
                results["total_size"] += result["file_info"]["file_size"]
                
            except Exception as e:
                results["failed_uploads"].append({
                    "filename": file.filename,
                    "error": str(e)
                })
                results["failed_count"] += 1
        
        return results
    
    def delete_file(self, file_path: str, user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            user_id: 用户ID
            
        Returns:
            Dict: 删除结果
        """
        try:
            # 安全检查：确保文件在允许的目录内
            abs_path = os.path.abspath(file_path)
            base_dir = os.path.abspath(self.config.BASE_UPLOAD_DIR)
            
            if not abs_path.startswith(base_dir):
                raise HTTPException(
                    status_code=403,
                    detail="不允许删除此路径的文件"
                )
            
            if not os.path.exists(file_path):
                raise HTTPException(
                    status_code=404,
                    detail="文件不存在"
                )
            
            # 删除文件
            os.remove(file_path)
            
            logger.info(f"文件删除成功: {file_path} (用户: {user_id})")
            
            return {
                "success": True,
                "message": "文件删除成功",
                "deleted_file": file_path
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"文件删除失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"文件删除失败: {str(e)}"
            )
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 文件信息
        """
        try:
            if not os.path.exists(file_path):
                raise HTTPException(
                    status_code=404,
                    detail="文件不存在"
                )
            
            stat = os.stat(file_path)
            
            return {
                "file_path": file_path,
                "file_name": os.path.basename(file_path),
                "file_size": stat.st_size,
                "created_time": datetime.fromtimestamp(stat.st_ctime, timezone.utc).isoformat(),
                "modified_time": datetime.fromtimestamp(stat.st_mtime, timezone.utc).isoformat(),
                "file_extension": os.path.splitext(file_path)[1].lower(),
                "is_file": os.path.isfile(file_path),
                "is_readable": os.access(file_path, os.R_OK),
                "is_writable": os.access(file_path, os.W_OK)
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取文件信息失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"获取文件信息失败: {str(e)}"
            )
    
    def list_files(self, directory: str, user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        列出目录中的文件
        
        Args:
            directory: 目录路径
            user_id: 用户ID
            
        Returns:
            Dict: 文件列表
        """
        try:
            # 安全检查
            abs_dir = os.path.abspath(directory)
            base_dir = os.path.abspath(self.config.BASE_UPLOAD_DIR)
            
            if not abs_dir.startswith(base_dir):
                raise HTTPException(
                    status_code=403,
                    detail="不允许访问此目录"
                )
            
            if not os.path.exists(directory):
                raise HTTPException(
                    status_code=404,
                    detail="目录不存在"
                )
            
            files = []
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                if os.path.isfile(item_path):
                    file_info = self.get_file_info(item_path)
                    files.append(file_info)
            
            return {
                "directory": directory,
                "file_count": len(files),
                "files": files
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"列出文件失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"列出文件失败: {str(e)}"
            )

    async def upload_file_with_conflict_handling(self,
                                                file: UploadFile,
                                                upload_path: str = "uploads/temp",
                                                user_id: Optional[int] = None,
                                                max_size: Optional[int] = None,
                                                allowed_types: Optional[List[str]] = None,
                                                conflict_strategy: FileConflictStrategy = FileConflictStrategy.RENAME,
                                                create_backup: bool = False,
                                                check_content: bool = True) -> FileUploadResponse:
        """
        带冲突处理的文件上传

        Args:
            file: 上传的文件对象
            upload_path: 上传路径
            user_id: 用户ID
            max_size: 最大文件大小
            allowed_types: 允许的文件类型
            conflict_strategy: 冲突处理策略
            create_backup: 是否创建备份
            check_content: 是否检查文件内容

        Returns:
            FileUploadResponse: 详细的上传响应
        """
        try:
            # 1. 基础文件验证
            validation_result = await self.validator.validate_file(
                file=file,
                upload_path=upload_path,
                max_size=max_size
            )

            if not validation_result['valid']:
                return FileUploadResponse(
                    success=False,
                    message="文件验证失败",
                    conflict_info=FileConflictInfo(
                        conflict_detected=False,
                        existing_file=None,
                        suggested_strategy=None,
                        suggested_filename=None,
                        conflict_reason="; ".join(validation_result['errors'])
                    )
                )

            # 2. 额外的类型检查
            if allowed_types:
                file_ext = os.path.splitext(validation_result['filename'])[1].lower()
                if file_ext not in allowed_types:
                    return FileUploadResponse(
                        success=False,
                        message=f"不允许的文件类型: {file_ext}",
                        conflict_info=FileConflictInfo(
                            conflict_detected=False,
                            existing_file=None,
                            suggested_strategy=None,
                            suggested_filename=None,
                            conflict_reason=f"文件类型 {file_ext} 不在允许列表中"
                        )
                    )

            # 3. 读取文件内容用于冲突检查
            file_content = await file.read()
            await file.seek(0)  # 重置文件指针

            # 4. 检查文件冲突
            conflict_info = await self.validator.check_file_conflict(
                filename=validation_result['safe_filename'],
                upload_path=upload_path,
                file_content=file_content if check_content else None,
                conflict_strategy=conflict_strategy
            )

            # 5. 处理冲突
            final_filename = validation_result['safe_filename']
            backup_info = None
            operation_log = []

            if conflict_info.conflict_detected:
                operation_log.append(f"检测到文件冲突: {conflict_info.conflict_reason}")

                if conflict_strategy == FileConflictStrategy.CANCEL:
                    return FileUploadResponse(
                        success=False,
                        message="文件上传被取消，因为检测到冲突",
                        conflict_info=conflict_info,
                        operation_log="; ".join(operation_log)
                    )

                elif conflict_strategy == FileConflictStrategy.RENAME:
                    final_filename = conflict_info.suggested_filename
                    operation_log.append(f"文件已重命名为: {final_filename}")

                elif conflict_strategy == FileConflictStrategy.REPLACE:
                    # 检查是否需要创建备份
                    if create_backup:
                        existing_file_path = os.path.join(
                            self.config.BASE_UPLOAD_DIR,
                            upload_path,
                            validation_result['safe_filename']
                        )
                        try:
                            backup_path = self.validator.create_backup_file(existing_file_path)
                            backup_info = {
                                "backup_path": backup_path,
                                "backup_created_at": datetime.now(timezone.utc).isoformat()
                            }
                            operation_log.append(f"已创建备份文件: {backup_path}")
                        except Exception as e:
                            operation_log.append(f"备份创建失败: {str(e)}")

                    # 检查文件内容是否相同
                    if (check_content and conflict_info.existing_file and
                        conflict_info.existing_file.is_identical):
                        return FileUploadResponse(
                            success=True,
                            message="文件内容相同，跳过上传",
                            file_info={
                                "filename": validation_result['safe_filename'],
                                "file_path": conflict_info.existing_file.file_path,
                                "file_size": conflict_info.existing_file.file_size,
                                "is_duplicate": True
                            },
                            conflict_info=conflict_info,
                            operation_log="; ".join(operation_log)
                        )

                    operation_log.append("文件将被覆盖")

            # 6. 保存文件
            final_file_path = os.path.join(
                self.config.BASE_UPLOAD_DIR,
                upload_path,
                final_filename
            )

            # 确保目录存在
            os.makedirs(os.path.dirname(final_file_path), exist_ok=True)

            # 保存文件
            with open(final_file_path, "wb") as buffer:
                buffer.write(file_content)

            # 验证文件保存
            if not os.path.exists(final_file_path):
                raise Exception("文件保存失败")

            saved_size = os.path.getsize(final_file_path)
            if saved_size != len(file_content):
                os.remove(final_file_path)
                raise Exception("文件保存后大小不匹配")

            operation_log.append(f"文件成功保存到: {final_file_path}")

            # 7. 生成响应
            file_url = f"/{os.path.join(upload_path, final_filename)}"

            upload_record = {
                "user_id": user_id,
                "original_filename": validation_result['filename'],
                "saved_filename": final_filename,
                "file_path": final_file_path,
                "file_size": saved_size,
                "file_type": validation_result['file_type'],
                "mime_type": validation_result['mime_type'],
                "file_hash": validation_result['file_hash'],
                "upload_time": datetime.now(timezone.utc).isoformat(),
                "conflict_handled": conflict_info.conflict_detected,
                "strategy_used": conflict_strategy.value if conflict_info.conflict_detected else None
            }

            logger.info(f"文件上传成功: {validation_result['filename']} -> {final_file_path}")

            return FileUploadResponse(
                success=True,
                message="文件上传成功",
                file_info=upload_record,
                file_url=file_url,
                conflict_info=conflict_info if conflict_info.conflict_detected else None,
                backup_info=backup_info,
                operation_log="; ".join(operation_log)
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            return FileUploadResponse(
                success=False,
                message=f"文件上传失败: {str(e)}",
                conflict_info=FileConflictInfo(
                    conflict_detected=False,
                    existing_file=None,
                    suggested_strategy=None,
                    suggested_filename=None,
                    conflict_reason=str(e)
                )
            )

    async def check_file_conflict_only(self, request: FileConflictCheckRequest) -> FileConflictCheckResponse:
        """
        仅检查文件冲突，不执行上传

        Args:
            request: 冲突检查请求

        Returns:
            FileConflictCheckResponse: 冲突检查响应
        """
        try:
            conflict_info = await self.validator.check_file_conflict(
                filename=request.filename,
                upload_path=request.upload_path,
                file_content=None,
                conflict_strategy=FileConflictStrategy.RENAME
            )

            recommendations = []
            available_strategies = [FileConflictStrategy.RENAME, FileConflictStrategy.CANCEL]

            if conflict_info.conflict_detected:
                recommendations.append("建议使用重命名策略避免冲突")

                if conflict_info.existing_file:
                    recommendations.append(f"现有文件大小: {conflict_info.existing_file.file_size} 字节")
                    recommendations.append(f"现有文件修改时间: {conflict_info.existing_file.modified_time}")

                # 如果用户有权限，可以提供替换选项
                available_strategies.append(FileConflictStrategy.REPLACE)
                recommendations.append("如有权限，可选择覆盖现有文件")
            else:
                recommendations.append("未检测到冲突，可以直接上传")

            return FileConflictCheckResponse(
                conflict_info=conflict_info,
                recommendations=recommendations,
                available_strategies=available_strategies
            )

        except Exception as e:
            logger.error(f"冲突检查失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"冲突检查失败: {str(e)}"
            )
