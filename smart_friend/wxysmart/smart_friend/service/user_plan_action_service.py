"""
用户计划表操作记录服务（简化版）
提供用户操作记录的创建、查询和管理功能
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from core.user_management.database.connection import get_db_session_context
from core.user_management.models.user_models import UserPlanAction, Child

logger = logging.getLogger(__name__)


class UserPlanActionService:
    """用户计划表操作记录服务类（简化版）"""
    
    @staticmethod
    def create_action(user_id: int, table_id: str, user_operation: str) -> Optional[int]:
        """
        创建用户操作记录
        
        Args:
            user_id: 用户ID
            table_id: 表ID（每天的表ID不同）
            user_operation: 用户操作（string）
            
        Returns:
            创建成功返回操作记录ID，失败返回None
        """
        try:
            with get_db_session_context() as session:
                # 验证用户是否存在
                user = session.query(Child).filter(Child.id == user_id).first()
                if not user:
                    logger.error(f"用户不存在: {user_id}")
                    return None
                
                # 创建操作记录
                action_data = {
                    "user_id": user_id,
                    "table_id": table_id,
                    "user_operation": user_operation,
                    "operation_time": datetime.now(),
                }
                
                action = UserPlanAction.create(session, **action_data)
                if action:
                    session.commit()
                    logger.info(f"成功创建用户操作记录: {action.id}")
                    return action.id
                else:
                    logger.error("创建用户操作记录失败")
                    return None
                    
        except Exception as e:
            logger.error(f"创建用户操作记录异常: {e}")
            return None
    
    @staticmethod
    def get_user_action_history(user_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取用户操作历史
        
        Args:
            user_id: 用户ID
            limit: 返回记录数量限制
            
        Returns:
            操作历史列表
        """
        try:
            with get_db_session_context() as session:
                actions = UserPlanAction.get_by_user_id(session, user_id, limit=limit)
                
                history = []
                for action in actions:
                    history.append({
                        "id": action.id,
                        "table_id": action.table_id,
                        "operation_time": action.operation_time.isoformat() if action.operation_time else None,
                        "user_operation": action.user_operation,
                        "user_id": action.user_id
                    })
                
                logger.info(f"获取用户 {user_id} 的操作历史，共 {len(history)} 条记录")
                return history
                
        except Exception as e:
            logger.error(f"获取用户操作历史异常: {e}")
            return []
    
    @staticmethod
    def get_table_action_history(table_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取指定表的操作历史
        
        Args:
            table_id: 表ID
            limit: 返回记录数量限制
            
        Returns:
            操作历史列表
        """
        try:
            with get_db_session_context() as session:
                actions = UserPlanAction.get_by_table_id(session, table_id, limit=limit)
                
                history = []
                for action in actions:
                    history.append({
                        "id": action.id,
                        "table_id": action.table_id,
                        "operation_time": action.operation_time.isoformat() if action.operation_time else None,
                        "user_operation": action.user_operation,
                        "user_id": action.user_id
                    })
                
                logger.info(f"获取表 {table_id} 的操作历史，共 {len(history)} 条记录")
                return history
                
        except Exception as e:
            logger.error(f"获取表操作历史异常: {e}")
            return []
    

    @staticmethod
    def generate_table_id(date_str: str = None) -> str:
        """
        生成表ID
        
        Args:
            date_str: 日期字符串，格式为YYYYMMDD，如果为None则使用当前日期
            
        Returns:
            表ID，格式为：YYYYMMDD_plan_XXX
        """
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
        
        # 简单的计数器，实际应用中可以使用更复杂的逻辑
        import random
        counter = random.randint(1, 999)
        
        return f"{date_str}_plan_{counter:03d}"


# 便捷函数
def log_user_action(user_id: int, table_id: str, operation: str) -> Optional[int]:
    """记录用户操作"""
    return UserPlanActionService.create_action(user_id, table_id, operation)

def log_voice_action(user_id: int, table_id: str, operation_detail: str) -> Optional[int]:
    """记录语音操作"""
    operation = f"语音操作: {operation_detail}"
    return UserPlanActionService.create_action(user_id, table_id, operation)

def log_text_action(user_id: int, table_id: str, operation_detail: str) -> Optional[int]:
    """记录文本操作"""
    operation = f"文本操作: {operation_detail}"
    return UserPlanActionService.create_action(user_id, table_id, operation)

def log_image_action(user_id: int, table_id: str, operation_detail: str) -> Optional[int]:
    """记录图像操作"""
    operation = f"图像操作: {operation_detail}"
    return UserPlanActionService.create_action(user_id, table_id, operation)

def get_today_table_id() -> str:
    """获取今天的表ID"""
    return UserPlanActionService.generate_table_id()

def get_user_today_actions(user_id: int) -> List[Dict[str, Any]]:
    """获取用户今天的操作记录"""
    today_table_id = get_today_table_id()
    return UserPlanActionService.get_table_action_history(today_table_id)
