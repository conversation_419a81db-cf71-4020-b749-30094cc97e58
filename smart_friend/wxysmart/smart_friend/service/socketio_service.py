#!/usr/bin/env python3
"""
Socket.IO服务模块
用于处理实时语音识别和音频数据传输
"""

import logging
import socketio
import base64
import numpy as np
import time
import threading
from typing import Optional

# 设置日志
logger = logging.getLogger(__name__)

class SocketIOService:
    """Socket.IO服务类"""
    
    def __init__(self):
        """初始化Socket.IO服务"""
        self.sio = socketio.AsyncServer(
            cors_allowed_origins="*",
            async_mode='asgi'
        )
        
        # ASR相关状态
        self.asr_client = None
        self.asr_connected = False
        self.is_recording = False
        self.last_speech_time = time.time()
        self.silence_timeout = 1.5
        
        # 注册事件处理器
        self._register_events()
        
        logger.info("Socket.IO服务初始化完成")
    
    def _register_events(self):
        """注册Socket.IO事件处理器"""
        
        @self.sio.event
        async def connect(sid, environ):
            """客户端连接事件"""
            logger.info(f"客户端已连接: {sid}")
            await self.sio.emit('status', {'message': '已连接到语音识别服务'}, room=sid)

        @self.sio.on('*')
        async def catch_all(event, sid, data=None):
            """捕获所有事件用于调试"""
            logger.info(f"收到Socket.IO事件: {event}, 客户端: {sid}, 数据: {data}")
        
        @self.sio.event
        async def disconnect(sid):
            """客户端断开连接事件"""
            logger.info(f"客户端已断开连接: {sid}")
        
        @self.sio.event
        async def start_recognition(sid):
            """开始语音识别事件"""
            try:
                logger.info(f"收到开始语音识别请求，客户端: {sid}")

                if not self.asr_connected or not self.asr_client:
                    logger.error("ASR服务未连接或客户端未初始化")
                    await self.sio.emit('error', {'message': '请先连接ASR服务'}, room=sid)
                    return

                # 启动语音识别
                logger.info("正在启动ASR语音识别...")
                success = self.asr_client.start_recognition(callback=self._asr_result_callback)
                if success:
                    # 重要：设置录音状态为True
                    self.is_recording = True
                    self.last_speech_time = time.time()
                    logger.info(f"Socket.IO录音状态已设置: {self.is_recording}")

                    await self.sio.emit('recognition_started', {
                        'message': '语音识别已开始',
                        'silence_timeout': self.silence_timeout
                    }, room=sid)
                    logger.info("语音识别已成功启动")
                else:
                    logger.error("ASR语音识别启动失败")
                    await self.sio.emit('error', {'message': '启动语音识别失败'}, room=sid)

            except Exception as e:
                logger.error(f"开始语音识别时出错: {e}")
                import traceback
                traceback.print_exc()
                await self.sio.emit('error', {'message': f'启动语音识别失败: {str(e)}'}, room=sid)
        
        @self.sio.event
        async def stop_recognition(sid):
            """停止语音识别事件"""
            try:
                logger.info(f"收到停止语音识别请求，客户端: {sid}")
                if self.asr_client and self.is_recording:
                    self.asr_client.stop_recognition()
                    self.is_recording = False
                    logger.info(f"Socket.IO录音状态已停止: {self.is_recording}")
                    await self.sio.emit('recognition_stopped', {'message': '语音识别已停止'}, room=sid)
                    logger.info("语音识别已停止")
                else:
                    logger.warning(f"停止识别请求被忽略 - ASR客户端: {bool(self.asr_client)}, 录音状态: {self.is_recording}")
                    await self.sio.emit('error', {'message': '当前没有进行语音识别'}, room=sid)

            except Exception as e:
                logger.error(f"停止语音识别时出错: {e}")
                await self.sio.emit('error', {'message': f'停止语音识别失败: {str(e)}'}, room=sid)
        
        @self.sio.event
        async def audio_data(sid, data):
            """处理音频数据事件"""
            try:
                if not self.asr_client:
                    logger.warning("ASR客户端未初始化，忽略音频数据")
                    return

                if not self.is_recording:
                    # logger.warning("当前未在录音状态，忽略音频数据")
                    return

                # 解码base64音频数据
                audio_base64 = data.get('audio', '')
                if not audio_base64:
                    logger.warning("收到空的音频数据")
                    return

                try:
                    audio_bytes = base64.b64decode(audio_base64)
                    audio_array = np.frombuffer(audio_bytes, dtype=np.int16)

                    # 发送音频数据到ASR服务
                    if self.asr_client.send_audio(audio_array):
                        self.last_speech_time = time.time()
                        # 每100个音频包打印一次日志，避免日志过多
                        if hasattr(self, '_audio_count'):
                            self._audio_count += 1
                        else:
                            self._audio_count = 1

                        if self._audio_count % 100 == 0:
                            logger.info(f"已处理 {self._audio_count} 个音频包")
                    else:
                        logger.warning("发送音频数据到ASR服务失败")

                except Exception as e:
                    logger.error(f"处理音频数据时出错: {e}")

            except Exception as e:
                logger.error(f"音频数据处理失败: {e}")
        
        @self.sio.event
        async def test_connection(sid, data):
            """测试连接事件"""
            logger.info(f"收到测试连接消息: {data}")
            await self.sio.emit('connection_confirmed', {
                'message': '音频传输通道已建立'
            }, room=sid)
    
    def _asr_result_callback(self, result_data):
        """ASR识别结果回调"""
        try:
            # 异步发送识别结果到前端
            import asyncio
            asyncio.create_task(self.sio.emit('recognition_result', {
                'text': result_data.get('text', ''),
                'is_final': result_data.get('is_final', False),
                'timestamp': time.time() * 1000
            }))
            
            logger.info(f"ASR识别结果: {result_data.get('text', '')}")
            
        except Exception as e:
            logger.error(f"处理ASR结果时出错: {e}")
    
    def set_asr_client(self, asr_client):
        """设置ASR客户端"""
        self.asr_client = asr_client
        self.asr_connected = asr_client is not None
        logger.info(f"ASR客户端已设置: {self.asr_connected}")
    
    def get_asgi_app(self, fastapi_app):
        """获取集成了Socket.IO的ASGI应用"""
        return socketio.ASGIApp(self.sio, fastapi_app)


# 全局Socket.IO服务实例
_socketio_service = None

def get_socketio_service() -> SocketIOService:
    """获取Socket.IO服务实例（单例模式）"""
    global _socketio_service
    if _socketio_service is None:
        _socketio_service = SocketIOService()
    return _socketio_service

def init_socketio_service() -> SocketIOService:
    """初始化Socket.IO服务"""
    return get_socketio_service()
