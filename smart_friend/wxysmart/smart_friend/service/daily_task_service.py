# 每日任务服务
import logging
import re
import requests
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session

from core.daily_tasks.models.daily_task_models import DailyTask, TaskItem
from core.user_management.database.connection import get_db_session_context
from core.planning.database.influxdb_connection import get_influxdb_manager

logger = logging.getLogger(__name__)


class DailyTaskService:
    """每日任务服务类"""

    def __init__(self):
        self.influxdb = get_influxdb_manager()  # 添加InfluxDB支持
        self.measurement = "daily_tasks"  # InfluxDB measurement名称
    
    def create_task(self, child_id: int, task_name: str, plan_id:int, description: str = "",**kwargs) -> Optional[Dict[str, Any]]:
        """创建每日任务"""
        try:
            with get_db_session_context() as session:
                # 过滤掉可能不存在的字段，避免数据库错误
                allowed_fields = {
                    'task_date', 'time_slot', 'estimated_duration', 'actual_duration',
                    'subject', 'task_type', 'customization', 'difficulty', 'solution',
                    'confidence_index', 'status', 'completion_percentage',
                    'difficulty_rating', 'satisfaction_rating', 'notes',
                    'total_points', 'bonus_points', 'points_reason'
                }

                filtered_kwargs = {k: v for k, v in kwargs.items() if k in allowed_fields}

                task = DailyTask.create_task(
                    session=session,
                    child_id=child_id,
                    task_name=task_name,
                    description=description,
                    plan_id=plan_id,
                    **filtered_kwargs
                )

                if task:
                    return task.to_dict()
                return None

        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return None

    def parse_sub_tasks_from_description(self, description: str) -> List[str]:
        """从描述中解析子任务"""
        if not description:
            return []

        # 常见的分隔符模式
        separators = [
            r'[，,、]',  # 中文逗号、英文逗号、顿号
            r'[；;]',    # 中文分号、英文分号
            r'[。]',     # 中文句号
            r'\s+',      # 空格
        ]

        # 尝试用不同分隔符拆分
        sub_tasks = []
        for separator in separators:
            parts = re.split(separator, description.strip())
            if len(parts) > 1:
                # 过滤空字符串和过短的内容
                sub_tasks = [part.strip() for part in parts if part.strip() and len(part.strip()) > 2]
                if len(sub_tasks) > 1:
                    break

        # 如果没有找到合适的分隔符，返回原描述作为单个任务
        if not sub_tasks:
            sub_tasks = [description.strip()]

        return sub_tasks

    def calculate_sub_task_time_slots(self, main_time_slot: str, sub_task_count: int) -> List[Dict[str, str]]:
        """计算子任务的时间段"""
        if not main_time_slot or sub_task_count <= 0:
            return []

        try:
            # 解析主任务时间段 "18:00 - 18:45"
            time_parts = main_time_slot.replace(' ', '').split('-')
            if len(time_parts) != 2:
                return []

            start_time_str, end_time_str = time_parts
            start_hour, start_minute = map(int, start_time_str.split(':'))
            end_hour, end_minute = map(int, end_time_str.split(':'))

            # 计算总时长（分钟）
            start_total_minutes = start_hour * 60 + start_minute
            end_total_minutes = end_hour * 60 + end_minute
            total_duration = end_total_minutes - start_total_minutes

            if total_duration <= 0:
                return []

            # 为每个子任务分配时间，预留5分钟休息时间
            rest_time = 5 if sub_task_count > 1 else 0
            available_time = total_duration - (sub_task_count - 1) * rest_time
            sub_task_duration = max(10, available_time // sub_task_count)  # 最少10分钟

            time_slots = []
            current_start = start_total_minutes

            for i in range(sub_task_count):
                # 计算当前子任务的开始和结束时间
                current_end = current_start + sub_task_duration

                # 确保不超过主任务结束时间
                if current_end > end_total_minutes:
                    current_end = end_total_minutes

                # 转换回时间格式
                start_h, start_m = divmod(current_start, 60)
                end_h, end_m = divmod(current_end, 60)

                time_slot = f"{start_h:02d}:{start_m:02d}-{end_h:02d}:{end_m:02d}"

                time_slots.append({
                    'time_slot': time_slot,
                    'start_time': f"{start_h:02d}:{start_m:02d}",
                    'end_time': f"{end_h:02d}:{end_m:02d}",
                    'duration_minutes': current_end - current_start,
                    'order_index': i + 1
                })

                # 下一个子任务开始时间（包含休息时间）
                current_start = current_end + rest_time

                # 如果已经到达或超过主任务结束时间，停止
                if current_start >= end_total_minutes:
                    break

            return time_slots

        except (ValueError, IndexError) as e:
            logger.error(f"解析时间段失败: {e}")
            return []

    def calculate_base_points(self, duration_minutes: int) -> int:
        """根据任务用时计算基础积分"""
        if duration_minutes <= 10:
            return 5  # 短任务
        elif duration_minutes <= 20:
            return 10  # 中等任务
        elif duration_minutes <= 30:
            return 15  # 长任务
        else:
            return 20  # 超长任务

    def calculate_total_points(self, sub_tasks_data: List[Dict]) -> Dict[str, int]:
        """计算任务总积分"""
        total_points = sum(task.get('points_earned', 0) for task in sub_tasks_data)
        total_bonus = sum(task.get('bonus_points', 0) for task in sub_tasks_data)

        return {
            'total_points': total_points + total_bonus,
            'bonus_points': total_bonus
        }

    def create_task_with_sub_tasks(self, child_id: int, task_name: str,plan_id:int, description: str = "", **kwargs) -> Optional[Dict[str, Any]]:
        """创建带子任务的每日任务"""
        try:
            with get_db_session_context() as session:
                # 创建主任务
                task = DailyTask.create_task(
                    session=session,
                    child_id=child_id,
                    task_name=task_name,
                    description=description,
                    plan_id=plan_id,
                    **kwargs
                )

                if not task:
                    return None

                # 解析子任务
                sub_tasks = self.parse_sub_tasks_from_description(description)

                if len(sub_tasks) > 1:
                    # 计算子任务时间段
                    main_time_slot = kwargs.get('time_slot', '')
                    time_slots = self.calculate_sub_task_time_slots(main_time_slot, len(sub_tasks))

                    # 收集子任务数据用于计算总积分
                    sub_tasks_data = []

                    # 创建子任务
                    for i, sub_task_content in enumerate(sub_tasks):
                        time_info = time_slots[i] if i < len(time_slots) else {}
                        duration = time_info.get('duration_minutes', 15)
                        base_points = self.calculate_base_points(duration)

                        # 创建子任务
                        sub_task_data = {
                            'task_content': sub_task_content,
                            'task_source': 'auto_parsed',
                            'time_slot': time_info.get('time_slot'),
                            'order_index': time_info.get('order_index', i + 1),
                            'estimated_minutes': duration,
                            'points_earned': base_points,
                            'bonus_points': 0,  # 初始奖励积分为0
                            'points_reason': f"完成子任务基础积分({duration}分钟)"
                        }

                        sub_tasks_data.append(sub_task_data)

                        # 如果有具体的开始和结束时间，转换为datetime
                        if 'start_time' in time_info and 'end_time' in time_info:
                            task_date = task.task_date or datetime.now()
                            base_date = task_date.date()

                            start_time_str = time_info['start_time']
                            end_time_str = time_info['end_time']

                            start_hour, start_minute = map(int, start_time_str.split(':'))
                            end_hour, end_minute = map(int, end_time_str.split(':'))

                            sub_task_data['start_time'] = datetime.combine(base_date, datetime.min.time().replace(hour=start_hour, minute=start_minute))
                            sub_task_data['end_time'] = datetime.combine(base_date, datetime.min.time().replace(hour=end_hour, minute=end_minute))

                        TaskItem.create_item(session, task.id, **sub_task_data)

                    # 计算并更新任务总积分
                    points_info = self.calculate_total_points(sub_tasks_data)
                    task.total_points = points_info['total_points']
                    task.bonus_points = points_info['bonus_points']
                    task.points_reason = f"完成{len(sub_tasks_data)}个子任务，总积分{points_info['total_points']}分"
                    session.commit()

                # 返回完整的任务信息（包含子任务）
                task_dict = task.to_dict()
                task_dict['task_items'] = [item.to_dict() for item in task.task_items]

                return task_dict

        except Exception as e:
            logger.error(f"创建带子任务的任务失败: {e}")
            return None

    def get_task_by_id(self, task_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取任务"""
        try:
            with get_db_session_context() as session:
                task = DailyTask.get_by_id(session, task_id)
                return task.to_dict() if task else None
        except Exception as e:
            logger.error(f"获取任务失败: {e}")
            return None

    def get_tasks_by_child_and_date(self, child_id: int, task_date: date) -> List[Dict[str, Any]]:
        """获取指定学生指定日期的任务"""
        try:
            with get_db_session_context() as session:
                tasks = DailyTask.get_by_child_and_date(session, child_id, task_date)
                return [task.to_dict() for task in tasks]
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return []
    
    def get_tasks_by_date_range(self, child_id: int, start_date: date, end_date: date,
                               subject: Optional[str] = None, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取指定日期范围的任务"""
        try:
            with get_db_session_context() as session:
                query = session.query(DailyTask).filter(
                    DailyTask.child_id == child_id,
                    DailyTask.task_date >= datetime.combine(start_date, datetime.min.time()),
                    DailyTask.task_date <= datetime.combine(end_date, datetime.max.time()),
                    DailyTask.is_active == True
                )

                if subject:
                    query = query.filter(DailyTask.subject == subject)
                if status:
                    query = query.filter(DailyTask.status == status)

                tasks = query.order_by(DailyTask.task_date.desc(), DailyTask.time_slot).all()
                return [task.to_dict() for task in tasks]
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return []
    
    def get_yesterday_tasks(self, child_id: int) -> List[Dict[str, Any]]:
        """获取昨日任务情况"""
        try:
            yesterday = date.today() - timedelta(days=1)
            return self.get_tasks_by_child_and_date(child_id, yesterday)
        except Exception as e:
            logger.error(f"获取昨日任务失败: {e}")
            return []

    def get_today_tasks(self, child_id: int) -> List[Dict[str, Any]]:
        """获取今日任务"""
        try:
            today = date.today()
            return self.get_tasks_by_child_and_date(child_id, today)
        except Exception as e:
            logger.error(f"获取今日任务失败: {e}")
            return []
    
    def create_sample_tasks(self, child_id: int) -> bool:
        """为指定儿童创建示例任务数据"""
        try:
            today = date.today()
            yesterday = today - timedelta(days=1)
            
            # 创建昨日任务
            yesterday_tasks = [
                {
                    "task_name": "数学作业",
                    "subject": "数学",
                    "time_slot": "18:00 - 18:45",
                    "task_date": datetime.combine(yesterday, datetime.min.time()),
                    "description": "练习册第8页，口算题卡15题",
                    "customization": "针对应用题理解困难，采用图解方式",
                    "difficulty": "应用题理解",
                    "solution": "先画图理解题意，再列式计算",
                    "confidence_index": 4,
                    "status": "completed",
                    "completion_percentage": 85.0,
                    "difficulty_rating": 3,
                    "satisfaction_rating": 4,
                    "notes": "应用题有进步，但计算速度还需提高"
                },
                {
                    "task_name": "语文作业",
                    "subject": "语文",
                    "time_slot": "19:00 - 19:30",
                    "task_date": datetime.combine(yesterday, datetime.min.time()),
                    "description": "背诵古诗《静夜思》，练字20个",
                    "customization": "结合听觉学习特点，先听朗读再背诵",
                    "difficulty": "古诗理解和记忆",
                    "solution": "通过音频朗读加深理解，分段背诵",
                    "confidence_index": 3,
                    "status": "completed",
                    "completion_percentage": 70.0,
                    "difficulty_rating": 4,
                    "satisfaction_rating": 3,
                    "notes": "背诵有困难，需要多次重复"
                },
                {
                    "task_name": "英语作业",
                    "subject": "英语",
                    "time_slot": "20:00 - 20:30",
                    "task_date": datetime.combine(yesterday, datetime.min.time()),
                    "description": "听读Unit 2单词，完成练习册第5页",
                    "customization": "利用听觉优势，多听多读",
                    "difficulty": "单词发音",
                    "solution": "跟读音频，家长纠正发音",
                    "confidence_index": 4,
                    "status": "completed",
                    "completion_percentage": 90.0,
                    "difficulty_rating": 2,
                    "satisfaction_rating": 5,
                    "notes": "英语学习兴趣很高，发音进步明显"
                }
            ]
            
            # 创建昨日任务（使用传统方法，因为已经完成）
            with get_db_session_context() as session:
                for task_data in yesterday_tasks:
                    task = DailyTask.create_task(session, child_id, **task_data)
                    if not task:
                        logger.error(f"创建昨日任务失败: {task_data['task_name']}")
                        return False

            logger.info(f"成功为儿童{child_id}创建示例任务数据")
            return True

        except Exception as e:
            logger.error(f"创建示例任务数据失败: {e}")
            return False

    async def soft_delete_prompt4_tasks(self, child_id: int) -> Dict[str, Any]:
        """
        软删除prompt4输入源的任务

        Args:
            child_id: 学生ID

        Returns:
            Dict: 软删除结果
        """
        try:
            with get_db_session_context() as session:
                today = date.today()

                print(f"\n🗑️ 开始软删除prompt4输入源的任务...")
                print(f"📅 目标日期: {today}")

                # 获取今日活跃任务的ID列表（这些是作为prompt4输入的任务）
                today_task_ids = session.query(DailyTask.id, DailyTask.task_name).filter(
                    DailyTask.child_id == child_id,
                    DailyTask.task_date >= datetime.combine(today, datetime.min.time()),
                    DailyTask.task_date < datetime.combine(today, datetime.max.time()),
                    DailyTask.is_active == True
                ).all()

                task_ids = [task_id[0] for task_id in today_task_ids]
                task_names = [task_info[1] for task_info in today_task_ids]

                print(f"📋 找到 {len(task_ids)} 个待软删除的prompt4输入任务:")
                for i, task_name in enumerate(task_names, 1):
                    print(f"  {i}. {task_name}")

                # 软删除对应的子任务
                sub_task_updated_count = 0
                if task_ids:
                    sub_task_updated_count = session.query(TaskItem).filter(
                        TaskItem.daily_task_id.in_(task_ids),
                        TaskItem.is_active == True
                    ).update({
                        "is_active": False,
                        "updated_at": datetime.now()
                    }, synchronize_session=False)

                    print(f"  🔄 软删除了 {sub_task_updated_count} 个子任务 (is_active设为0)")
                    logger.info(f"将{sub_task_updated_count}个今日子任务设为非活跃状态")

                # 软删除主任务
                updated_count = session.query(DailyTask).filter(
                    DailyTask.child_id == child_id,
                    DailyTask.task_date >= datetime.combine(today, datetime.min.time()),
                    DailyTask.task_date < datetime.combine(today, datetime.max.time()),
                    DailyTask.is_active == True
                ).update({
                    "is_active": False,
                    "updated_at": datetime.now()
                })

                print(f"  🔄 软删除了 {updated_count} 个主任务 (is_active设为0)")
                print(f"✅ prompt4输入源任务软删除完成")
                logger.info(f"将{updated_count}个今日主任务设为非活跃状态")

                session.commit()

                return {
                    "success": True,
                    "message": "prompt4输入源任务软删除成功",
                    "data": {
                        "deleted_main_tasks": updated_count,
                        "deleted_sub_tasks": sub_task_updated_count,
                        "task_names": task_names
                    }
                }

        except Exception as e:
            logger.error(f"软删除prompt4输入源任务失败: {e}")
            return {
                "success": False,
                "message": f"软删除失败: {str(e)}"
            }

    async def confirm_task_plan(self, child_id: int, task_plan: List[Dict[str, Any]],planID: int) -> Dict[str, Any]:
        """
        确认任务计划并存储到数据库

        Args:
            child_id: 学生ID
            task_plan: 前端任务计划数据

        Returns:
            Dict: 处理结果
        """
        try:
            with get_db_session_context() as session:
                created_tasks = []

                # 注意：软删除逻辑已移至生成任务时执行，这里不再重复执行
                print(f"\n📝 开始处理任务计划确认...")

                # 处理前端任务计划数据
                for task_data in task_plan:
                    try:
                        # 创建主任务
                        main_task = DailyTask.create_task(
                            session=session,
                            child_id=child_id,
                            task_name=task_data.get('task_name', task_data.get('任务名称', '')),
                            description=f"AI生成的学习任务计划",
                            subject=task_data.get('subject', '其他'),
                            task_date=datetime.now(),
                            time_slot=task_data.get('time_slot', task_data.get('时间段', '')),
                            customization=task_data.get('customization', task_data.get('定制方案', '')),
                            difficulty=task_data.get('difficulty', task_data.get('难点', '')),
                            solution=task_data.get('solution', task_data.get('解决方案', '')),
                            confidence_index=task_data.get('confidence_index', task_data.get('信心指数', 3)),
                            status="pending",
                            plan_id=planID,
                        )

                        if main_task:
                            # 创建任务字典，包含子任务信息
                            task_dict = main_task.to_dict()
                            task_dict['sub_tasks'] = []  # 初始化子任务列表

                            # 3. 处理子任务
                            sub_tasks = task_data.get('sub_tasks', task_data.get('子任务', []))
                            if isinstance(sub_tasks, list):
                                for sub_task in sub_tasks:
                                    if isinstance(sub_task, dict):
                                        sub_task_content = sub_task.get('sub_task_name', sub_task.get('content', ''))
                                        sub_task_time = sub_task.get('time_slot', '')
                                    else:
                                        sub_task_content = str(sub_task)
                                        sub_task_time = ''

                                    if sub_task_content:
                                        created_sub_task = TaskItem.create_item(
                                            session=session,
                                            daily_task_id=main_task.id ,
                                            task_content=sub_task_content,
                                            task_source="AI任务生成",
                                            time_slot=sub_task_time,
                                            created_at= datetime.now().replace(microsecond=0),
                                            updated_at= datetime.now().replace(microsecond=0),
                                            plan_id=planID,
                                        )

                                        # 将子任务信息添加到任务字典中
                                        if created_sub_task:
                                            task_dict['sub_tasks'].append({
                                                'id': created_sub_task.id,
                                                'task_content': created_sub_task.task_content,
                                                'time_slot': created_sub_task.time_slot,
                                                'task_source': created_sub_task.task_source
                                            })

                            created_tasks.append(task_dict)

                    except Exception as e:
                        logger.error(f"处理任务数据失败: {e}, 任务数据: {task_data}")
                        continue

                session.commit()

                print(f"✅ 任务计划确认完成，成功创建了{len(created_tasks)}个任务")

                return {
                    "success": True,
                    "message": f"成功确认任务计划，创建了{len(created_tasks)}个任务",
                    "data": {
                        "created_tasks": created_tasks,
                        "storage_summary": {
                            "sqlite_tasks": len(created_tasks),
                            "total_tasks": len(task_plan)
                        }
                    }
                }

        except Exception as e:
            logger.error(f"确认任务计划失败: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    async def get_today_tasks_with_subtasks(self, child_id: int) -> Dict[str, Any]:
        """
        获取今日任务及其子任务（用于前端显示）

        Args:
            child_id: 学生ID

        Returns:
            Dict: 包含任务列表的结果
        """
        try:
            with get_db_session_context() as session:
                today = date.today()

                # 查询今日的活跃任务
                tasks = session.query(DailyTask).filter(
                    DailyTask.child_id == child_id,
                    DailyTask.task_date >= datetime.combine(today, datetime.min.time()),
                    DailyTask.task_date < datetime.combine(today, datetime.max.time()),
                    DailyTask.is_active == True
                ).order_by(DailyTask.created_at).all()

                result_tasks = []
                for task in tasks:
                    # 查询活跃的子任务
                    sub_tasks = session.query(TaskItem).filter(
                        TaskItem.daily_task_id == task.id,
                        TaskItem.is_active == True
                    ).order_by(TaskItem.created_at).all()

                    task_dict = {
                        "id": task.id,
                        "task_name": task.task_name,
                        "subject": task.subject,
                        "time_slot": task.time_slot,
                        "description": task.description,
                        "customization": task.customization,
                        "difficulty": task.difficulty,
                        "solution": task.solution,
                        "confidence_index": task.confidence_index,
                        "status": task.status,
                        "sub_tasks": [
                            {
                                "id": st.id,
                                "sub_task_name": st.task_content,  # 保持兼容性
                                "task_content": st.task_content,   # 新字段名
                                "time_slot": st.time_slot,
                                "task_source": st.task_source,
                                "daily_task_id": st.daily_task_id,
                                "is_completed": st.is_completed,
                                "created_at": st.created_at.isoformat() if st.created_at else None
                            } for st in sub_tasks
                        ]
                    }
                    result_tasks.append(task_dict)

                return {
                    "success": True,
                    "message": f"成功获取{len(result_tasks)}个今日任务",
                    "data": result_tasks
                }

        except Exception as e:
            logger.error(f"获取今日任务失败: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    async def add_subtask(self, task_name: str, plan_id:int,sub_task_content: str,daily_task_id:int,
                         sub_task_source: str = "手动添加", time_slot: str = None) -> Dict[str, Any]:
        """
        添加子任务

        Args:
            task_name: 主任务名称
            sub_task_content: 子任务内容
            sub_task_source: 子任务来源
            time_slot: 时间段

        Returns:
            Dict: 处理结果
        """
        try:
            with get_db_session_context() as session:
                # 查找今日的活跃主任务
                today = date.today()
                main_task = session.query(DailyTask).filter(
                    DailyTask.task_date >= datetime.combine(today, datetime.min.time()),
                    DailyTask.task_date < datetime.combine(today, datetime.max.time()),
                    DailyTask.is_active == True,
                    DailyTask.plan_id == plan_id
                ).first()

                if not main_task:
                    return {
                        "success": False,
                        "message": f"未找到名为'{task_name}'的活跃任务"
                    }

                # 创建子任务
                sub_task = TaskItem.create_item(
                    session=session,
                    plan_id=plan_id,
                    daily_task_id=daily_task_id,
                    task_content=sub_task_content,
                    task_source=sub_task_source,
                    time_slot=time_slot,
                    created_at=datetime.now().replace(microsecond=0),
                    updated_at=datetime.now().replace(microsecond=0)
                )

                if sub_task:
                    session.commit()
                    return {
                        "success": True,
                        "message": "子任务添加成功",
                        "data": {
                            "sub_task_id": sub_task.id,
                            "task_name": task_name,
                            "sub_task_content": sub_task_content,
                            
                        }
                    }
                else:
                    return {
                        "success": False,
                        "message": "子任务创建失败"
                    }

        except Exception as e:
            logger.error(f"添加子任务失败: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    async def remove_subtask(self, task_name: str = None, sub_task_content: str = None,
                           sub_task_id: int = None) -> Dict[str, Any]:
        """
        删除子任务（软删除）

        Args:
            task_name: 主任务名称（兼容性参数）
            sub_task_content: 子任务内容（兼容性参数）
            sub_task_id: 子任务ID（推荐使用）

        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始删除子任务 - task_name: {task_name}, sub_task_content: {sub_task_content}, sub_task_id: {sub_task_id}")

            with get_db_session_context() as session:
                sub_task = None

                # 优先使用子任务ID查找
                if sub_task_id:
                    logger.info(f"通过子任务ID删除: {sub_task_id}")
                    sub_task = session.query(TaskItem).filter(
                        TaskItem.id == sub_task_id,
                        TaskItem.is_active == True
                    ).first()

                    if not sub_task:
                        logger.warning(f"未找到ID为{sub_task_id}的活跃子任务")
                        return {
                            "success": False,
                            "message": f"未找到ID为{sub_task_id}的活跃子任务"
                        }

                # 如果没有提供ID，则使用传统方式查找
                elif task_name and sub_task_content:
                    # 查找今日的活跃主任务
                    today = date.today()
                    main_task = session.query(DailyTask).filter(
                        DailyTask.task_name == task_name,
                        DailyTask.task_date >= datetime.combine(today, datetime.min.time()),
                        DailyTask.task_date < datetime.combine(today, datetime.max.time()),
                        DailyTask.is_active == True
                    ).first()

                    if not main_task:
                        return {
                            "success": False,
                            "message": f"未找到名为'{task_name}'的活跃任务"
                        }

                    # 查找子任务
                    sub_task = session.query(TaskItem).filter(
                        TaskItem.daily_task_id == main_task.id,
                        TaskItem.task_content == sub_task_content,
                        TaskItem.is_active == True
                    ).first()

                    if not sub_task:
                        return {
                            "success": False,
                            "message": f"未找到内容为'{sub_task_content}'的子任务"
                        }
                else:
                    return {
                        "success": False,
                        "message": "必须提供子任务ID或者任务名称和子任务内容"
                    }

                # 软删除子任务
                sub_task.is_active = False
                sub_task.updated_at = datetime.now()
                session.commit()

                return {
                    "success": True,
                    "message": "子任务删除成功",
                    "data": {
                        "sub_task_id": sub_task.id,
                        "task_name": sub_task.daily_task.task_name if sub_task.daily_task else task_name,
                        "sub_task_content": sub_task.task_content
                    }
                }

        except Exception as e:
            logger.error(f"删除子任务失败: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    async def restore_subtask(self, task_name: str = None, sub_task_content: str = None,
                            sub_task_id: int = None) -> Dict[str, Any]:
        """
        撤销删除子任务（将is_active改为True）

        Args:
            task_name: 主任务名称（兼容性参数）
            sub_task_content: 子任务内容（兼容性参数）
            sub_task_id: 子任务ID（推荐使用）

        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始恢复子任务 - task_name: {task_name}, sub_task_content: {sub_task_content}, sub_task_id: {sub_task_id}")

            with get_db_session_context() as session:
                sub_task = None

                # 优先使用子任务ID查找
                if sub_task_id:
                    logger.info(f"通过子任务ID查找: {sub_task_id}")
                    sub_task = session.query(TaskItem).filter(
                        TaskItem.id == sub_task_id,
                        TaskItem.is_active == False  # 查找已删除的任务
                    ).first()

                    if not sub_task:
                        logger.warning(f"未找到ID为{sub_task_id}的已删除子任务")
                        return {
                            "success": False,
                            "message": f"未找到ID为{sub_task_id}的已删除子任务"
                        }

                # 如果没有提供ID，则使用传统方式查找
                elif task_name and sub_task_content:
                    logger.info(f"通过任务名称和子任务内容查找: {task_name} - {sub_task_content}")

                    # 查找今日的主任务（不限制is_active状态，因为主任务可能也被软删除了）
                    today = date.today()
                    main_task = session.query(DailyTask).filter(
                        DailyTask.task_name == task_name,
                        DailyTask.task_date >= datetime.combine(today, datetime.min.time()),
                        DailyTask.task_date < datetime.combine(today, datetime.max.time())
                        # 移除 is_active 限制，允许查找已删除的主任务
                    ).first()

                    if not main_task:
                        logger.warning(f"未找到名为'{task_name}'的任务")
                        return {
                            "success": False,
                            "message": f"未找到名为'{task_name}'的任务"
                        }

                    logger.info(f"找到主任务: {main_task.id}, is_active: {main_task.is_active}")

                    # 查找已删除的子任务，使用模糊匹配处理空格问题
                    sub_task = session.query(TaskItem).filter(
                        TaskItem.daily_task_id == main_task.id,
                        TaskItem.is_active == False  # 查找已删除的任务
                    ).all()

                    # 手动匹配，处理空格问题
                    matched_sub_task = None
                    for task_item in sub_task:
                        if task_item.task_content.strip() == sub_task_content.strip():
                            matched_sub_task = task_item
                            break

                    sub_task = matched_sub_task

                    if not sub_task:
                        logger.warning(f"未找到已删除的子任务'{sub_task_content}'")
                        # 列出所有已删除的子任务用于调试
                        all_deleted_subtasks = session.query(TaskItem).filter(
                            TaskItem.daily_task_id == main_task.id,
                            TaskItem.is_active == False
                        ).all()
                        logger.info(f"主任务{main_task.id}下的所有已删除子任务:")
                        for item in all_deleted_subtasks:
                            logger.info(f"  - ID: {item.id}, 内容: '{item.task_content}'")

                        return {
                            "success": False,
                            "message": f"未找到已删除的子任务'{sub_task_content}'"
                        }
                else:
                    logger.error("必须提供子任务ID或者任务名称和子任务内容")
                    return {
                        "success": False,
                        "message": "必须提供子任务ID或者任务名称和子任务内容"
                    }

                logger.info(f"找到子任务: {sub_task.id}, 当前is_active: {sub_task.is_active}")

                # 恢复子任务（设置is_active为True）
                sub_task.is_active = True
                sub_task.updated_at = datetime.now()
                session.commit()

                logger.info(f"子任务恢复成功: {sub_task.id}")

                # 安全地获取任务名称
                try:
                    task_name_result = sub_task.daily_task.task_name if sub_task.daily_task else task_name
                except Exception as e:
                    logger.warning(f"获取主任务名称失败: {e}, 使用传入的task_name: {task_name}")
                    task_name_result = task_name

                return {
                    "success": True,
                    "message": "子任务恢复成功",
                    "data": {
                        "sub_task_id": sub_task.id,
                        "task_name": task_name_result,
                        "sub_task_content": sub_task.task_content
                    }
                }

        except Exception as e:
            logger.error(f"恢复子任务失败: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"恢复子任务失败: {str(e)}"
            }

    async def _save_to_timeseries_db(self, child_id: int, task_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        将任务数据保存到时序数据库

        Args:
            child_id: 学生ID
            task_data: 任务数据

        Returns:
            Dict: 保存结果，失败返回None
        """
        try:
            # 准备时序数据库的数据格式
            timeseries_data = {
                "child_id": child_id,
                "task_name": task_data.get('task_name', task_data.get('任务名称', '')),
                "time_slot": task_data.get('time_slot', task_data.get('时间段', '')),
                "subject": task_data.get('subject', '其他'),
                "customization": task_data.get('customization', task_data.get('定制方案', '')),
                "difficulty": task_data.get('difficulty', task_data.get('难点', '')),
                "solution": task_data.get('solution', task_data.get('解决方案', '')),
                "confidence_index": task_data.get('confidence_index', task_data.get('信心指数', 3)),
                "plan_date": datetime.now().isoformat(),
                "status": "pending",
                "notes": "来自AI任务生成确认"
            }

            # 处理子任务
            sub_tasks = task_data.get('sub_tasks', task_data.get('子任务', []))
            if isinstance(sub_tasks, list):
                formatted_sub_tasks = []
                for sub_task in sub_tasks:
                    if isinstance(sub_task, dict):
                        formatted_sub_tasks.append({
                            "task": sub_task.get('sub_task_name', sub_task.get('content', '')),
                            "source": "AI任务生成"
                        })
                    else:
                        formatted_sub_tasks.append({
                            "task": str(sub_task),
                            "source": "AI任务生成"
                        })
                timeseries_data["sub_tasks"] = formatted_sub_tasks

            # 调用时序数据库API - 使用配置的端口
            import os

            # 获取当前应用端口，默认为8014
            current_port = os.getenv("PORT", "8014")
            api_url = f"http://localhost:{current_port}/api/v1/planning/plans"

            logger.info(f"正在保存任务到时序数据库: {timeseries_data['task_name']}, URL: {api_url}")

            response = requests.post(
                api_url,
                json=timeseries_data,
                headers={"Content-Type": "application/json"},
                timeout=15  # 增加超时时间到15秒
            )

            if response.status_code == 200:
                result = response.json()
                logger.info(f"成功保存任务到时序数据库: {timeseries_data['task_name']}, Plan ID: {result.get('plan_id', 'Unknown')}")
                return result
            else:
                logger.error(f"保存到时序数据库失败: HTTP {response.status_code}, {response.text}")
                return None

        except requests.exceptions.Timeout:
            logger.error(f"保存到时序数据库超时: {task_data.get('task_name', 'Unknown')}")
            return None
        except requests.exceptions.ConnectionError:
            logger.error(f"无法连接到时序数据库服务: {task_data.get('task_name', 'Unknown')}")
            return None
        except Exception as e:
            logger.error(f"保存到时序数据库时发生错误: {e}, 任务: {task_data.get('task_name', 'Unknown')}")
            return None

    def _save_to_influxdb(self, task_data: Dict[str, Any]) -> bool:
        """
        将任务数据保存到InfluxDB

        Args:
            task_data: 任务数据

        Returns:
            bool: 保存是否成功
        """
        try:
            if not self.influxdb.check_connection():
                logger.warning("InfluxDB连接不可用，跳过InfluxDB存储")
                return False

            # 准备标签
            tags = {
                "child_id": str(task_data.get("child_id", "")),
                "subject": task_data.get("subject", "其他"),
                "status": task_data.get("status", "pending")
            }

            # 准备字段
            fields = {
                "task_name": task_data.get("task_name", ""),
                "time_slot": task_data.get("time_slot", ""),
                "description": task_data.get("description", ""),
                "customization": task_data.get("customization", ""),
                "difficulty": task_data.get("difficulty", ""),
                "solution": task_data.get("solution", ""),
                "confidence_index": task_data.get("confidence_index", 3),
                "total_points": task_data.get("total_points", 0),
                "completion_percentage": task_data.get("completion_percentage", 0.0)
            }

            # 处理子任务
            sub_tasks = task_data.get("task_items", [])
            if sub_tasks:
                import json
                fields["sub_tasks_count"] = len(sub_tasks)
                fields["sub_tasks_data"] = json.dumps([
                    {
                        "content": item.get("task_content", ""),
                        "source": item.get("task_source", ""),
                        "time_slot": item.get("time_slot", "")
                    } for item in sub_tasks
                ], ensure_ascii=False)
            else:
                fields["sub_tasks_count"] = 0
                fields["sub_tasks_data"] = "[]"

            # 写入InfluxDB
            success = self.influxdb.write_point(
                measurement=self.measurement,
                tags=tags,
                fields=fields,
                timestamp=datetime.now()
            )

            if success:
                logger.debug(f"成功将任务数据保存到InfluxDB: {task_data.get('task_name', 'Unknown')}")
            else:
                logger.warning(f"保存任务数据到InfluxDB失败: {task_data.get('task_name', 'Unknown')}")

            return success

        except Exception as e:
            logger.error(f"保存任务数据到InfluxDB时发生错误: {e}")
            return False

    async def _save_to_planning_db(self, child_id: int, task_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        将任务数据保存到时序数据库（使用planning接口）

        Args:
            child_id: 学生ID
            task_data: 任务数据

        Returns:
            Dict: 保存结果，包含plan_id等信息
        """
        try:
            import os

            # 获取当前应用端口，默认为8014
            current_port = os.getenv("PORT", "8014")
            api_url = f"http://localhost:{current_port}/api/v1/planning/plans"

            # 构建planning接口需要的数据格式
            planning_data = {
                "child_id": child_id,
                "task_name": task_data.get('task_name', task_data.get('任务名称', '')),
                "time_slot": task_data.get('time_slot', ''),
                "subject": task_data.get('subject', '其他'),
                "customization": task_data.get('customization', ''),
                "difficulty": task_data.get('difficulty', ''),
                "solution": task_data.get('solution', ''),
                "confidence_index": task_data.get('confidence_index', 3),
                "plan_date": datetime.now().isoformat(),
                "status": "pending",
                "notes": f"来源: 任务计划确认"
            }

            # 处理子任务
            sub_tasks = []
            task_items = task_data.get('task_items', [])
            if task_items:
                for item in task_items:
                    sub_task = {
                        "task": item.get('task_content', ''),
                        "source": item.get('task_source', '任务计划')
                    }
                    if sub_task["task"]:  # 只添加非空的子任务
                        sub_tasks.append(sub_task)

            planning_data["sub_tasks"] = sub_tasks

            logger.info(f"正在保存任务到时序数据库: {planning_data['task_name']}, URL: {api_url}")

            response = requests.post(
                api_url,
                json=planning_data,
                headers={"Content-Type": "application/json"},
                timeout=10  # 10秒超时
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    plan_id = result.get("plan_id")
                    logger.info(f"成功保存任务到时序数据库: {planning_data['task_name']}, Plan ID: {plan_id}")
                    return {
                        "plan_id": plan_id,
                        "task_name": planning_data['task_name'],
                        "timestamp": result.get("timestamp")
                    }
                else:
                    logger.error(f"保存到时序数据库失败: {result.get('message', '未知错误')}")
                    return None
            else:
                logger.error(f"保存到时序数据库HTTP错误: {response.status_code}, {response.text}")
                return None

        except requests.exceptions.Timeout:
            logger.error(f"保存到时序数据库超时: {task_data.get('task_name', 'Unknown')}")
            return None
        except Exception as e:
            logger.error(f"保存任务到时序数据库时发生错误: {e}")
            return None

    async def delete_task(self, task_id: int) -> Dict[str, Any]:
        """
        删除指定任务（软删除）

        Args:
            task_id: 任务ID

        Returns:
            Dict: 删除结果
        """
        try:
            with get_db_session_context() as session:
                # 查找任务
                task = session.query(DailyTask).filter(
                    DailyTask.id == task_id,
                    DailyTask.is_active == True
                ).first()

                if not task:
                    return {
                        "success": False,
                        "message": "任务不存在或已被删除"
                    }

                # 软删除任务
                task.is_active = False
                task.updated_at = datetime.now()

                # 软删除相关子任务
                sub_task_count = session.query(TaskItem).filter(
                    TaskItem.daily_task_id == task_id,
                    TaskItem.is_active == True
                ).update({
                    "is_active": False,
                    "updated_at": datetime.now()
                })

                session.commit()

                return {
                    "success": True,
                    "message": "任务删除成功",
                    "data": {
                        "task_id": task_id,
                        "task_name": task.task_name,
                        "deleted_subtasks": sub_task_count
                    }
                }

        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    async def restore_task(self, task_id: int) -> Dict[str, Any]:
        """
        撤销删除任务（将is_active改为True）

        Args:
            task_id: 任务ID

        Returns:
            Dict: 撤销删除结果
        """
        try:
            with get_db_session_context() as session:
                # 查找已删除的任务
                task = session.query(DailyTask).filter(
                    DailyTask.id == task_id,
                    DailyTask.is_active == False
                ).first()

                if not task:
                    return {
                        "success": False,
                        "message": "未找到已删除的任务"
                    }

                # 恢复任务
                task.is_active = True
                task.updated_at = datetime.now()

                # 恢复相关子任务
                sub_task_count = session.query(TaskItem).filter(
                    TaskItem.daily_task_id == task_id,
                    TaskItem.is_active == False
                ).update({
                    "is_active": True,
                    "updated_at": datetime.now()
                })

                session.commit()

                return {
                    "success": True,
                    "message": "任务恢复成功",
                    "data": {
                        "task_id": task_id,
                        "task_name": task.task_name,
                        "restored_subtasks": sub_task_count
                    }
                }

        except Exception as e:
            logger.error(f"恢复任务失败: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    async def get_deleted_tasks(self, child_id: int, days_back: int = 7) -> List[Dict[str, Any]]:
        """
        获取已删除的任务列表

        Args:
            child_id: 学生ID
            days_back: 回溯天数

        Returns:
            List[Dict]: 已删除的任务列表
        """
        try:
            with get_db_session_context() as session:
                start_date = date.today() - timedelta(days=days_back)

                deleted_tasks = session.query(DailyTask).filter(
                    DailyTask.child_id == child_id,
                    DailyTask.task_date >= datetime.combine(start_date, datetime.min.time()),
                    DailyTask.is_active == False
                ).order_by(DailyTask.updated_at.desc()).all()

                result = []
                for task in deleted_tasks:
                    task_dict = task.to_dict()

                    # 获取已删除的子任务
                    deleted_subtasks = session.query(TaskItem).filter(
                        TaskItem.daily_task_id == task.id,
                        TaskItem.is_active == False
                    ).all()

                    task_dict['deleted_subtasks'] = [item.to_dict() for item in deleted_subtasks]
                    result.append(task_dict)

                return result

        except Exception as e:
            logger.error(f"获取已删除任务失败: {e}")
            return []

