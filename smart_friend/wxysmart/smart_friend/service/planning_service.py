# 学习计划数据服务
import logging
import json
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from influxdb_client import Point

from core.planning.database.influxdb_connection import get_influxdb_manager

logger = logging.getLogger(__name__)

# OpenManus integration for enhanced planning
try:
    from .openmanus_service import get_openmanus_service, is_openmanus_available
    OPENMANUS_INTEGRATION = True
    logger.info("✅ OpenManus integration available in PlanningService")
except ImportError:
    OPENMANUS_INTEGRATION = False
    logger.info("⚠️ OpenManus integration not available in PlanningService")


class PlanningService:
    """学习计划数据服务类"""
    
    def __init__(self):
        self.influxdb = get_influxdb_manager()
        self.measurement = "study_plans"
        logger.info("PlanningService 初始化完成")
        logger.debug(f"使用 measurement: {self.measurement}")
    
    def create_plan(self, child_id: int, plan_data: Dict[str, Any]) -> Optional[str]:
        """创建学习计划

        Args:
            child_id: 学生ID
            plan_data: 计划数据

        Returns:
            str: 计划ID，失败返回None
        """
        start_time = datetime.now(timezone.utc)
        logger.info(f"开始创建学习计划 - 学生ID: {child_id}, 学科: {plan_data.get('subject', 'N/A')}, 任务: {plan_data.get('task_name', 'N/A')}")
        logger.debug(f"计划数据详情: {plan_data}")

        try:
            # 生成唯一计划ID
            plan_id = str(uuid.uuid4())
            logger.debug(f"生成计划ID: {plan_id}")
            
            # 准备标签（用于索引和分组）
            tags = {
                "plan_id": plan_id,
                "child_id": str(child_id),
                "subject": plan_data.get("subject", ""),
                "status": plan_data.get("status", "pending"),
                "date": plan_data.get("plan_date", datetime.now(timezone.utc)).strftime("%Y-%m-%d")
            }
            logger.debug(f"准备标签数据: {tags}")
            
            # 准备字段（实际的数据）
            fields = {
                "task_name": plan_data.get("task_name", ""),
                "time_slot": plan_data.get("time_slot", ""),
                "sub_tasks": json.dumps(plan_data.get("sub_tasks", []), ensure_ascii=False),
                "customization": plan_data.get("customization", ""),
                "difficulty": plan_data.get("difficulty", ""),
                "solution": plan_data.get("solution", ""),
                "confidence_index": plan_data.get("confidence_index", 0),
                "notes": plan_data.get("notes", ""),
                "created_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
            logger.debug(f"准备字段数据: {fields}")
            
            # 设置时间戳
            timestamp = plan_data.get("plan_date")
            if timestamp and isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            elif not timestamp:
                timestamp = datetime.now(timezone.utc)
            logger.debug(f"设置时间戳: {timestamp}")

            # 写入数据
            logger.debug(f"开始写入数据到 InfluxDB - measurement: {self.measurement}")
            success = self.influxdb.write_point(
                measurement=self.measurement,
                tags=tags,
                fields=fields,
                timestamp=timestamp
            )

            # 计算操作耗时
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000

            if success:
                logger.info(f"✅ 成功创建学习计划 - 学生ID: {child_id}, 计划ID: {plan_id}, 学科: {plan_data.get('subject', 'N/A')}, 耗时: {duration_ms:.2f}ms")
                return plan_id
            else:
                logger.error(f"❌ 创建学习计划失败 - 学生ID: {child_id}, 学科: {plan_data.get('subject', 'N/A')}, 耗时: {duration_ms:.2f}ms")
                return None

        except Exception as e:
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000
            logger.error(f"❌ 创建学习计划时发生异常 - 学生ID: {child_id}, 学科: {plan_data.get('subject', 'N/A')}, 错误: {e}, 耗时: {duration_ms:.2f}ms")
            return None
    
    def get_plans(self, child_id: int, subject: Optional[str] = None,
                  start_date: Optional[datetime] = None,
                  end_date: Optional[datetime] = None,
                  status: Optional[str] = None,
                  limit: int = 100) -> List[Dict[str, Any]]:
        """获取学习计划
        Args:
            child_id: 学生ID
            subject: 学科（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
            status: 计划状态（可选）
            limit: 返回记录数限制

        Returns:
            List[Dict]: 计划列表
        """
        start_time = datetime.now(timezone.utc)
        logger.info(f"开始获取学习计划 - 学生ID: {child_id}, 学科: {subject}, 状态: {status}, 限制: {limit}")
        logger.debug(f"查询参数 - 开始日期: {start_date}, 结束日期: {end_date}")

        try:
            # 构建Flux查询
            query_parts = [
                f'from(bucket: "daily_learning")',
                f'|> range(start: {start_date.isoformat() if start_date else "-30d"}, stop: {end_date.isoformat() if end_date else "now()"})',
                f'|> filter(fn: (r) => r._measurement == "{self.measurement}")',
                f'|> filter(fn: (r) => r.child_id == "{child_id}")'
            ]
            
            if subject:
                query_parts.append(f'|> filter(fn: (r) => r.subject == "{subject}")')
            
            if status:
                query_parts.append(f'|> filter(fn: (r) => r.status == "{status}")')
            
            query_parts.extend([
                '|> sort(columns: ["_time"], desc: true)',
                f'|> limit(n: {limit})'
            ])
            
            flux_query = '\n  '.join(query_parts)
            logger.debug(f"构建的 Flux 查询: {flux_query}")

            # 执行查询
            logger.debug("开始执行 InfluxDB 查询")
            raw_data = self.influxdb.query_data(flux_query)
            logger.debug(f"查询返回原始数据条数: {len(raw_data) if raw_data else 0}")

            # 处理查询结果
            plans = self._process_query_results(raw_data)

            # 计算操作耗时
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000

            logger.info(f"✅ 成功获取学习计划 - 学生ID: {child_id}, 返回计划数: {len(plans)}, 耗时: {duration_ms:.2f}ms")
            return plans

        except Exception as e:
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000
            logger.error(f"❌ 获取学习计划时发生异常 - 学生ID: {child_id}, 错误: {e}, 耗时: {duration_ms:.2f}ms")
            return []
    
    def get_plan_by_id(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """根据计划ID获取单个计划

        Args:
            plan_id: 计划ID

        Returns:
            Dict: 计划数据，未找到返回None
        """
        start_time = datetime.now(timezone.utc)
        logger.info(f"开始获取单个计划 - 计划ID: {plan_id}")

        try:
            flux_query = f'''
                from(bucket: "daily_learning")
                |> range(start: -365d)
                |> filter(fn: (r) => r._measurement == "{self.measurement}")
                |> filter(fn: (r) => r.plan_id == "{plan_id}")
                |> sort(columns: ["_time"], desc: true)
                |> limit(n: 1)
            '''
            
            logger.debug(f"执行查询获取计划: {plan_id}")
            raw_data = self.influxdb.query_data(flux_query)
            plans = self._process_query_results(raw_data)

            # 计算操作耗时
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000

            if plans:
                logger.info(f"✅ 成功找到计划 - 计划ID: {plan_id}, 耗时: {duration_ms:.2f}ms")
                return plans[0]
            else:
                logger.warning(f"⚠️ 未找到计划 - 计划ID: {plan_id}, 耗时: {duration_ms:.2f}ms")
                return None

        except Exception as e:
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000
            logger.error(f"❌ 获取计划时发生异常 - 计划ID: {plan_id}, 错误: {e}, 耗时: {duration_ms:.2f}ms")
            return None
    
    def update_plan(self, plan_id: str, update_data: Dict[str, Any]) -> bool:
        """更新学习计划

        Args:
            plan_id: 计划ID
            update_data: 更新数据

        Returns:
            bool: 是否成功更新
        """
        start_time = datetime.now(timezone.utc)
        logger.info(f"开始更新学习计划 - 计划ID: {plan_id}")
        logger.debug(f"更新数据: {update_data}")

        try:
            # 先获取现有计划
            logger.debug(f"获取现有计划: {plan_id}")
            existing_plan = self.get_plan_by_id(plan_id)
            if not existing_plan:
                logger.error(f"❌ 计划不存在，无法更新 - 计划ID: {plan_id}")
                return False
            
            # 合并更新数据
            logger.debug("合并更新数据到现有计划")
            updated_plan = existing_plan.copy()
            updated_plan.update(update_data)
            updated_plan["updated_at"] = datetime.now(timezone.utc).isoformat()
            logger.debug(f"合并后的计划数据: {updated_plan}")
            
            # 准备标签
            tags = {
                "plan_id": plan_id,
                "child_id": str(updated_plan.get("child_id", "")),
                "subject": updated_plan.get("subject", ""),
                "status": updated_plan.get("status", "pending"),
                "date": updated_plan.get("plan_date", datetime.now(timezone.utc)).strftime("%Y-%m-%d")
            }
            
            # 准备字段
            fields = {
                "task_name": updated_plan.get("task_name", ""),
                "time_slot": updated_plan.get("time_slot", ""),
                "sub_tasks": json.dumps(updated_plan.get("sub_tasks", []), ensure_ascii=False),
                "customization": updated_plan.get("customization", ""),
                "difficulty": updated_plan.get("difficulty", ""),
                "solution": updated_plan.get("solution", ""),
                "confidence_index": updated_plan.get("confidence_index", 0),
                "notes": updated_plan.get("notes", ""),
                "created_at": updated_plan.get("created_at", ""),
                "updated_at": updated_plan["updated_at"]
            }
            
            # 写入更新数据
            logger.debug("开始写入更新数据到 InfluxDB")
            success = self.influxdb.write_point(
                measurement=self.measurement,
                tags=tags,
                fields=fields,
                timestamp=datetime.now(timezone.utc)
            )

            # 计算操作耗时
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000

            if success:
                logger.info(f"✅ 成功更新学习计划 - 计划ID: {plan_id}, 耗时: {duration_ms:.2f}ms")
            else:
                logger.error(f"❌ 更新学习计划失败 - 计划ID: {plan_id}, 耗时: {duration_ms:.2f}ms")

            return success

        except Exception as e:
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000
            logger.error(f"❌ 更新学习计划时发生异常 - 计划ID: {plan_id}, 错误: {e}, 耗时: {duration_ms:.2f}ms")
            return False
    
    def delete_plans(self, child_id: int,
                    plan_id: Optional[str] = None,
                    start_date: Optional[datetime] = None,
                    end_date: Optional[datetime] = None,
                    subject: Optional[str] = None) -> bool:
        """删除学习计划

        Args:
            child_id: 学生ID
            plan_id: 计划ID（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
            subject: 学科（可选）

        Returns:
            bool: 是否成功删除
        """
        start_time = datetime.now(timezone.utc)
        logger.info(f"开始删除学习计划 - 学生ID: {child_id}, 计划ID: {plan_id}, 学科: {subject}")
        logger.debug(f"删除条件 - 开始日期: {start_date}, 结束日期: {end_date}")

        try:
            # 构建删除条件
            predicate_parts = [f'child_id="{child_id}"']

            if plan_id:
                predicate_parts.append(f'plan_id="{plan_id}"')

            if subject:
                predicate_parts.append(f'subject="{subject}"')

            predicate = ' AND '.join(predicate_parts)
            logger.debug(f"删除条件谓词: {predicate}")
            
            # 设置时间范围
            if not start_date:
                start_date = datetime.now(timezone.utc) - timedelta(days=365)
            if not end_date:
                end_date = datetime.now(timezone.utc)
            logger.debug(f"删除时间范围: {start_date} 到 {end_date}")

            # 执行删除
            logger.debug("开始执行 InfluxDB 删除操作")
            success = self.influxdb.delete_data(
                start=start_date,
                stop=end_date,
                predicate=predicate
            )

            # 计算操作耗时
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000

            if success:
                logger.info(f"✅ 成功删除学习计划 - 学生ID: {child_id}, 计划ID: {plan_id}, 学科: {subject}, 耗时: {duration_ms:.2f}ms")
            else:
                logger.error(f"❌ 删除学习计划失败 - 学生ID: {child_id}, 计划ID: {plan_id}, 学科: {subject}, 耗时: {duration_ms:.2f}ms")

            return success

        except Exception as e:
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000
            logger.error(f"❌ 删除学习计划时发生异常 - 学生ID: {child_id}, 错误: {e}, 耗时: {duration_ms:.2f}ms")
            return False
    
    def get_plan_statistics(self, child_id: int,
                           days: int = 7,
                           subject: Optional[str] = None) -> Dict[str, Any]:
        """获取计划统计信息

        Args:
            child_id: 学生ID
            days: 统计天数
            subject: 学科（可选）

        Returns:
            Dict: 统计信息
        """
        start_time = datetime.now(timezone.utc)
        logger.info(f"开始获取计划统计信息 - 学生ID: {child_id}, 统计天数: {days}, 学科: {subject}")

        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days)
            logger.debug(f"统计时间范围: {start_date} 到 {end_date}")

            plans = self.get_plans(
                child_id=child_id,
                subject=subject,
                start_date=start_date,
                end_date=end_date,
                limit=1000
            )
            logger.debug(f"获取到 {len(plans)} 个计划用于统计")
            
            if not plans:
                logger.info(f"📊 计划统计结果 - 学生ID: {child_id}, 无计划数据")
                return {
                    "total_plans": 0,
                    "completed_plans": 0,
                    "pending_plans": 0,
                    "in_progress_plans": 0,
                    "completion_rate": 0,
                    "subjects_distribution": {},
                    "average_confidence": 0,
                    "period_days": days
                }
            
            # 计算统计信息
            logger.debug("开始计算统计信息")
            total_plans = len(plans)
            completed_plans = len([p for p in plans if p.get("status") == "completed"])
            pending_plans = len([p for p in plans if p.get("status") == "pending"])
            in_progress_plans = len([p for p in plans if p.get("status") == "in_progress"])

            completion_rate = (completed_plans / total_plans * 100) if total_plans > 0 else 0

            # 学科分布
            subjects_distribution = {}
            for plan in plans:
                subject_name = plan.get("subject", "未知")
                subjects_distribution[subject_name] = subjects_distribution.get(subject_name, 0) + 1

            # 平均信心指数
            confidence_values = [p.get("confidence_index", 0) for p in plans if p.get("confidence_index")]
            average_confidence = sum(confidence_values) / len(confidence_values) if confidence_values else 0

            # 计算操作耗时
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000

            statistics = {
                "total_plans": total_plans,
                "completed_plans": completed_plans,
                "pending_plans": pending_plans,
                "in_progress_plans": in_progress_plans,
                "completion_rate": completion_rate,
                "subjects_distribution": subjects_distribution,
                "average_confidence": average_confidence,
                "period_days": days
            }

            logger.info(f"📊 计划统计结果 - 学生ID: {child_id}, 总计划: {total_plans}, 完成率: {completion_rate:.1f}%, 耗时: {duration_ms:.2f}ms")
            logger.debug(f"详细统计信息: {statistics}")

            return statistics

        except Exception as e:
            end_time = datetime.now(timezone.utc)
            duration_ms = (end_time - start_time).total_seconds() * 1000
            logger.error(f"❌ 获取计划统计信息时发生异常 - 学生ID: {child_id}, 错误: {e}, 耗时: {duration_ms:.2f}ms")
            return {}
    
    def _process_query_results(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理查询结果，将InfluxDB的原始数据转换为更友好的格式"""
        logger.debug(f"开始处理查询结果，原始数据条数: {len(raw_data) if raw_data else 0}")
        processed_plans = {}
        
        for record in raw_data:
            plan_id = record['tags'].get('plan_id', 'unknown')
            
            if plan_id not in processed_plans:
                processed_plans[plan_id] = {
                    'plan_id': plan_id,
                    'timestamp': record['time'],
                    'child_id': int(record['tags'].get('child_id', 0)),
                    'subject': record['tags'].get('subject', ''),
                    'status': record['tags'].get('status', ''),
                    'date': record['tags'].get('date', ''),
                    'plan_date': record['time']
                }
            
            # 添加字段值
            field_name = record['field']
            field_value = record['value']
            
            # 特殊处理JSON字段
            if field_name == 'sub_tasks' and isinstance(field_value, str):
                try:
                    processed_plans[plan_id][field_name] = json.loads(field_value)
                except json.JSONDecodeError:
                    logger.warning(f"解析子任务JSON失败 - 计划ID: {plan_id}, 值: {field_value}")
                    processed_plans[plan_id][field_name] = []
            else:
                processed_plans[plan_id][field_name] = field_value

        result = list(processed_plans.values())
        logger.debug(f"查询结果处理完成，返回 {len(result)} 个计划")
        return result

    # OpenManus Enhancement Methods (Added without removing existing code)
    def openmanus_enhanced_create_plan(self, child_id: int, objective: str, resources: str = "", context: str = "") -> Optional[str]:
        """
        Create enhanced learning plan using OpenManus with fallback to original method

        Args:
            child_id: Student ID
            objective: Learning objective
            resources: Available resources
            context: Additional context

        Returns:
            Plan ID if successful, None otherwise
        """
        if OPENMANUS_INTEGRATION and is_openmanus_available():
            try:
                openmanus_service = get_openmanus_service()
                result = openmanus_service.create_task_plan(objective, resources, context)

                if result.get('success'):
                    logger.info("✅ Using OpenManus enhanced plan creation")

                    # Convert OpenManus plan to wxysmart format
                    plan_data = {
                        'title': f"OpenManus Enhanced Plan: {objective}",
                        'description': result.get('plan', {}).get('detailed_plan', objective),
                        'objective': objective,
                        'resources': resources,
                        'context': context,
                        'enhanced_by': 'openmanus',
                        'plan_details': result.get('plan', {}),
                        'status': result.get('status', 'active'),
                        'created_at': datetime.now().isoformat()
                    }

                    # Use original create_plan method to store in database
                    return self.create_plan(child_id, plan_data)
                else:
                    logger.warning("⚠️ OpenManus plan creation failed, using fallback")
            except Exception as e:
                logger.error(f"❌ OpenManus plan creation error: {e}, using fallback")

        # Fallback to original planning method
        logger.info("🔄 Using standard planning method")
        plan_data = {
            'title': f"Learning Plan: {objective}",
            'description': objective,
            'objective': objective,
            'resources': resources,
            'context': context,
            'enhanced_by': 'standard',
            'status': 'active',
            'created_at': datetime.now().isoformat()
        }

        return self.create_plan(child_id, plan_data)

    def openmanus_analyze_plan_progress(self, child_id: int, plan_id: str) -> Dict[str, Any]:
        """
        Analyze plan progress using OpenManus insights

        Args:
            child_id: Student ID
            plan_id: Plan ID

        Returns:
            Dict containing progress analysis
        """
        # Get existing plan data
        plans = self.get_plans_by_child(child_id)
        current_plan = None

        for plan in plans:
            if plan.get('plan_id') == plan_id:
                current_plan = plan
                break

        if not current_plan:
            return {
                'success': False,
                'message': 'Plan not found'
            }

        if OPENMANUS_INTEGRATION and is_openmanus_available():
            try:
                openmanus_service = get_openmanus_service()

                # Create analysis prompt
                analysis_prompt = f"""Analyze the progress of this learning plan:

Plan Title: {current_plan.get('title', 'Unknown')}
Objective: {current_plan.get('objective', 'Unknown')}
Status: {current_plan.get('status', 'Unknown')}
Created: {current_plan.get('created_at', 'Unknown')}

Please provide:
1. Progress assessment
2. Areas of strength
3. Areas needing improvement
4. Recommendations for next steps
5. Motivational feedback for the child

Keep the analysis encouraging and age-appropriate."""

                result = openmanus_service.process_user_input(analysis_prompt)

                if result.get('success'):
                    logger.info("✅ Using OpenManus progress analysis")
                    return {
                        'success': True,
                        'analysis': result.get('message', ''),
                        'summary': result.get('summary', ''),
                        'enhanced_by': 'openmanus',
                        'plan_data': current_plan
                    }
            except Exception as e:
                logger.error(f"❌ OpenManus progress analysis error: {e}")

        # Fallback to basic analysis
        logger.info("🔄 Using basic progress analysis")
        return {
            'success': True,
            'analysis': f"Plan '{current_plan.get('title', 'Unknown')}' is currently {current_plan.get('status', 'active')}. Keep up the good work!",
            'summary': 'Basic progress summary',
            'enhanced_by': 'standard',
            'plan_data': current_plan
        }

    def get_openmanus_planning_suggestions(self, child_id: int, subject: str = "") -> Dict[str, Any]:
        """
        Get OpenManus-powered planning suggestions for a child

        Args:
            child_id: Student ID
            subject: Optional subject focus

        Returns:
            Dict containing planning suggestions
        """
        if OPENMANUS_INTEGRATION and is_openmanus_available():
            try:
                openmanus_service = get_openmanus_service()

                # Get existing plans for context
                existing_plans = self.get_plans_by_child(child_id)
                context = f"Student has {len(existing_plans)} existing plans."

                suggestion_prompt = f"""Provide learning plan suggestions for a child student.

Subject focus: {subject if subject else 'General learning'}
Existing plans context: {context}

Please suggest:
1. 3-5 specific learning objectives
2. Age-appropriate activities
3. Time management tips
4. Fun learning methods
5. Progress tracking ideas

Make suggestions engaging and suitable for children."""

                result = openmanus_service.process_user_input(suggestion_prompt)

                if result.get('success'):
                    logger.info("✅ Using OpenManus planning suggestions")
                    return {
                        'success': True,
                        'suggestions': result.get('message', ''),
                        'summary': result.get('summary', ''),
                        'enhanced_by': 'openmanus',
                        'subject': subject,
                        'existing_plans_count': len(existing_plans)
                    }
            except Exception as e:
                logger.error(f"❌ OpenManus suggestions error: {e}")

        # Fallback to basic suggestions
        logger.info("🔄 Using basic planning suggestions")
        basic_suggestions = [
            "Set clear daily learning goals",
            "Create a consistent study schedule",
            "Use visual aids and interactive materials",
            "Take regular breaks during study sessions",
            "Celebrate small achievements"
        ]

        return {
            'success': True,
            'suggestions': "\n".join([f"{i+1}. {suggestion}" for i, suggestion in enumerate(basic_suggestions)]),
            'summary': 'Basic planning suggestions provided',
            'enhanced_by': 'standard',
            'subject': subject
        }
