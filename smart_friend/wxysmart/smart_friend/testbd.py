#!/usr/bin/env python3
"""
坐姿检测程序
基于body_detection库的StandalonePoseAnalyzer进行坐姿分析
"""

import cv2
import sys
import os

# 导入body_detection模块
try:
    from body_detection import StandalonePoseAnalyzer
    print("✅ 成功导入 StandalonePoseAnalyzer")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在lyk虚拟环境下运行: conda activate lyk")
    print("并确保body_detection库已正确安装")
    sys.exit(1)
def main():
    """主函数"""
    print("🔍 坐姿检测程序启动...")    
    # 读取图像
    image_path = "2.jpg"
    print(f"📖 读取图像: {image_path}")
    frame = cv2.imread(image_path)
    if frame is None:
        print(f"❌ 无法读取图像文件: {image_path}")
        print("请确保图像文件存在且格式正确")
        sys.exit(1)
    
    print(f"✅ 图像读取成功，尺寸: {frame.shape}")
    
    # 创建坐姿分析器
    print("🔧 初始化坐姿分析器...")
    analyzer = StandalonePoseAnalyzer(
        head_tilt_threshold=0.13,        # 头部倾斜阈值
        shoulder_level_threshold=0.25,   # 肩膀水平阈值
        strict_shoulder_threshold=0.1,   # 严格肩膀阈值
        shoulder_symmetry_min=0.8,       # 肩膀对称性最小值
        shoulder_symmetry_max=1.2,       # 肩膀对称性最大值
        body_straightness_factor=1.3     # 身体挺直系数
    )
    
    # 执行坐姿分析
    print("🔍 开始坐姿分析...")
    result, annotated_image = analyzer.analyze_posture_from_image_with_visualization(frame)
    print(result)
if __name__ == "__main__":
    main()
