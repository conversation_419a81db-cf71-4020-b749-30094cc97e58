把每个接口的出参入参，使用方法用简短的文字描述清楚

# 1.家长管理：/api/v1/user-management/parents/

## 1.1创建家长Create Parent

### 请求方式：POST

### Request  URL：/api/v1/user-management/parents/

### 入参（Request Parameters）

| 字段名         | 描述   | 类型         | 是否必填 | 说明                 |
| ----------- | ---- | ---------- | ---- | ------------------ |
| name        | 家长姓名 | str        | 必填   | max\_length=100    |
| gender      | 性别   | GenderEnum | 可选   | 固定值枚举（female，male） |
| age         | 年龄   | int        | 可选   | ge=18, le=100      |
| phone       | 手机号码 | str        | 可选   | max\_length=20     |
| email       | 邮箱地址 | str        | 可选   | max\_length=100    |
| wechat\_id  | 微信号  | str        | 可选   | max\_length=50     |
| notes       | 备注信息 | str        | 可选   | 无                  |
| id          |      | int        |      | 系统自动生成的家长 ID       |
| is\_active  |      | bool       |      | 是否处于启用状态           |
| created\_at |      | string     |      | 创建时间（ISO 时间格式）     |
| updated\_at |      | string     |      | 更新时间（ISO 时间格式）     |

### 出参（Response Parameters）：包含家长信息的json

### 示例（Examples）：完整的请求/响应示例

```bash
curl -X 'POST' \
  'http://localhost:8011/api/v1/user-management/parents/' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "张三",
  "gender": "male",
  "age": 38,
  "phone": "11123",
  "email": "234",
  "wechat_id": "454444",
  "notes": "无"
}'
```

```json
{
  "name": "张三",
  "gender": "male",
  "age": 38,
  "phone": "11123",
  "email": "234",
  "wechat_id": "454444",
  "notes": "无",
  "id": 9,
  "is_active": true,
  "created_at": "2025-07-01T03:33:13",
  "updated_at": "2025-07-01T03:33:13"
}
```

## 1.2获取家长列表Get Parent

### 请求方式：GET

### Request  URL：/api/v1/user-management/parents/

### 入参（Request Parameters）

| 字段名        | 描述         | 类型               | 是否必填 | 说明            | 入参类型  |
| ---------- | ---------- | ---------------- | ---- | ------------- | ----- |
| skip       | 跳过的记录数     | int              | 必填   | 分页用           | query |
| limit      | 返回记录数      | int              | 可选   | ge=1, le=1000 | query |
| is\_active | 是否只返回激活的家长 | bool（true/false） | 可选   |               | query |

### 出参（Response Parameters）：包含家长信息的json

### 示例（Examples）：完整的请求/响应示例

```bash
curl -X 'GET' \
  'http://localhost:8011/api/v1/user-management/parents/?skip=0&limit=100&is_active=true' \
  -H 'accept: application/json'
```

```json
[
  {
    "name": "王建国",
    "gender": null,
    "age": null,
    "phone": "13800138001",
    "email": "<EMAIL>",
    "wechat_id": null,
    "notes": "职业: 工程师, 学历: 本科, 教育方式: 严格型, 关注点: 希望孩子数学成绩提高,担心注意力不集中",
    "id": 3,
    "is_active": true,
    "created_at": "2025-06-17T09:05:49",
    "updated_at": "2025-06-17T09:05:49"
  },
  {
    "name": "李美丽",
    "gender": null,
    "age": null,
    "phone": "13800138002",
    "email": "<EMAIL>",
    "wechat_id": null,
    "notes": "职业: 教师, 学历: 硕士, 教育方式: 温和型, 关注点: 希望孩子全面发展,注重阅读习惯培养",
    "id": 4,
    "is_active": true,
    "created_at": "2025-06-17T09:06:41",
    "updated_at": "2025-06-17T09:06:41"
  } 
]
```



## 1.3获取单个家长Get Parent（by parent\_id）

### 请求方式：GET

### Request  URL：/api/v1/user-management/parents/{parent\_id}

### 入参（Request Parameters）

| 字段名        | 描述   | 类型  | 是否必填 | 说明 | 入参类型 |
| ---------- | ---- | --- | ---- | -- | ---- |
| parent\_id | 家长ID | int | 必填   |    | path |

### 出参（Response Parameters）：包含家长信息的json

### 示例（Examples）：完整的请求/响应示例

```bash
curl -X 'GET' \
  'http://localhost:8011/api/v1/user-management/parents/4' \
  -H 'accept: application/json'
```

```json
{
  "name": "李美丽",
  "gender": null,
  "age": null,
  "phone": "13800138002",
  "email": "<EMAIL>",
  "wechat_id": null,
  "notes": "职业: 教师, 学历: 硕士, 教育方式: 温和型, 关注点: 希望孩子全面发展,注重阅读习惯培养",
  "id": 4,
  "is_active": true,
  "created_at": "2025-06-17T09:06:41",
  "updated_at": "2025-06-17T09:06:41"
}
```

## 1.4更新家长信息Update Parent（by parent\_id）

### 请求方式：PUT

### Request  URL：/api/v1/user-management/parents/{parent\_id}

### 入参（Request Parameters）

| 字段名          | 描述   | 类型  | 是否必填 | 说明                        | 入参类型 |
| ------------ | ---- | --- | ---- | ------------------------- | ---- |
| parent\_id   | 家长ID | int | 必填   |                           | path |
| parent\_data |      |     |      | 只提供要更新的字段（姓名、性别、年龄......） | body |

### 出参（Response Parameters）：修改后的家长信息的json

### 示例（Examples）：完整的请求/响应示例

```bash
curl -X 'PUT' \
  'http://localhost:8011/api/v1/user-management/parents/5' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "age": 40,
  "wechat_id": "string123455"
}'
```

```json
{
  "name": "张强",
  "gender": "male",
  "age": 40,
  "phone": "13800138003",
  "email": "<EMAIL>",
  "wechat_id": "string123455",
  "notes": "医生，民主型教育方式",
  "id": 5,
  "is_active": true,
  "created_at": "2025-06-17T09:07:38",
  "updated_at": "2025-07-01T13:57:46.613566"
}
```



## 1.5软删除家长（将is\_activate设为false）Delete Parent（by parent\_id）

### 请求方式：DELETE

### Request  URL：/api/v1/user-management/parents/{parent\_id}

### 入参（Request Parameters）

| 字段名        | 描述   | 类型  | 是否必填 | 说明 | 入参类型 |
| ---------- | ---- | --- | ---- | -- | ---- |
| parent\_id | 家长ID | int | 必填   |    | path |

### 出参（Response Parameters）：204 No Content

### 示例（Examples）：完整的请求/响应示例

```bash
curl -X 'DELETE' \
  'http://localhost:8011/api/v1/user-management/parents/1' \
  -H 'accept: */*'
```

```json
204 No Content
```

## 1.6获取家长的所有小孩Get Parent Children（by parent\_id）

### 请求方式：GET

### Request  URL：/api/v1/user-management/parents/{parent\_id}/children

### 入参（Request Parameters）

| 字段名        | 描述   | 类型  | 是否必填 | 说明 | 入参类型 |
| ---------- | ---- | --- | ---- | -- | ---- |
| parent\_id | 家长ID | int | 必填   |    | path |

### 出参（Response Parameters）：家长对应的小孩信息json

### 示例（Examples）：完整的请求/响应示例

```bash
curl -X 'GET' \
  'http://localhost:8011/api/v1/user-management/parents/4/children' \
  -H 'accept: application/json'
```

```json
[
  {
    "id": 3,
    "name": "小红",
    "nickname": "红红",
    "gender": null,
    "age": 7,
    "academic_level": "优秀",
    "school_name": null
  }
]
```

# 2.家长小孩关系管理

## 2.1创建家长小孩关系Create Relationship

### 请求方式：POST

### Request  URL：/api/v1/user-management/relationships/

### 入参（Request Parameters）

| 字段名                  | 描述       | 类型               | 是否必填        | 说明   | 入参类型 |
| -------------------- | -------- | ---------------- | ----------- | ---- | ---- |
| child\_id            | 小孩ID     | int              | 必填          |      | body |
| parent\_id           | 家长ID     | int              | 必填          |      | body |
| relationship\_type   | 关系类型     | RelationshipEnum | 必填          | 枚举类型 | body |
| is\_primary\_contact | 是否为主要联系人 | bool             | 可选（默认false） |      | body |
| notes                | 关系备注     | str              | 可选          |      | body |

### 出参（Response Parameters）：家长-小孩对应关系json

### 示例（Examples）：完整的请求/响应示例

```bash
curl -X 'POST' \
  'http://localhost:8011/api/v1/user-management/relationships/' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "child_id": 7,
  "parent_id": 7,
  "relationship_type": "father",
  "is_primary_contact": false,
  "notes": "string"
}'
```

```json
{
  "child_id": 7,
  "parent_id": 7,
  "relationship_type": "father",
  "is_primary_contact": false,
  "notes": "string",
  "id": 7,
  "created_at": "2025-07-01T06:48:31"
}
```



## 2.2软删除家长小孩关系（将is\_activate设为false）Delete Relationship

### 请求方式：DELETE

### Request  URL：/api/v1/user-management/relationships/{relationship\_id}

### 入参（Request Parameters）

| 字段名               | 描述   | 类型  | 是否必填 | 说明 | 入参类型 |
| ----------------- | ---- | --- | ---- | -- | ---- |
|  relationship\_id | 关系ID | int | 必填   |    | path |

### 出参（Response Parameters）：204 No Content

### 示例（Examples）：完整的请求/响应示例

```bash
curl -X 'DELETE' \
  'http://localhost:8011/api/v1/user-management/relationships/1' \
  -H 'accept: */*'
```

```plain&#x20;text
204 No Content
```

## 2.3获取指定小孩的所有家长Get Child Parents

### 请求方式：GET

### Request  URL：/api/v1/user-management/relationships/child/{child\_id}/parents

### 入参（Request Parameters）

| 字段名       | 描述   | 类型  | 是否必填 | 说明 | 入参类型 |
| --------- | ---- | --- | ---- | -- | ---- |
| child\_id | 小孩ID | int | 必填   |    | path |

### 出参（Response Parameters）：指定小孩的所有家长信息json

### 示例（Examples）：完整的请求/响应示例

```plain&#x20;text
curl -X 'GET' \
  'http://localhost:8011/api/v1/user-management/relationships/child/4/parents' \
  -H 'accept: application/json'
```

```json
[
  {
    "id": 5,
    "name": "张强",
    "gender": "male",
    "age": 40,
    "phone": "13800138003",
    "email": "<EMAIL>",
    "wechat_id": "string123455"
  }
]
```

## 2.4获取指定家长的所有小孩Get Parent Children【功能与1.6重复】

### 请求方式：GET

### Request  URL：/api/v1/user-management/relationships/parent/{parent\_id}/children

### 入参（Request Parameters）

| 字段名        | 描述   | 类型  | 是否必填 | 说明 | 入参类型 |
| ---------- | ---- | --- | ---- | -- | ---- |
| parent\_id | 家长ID | int | 必填   |    | path |

### 出参（Response Parameters）：家长所有小孩信息json

### 示例（Examples）：完整的请求/响应示例

```plain&#x20;text
curl -X 'GET' \
  'http://localhost:8011/api/v1/user-management/relationships/parent/5/children' \
  -H 'accept: application/json'
```

```json
[
  {
    "id": 4,
    "name": "小刚",
    "nickname": "刚刚",
    "gender": "male",
    "age": 9,
    "academic_level": "primary_3",
    "school_name": "北京市第三小学"
  }
]
```



# 3.小孩管理：

## 1、创建小孩信息进入数据库：

api：/api/v1/user-management/children/

数据表：

## 2、从数据库中获取小孩信息

api: /api/v1/user-management/children/





# 4.用户日志记录

## 1、创建用户操作记录：

**API：/api/v1/user-plan-actions/actions**

| 字段名称            | 数据类型                         | 操作说明                       |
| --------------- | ---------------------------- | -------------------------- |
| user\_id        | int                          | 有一个外键约束，引用自children表中的id字段 |
| table\_id       | string(这里要输入的是数字，设置的数据类型有问题） | 表id                        |
| user\_operation | string                       | 用户操作                       |

## 2、获取用户操作历史

**API: /api/v1/user-plan-actions/actions/{user\_id}**

返回指定用户的操作历史记录，按操作时间倒序排列

**输入参数：**

| 字段名称     | 数据类型 | 说明            |
| -------- | ---- | ------------- |
| user\_id | int  | 孩子id          |
| limit    | int  | max:200,min:1 |

**输出：**

```json
[
  {
    "id": 13,
    "table_id": "4",
    "operation_time": "2025-06-26T14:46:12.325722",
    "user_operation": "修改子任务 写一个作文。 （所属任务 语文作业）的 结束 时间为 14:41",
    "user_id": 4
  }
 ]
```

## 3、获取表历史操作：

**API： /api/v1/user-plan-actions/actions/table/{table\_id}**

**输入参数：**

| 字段名称      | 数据类型                 | 说明                     |
| --------- | -------------------- | ---------------------- |
| table\_id | string(设置有问题，输入应为数字） |                        |
| limit     | Int                  | 返回记录数量限制，min:1,max:200 |

**输出：**

```json
[
  {
    "id": 13,
    "table_id": "4",
    "operation_time": "2025-06-26T14:46:12.325722",
    "user_operation": "修改子任务 写一个作文。 （所属任务 语文作业）的 结束 时间为 14:41",
    "user_id": 4
  }
]
```

## 4、生成表id

**API： /api/v1/user-plan-actions/utils/generate-table-id**

\*\*\*仍需要修改，目前设定的是每天的表id不同，且设置的数据类型存在问题

输入参数：date\_str 日期字符串。实际使用的表id应为int类型

输出：

```python
{
  "table_id": "1_plan_917"
}
```

# 5、ASR 功能列表

## 1. 建立ASR服务

* API: `POST /api/v1/asr/connect`

* 功能: 建立与ASR服务的连接，初始化语音识别客户端

* 实现逻辑:

  * 检查ASR服务可用性，如果已连接，直接返回成功

  * 使用请求参数或默认配置创建`VolcanoASRClient`实例调用`_asr_client.connect()`连接到ASR服务。如果连接成功，将ASR客户端设置到Socket.IO服务中

  * 返回连接结果，包括连接ID和模型信息

## 2. 断开ASR服务

* API: `POST /api/v1/asr/disconnect`

* 功能: 断开与ASR服务的连接，释放相关资源

* 实现逻辑:

  * 检查ASR服务是否已连接

  * 如果已连接，调用`_asr_client.disconnect()`断开连接

  * 重置连接状态和识别结果

  * 返回断开连接的结果

## 3. 启动语音识别

* API: `POST /api/v1/asr/start`

* 功能: 启动语音识别会话，准备接收音频数据

* 实现逻辑:

  * 检查ASR服务是否已连接

  * 清空之前的识别结果

  * 检查当前识别状态，如果已在识别则先停止

  * 调用`_asr_client.start_recognition()`启动识别

  * 记录识别开始时间和状态

  * 返回启动结果

## 4. 停止语音识别

* API: `POST /api/v1/asr/stop`

* 功能: 停止当前的语音识别会话

* 实现逻辑:

  * 检查ASR服务是否已连接

  * 如果正在识别，调用`_asr_client.stop_recognition()`停止识别

  * 更新识别状态

  * 返回停止结果

## 5. 发送音频数据

* API: `POST /api/v1/asr/send_audio`

* 功能: 向ASR服务发送Base64编码的音频数据进行识别

* 实现逻辑:

  * 检查ASR服务是否已连接

  * 解码Base64音频数据

  * 调用`_asr_client.send_audio()`发送音频数据到ASR服务

  * 返回发送结果

## 6. 获取ASR识别结果

* API: `GET /api/v1/asr/results`

* 功能: 获取当前的语音识别结果

* 实现逻辑:

  * 检查ASR服务是否已连接

  * 从全局变量`_asr_results`中获取识别结果

  * 返回结果列表，包括文本内容、是否最终结果和时间戳

## 7. 获取ASR服务状态

* API: `GET /api/v1/asr/status`

* 功能: 获取ASR服务的当前状态信息

* 实现逻辑:

  * 检查ASR服务是否已连接

  * 获取当前识别状态、连接状态等信息

  * 返回状态信息，包括是否健康、连接ID等

## 8. 获取可用的ASR模型列表

* API: `GET /api/v1/asr/models`

* 功能: 获取系统支持的所有ASR模型信息

* 实现逻辑:

  * 返回预定义的ASR模型列表

  * 包括模型ID、名称、语言、采样率等信息

  * 返回模型总数和详细信息

## 9. 获取ASR服务统计信息

* API: `GET /api/v1/asr/statistics`

* 功能: 获取ASR服务的使用统计信息

* 实现逻辑:

  * 从全局变量`_asr_statistics`中获取统计数据

  * 计算服务运行时间和成功率

  * 返回统计信息，包括请求总数、成功数、失败数、成功率和运行时间

## 10. 批量处理音频文件

* API: `POST /api/v1/asr/batch`

* 功能: 批量处理多个音频文件，进行语音识别

* 实现逻辑:

  * 接收包含多个音频文件路径或Base64编码数据的请求

  * 依次处理每个音频文件

  * 记录每个文件的识别结果

  * 返回批处理结果，包括成功和失败的数量

## 11. 健康检查

* API: `GET /api/v1/asr/health`

* 功能: 检查ASR服务的健康状态

* 实现逻辑:

  * 检查ASR服务是否可用

  * 检查连接状态

  * 返回健康状态信息



# 6、TTS 功能列表

## 12. 初始化TTS服务

* API: `POST /api/v1/tts/init`

* 功能: 初始化TTS服务，加载TTS引擎和相关资源

* 实现逻辑:

  * 调用`init_tts_service()`函数初始化TTS服务

  * 记录初始化时间并返回初始化结果

  * 如果初始化成功，返回成功响应；否则抛出HTTP 500错误

## 13. 播放文本转语音

* API: `POST /api/v1/tts/play`

* 功能: 将文本转换为语音并播放，支持同步和异步模式

* 实现逻辑:

  * 接收tts播放请求`（TTSPlayRequest`）包含文本内容、是否异步播放等参数

  * 如果请求返回音频数据，则生成音频文件并返回Base64编码的音频数据，否则调用`tts_service.play_text()`播放语音，并同时生成音频文件供web播放。

  * 返回播放结果，包括文件路径和音频URL等信息

## 14. 生成TTS音频文件

* API: `POST /api/v1/tts/generate`

* 功能: 将文本转换为音频文件并保存

* 实现逻辑:

  * 接收TTS生成文件请求类（`TTSGenerateRequest`），这个类里面包含文本内容和输出路径

  * 调用`tts_service.generate_file()`生成音频文件

  * 检查文件是否成功生成，并返回文件路径、大小等信息

  * 如果生成失败，抛出HTTP 500错误

## 15. 下载TTS音频文件

在tts服务API实现中，这里采用了先生成文件再下载文件，但是我觉得采用流式传输到客户端,即网页浏览器更好一点

* API: `GET /api/v1/tts/download/{filename}`

* 功能: 根据文件名下载生成的音频文件

* 实现逻辑:

  * 从URL路径中获取文件名（`tts_service.temp_audio_dir` 和 `filename` 这两个路径组件拼接成一个完整的文件路径）

  * 在TTS服务的临时音频目录中查找对应文件

  * 如果文件存在，使用`FileResponse`

  * 返回文件，如果文件不存在，抛出HTTP 404错误

## 16. 获取TTS服务状态

* API: `GET /api/v1/tts/status`

* 功能: 返回TTS服务的详细状态信息

* 实现逻辑:

  * 调用`tts_service.get_status()`获取服务状态

  * 返回状态信息，包括是否初始化、TTS是否可用等（`TTSHealthResponse`：返回一个 `TTSHealthResponse` 对象。`is_healthy`：通过 `status_info` 中的 `initialized` 和 `tts_available` 字段判断服务是否健康。只有当这两个字段都为 `True` 时，`is_healthy` 才为 `True`）

  * 即使发生错误也返回基本状态信息，不抛出异常

## 17. 清理TTS临时文件

好像功能并没有实现。。

* API: `POST/api/v1/tts/cleanup`

* 功能: 删除旧的临时音频文件，保留指定数量的最新文件

* 实现逻辑:

  * 接收`TTSCleanupRequest`请求，包含要保留的最大文件数

  * 统计清理前的文件数量

  * 调用`tts_service.cleanup_temp_files()`执行清理

  * 统计清理后的文件数量，计算删除的文件数

  * 返回清理结果，包括删除和保留的文件数量

## 18. 批量生成TTS音频文件

没用

* API: `POST /api/v1/tts/batch`

* 功能: 批量处理多个文本，生成对应的音频文件

* 实现逻辑:

  * 接收

  * ` TTSBatchRequest`

  * 请求，包含文本列表

  * 遍历文本列表，为每个文本生成音频文件

  * 记录每个文本的处理结果（成功或失败）

  * 统计成功和失败的数量，返回批处理结果

## 19. 获取可用的TTS发音人列表

目前没有使用到。

* API: `GET /api/v1/tts/speakers`

* 功能: 返回系统支持的所有发音人信息

* 实现逻辑:

  * 返回预定义的发音人列表，包括ID、名称、语言、性别和描述

  * 列表包括"双快思思"、"温暖男声"、"清新女声"等发音人

  * 返回发音人总数和详细信息

## 20. 更新TTS配置

目前没有使用到

* API: `POST /api/v1/tts/config`

* 功能: 更新TTS服务的配置参数，如发音人、应用ID等

* 实现逻辑:

  * 接收`TTSConfigRequest`请求，包含应用ID、令牌和发音人等参数

  * 更新TTS服务的配置参数

  * 重新初始化TTS客户端以应用新配置

  * 返回更新结果，包括更新后的配置信息





# 7.prompt-generation提示词生成API

## 7.1Generate\_Promopt生成综合学习prompt【不好用】

### 请求方式：GET

### Request  URL：/api/v1/prompt-generation/generate

### 功能逻辑

&#x20;获取儿童个人档案，获取今日作业/学习计划，获取近期作业完成情况，生成结构化 Prompt 文本，数据统计摘要

### 入参（Request Parameters）

| 字段名                         | 描述                | 类型   | 是否必填 | 说明           | 入参类型 |
| --------------------------- | ----------------- | ---- | ---- | ------------ | ---- |
| child\_id                   | 儿童ID              | int  | 必填   |              | body |
| days\_back                  | 回溯天数              | int  |      |              | body |
| include\_today\_homework    | 是否包含今日作业          | bool |      | （1-30天，默认7天） | body |
| include\_recent\_completion | 是否包含近期完成情况        | bool |      | （默认true）     | body |
| subject\_filter             | 学科筛选，是否只获取特定学科的数据 | str  | 可选   |              | body |
| prompt\_template            | 自定义prompt模板       | str  | 可选   |              | body |

### 出参（Response Parameters）：

儿童个人档案摘要，今日学习任务列表，近期学习表现数据，结构化的prompt文本，数据统计摘要组成的json

### 示例（Examples）：完整的请求/响应示例

```json
{
  "child_id": 1,
  "days_back": 7,
  "include_today_homework": true,
  "include_recent_completion": true,
  "subject_filter": "string",
  "prompt_template": "学生信息：{child_name}（{child_nickname}），{child_age}岁，学习特点：{learning_style}，擅长科目：{good_at_subjects}，薄弱科目：{weak_at_subjects}，今日任务数量：{today_homework_count}，近期记录{recent_records_count}"
}
```

```json
{
  "child_id": 1,
  "generated_at": "2025-07-01T08:51:54.735085Z",
  "child_profile": {
    "id": 1,
    "name": "新结构测试小孩",
    "nickname": "新小测",
    "age": 7,
    "academic_level": "primary_1",
    "school_name": "新结构测试小学",
    "learning_style": "听觉学习者",
    "attention_span_minutes": 40,
    "personality_traits": "聪明好学",
    "favorite_subjects": "数学，英语",
    "disliked_subjects": null,
    "good_at_subjects": null,
    "weak_at_subjects": null
  },
  "today_homework": [],
  "recent_completion": [],
  "structured_prompt": "学生信息：新结构测试小孩（新小测），7岁，学习特点：听觉学习者，擅长科目：，薄弱科目：，今日任务数量：0，近期记录0",
  "prompt_sections": {
    "basic_info": {
      "name": "新结构测试小孩",
      "nickname": "新小测",
      "age": 7,
      "academic_level": "primary_1",
      "school_name": "新结构测试小学"
    },
    "learning_characteristics": {
      "learning_style": "听觉学习者",
      "attention_span_minutes": 40,
      "personality_traits": "聪明好学"
    },
    "subject_preferences": {
      "good_at_subjects": null,
      "weak_at_subjects": null,
      "favorite_subjects": "数学，英语",
      "disliked_subjects": null
    },
    "today_tasks": [],
    "recent_performance": []
  },
  "data_summary": {
    "child_name": "新结构测试小孩",
    "today_homework_count": 0,
    "recent_records_count": 0,
    "data_date_range": null,
    "subjects_covered": [],
    "avg_completion_rate": 0,
    "avg_accuracy_rate": 0,
    "avg_concentration_level": 0,
    "total_study_time": 0
  }
}
```



## 7.2简化的prompt生成接口Generte Prompt Simple

### 请求方式：GET

### Request  URL：/api/v1/prompt-generation/generate/{child\_id}

相比于7.1，入参少了**prompt\_template**



## 7.3预览数据接口Preview Data

### 请求方式：GET

### Request  URL：/api/v1/prompt-generation/preview/{child\_id}

### 功能逻辑：

在生成prompt之前，预览将要使用的原始数据，用于调试和验证

### 入参（Request Parameters）

| 字段名             | 描述                | 类型  | 是否必填 | 说明 | 入参类型  |
| --------------- | ----------------- | --- | ---- | -- | ----- |
| child\_id       | 儿童ID              | int | 必填   |    | path  |
| days\_back      | 回溯天数              | int |      |    | query |
| subject\_filter | 学科筛选，是否只获取特定学科的数据 | str | 可选   |    | query |

### 出参（Response Parameters）：

儿童个人档案摘要，今日学习任务列表，近期学习表现数据，数据统计摘要json



## 7.4生成完整的任务计划表Generate Task Prompt

### 请求方式：POST

### Request  URL：/api/v1/prompt-generation/task-prompt

### 功能逻辑：

获取prompt1-4，套用任务生成的prompt模版生成最终的prompt

### 入参（Request Parameters）

| 字段名                       | 描述                | 类型   | 是否必填 | 说明             | 入参类型 |
| ------------------------- | ----------------- | ---- | ---- | -------------- | ---- |
| child\_id                 | 儿童ID              | int  | 必填   |                | body |
| days\_back                | 回溯天数              | int  |      |                | body |
| include\_yesterday\_tasks | 是否包含昨日作业          | bool |      | （1-30天，默认7天）   | body |
| prompt\_type              | prompt模板类型        | str  |      | 默认task\_prompt | body |
| subject\_filter           | 学科筛选，是否只获取特定学科的数据 | str  | 可选   |                | body |

### 出参（Response Parameters）：

prompt1-4,final\_prompt(最终格式化的完整prompt),原始数据用于调试

### 示例（Examples）：完整的请求/响应示例

```json
curl -X 'POST' \
  'http://localhost:8011/api/v1/prompt-generation/task-prompt' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "child_id": 4,
  "days_back": 7,
  "include_yesterday_tasks": true,
  "template_type": "task_prompt",
  "subject_filter": "string"
}'
```

```sql
{
  "child_id": 4,
  "generated_at": "2025-07-01T09:06:52.556265Z",
  "template_type": "task_prompt",
  "prompt1": "## 孩子个人肖像\n\n**基本信息：**\n- 姓名：小刚\n- 昵称：刚刚\n- 年龄：9岁\n- 学业等级：primary_3\n- 学校：北京市第三小学\n\n**学习特征：**\n- 学习风格：动觉学习者\n- 注意力持续时间：15分钟\n- 性格特点：精力充沛,喜欢运动,动手能力强\n\n**学科偏好：**\n- 喜欢的学科：未知\n- 不喜欢的学科：未知\n- 擅长的科目：体育,科学\n- 薄弱的科目：语文,美术",
  "prompt2": "## 历史学习情况\n\n暂无近期学习记录。",
  "prompt3": "## 昨日任务情况\n\n暂无昨日任务记录。",
  "prompt4": "## 今日任务要求\n\n暂无今日学习任务。",
  "final_prompt": "\n你是一个专业的儿童教育规划师，需要根据以下四个维度的信息生成个性化的当日任务计划表。\n\n【重要规则 - 必须严格遵守】：\n1. 🕐 时间安排：必须为每个任务安排具体的时间段，格式为\"HH:MM - HH:MM\"（如：18:00 - 18:45），根据儿童注意力特点，单个任务15-45分钟\n      休息间隔：任务间安排5-15分钟休息，避免疲劳累积\n- 难易搭配：先易后难，或难易交替，保持学习状态\n2. 📝 任务命名：大任务统一命名为\"学科名称+作业\"格式（从这些进行选择：数学作业、语文作业、英语作业、音乐作业、美术作业、科学作业、道德与法治作业、体育作业、信息科技作业）。\n3. 🈶 语言要求：除英语学科的具体学习内容外，其他部分均使用中文表述\n4. ⏰ 时间限制：作业时间安排在18:00-21:30之间，绝不能超过21:30\n5. 📋 任务完整性：必须完整包含prompt4的所有任务要求，不得有任何遗漏\n6. ⚡ 每日任务必须针对薄弱学科新增创建一个针对性的任务安排，任务安排必须给出具体内容：\n      例如出了3道题必须给出这3条题目每条题目的具体题目描述，或者阅读任务必须给出具体读物名字，不得省略内容。任务总时间不得超过三十分钟。\n7. 🎯 根据prompt3针对性调整作业时长和任务安排，确保高效学习时间（较早时间）合理安排于完成较差的学科。\n8. ✓ 所有任务安排时间之间不能重叠，并且适当在任务之间安排休息时间\n9. 📚 **学科集中安排原则**：**同一学科的所有任务必须集中安排在连续的时间段内**，避免学科间频繁切换。\n   - 例如：如果有数学练习册、数学口算题、数学应用题，应该连续安排在18:00-19:15这样的时间段内\n   - 学科间切换时安排10-15分钟休息，帮助大脑转换思维模式\n   - 优先级：薄弱学科 > 作业量大的学科 > 其他学科\n   - 同一学科内部按难易程度排序：简单任务→复杂任务，建立学习信心\n10. 🧠 教育心理学原则：遵循学习心理学和教育心理学规律安排任务\n11. 每个学科安排的单个子任务的时间也必须给出\n\n\n根据孩子的肖像，学习习惯和历史学习记录中的能力评估，调整任务难度和顺序，尽量最优化学习时间的使用，针对薄弱学科尽可能多的安排时间。\n\n【学习心理学与教育心理学指导原则】：\n🧠 认知负荷理论：避免同时安排过多复杂任务，合理分配认知资源\n📈 学习曲线原理：从简单到复杂，循序渐进安排任务难度\n⚡ 注意力规律：根据儿童注意力特点，合理安排任务时长和休息\n🎯 动机激发：结合兴趣和成就感，在适当位置安排孩子喜欢的学科，提高学习动机\n🔄 记忆巩固：合理安排复习和新学内容的时间间隔\n👥 个体差异：充分考虑孩子的学习风格和能力特点\n🏆 正强化原理：通过积分奖励等方式增强学习行为\n💪 体力分配：交替安排需要不同能力的任务（如读写交替）\n🔗 **学科连续性原理**：同一学科任务连续安排有助于：\n   - 减少认知切换成本，提高学习效率\n   - 保持学科思维的连贯性和深度\n   - 降低因频繁切换学科导致的注意力分散\n   - 建立学科内知识的关联性和系统性\n\n\n1. 【孩子个人肖像】(prompt1 - 基础特征)：\n## 孩子个人肖像\n\n**基本信息：**\n- 姓名：小刚\n- 昵称：刚刚\n- 年龄：9岁\n- 学业等级：primary_3\n- 学校：北京市第三小学\n\n**学习特征：**\n- 学习风格：动觉学习者\n- 注意力持续时间：15分钟\n- 性格特点：精力充沛,喜欢运动,动手能力强\n\n**学科偏好：**\n- 喜欢的学科：未知\n- 不喜欢的学科：未知\n- 擅长的科目：体育,科学\n- 薄弱的科目：语文,美术\n\n2. 【近期完成情况】(prompt2 - 行为模式)：\n## 历史学习情况\n\n暂无近期学习记录。\n\n3. 【昨日任务情况】(prompt3 - 作业情况记录)：\n## 昨日任务情况\n\n暂无昨日任务记录。\n\n4. 【今日作业列表】(prompt4 - 外部输入)：\n## 今日任务要求\n\n暂无今日学习任务。\n\n▌ 儿童个性化任务规划（v3.0）\n╔══════════════════════════════╗\n║  👶 姓名：[自动从prompt1提取] ║\n║  📅 日期：[自动生成当日日期]   ║\n║  🔍 能力评估：[综合prompt1+2] ║\n╚══════════════════════════════╝\n\n[1] 「学科1作业」（严格按照命名规范）\n   ├─ ⏰ 时段：[具体时间段，如18:00 - 18:45]\n   ├─ 📅 子任务：[根据prompt4制定，使用中文描述]\n   ├─ 🎯 定制：[结合历史弱点改进，中文表述]\n   ├─ ❗ 难点：[根据prompt2预测，中文表述]\n   └─ 💡 方案：[具体策略+趣味元素，中文表述]\n\n[2] 「学科2作业」（严格按照命名规范）\n   ├─ ⏰ 时段：[具体时间段，如19:00 - 19:30]\n   ├─ 📅 子任务：[根据prompt4制定]\n   ├─ 🎯 定制：[结合历史弱点改进，中文表述]\n   ├─ ❗ 难点：[根据prompt2预测，中文表述]\n   └─ 💡 方案：[具体策略+趣味元素，中文表述]\n   \n\n\n■ 动态调节建议（基于教育心理学）：\n⚠️ 警惕：[历史负面行为]+对应任务项，采用正强化替代惩罚\n💡 创新：[针对prompt4任务的新教法]，结合多元智能理论\n🎯 拓展：[从任务中发现的潜能方向，针对薄弱学科尽可能强化学习]\n🧠 认知策略：根据学习风格调整教学方法（视觉、听觉、动觉学习者）\n🎮 游戏化：适当融入游戏元素，提高学习兴趣和参与度\n📊 反馈机制：及时提供积极反馈，增强学习自信心\n\n\n■ 四维度校验（教育心理学视角）：\n✓ 匹配度：所有任务符合孩子性格特征和学习风格\n✓ 延续性：每项任务都规避了历史问题，采用正向引导\n✓ 完整度：100%覆盖prompt4所有原始要求\n✓ 科学性：时间安排、难度梯度、休息间隔符合儿童认知发展规律\n\n【最终输出要求 - 严格执行】：\n\n1. 📊 信心指数：包含「执行信心指数」评估（1-5分）\n2. 🎯 复杂任务：突出显示任何被拆分的复杂任务\n3. 🎁 奖励机制：使用积分作为奖励，不使用其他奖励方式\n4. 📋 输出格式：使用JSON格式输出任务安排列表\n5. 📚 **学科集中要求**：确保同一学科的所有任务连续安排，学科间安排适当休息\n\n\n【JSON输出格式示例 - 学科集中安排】：\n\n[\n  {\n    \"task_name\": \"数学作业\",\n    \"time_slot\": \"18:00 - 19:00\",\n    \"subject\": \"数学\",\n    \"sub_tasks\": [\n      {\n        \"sub_task_name\": \"练习册第10页 (来自prompt4第1项)\",\n        \"time_slot\": \"18:00 - 18:15\"\n      },\n      {\n        \"sub_task_name\": \"口算题卡20题 (来自prompt4第2项)\",\n        \"time_slot\": \"18:15 - 18:30\"\n      },\n      {\n        \"sub_task_name\": \"数学应用题3道 (来自prompt4第3项)\",\n        \"time_slot\": \"18:30 - 19:00\"\n      }\n    ],\n    \"customization\": \"针对应用题理解困难，采用图解方式\",\n    \"difficulty\": \"应用题理解\",\n    \"solution\": \"先画图理解题意，再列式计算\",\n    \"confidence_index\": 4\n  },\n  {\n    \"task_name\": \"休息时间\",\n    \"time_slot\": \"19:00 - 19:15\",\n    \"subject\": \"休息\",\n    \"sub_tasks\": [\n      {\n        \"sub_task_name\": \"学科切换休息，准备语文学习\",\n        \"time_slot\": \"19:00 - 19:15\"\n      }\n    ],\n    \"customization\": \"深呼吸，整理数学用品，准备语文材料\",\n    \"difficulty\": \"无\",\n    \"solution\": \"轻松活动，调整学习状态\",\n    \"confidence_index\": 5\n  },\n  {\n    \"task_name\": \"语文作业\",\n    \"time_slot\": \"19:15 - 20:00\",\n    \"subject\": \"语文\",\n    \"sub_tasks\": [\n      {\n        \"sub_task_name\": \"课文朗读 (来自prompt4第4项)\",\n        \"time_slot\": \"19:15 - 19:25\"\n      },\n      {\n        \"sub_task_name\": \"生字练习 (来自prompt4第5项)\",\n        \"time_slot\": \"19:25 - 19:40\"\n      },\n      {\n        \"sub_task_name\": \"作文草稿 (来自prompt4第6项)\",\n        \"time_slot\": \"19:40 - 20:00\"\n      }\n    ],\n    \"customization\": \"针对写作困难，先列提纲再写作\",\n    \"difficulty\": \"作文构思\",\n    \"solution\": \"使用思维导图整理写作思路\",\n    \"confidence_index\": 3\n  }\n]\n\n\n最终输出：请严格按照上述格式，给我一个完整的JSON格式任务安排列表。**特别注意：同一学科的所有任务必须连续安排，不同学科间安排休息时间。**\n",
  "child_profile": {
    "id": 4,
    "name": "小刚",
    "nickname": "刚刚",
    "age": 9,
    "academic_level": "primary_3",
    "school_name": "北京市第三小学",
    "learning_style": "动觉学习者",
    "attention_span_minutes": 15,
    "personality_traits": "精力充沛,喜欢运动,动手能力强",
    "favorite_subjects": null,
    "disliked_subjects": null,
    "good_at_subjects": "体育,科学",
    "weak_at_subjects": "语文,美术"
  },
  "today_homework": [],
  "recent_completion": [],
  "yesterday_tasks": [],
  "soft_delete_info": null
}
```





## 7.5简化的任务prompt生成接口Generate Task Prompt Simple【与7.4重复】

### 请求方式：GET

### Request  URL：/api/v1/prompt-generation/task-prompt/{child\_id}

### 功能逻辑：用于简单的调用场景



## 7.6获取可用的prompt模板列Get Available Templates【目前该接口不可以，报错500， Internal Server Error】

### 请求方式：GET

### Request  URL：/api/v1/prompt-generation/templates



## 7.7获取prompt各个部分的详细内容Get Prompt Sections【与7.4重复】

### 请求方式：GET

### Request  URL：/api/v1/prompt-generation/prompt-sections/{child\_id}

### 功能逻辑

分别返回prompt1-prompt4的详细内容，用于调试和预览。

# 8.豆包相关API

## 8.1聊天补全接口Chat Completion【流式输出似乎不可用】

### 请求方式：POST

### Request  URL：/api/v1/chat/completions

### 功能逻辑：

支持多轮对话，可以传入完整的对话历史。

### 入参（Request Parameters）

| 字段名         | 描述                                               | 类型    | 是否必填 | 说明 | 入参类型 |
| ----------- | ------------------------------------------------ | ----- | ---- | -- | ---- |
| messages    | 对话消息列表，格式为 \[{"role": "user", "content": "..."}] | list  |      |    | body |
| temperature | 温度参数，控制随机性                                       | float | 可选   |    | body |
| top\_p      | Top-p参数，控制多样性                                    | float | 可选   |    | body |
| max\_tokens | 最大token数                                         | int   | 可选   |    | body |
| stream      | 是否流式输出                                           | bool  | 可选   |    | body |

### 出参（Response Parameters）：包含回答的json



### 示例（Examples）：完整的请求/响应示例

```json
curl -X 'POST' \
  'http://localhost:8011/api/v1/chat/completions' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "messages": [
    {
      "role": "user",
      "content": "你好，你是"
    }
  ],
  "temperature": 1,
  "top_p": 1,
  "max_tokens": 100,
  "stream": false
}'
```

```json
{
  "success": true,
  "response_text": "我是豆包，能陪你交流各种话题，答疑解惑、谈天说地呢！ ",
  "model": "doubao-1-5-vision-pro-32k-250115",
  "usage": {
    "prompt_tokens": 14,
    "completion_tokens": 22,
    "total_tokens": 36
  },
  "timestamp": "2025-07-02T09:47:52.298972",
  "error": null,
  "data": {
    "choices": [
      {
        "finish_reason": "stop",
        "index": 0,
        "logprobs": null,
        "message": {
          "content": "我是豆包，能陪你交流各种话题，答疑解惑、谈天说地呢！ ",
          "role": "assistant"
        }
      }
    ],
    "created": 1751420816,
    "id": "0217514208138251ec5ecf68116e0c07ddb7f1dbf3b5098fb4684",
    "model": "doubao-1-5-vision-pro-32k-250115",
    "service_tier": "default",
    "object": "chat.completion",
    "usage": {
      "completion_tokens": 22,
      "prompt_tokens": 14,
      "total_tokens": 36,
      "prompt_tokens_details": {
        "cached_tokens": 0
      },
      "completion_tokens_details": {
        "reasoning_tokens": 0
      }
    }
  }
}
```

## 8.2简单聊天接口Simple Chat

### 请求方式：POST

### Request  URL：/api/v1/chat/simple

### 功能逻辑：

直接传入prompt，适用于单轮对话场景。

### 入参（Request Parameters）

prompt, temperature, top\_p, max\_tokens

### 出参（Response Parameters）：与8.1类似

## 8.3获取当前模型信息Get Model Info【感觉没什么用处】

### 请求方式：GET

### Request  URL：/api/v1/model/info

### 入参（Request Parameters）：无参数

### 出参（Response Parameters）：模型信息json

### 示例（Examples）：完整的请求/响应示例

```plain&#x20;text
curl -X 'GET' \
  'http://localhost:8011/api/v1/model/info' \
  -H 'accept: application/json'
```

```json
{
  "model_name": "doubao-1-5-vision-pro-32k-250115",
  "api_key": "945a7413-a...",
  "base_url": "https://ark.cn-beijing.volces.com/api/v3"
}
```

## 8.4健康检查接口Health Check

### 请求方式：GET

### Request  URL：/api/v1/health

### 功能逻辑：检查豆包服务是否正常工作。

### 入参（Request Parameters）：无

| 字段名 | 描述 | 类型 | 是否必填 | 说明 | 入参类型 |
| --- | -- | -- | ---- | -- | ---- |
|     |    |    |      |    |      |

### 出参（Response Parameters）：豆包模型健康信息json

### 示例（Examples）：完整的请求/响应示例

```plain&#x20;text
curl -X 'GET' \
  'http://localhost:8011/api/v1/health' \
  -H 'accept: application/json'
```

```json
{
  "service_name": "DoubaoService",
  "is_healthy": true,
  "model_info": {
    "model_name": "doubao-1-5-vision-pro-32k-250115",
    "api_key": "945a7413-a...",
    "base_url": "https://ark.cn-beijing.volces.com/api/v3"
  },
  "connection_status": {
    "is_connected": true,
    "message": "连接正常",
    "timestamp": "2025-07-02T10:00:43.479464"
  },
  "last_check_time": "2025-07-02T10:00:43.479464"
}
```

## 8.5创建新的豆包服务实例Create Service

### 请求方式：POST

### Request  URL：/api/v1/service/create

### 功能逻辑：

使用自定义配置创建服务实例，适用于需要使用不同配置的场景。

### 入参（Request Parameters）

| 字段名         | 描述            | 类型    | 是否必填 | 说明 | 入参类型 |
| ----------- | ------------- | ----- | ---- | -- | ---- |
| api\_key    |               |       |      |    | body |
| ase\_url    |               |       |      |    | body |
| model\_name |               |       |      |    | body |
| temperature | 温度参数，控制随机性    | float | 可选   |    | body |
| top\_p      | Top-p参数，控制多样性 | float | 可选   |    | body |
| max\_tokens | 最大token数      | int   | 可选   |    | body |
| timeout     | 请求超时时间        | int   |      |    | body |

### 出参（Response Parameters）：创建信息json

### 示例（Examples）：完整的请求/响应示例

```json
curl -X 'POST' \
  'http://localhost:8011/api/v1/service/create' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "api_key": "string",
  "base_url": "https://ark.cn-beijing.volces.com/api/v3",
  "model_name": "doubao-1-5-vision-pro-32k-250115",
  "temperature": 0.7,
  "top_p": 0.9,
  "max_tokens": 4000,
  "timeout": 180
}'
```

```json
{
  "success": false,
  "data": {
    "model_name": "doubao-1-5-vision-pro-32k-250115",
    "base_url": "https://ark.cn-beijing.volces.com/api/v3",
    "connection_valid": false
  },
  "message": "服务创建成功但连接验证失败",
  "timestamp": "2025-07-02T10:08:34.000417"
}
```

## 8.6测试连接Test Connection

### 请求方式：POST

### Request  URL：/api/v1/test/connection

### 功能逻辑：

测试与豆包API的连接是否正常

### 入参（Request Parameters）：无

### 出参（Response Parameters）：

```json
{
  "is_connected": true,
  "message": "string",
  "timestamp": "string"
}
```



# 9.计划修改

## 9.1修改学习计划Modify Study Plan

### 请求方式：POST

### Request  URL：/api/v1/planning/modify

### 功能逻辑：

先生成修改计划表prompt，调用豆包模型生成修改后的计划表，解析结果，提前结构化的修改后的计划表

### 入参（Request Parameters）

| 字段名                   | 描述            | 类型   | 是否必填      | 说明                                                     | 入参类型 |
| --------------------- | ------------- | ---- | --------- | ------------------------------------------------------ | ---- |
| child\_id             |  学生ID         | int  | 必填        |                                                        |      |
| original\_plan        | 原有任务计划表JSON数据 | list | 必填        |                                                        |      |
| modification\_request | 用户的修改意见和要求    | str  | 必填        |                                                        |      |
| modification\_type    | 修改类型          | str  | 可选默认other | add\_task，remove\_task，modify\_task，adjust\_time，other |      |
| plan\_date            | 计划日期          | date | 可选        |                                                        |      |

### 出参（Response Parameters）：

&#x20;   \- \*\*success\*\*: 修改是否成功

&#x20;   \- \*\*message\*\*: 处理结果消息

&#x20;   \- \*\*original\_plan\*\*: 原有计划表

&#x20;   \- \*\*modified\_plan\*\*: 修改后的计划表

&#x20;   \- \*\*modification\_prompt\*\*: 用于修改的完整prompt

&#x20;   \- \*\*ai\_response\*\*: AI原始响应

### 示例（Examples）：完整的请求/响应示例

```json
{
    "child_id": 1,
    "original_plan": [
        {
            "task_name": "数学作业",
            "time_slot": "18:00 - 18:45",
            "subject": "数学",
            "sub_tasks": [
                {
                    "task_content": "练习册第9页",
                    "time_slot": "18:00-18:15",
                    "estimated_minutes": 15,
                    "order_index": 1
                }
            ],
            "customization": "针对乘法表记忆困难",
            "difficulty": "乘法表记忆",
            "solution": "听乘法口诀歌",
            "confidence_index": 3
        }
    ],
    "modification_request": "在数学作业中增加两道应用题练习",
    "modification_type": "add_task"
}
```

```sql
{
  "child_id": 1,
  "generated_at": "2025-07-02T02:34:20.360776Z",
  "modification_type": "add_task",
  "original_plan": [
    {
      "task_name": "数学作业",
      "time_slot": "18:00 - 18:45",
      "subject": "数学",
      "sub_tasks": [
        {
          "task_content": "练习册第9页",
          "time_slot": "18:00-18:15",
          "estimated_minutes": 15,
          "order_index": 1
        }
      ],
      "customization": "针对乘法表记忆困难",
      "difficulty": "乘法表记忆",
      "solution": "听乘法口诀歌",
      "confidence_index": 3
    }
  ],
  "modification_request": "在数学作业中增加两道应用题练习",
  "modified_plan": [
    {
      "task_name": "数学作业",
      "time_slot": "18:00 - 19:00",
      "subject": "数学",
      "sub_tasks": [
        {
          "task_content": "练习册第9页",
          "time_slot": "18:00 - 18:15",
          "estimated_minutes": 15,
          "order_index": 1
        },
        {
          "task_content": "休息",
          "time_slot": "18:15 - 18:20",
          "estimated_minutes": 5,
          "order_index": 2
        },
        {
          "task_content": "听乘法口诀歌（边听边跟读）",
          "time_slot": "18:20 - 18:30",
          "estimated_minutes": 10,
          "order_index": 3
        },
        {
          "task_content": "休息",
          "time_slot": "18:30 - 18:35",
          "estimated_minutes": 5,
          "order_index": 4
        },
        {
          "task_content": "两道应用题练习",
          "time_slot": "18:35 - 18:50",
          "estimated_minutes": 15,
          "order_index": 5
        },
        {
          "task_content": "休息",
          "time_slot": "18:50 - 19:00",
          "estimated_minutes": 10,
          "order_index": 6
        }
      ],
      "customization": "针对乘法表记忆困难，采用听乘法口诀歌的方式加强记忆",
      "difficulty": "乘法表记忆，应用题解答",
      "solution": "听乘法口诀歌，边听边跟读，多重复几遍；解答应用题时圈画关键信息",
      "confidence_index": 3
    }
  ],
  "modification_prompt": "\n你是一个专业的儿童学习计划优化助手。请根据以下要求调整任务计划表：\n\n#### 你需要参考的任务设计准则：\n\n【重要规则 - 必须严格遵守】：\n1. 🕐 时间安排：必须为每个任务安排具体的时间段，格式为\"HH:MM - HH:MM\"（如：18:00 - 18:45），根据儿童注意力特点，单个任务15-45分钟\n      休息间隔：任务间安排5-15分钟休息，避免疲劳累积\n- 难易搭配：先易后难，或难易交替，保持学习状态\n2. 📝 任务命名：大任务统一命名为\"学科名称+作业\"格式（从这些进行选择：数学作业、语文作业、英语作业、音乐作业、美术作业、科学作业、道德与法治作业、体育作业、信息科技作业）。\n3. 🈶 语言要求：除英语学科的具体学习内容外，其他部分均使用中文表述\n4. ⏰ 时间限制：作业时间安排在18:00-21:30之间，绝不能超过21:30\n5. 📋 任务完整性：必须完整包含原有任务计划表的所有任务要求，不得有任何遗漏\n6. 📚 **学科集中安排原则**：**同一学科的所有任务必须集中安排在连续的时间段内**，避免学科间频繁切换。\n   - 修改时必须保持学科任务的连续性\n   - 学科间切换时安排10-15分钟休息\n   - 同一学科内部按难易程度排序\n7. 🎯 根据用户修改意见针对性调整作业时长和任务安排，确保高效学习时间（较早时间）合理安排于完成较差的学科。\n8. ✓ 所有任务安排时间之间不能重叠，并且适当在任务之间安排休息时间\n9. 🧠 教育心理学原则：遵循学习心理学和教育心理学规律安排任务\n\n【学习心理学与教育心理学指导原则】：\n🧠 认知负荷理论：避免同时安排过多复杂任务，合理分配认知资源\n📈 学习曲线原理：从简单到复杂，循序渐进安排任务难度\n⚡ 注意力规律：根据儿童注意力特点，合理安排任务时长和休息\n🎯 动机激发：结合兴趣和成就感，在适当位置安排孩子喜欢的学科，提高学习动机\n🔄 记忆巩固：合理安排复习和新学内容的时间间隔\n👥 个体差异：充分考虑孩子的学习风格和能力特点\n🏆 正强化原理：通过积分奖励等方式增强学习行为\n💪 体力分配：交替安排需要不同能力的任务（如读写交替）\n🔗 **学科连续性原理**：修改时保持同一学科任务的连续性，减少认知切换成本\n\n#### 输入信息\n\n1. 原有任务计划表（JSON格式）：\n[\n  {\n    \"task_name\": \"数学作业\",\n    \"time_slot\": \"18:00 - 18:45\",\n    \"subject\": \"数学\",\n    \"sub_tasks\": [\n      {\n        \"task_content\": \"练习册第9页\",\n        \"time_slot\": \"18:00-18:15\",\n        \"estimated_minutes\": 15,\n        \"order_index\": 1\n      }\n    ],\n    \"customization\": \"针对乘法表记忆困难\",\n    \"difficulty\": \"乘法表记忆\",\n    \"solution\": \"听乘法口诀歌\",\n    \"confidence_index\": 3\n  }\n]\n\n2. 用户修改意见：\n在数学作业中增加两道应用题练习\n\n3. 修改类型：add_task\n\n#### 修改要求\n\n请根据用户的修改意见，对原有计划表进行调整：\n\n- 如果是添加任务：将新任务合理插入到现有计划中，重新调整时间分配\n- 如果是删除任务：移除指定任务，重新优化剩余任务的时间安排\n- 如果是修改任务：更新任务内容、时间或难度，确保整体协调\n- 如果是调整时间：重新分配时间段，确保无冲突且符合学习规律\n- 如果是其他修改：根据具体要求进行相应调整\n\n#### 输出调整后的完整计划表：\n- 以JSON格式返回，确保所有字段完整\n- 必须包含完整的任务列表，不能遗漏任何必要信息\n- 时间安排必须合理，无冲突\n- 保持原有的教育心理学设计原则\n\n【JSON输出格式】：\n\n[\n  {\n    \"task_name\": \"数学作业\",\n    \"time_slot\": \"18:00 - 18:45\",\n    \"subject\": \"数学\",\n    \"sub_tasks\": [\n      {\n        \"task_content\": \"练习册第9页\",\n        \"time_slot\": \"18:00-18:15\",\n        \"estimated_minutes\": 15,\n        \"order_index\": 1\n      },\n      {\n        \"task_content\": \"休息\",\n        \"time_slot\": \"18:15-18:20\",\n        \"estimated_minutes\": 5,\n        \"order_index\": 2\n      },\n      {\n        \"task_content\": \"口算题卡20题\",\n        \"time_slot\": \"18:20-18:35\",\n        \"estimated_minutes\": 15,\n        \"order_index\": 3\n      }\n    ],\n    \"customization\": \"针对乘法表记忆困难，采用听乘法口诀歌的方式加强记忆\",\n    \"difficulty\": \"乘法表记忆\",\n    \"solution\": \"听乘法口诀歌，边听边跟读，多重复几遍\",\n    \"confidence_index\": 3\n  }\n]\n\n\n#### 其他注意事项\n- 若时间冲突，优先保证核心任务（如学校作业）的完成，灵活调整时间，尽量避免冲突\n- 积分奖励机制需与原有规则保持一致\n- 返回结果前，检查所有时间是否合理，避免过度紧凑\n\n■ 动态调节建议（基于教育心理学）：\n⚠️ 警惕：[历史负面行为]+对应任务项，采用正强化替代惩罚\n🎯 拓展：[从任务中发现的潜能方向，针对薄弱学科尽可能强化学习]\n🧠 认知策略：根据学习风格调整教学方法（视觉、听觉、动觉学习者）\n🎮 游戏化：适当融入游戏元素，提高学习兴趣和参与度\n📊 反馈机制：及时提供积极反馈，增强学习自信心\n\n■ 四维度校验（教育心理学视角）：\n✓ 匹配度：所有任务符合孩子性格特征和学习风格\n✓ 延续性：每项任务都规避了历史问题，采用正向引导\n✓ 完整度：100%覆盖所有原始要求\n✓ 科学性：时间安排、难度梯度、休息间隔符合儿童认知发展规律\n\n请开始调整任务计划表，并返回优化后的JSON结果。\n",
  "ai_response": "[\n  {\n    \"task_name\": \"数学作业\",\n    \"time_slot\": \"18:00 - 19:00\",\n    \"subject\": \"数学\",\n    \"sub_tasks\": [\n      {\n        \"task_content\": \"练习册第9页\",\n        \"time_slot\": \"18:00 - 18:15\",\n        \"estimated_minutes\": 15,\n        \"order_index\": 1\n      },\n      {\n        \"task_content\": \"休息\",\n        \"time_slot\": \"18:15 - 18:20\",\n        \"estimated_minutes\": 5,\n        \"order_index\": 2\n      },\n      {\n        \"task_content\": \"听乘法口诀歌（边听边跟读）\",\n        \"time_slot\": \"18:20 - 18:30\",\n        \"estimated_minutes\": 10,\n        \"order_index\": 3\n      },\n      {\n        \"task_content\": \"休息\",\n        \"time_slot\": \"18:30 - 18:35\",\n        \"estimated_minutes\": 5,\n        \"order_index\": 4\n      },\n      {\n        \"task_content\": \"两道应用题练习\",\n        \"time_slot\": \"18:35 - 18:50\",\n        \"estimated_minutes\": 15,\n        \"order_index\": 5\n      },\n      {\n        \"task_content\": \"休息\",\n        \"time_slot\": \"18:50 - 19:00\",\n        \"estimated_minutes\": 10,\n        \"order_index\": 6\n      }\n    ],\n    \"customization\": \"针对乘法表记忆困难，采用听乘法口诀歌的方式加强记忆\",\n    \"difficulty\": \"乘法表记忆，应用题解答\",\n    \"solution\": \"听乘法口诀歌，边听边跟读，多重复几遍；解答应用题时圈画关键信息\",\n    \"confidence_index\": 3\n  }\n]",
  "success": true,
  "message": "成功修改学习计划，共1个任务",
  "error_details": null
}
```

## 9.2简化的计划表修改接口Modify Study Plan Simple【与9.1类似，9.1的字段类型和验证规则更严格，simple内部需要将简化请求转换为完整请求】

### 请求方式：POST

### Request  URL：/api/v1/planning/modify-simple

### 功能逻辑：

通过表单参数快速修改学习计划，适用于简单的调用场景。



## 9.3批量修改学习计划Modify Study Plans Batch

### 请求方式：POST

### Request  URL：/api/v1/planning/batch-modify

### 功能逻辑：

遍历每个请求，每个请求的逻辑和9.1一致入



# 10.任务输入task\_input【可删除】

## 10.1处理文本输入的任务Process Text Task Input

### 请求方式：POST

### Request  URL：/api/v1/task-input/text

### 功能逻辑：

将用户输入的文本内容解析为结构化的今日任务，并存储到数据库。【并没有成功存储】

task\_input\_service是核心处理模块，\_process\_input函数负责生成任务解析的Prompt（task\_input\_template.py）、调用AI模型（doubao\_service.chat\_completion）解析文本内容、解析AI返回的结构化任务数据、验证任务数据有效性、存储任务到数据库（依赖DailyTaskService）

### 入参（Request Parameters）

| 字段名           | 描述   | 类型 | 是否必填 | 说明 | 入参类型 |
| ------------- | ---- | -- | ---- | -- | ---- |
| child\_id     | 学生ID |    | 必填   |    | body |
| text\_content | 文本内容 |    | 必填   |    | body |

### 出参（Response Parameters）：解析出的任务

### 示例（Examples）：完整的请求/响应示例

```plain&#x20;text
curl -X 'POST' \
  'http://localhost:8011/api/v1/task-input/text' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "child_id": 1,
  "text_content": "今天数学作业：完成练习册第15-16页，重点是分数加减法。语文要背诵《静夜思》。"
}'
```

```json
{
  "success": true,
  "message": "成功处理text输入，解析并存储0个任务",
  "child_id": 1,
  "input_type": "text",
  "input_content": "今天数学作业：完成练习册第15-16页，重点是分数加减法。语文要背诵《静夜思》。",
  "tasks": [
    {
      "task_name": "数学作业",
      "subject": "数学",
      "description": "完成练习册第15-16页，重点练习分数加减法",
      "estimated_duration": 30,
      "difficulty_level": 3,
      "materials_needed": "数学练习册、笔、草稿纸",
      "time_requirement": "",
      "priority": "高",
      "notes": "重点关注分数运算",
      "sub_tasks": [
        {
          "task": "完成练习册第15页",
          "source": "学校作业"
        },
        {
          "task": "完成练习册第16页",
          "source": "学校作业"
        },
        {
          "task": "重点练习分数加减法",
          "source": "学校作业"
        }
      ]
    },
    {
      "task_name": "语文作业",
      "subject": "语文",
      "description": "背诵古诗《静夜思》",
      "estimated_duration": 20,
      "difficulty_level": 2,
      "materials_needed": "语文课本",
      "time_requirement": "",
      "priority": "中",
      "notes": "可以先理解诗意再背诵",
      "sub_tasks": [
        {
          "task": "熟读《静夜思》",
          "source": "学校作业"
        },
        {
          "task": "理解诗意",
          "source": "学校作业"
        },
        {
          "task": "背诵《静夜思》",
          "source": "学校作业"
        }
      ]
    }
  ],
  "stored_task_ids": [],
  "total_tasks": 2,
  "stored_tasks": 0,
  "processed_at": "2025-07-02T06:01:11.209768Z"
}
```

## 10.2处理语音输入的任务Process Voice Task Input

### 请求方式：POST

### Request  URL：/api/v1/task-input/voice

### 功能逻辑：与10.1类似，【并没有成功存储】

## 10.3处理图片输入Process Image Task Input【接口不可用，报错500Internal Server Error】

### 请求方式：POST

### Request  URL：/api/v1/task-input/image

### 功能逻辑：



### 入参（Request Parameters）

| 字段名         | 描述   | 类型          | 是否必填 | 说明               | 入参类型 |
| ----------- | ---- | ----------- | ---- | ---------------- | ---- |
| child\_id   | 学生ID | int         | 必填   |                  |      |
| image\_file | 图片文件 | str(binary) | 必填   | 支持jpg、png、gif等格式 |      |

### 出参（Response Parameters）：

### 示例（Examples）：完整的请求/响应示例

```plain&#x20;text
curl -X 'POST' \
  'http://localhost:8011/api/v1/task-input/image' \
  -H 'accept: application/json' \
  -H 'Content-Type: multipart/form-data' \
  -F 'child_id=2' \
  -F 'image_file=@微信图片_20250702144112.jpg;type=image/jpeg'
```

```plain&#x20;text
{
  "detail": "处理图片输入失败: 1 validation error for TaskInputResponse\ninput_content\n  Field required [type=missing, input_value={'success': False, 'messa...': 'image', 'tasks': []}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing"
}
```

## 10.4处理Base64编码图片输入的任务Process Image Base64 Task Input

### 请求方式：POST

### Request  URL：/api/v1/task-input/image-base64

### 功能逻辑：



### 入参（Request Parameters）

| 字段名               | 描述            | 类型  | 是否必填 | 说明 | 入参类型 |
| ----------------- | ------------- | --- | ---- | -- | ---- |
| child\_id         | 孩子ID          | int | 必填   |    |      |
|     image\_base64 | Base64编码的图片数据 | str | 必填   |    |      |

### 出参（Response Parameters）：

### 示例（Examples）：完整的请求/响应示例

```json
```

```sql
```

## 10.5健康检查接口Health Check

### 请求方式：GET

### Request  URL：/api/v1/task-input/health



## 10.6获取支持的输入类型Get Supported Input Types

### 请求方式：GET

### Request  URL：/api/v1/task-input/input-types

### 功能逻辑：



### 入参（Request Parameters）：无

### 示例（Examples）：完整的请求/响应示例

```plain&#x20;text
curl -X 'GET' \
  'http://localhost:8011/api/v1/task-input/input-types' \
  -H 'accept: application/json'
```

```json
{
  "supported_types": [
    {
      "type": "text",
      "name": "文本输入",
      "description": "直接输入文本内容",
      "endpoint": "/text",
      "method": "POST",
      "example": {
        "child_id": 1,
        "text_content": "今天数学作业：完成练习册第15-16页"
      }
    },
    {
      "type": "voice",
      "name": "语音输入",
      "description": "语音转文字后的内容",
      "endpoint": "/voice",
      "method": "POST",
      "example": {
        "child_id": 1,
        "voice_text": "今天老师说要做英语听力练习"
      }
    },
    {
      "type": "image",
      "name": "图片输入",
      "description": "上传图片文件，自动识别文字",
      "endpoint": "/image",
      "method": "POST",
      "content_type": "multipart/form-data",
      "supported_formats": [
        "jpeg",
        "jpg",
        "png",
        "gif",
        "bmp"
      ]
    },
    {
      "type": "image_base64",
      "name": "Base64图片输入",
      "description": "Base64编码的图片数据",
      "endpoint": "/image-base64",
      "method": "POST",
      "example": {
        "child_id": 1,
        "image_base64": "data:image/jpeg;base64,..."
      }
    }
  ],
  "common_fields": {
    "child_id": "学生ID（必填）",
    "response_format": "统一的TaskInputResponse格式"
  }
}
```



# 11.日常任务Daily Tasks【可删除，接口都没用】

## 11.1获取指定学生的当日任务get today tasks

### 请求方式：GET

### Request  URL：/api/v1/daily-tasks/child/{child\_id}/today

### 功能逻辑：

获取指定学生的当日任务,通过 `DailyTaskService` 的 `get_tasks_by_date` 方法查询指定学生当日的主任务,对每个主任务（如果有 `id` 属性），通过 `get_task_items` 方法获取子任务列表，并转换为字典格式

### 入参（Request Parameters）：

| 字段名       | 描述    | 类型  | 是否必填 | 说明 | 入参类型 |
| --------- | ----- | --- | ---- | -- | ---- |
| child\_id |  学生ID | int | 必填   |    | path |

### 出参：孩子的当日任务

## 11.2创建新任务create task

### 请求方式：POST

### Request  URL：/api/v1/daily-tasks/create

### 功能逻辑：

通过 `DailyTaskService` 的 `create_task` 方法创建任务，传入准备好的 `task_data`

### 入参（Request Parameters）：

| 字段名                 | 描述               | 类型  | 是否必填 | 说明                             | 入参类型 |
| ------------------- | ---------------- | --- | ---- | ------------------------------ | ---- |
| child\_id           | 学生ID             | Int | 必填   |                                | body |
| task\_name          | 任务名称             | str | 必填   |                                | body |
| description         | 任务描述             | str | 必填   |                                | body |
| subject             | 学科               | str | 必填   |                                | body |
| task\_date          | 任务日期 (YYYY-MM-DD | str | 可选   |                                | body |
| time\_slot          | 时间段              | str | 可选   |                                | body |
| estimated\_duration | 预计时长(分钟)         | int | 可选   |                                | body |
| difficulty          | 难点               | str | 可选   |                                | body |
| confidence\_index   | 信心指数(1-5)        | str | 可选   |                                | body |
| status              | 任务状态             | str |      | pending/in\_progress/completed |      |

### 出参（Response Parameters）：新建的任务信息

## 11.3更新任务状态

### 请求方式：PUT

### Request  URL：/api/v1/daily-tasks/{task\_id}/status

### 功能逻辑：



### 入参（Request Parameters）：

| 字段名         | 描述   | 类型  | 是否必填 | 说明 | 入参类型 |
| ----------- | ---- | --- | ---- | -- | ---- |
| task\_id    | 任务ID | int | 是    |    | path |
| description | 描述   | str | 是    |    | body |

###

# 12.多模态任务输入

## 12.1处理文本输入的任务（多模态版本）process multimodal text task input【存储失败，后面要修（已修复，是由于输入必须要填plan\_id，但是用户输入的任务暂时不输入任何ID，所以将plan\_id设为可选）】

### 请求方式：POST

### Request  URL：/api/v1/multimodal-task-input/text

### 功能逻辑：

获取 `MultimodalTaskInputService` 服务实例，调用其 `process_text_input` 方法处理文本输入，

服务层会使用simple\_chat模型，套用TASK\_INPUT\_PARSING\_TEMPLATE模版，提取任务名称、学科、预计时长等结构化信息。解析后的任务会被存储到数据库（通过 `DailyTaskService.create_task` 方法）。

### 入参（Request Parameters）

| 字段名           | 描述   | 类型  | 是否必填 | 说明 | 入参类型 |
| ------------- | ---- | --- | ---- | -- | ---- |
| child\_id     | 学生ID | int | 必填   |    | body |
| text\_content | 文本内容 | str | 必填   |    | body |

### 示例（Examples）：完整的请求/响应示例

```plain&#x20;text
curl -X 'POST' \
  'http://localhost:8011/api/v1/multimodal-task-input/text' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "child_id": 4,
  "text_content": "语文作业"
}'
```

```json
{
  "success": true,
  "message": "成功处理text输入，解析并存储0个任务",
  "child_id": 4,
  "input_type": "text",
  "tasks": [
    {
      "task_name": "语文作业",
      "subject": "语文",
      "description": "完成语文作业",
      "estimated_duration": 30,
      "difficulty_level": 3,
      "materials_needed": "语文课本、练习本、笔",
      "time_requirement": "",
      "priority": "高",
      "notes": "由于输入信息不明确，默认完成常规语文作业任务",
      "sub_tasks": []
    }
  ],
  "stored_task_ids": [],
  "total_tasks": 1,
  "stored_tasks": 0,
  "processed_at": "2025-07-03T01:45:32.709338Z"
}
```

## 12.2处理语音输入的任务process multimodal voice task input【存储失败，已解决】

## 请求方式：POST

### Request  URL：/api/v1/multimodal-task-input/voice

### 功能逻辑：



### 入参（Request Parameters）：同12.1

## 12.3处理图片输入的任务process multimodal image task input

### 请求方式：POST

### Request  URL：/api/v1/multimodal-task-input/image

### 功能逻辑：



### 入参（Request Parameters）

| 字段名         | 描述   | 类型          | 是否必填 | 说明 | 入参类型 |
| ----------- | ---- | ----------- | ---- | -- | ---- |
| child\_id   | 学生ID | int         | 必填   |    | body |
| image\_file | 图片文件 | str（binary） | 必填   |    | body |



## 12.4处理图片输入的任务【与12.3的区别，用于图像添加任务（只是将识别的内容填入输入框，不直接创建任务），只解析，不存储】

将12.3和12.4接口合并了，在12.3接口的基础上新增一个参数：“是否存储”，删除了12.4接口，从而减少冗余。

## 12.5健康点检测【可删】



# 13.任务计划

## 13.1保存前端生成任务【未使用，可删】

## 13.2获取指定学生今日的任务计划【未使用，可删】

## 13.3删除指定任务【未使用，可删】

## 13.4更新任务by task ID【可删，前端没用】

## 13.为指定任务添加子任务add subtask

### Request  URL：/api/v1/task-plan/task/{task\_id}/subtask【可删，前端实际使用使用的是`/api/add_subtask`接口】

# 14.任务确定

## 14.1确认任务数据接口

### 请求方式：POST

### Request  URL：/api/check\_task\_data

### 功能逻辑：

该接口在 `checkTaskButton`（任务确认按钮）的点击事件中被调用，触发时机为用户尝试确认任务执行时。是任务状态验证接口

### 入参（Request Parameters）：无

### 示例（Examples）：完整的请求/响应示例

```plain&#x20;text
curl -X 'POST' \
  'http://localhost:8011/api/check_task_data' \
  -H 'accept: application/json' \
  -d ''
```

```plain&#x20;text
{
  "success": true,
  "message": "任务确认成功",
  "data": {}
}
```

## 14.2提交完成任务接口

### 请求方式：POST

### Request  URL：/api/sumbitFinishTask

### 功能逻辑：

* 当前逻辑：仅记录日志并返回成功响应（`success: true`），未实现具体业务逻辑。

* 预留扩展：注释提示可添加以下功能（需结合业务需求实现）：

  * 标记任务为完成状态（更新 `daily_tasks` 表的 `status` 字段为 `completed`）。

  * 计算任务积分（根据完成情况更新 `total_points` 或 `bonus_points`）。

  * 记录任务完成时间（更新 `updated_at` 字段）。

  * 关联子任务状态（同步更新 `task_items` 表中对应子任务的 `status`）。

### 入参（Request Parameters）:无

### 示例（Examples）：完整的请求/响应示例

```plain&#x20;text
curl -X 'POST' \
  'http://localhost:8011/api/sumbitFinishTask' \
  -H 'accept: application/json' \
  -d ''
```

```plain&#x20;text
{
  "success": true,
  "message": "任务提交成功",
  "data": {}
}
```

## 14.3确认任务计划接口confirm\_task\_plan

### 请求方式：POST

### Request  URL：/api/confirm\_task\_plan

### 功能逻辑：

将前端个性化任务计划展示的任务相关信息存入数据库， 并返回带有任务ID的完整数据给前端。

通过 `DailyTaskService` 的 `confirm_task_plan` 方法，将任务计划数据（`task_plan`）、学生ID（`child_id`）和计划ID（`planID`）传递给服务层，执行数据库存储操作。调用 `DailyTaskService.get_today_tasks_with_subtasks` 方法，获取今日任务及其子任务数据（包含数据库自动生成的任务ID），并将其添加到结果中返回给前端。

### 入参（Request Parameters）

| 字段名        | 描述   | 类型   | 是否必填 | 说明 | 入参类型 |
| ---------- | ---- | ---- | ---- | -- | ---- |
| child\_id  | 孩子ID | int  | 是    |    | body |
| task\_plan | 计划任务 | list | 是    |    | body |
| planID     | 计划ID | int  | 是    |    | body |

## 14.4存储任务计划并立即返回带ID的任务数据【可删除，该函数的功能是 `confirm_task_plan` + `get_today_tasks_with_subtasks` 的组合，但前端选择分别调用这两个接口】

### 请求方式：

### Request  URL：

### 功能逻辑：



### 入参（Request Parameters）

| 字段名 | 描述 | 类型 | 是否必填 | 说明 | 入参类型 |
| --- | -- | -- | ---- | -- | ---- |
|     |    |    |      |    |      |
|     |    |    |      |    |      |

### 示例（Examples）：完整的请求/响应示例

```plain&#x20;text
```

```json
```

## 14. 5获取今日任务及其子任务接get\_today\_tasks

### 请求方式：GET

### Request  URL：/api/get\_today\_tasks/{child\_id}

### 功能逻辑：

通过 `DailyTaskService` 服务实例调用 `get_today_tasks_with_subtasks` 方法，执行具体的数据库查询逻辑。该方法负责：

* 查询 `DailyTask` 表中属于该学生且日期为今日的任务。

* 关联查询每个任务的子任务（`TaskItem` 表）。

* 将任务与子任务数据整合为结构化结果返回。

### 入参（Request Parameters）：孩子ID





# 15.子任务

## 15.1添加子任务add\_subtask

### 请求方式：POST

### Request  URL：/api/add\_subtask

### 功能逻辑：

通过 `DailyTaskService` 实例调用 `add_subtask` 方法，将请求参数传递给服务层，执行实际的子任务添加逻辑。

### 入参（Request Parameters）

| 字段名                | 描述 | 类型  | 是否必填 | 说明 | 入参类型 |
| ------------------ | -- | --- | ---- | -- | ---- |
| child\_id          |    | int |      |    |      |
| plan\_id           |    | int |      |    |      |
| sub\_task\_content |    | str |      |    |      |
| daily\_task\_id    |    | int |      |    |      |
| sub\_task\_source  |    | str |      |    |      |
| time\_slot         |    | str | 否    |    |      |

## 15.2删除子任务remove subtask

### 请求方式：POST

### Request  URL：/api/remove\_subtask

### 功能逻辑：

* 根据是否提供`sub_task_id`，记录不同的操作日志（如“通过ID删除子任务: 123”或“删除子任务: 练习册第10页 从任务: 数学作业”）。

* 服务层调用：通过 `DailyTaskService` 实例调用 `remove_subtask` 方法，传递 `task_name`、`sub_task_content` 和 `sub_task_id`，执行实际的软删除操作（设置子任务的 `is_active` 字段为 `False`）。

### 入参（Request Parameters）

| 字段名                | 描述 | 类型  | 是否必填 | 说明 | 入参类型 |
| ------------------ | -- | --- | ---- | -- | ---- |
| child\_id          |    | int |      |    |      |
| task\_name         |    | str |      |    |      |
| sub\_task\_content |    | str |      |    |      |
| sub\_task\_id      |    | int |      |    |      |

## 15.3撤销删除子任务接口restore\_subtask

### 请求方式：POST

### Request  URL：/api/restore\_subtask

### 功能逻辑：

* 日志记录：根据是否提供 `sub_task_id`，记录不同的操作日志（如“通过ID撤销删除子任务: 123”或“撤销删除子任务: 练习册第10页 从任务: 数学作业”）。

* 服务层调用：通过 `DailyTaskService` 实例调用 `restore_subtask` 方法，传递 `task_name`、`sub_task_content` 和 `sub_task_id`，执行实际的恢复操作（将子任务的 `is_active` 字段设为 `True`）。

### 入参（Request Parameters）：与15.2相同

## 15.4获取指定任务的所有子任务【未使用，可删除】



# 16.task-plans

## 16.1创建新的任务计划create task plan

### 请求方式：post

### Request  URL：/api/task-plans/

### 功能逻辑：

【并触发任务确认流程】这个可以删除

1. **任务计划创建**

调用 `TaskPlan.create_plan` 方法，传入会话、学生ID和计划内容，创建新的任务计划记录到数据库的 `TaskPlan` 表中。若创建失败（返回 `None`），记录错误并返回 `500 Internal Server Error`。

***

* **数据库事务提交**

创建成功后，通过 `session.commit()` 提交事务，确保任务计划数据持久化到数据库。

***

* **构造响应对象**

使用 `TaskPlanResponse` 模型封装返回数据，包含任务计划的ID、学生ID、内容、创建时间和更新时间（格式化为ISO字符串）。

### 入参（Request Parameters）：child\_id，contend

## 16.2获取任务计划列表【可删，没用上】

## 16.3根据ID获取任务计划【可删，没用上】

## 16.4更新任务计划【可删，没用上】

## 16.5删除任务计划【可删，没用上】

## 16.6根据ID获取任务计划，包含学科任务及子任务信息

### 请求方式：get

### Request  URL：/api/task-plans/getplanbychildid/{child\_id}

### 功能逻辑：

1. **查询最新任务计划**

* 过滤条件：`TaskPlan.child_id == child_id`（仅查询该学生的任务计划）。

* 排序：按 `created_at`（创建时间）降序排列，确保获取的是最新的任务计划。

* 校验：若未找到任务计划（`latest_task_plan` 为 `None`），抛出 `404 Not Found` 异常。

***

* **获取关联的每日任务（DailyTask）**

根据最新任务计划的ID（`latest_task_plan.id`），查询所有关联的、有效的每日任务：

* 关联条件：`DailyTask.plan_id == latest_task_plan.id`（每日任务属于该任务计划）。

* 有效性校验：`DailyTask.is_active == True`（仅查询未被删除的每日任务）。

***

* **遍历每日任务并收集子任务**

对每个每日任务（`daily_task`），查询其关联的子任务（`TaskItem`）：

* 关联条件：`TaskItem.daily_task_id == daily_task.id`（子任务属于该每日任务）。

* 有效性校验：`TaskItem.is_active == True`（仅查询未被删除的子任务）。

### 入参（Request Parameters）

| 字段名       | 描述 | 类型 | 是否必填 | 说明 | 入参类型 |
| --------- | -- | -- | ---- | -- | ---- |
| child\_id |    |    |      |    | path |

## 16.7获取指定计划下的所有任务【可删，没用上】

## 16.8更新子任务时间

### 请求方式：post

### Request  URL：/api/task-plans/updateSubtaskTime

### 功能逻辑：

通过 `session.query(TaskItem).filter(TaskItem.id == request.sub_task_id).first()` 查询子任务记录：

* 若未找到（`sub_task` 为 `None`），抛出 `404 Not Found` 异常。

***

1. **解析现有时间槽**

从子任务的 `time_slot` 字段（格式如 `"18:00 - 18:30"`）中提取当前的开始时间和结束时间：

* 若 `time_slot` 为空（如初始状态），默认设置为 `"00:00 - 00:00"`。

***

* **更新时间槽**

根据 `time_type` 更新对应时间：

* 若 `time_type` 为 `start`，用 `new_time` 替换开始时间。

* 若 `time_type` 为 `end`，用 `new_time` 替换结束时间。

***

* **持久化修改**

- 更新子任务的 `time_slot` 字段为新的时间范围（如 `"18:30 - 19:00"`）。

- 更新 `updated_at` 字段为当前时间（记录最后修改时间）。

- 提交数据库会话（`session.commit()`），将修改持久化到数据库。

### 入参（Request Parameters）

| 字段名           | 描述 | 类型  | 是否必填 | 说明 | 入参类型 |
| ------------- | -- | --- | ---- | -- | ---- |
| sub\_task\_id |    | int |      |    |      |
| new\_time     |    | str |      |    |      |
| time\_type    |    | str |      |    |      |



# 文件上传（file- upload）

### **API 端点概览**

| 端点                                            | 方法     | 描述       |
| --------------------------------------------- | ------ | -------- |
| `/api/v1/files/upload`                        | POST   | 单文件上传    |
| `/api/v1/files/upload-multiple`               | POST   | 批量文件上传   |
| `/api/v1/files/upload-with-conflict-handling` | POST   | 带冲突处理的上传 |
| `/api/v1/files/validate`                      | POST   | 仅验证文件    |
| `/api/v1/files/delete`                        | DELETE | 删除文件     |
| `/api/v1/files/info`                          | GET    | 获取文件信息   |
| `/api/v1/files/list`                          | GET    | 列出目录文件   |
| `/api/v1/files/config`                        | GET    | 获取上传配置   |
| `/api/v1/files/health`                        | GET    | 服务健康检查   |
| `/api/v1/files/check-conflict`                | POST   | 检查文件冲突   |

### 1.文件上传配置类：

配置路径：core/file\_upload/config.py

```python
# 允许的文件扩展名白名单
    ALLOWED_EXTENSIONS: Set[str] = {
        # 图片文件
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg',
        # 文档文件
        '.pdf', '.doc', '.docx', '.txt', '.md', '.rtf',
        # 表格文件
        '.xls', '.xlsx', '.csv',
        # 压缩文件
        '.zip', '.rar', '.7z',
        # 音频文件
        '.mp3', '.wav', '.m4a', '.aac',
        # 视频文件
        '.mp4', '.avi', '.mov', '.wmv', '.flv',
        # 其他
        '.json', '.xml'
    }
    
    # 允许的MIME类型白名单
    ALLOWED_MIME_TYPES: Set[str] = {
        # 图片
        'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 
        'image/webp', 'image/svg+xml',
        # 文档
        'application/pdf', 'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain', 'text/markdown', 'application/rtf',
        # 表格
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv',
        # 压缩
        'application/zip', 'application/x-rar-compressed',
        'application/x-7z-compressed',
        # 音频
        'audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/aac',
        # 视频
        'video/mp4', 'video/x-msvideo', 'video/quicktime',
        'video/x-ms-wmv', 'video/x-flv',
        # 其他
        'application/json', 'application/xml', 'text/xml'
    }
    
    # 文件大小限制 (字节)
    MAX_FILE_SIZE: Dict[str, int] = {
        'image': 10 * 1024 * 1024,      # 10MB
        'document': 50 * 1024 * 1024,   # 50MB
        'video': 500 * 1024 * 1024,     # 500MB
        'audio': 100 * 1024 * 1024,     # 100MB
        'archive': 100 * 1024 * 1024,   # 100MB
        'default': 10 * 1024 * 1024     # 10MB
    }
    
    # 允许的存储路径白名单
    ALLOWED_UPLOAD_PATHS: List[str] = [
        'uploads/images',
        'uploads/documents', 
        'uploads/videos',
        'uploads/audio',
        'uploads/archives',
        'uploads/temp',
        'uploads/user_files',
        'uploads/learning_materials'
    ]
    
    # 文件类型分类
    FILE_TYPE_CATEGORIES: Dict[str, List[str]] = {
        'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
        'document': ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf'],
        'spreadsheet': ['.xls', '.xlsx', '.csv'],
        'archive': ['.zip', '.rar', '.7z'],
        'audio': ['.mp3', '.wav', '.m4a', '.aac'],
        'video': ['.mp4', '.avi', '.mov', '.wmv', '.flv'],
        'data': ['.json', '.xml']
    }
    
    # 危险文件扩展名黑名单
    DANGEROUS_EXTENSIONS: Set[str] = {
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
        '.jar', '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl',
        '.sh', '.bash', '.ps1', '.msi', '.deb', '.rpm', '.dmg'
    }
```

### 2.单文件上传 upload file&#x20;

| `/api/v1/files/upload` | POST | 单文件上传 |
| ---------------------- | ---- | ----- |

入参说明：

* **file**: 要上传的文件

* **upload\_path**: 上传路径（必须在白名单中）

* **user\_id**: 用户ID（可选）

* **max\_size**: 最大文件大小限制（字节）

* **allowed\_types**: 允许的文件扩展名，如 ".jpg,.png,.pdf"

```python
# 基础文件上传（注意修改端口号）
curl -X POST "http://localhost:8012/api/v1/files/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/your/file.jpg" \
  -F "upload_path=uploads/images" \
  -F "user_id=123" \
  -F "max_size=5242880"

# 指定允许的文件类型
curl -X POST "http://localhost:8012/api/v1/files/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/document.pdf" \
  -F "upload_path=uploads/documents" \
  -F "allowed_types=.pdf,.doc,.docx"
```

```json
{
  "success": true,
  "message": "文件上传成功",
  "file_info": {
    "user_id": 123,
    "original_filename": "document.pdf",
    "saved_filename": "document.pdf",
    "file_path": "/uploads/documents/document.pdf",
    "file_size": 1048576,
    "file_type": "document",
    "mime_type": "application/pdf",
    "file_hash": "sha256_hash_here",
    "upload_time": "2025-07-01T12:00:00Z",
    "status": "success"
  },
  "file_url": "/uploads/documents/document.pdf",
  "validation_info": {
    "valid": true,
    "filename": "document.pdf",
    "safe_filename": "document.pdf",
    "file_size": 1048576,
    "file_type": "document",
    "mime_type": "application/pdf",
    "errors": []
  }
}
```

使用示例：

```python
# 单文件上传
def upload_file(file_path, upload_path="uploads/temp", user_id=None):
    url = "http://localhost:端口号/api/v1/files/upload"
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {
            'upload_path': upload_path,
            'user_id': user_id,
            'allowed_types': '.jpg,.png,.pdf'
        }
        
        response = requests.post(url, files=files, data=data)
        return response.json()

# 使用示例
result = upload_file('example.jpg', 'uploads/images', 123)
print(result)
```

### 3.批量文件上传

| `/api/v1/files/upload-multiple` | POST | 批量文件上传 |
| ------------------------------- | ---- | ------ |

* **files**: 要上传的文件列表

* **upload\_path**: 上传路径（必须在白名单中）

* **user\_id**: 用户ID（可选）

* **max\_size**: 最大文件大小限制（字节）

* **allowed\_types**: 允许的文件扩展名，如 ".jpg,.png,.pdf"

```bash
curl -X POST "http://localhost:8012/api/v1/files/upload-multiple" \
  -H "Content-Type: multipart/form-data" \
  -F "files=@/path/to/file1.jpg" \
  -F "files=@/path/to/file2.png" \
  -F "files=@/path/to/file3.gif" \
  -F "upload_path=uploads/gallery" \
  -F "user_id=123" \
  -F "allowed_types=.jpg,.png,.gif"
```

```json
{
  "success_count": 2,
  "failed_count": 1,
  "total_count": 3,
  "successful_uploads": [
    {
      "success": true,
      "message": "文件上传成功",
      "file_info": {
        "original_filename": "file1.jpg",
        "saved_filename": "file1.jpg",
        "file_size": 524288
      }
    }
  ],
  "failed_uploads": [
    {
      "filename": "file3.gif",
      "error": "文件大小超出限制"
    }
  ],
  "total_size": 1048576
}
```



### 4.带冲突处理的上传

| `/api/v1/files/upload-with-conflict-handling` | POST | 带冲突处理的上传 |
| --------------------------------------------- | ---- | -------- |

支持的冲突处理策略:

* rename: 自动重命名（默认）

* replace: 覆盖现有文件

* cancel: 取消上传

```bash
# 自动重命名策略
curl -X POST "http://localhost:8012/api/v1/files/upload-with-conflict-handling" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/existing_file.jpg" \
  -F "upload_path=uploads/images" \
  -F "conflict_strategy=rename" \
  -F "check_content=true"

# 覆盖策略（带备份）
curl -X POST "http://localhost:8012/api/v1/files/upload-with-conflict-handling" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/new_version.jpg" \
  -F "upload_path=uploads/images" \
  -F "conflict_strategy=replace" \
  -F "create_backup=true"
```

```json
{
  "success": true,
  "message": "文件上传成功",
  "file_info": {
    "original_filename": "image.jpg",
    "saved_filename": "image_1719840000.jpg",
    "conflict_handled": true,
    "strategy_used": "rename"
  },
  "file_url": "/uploads/images/image_1719840000.jpg",
  "conflict_info": {
    "conflict_detected": true,
    "existing_file": {
      "file_name": "image.jpg",
      "file_size": 1024000,
      "modified_time": "2025-07-01T11:00:00Z"
    },
    "suggested_strategy": "rename",
    "suggested_filename": "image_1719840000.jpg",
    "conflict_reason": "文件 image.jpg 已存在于目标路径"
  },
  "operation_log": "检测到文件冲突: 文件 image.jpg 已存在于目标路径; 文件已重命名为: image_1719840000.jpg; 文件成功保存到: /uploads/images/image_1719840000.jpg"
}
```

### 5.验证文件是否冲突

| `/api/v1/files/validate` | POST | 仅验证文件 |
| ------------------------ | ---- | ----- |

仅验证文件，不实际上传

* **file**: 要验证的文件

* **upload\_path**: 目标上传路径

* **max\_size**: 最大文件大小限制（字节）

```bash
curl -X POST "http://localhost:8011/api/v1/files/validate" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/test_file.pdf" \
  -F "upload_path=uploads/documents" \
  -F "max_size=10485760"
```

### 6.删除文件&#x20;

| `/api/v1/files/delete` | DELETE | 删除文件 |
| ---------------------- | ------ | ---- |

* **file\_path**: 要删除的文件路径

* **user\_id**: 用户ID（可选）

```bash
curl -X DELETE "http://localhost:8011/api/v1/files/delete?file_path=/uploads/temp/old_file.txt&user_id=123"
```

### 7.获取文件信息

| `/api/v1/files/info` | GET | 获取文件信息 |
| -------------------- | --- | ------ |

* **file\_path**: 文件路径

```bash
curl -X 'GET' \
  'http://localhost:8012/api/v1/files/info?file_path=uploads%2Fuploads%2Ftemp%2Fconflict_test_1750229636.txt' \
  -H 'accept: application/json'
```

```json
{
  "file_path": "uploads/uploads/temp/conflict_test_1750229636.txt",
  "file_name": "conflict_test_1750229636.txt",
  "file_size": 40,
  "created_time": "2025-06-19T07:26:25.236454+00:00",
  "modified_time": "2025-06-19T07:26:14.201220+00:00",
  "file_extension": ".txt",
  "is_file": true,
  "is_readable": true,
  "is_writable": true
}
```



### 8.列出文件目录

| `/api/v1/files/list` | GET | 列出目录文件 |
| -------------------- | --- | ------ |

列出目录中的文件

* **directory**: 目录路径

* **user\_id**: 用户ID（可选）

```bash
curl -X 'GET' \
  'http://localhost:8012/api/v1/files/list?directory=uploads%2Fuploads%2Fuser_files' \
  -H 'accept: application/json'
```

```json
{
  "directory": "uploads/uploads/user_files",
  "file_count": 1,
  "files": [
    {
      "file_path": "uploads/uploads/user_files/优化版中国湖泊扰动指数空间分布图.png",
      "file_name": "优化版中国湖泊扰动指数空间分布图.png",
      "file_size": 16944275,
      "created_time": "2025-06-19T07:26:55.560119+00:00",
      "modified_time": "2025-06-19T07:26:20.948842+00:00",
      "file_extension": ".png",
      "is_file": true,
      "is_readable": true,
      "is_writable": true
    }
  ]
}
```

### 9.获取上传配置

获取文件上传配置信息

| `/api/v1/files/config` | GET | 获取上传配置 |
| ---------------------- | --- | ------ |

```bash
curl -X 'GET' \
  'http://localhost:8012/api/v1/files/config' \
  -H 'accept: application/json'
```

### 10.检测文件冲突

| `/api/v1/files/check-conflict` | POST | 检查文件冲突 |
| ------------------------------ | ---- | ------ |

检查文件冲突（不执行上传）

用于在上传前预检查是否存在文件冲突

传入参数类型：

```json
{
  "filename": "string",
  "upload_path": "string",
  "file_size": 0,
  "file_hash": "string"
}
```

# 每日学习数据路由（daily-learning)- Influx DB

### 1.**创建学习记录**

**端点**: \`POST /api/v1/planning/daily-learning/learning-records\`

创建每日学习记录

* **child\_id**: 孩子ID

* **subject**: 学科名称

* **activity\_type**: 活动类型（lesson, exercise, test, project, game）

* **study\_duration\_minutes**: 学习时长（分钟）

* **completion\_rate**: 完成率（0-100）

* **accuracy\_rate**: 正确率（0-100）

* **score**: 得分

* **enjoyment\_rating**: 享受程度（1-5）

* **notes**: 学习笔记

```bash
# 基础学习记录
curl -X POST "http://localhost:8011/api/v1/planning/daily-learning/learning-records" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "subject": "数学",
    "activity_type": "homework",
    "study_duration_minutes": 45.0,
    "completion_rate": 85.5,
    "accuracy_rate": 92.0,
    "enjoyment_rating": 4,
    "concentration_level": 3,
    "internal_interruptions": 2,
    "points_earned": 85
  }'

# 详细学习记录
curl -X POST "http://localhost:8011/api/v1/planning/daily-learning/learning-records" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "subject": "英语",
    "activity_type": "lesson",
    "difficulty_level": 3,
    "concentration_level": 4,
    "internal_interruptions": 1,
    "desk_leaving_times": 0,
    "homework_completion_rate": 100.0,
    "completion_rate": 95.0,
    "accuracy_rate": 88.0,
    "total_duration_minutes": 60.0,
    "subject_duration_minutes": 45.0,
    "study_duration_minutes": 45.0,
    "is_behind_schedule": false,
    "schedule_deviation_minutes": -5.0,
    "points_earned": 90,
    "bonus_points": 10,
    "is_strong_subject": true,
    "subject_performance_level": 4,
    "score": 88.0,
    "max_score": 100.0,
    "enjoyment_rating": 5,
    "difficulty_rating": 3,
    "motivation_level": 4,
    "questions_asked": 3,
    "help_requests": 1,
    "break_times": 2,
    "notes": "今天英语学习状态很好，主动提问积极"
  }'
```

```json
{
  "success": true,
  "message": "成功添加孩子1的数学学习记录",
  "timestamp": "2025-07-01T12:00:00.000Z"
}
```

### 2.**获取学习记录**

**端点**: \`GET /api/v1/planning/daily-learning/learning-records/{child\_id}\`



```bash
# 获取所有学习记录
curl "http://localhost:8011/api/v1/planning/daily-learning/learning-records/1"

# 按学科筛选
curl "http://localhost:8011/api/v1/planning/daily-learning/learning-records/1?subject=数学&limit=10"

# 按时间范围筛选
curl "http://localhost:8011/api/v1/planning/daily-learning/learning-records/1?start_date=2025-06-01T00:00:00Z&end_date=2025-07-01T23:59:59Z&limit=50"

# 组合筛选条件
curl "http://localhost:8011/api/v1/planning/daily-learning/learning-records/1?subject=英语&start_date=2025-06-15T00:00:00Z&limit=20"
```

### 3.删除学习记录

**端点**: \`DELETE /api/v1/planning/daily-learning/learning-records\`

删除指定时间范围的学习记录

* **child\_id**: 孩子ID

* **start\_date**: 开始日期

* **end\_date**: 结束日期

* **subject**: 学科筛选（可选）

输入格式：

```json
{
  "child_id": 0,
  "start_date": "2025-07-01T08:12:56.458Z",
  "end_date": "2025-07-01T08:12:56.458Z",
  "subject": "string"
}
```

```bash
# 删除指定时间范围的记录
curl -X DELETE "http://localhost:8011/api/v1/planning/daily-learning/learning-records" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "start_date": "2025-06-01T00:00:00Z",
    "end_date": "2025-06-30T23:59:59Z"
  }'

# 删除特定学科的记录
curl -X DELETE "http://localhost:8011/api/v1/planning/daily-learning/learning-records" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "subject": "数学",
    "start_date": "2025-06-15T00:00:00Z",
    "end_date": "2025-06-20T23:59:59Z"
  }'
```

```json
{
  "success": true,
  "message": "成功删除孩子1在指定时间范围内的学习记录",
  "deleted_period": {
    "start_date": "2025-06-01T00:00:00Z",
    "end_date": "2025-06-30T23:59:59Z",
    "subject": null
  }
}
```

### 4.**获取学习统计**

**端点**: \`GET /api/v1/planning/daily-learning/learning-statistics/{child\_id}\`

#### **cURL 示例**

```bash
# 获取7天统计
curl "http://localhost:8011/api/v1/planning/daily-learning/learning-statistics/1?days=7"

# 获取30天数学统计
curl "http://localhost:8011/api/v1/planning/daily-learning/learning-statistics/1?days=30&subject=数学"

# 获取一年统计
curl "http://localhost:8011/api/v1/planning/daily-learning/learning-statistics/1?days=365"
```

#### **响应示例**

```json
{
  "child_id": 1,
  "period_days": 7,
  "subject": null,
  "total_study_time": 315.0,
  "average_daily_time": 45.0,
  "total_sessions": 12,
  "average_completion_rate": 87.5,
  "average_accuracy_rate": 89.2,
  "average_enjoyment_rating": 4.1,
  "average_concentration_level": 3.8,
  "total_points_earned": 1020,
  "subject_breakdown": {
    "数学": {
      "total_time": 135.0,
      "sessions": 5,
      "avg_completion_rate": 85.0,
      "avg_accuracy_rate": 91.0
    },
    "英语": {
      "total_time": 120.0,
      "sessions": 4,
      "avg_completion_rate": 92.0,
      "avg_accuracy_rate": 87.0
    },
    "语文": {
      "total_time": 60.0,
      "sessions": 3,
      "avg_completion_rate": 85.0,
      "avg_accuracy_rate": 89.0
    }
  },
  "trends": {
    "completion_rate_trend": "improving",
    "study_time_trend": "stable",
    "concentration_trend": "improving"
  }
}
```

# 学习计划数据路由  (planning)- Influx DB

### 1.创建学习计划

**端点**: \`POST /api/v1/planning/plans/plans\`

* **child\_id**: 学生ID

* **task\_name**: 任务名称

* **time\_slot**: 时段（格式：HH:MM - HH:MM）

* **subject**: 学科

* **sub\_tasks**: 子任务列表

* **customization**: 定制说明（可选）

* **difficulty**: 难点（可选）

* **solution**: 解决方案（可选）

* **confidence\_index**: 执行信心指数1-5（可选）

* **plan\_date**: 计划日期（可选）

* **status**: 计划状态（可选，默认pending）

* **notes**: 备注（可选）

#### **cURL 示例**

```bash
# 基础学习计划
curl -X POST "http://localhost:8011/api/v1/planning/plans/plans" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "task_name": "数学作业",
    "time_slot": "18:00 - 18:45",
    "subject": "数学",
    "sub_tasks": [
      {
        "task_content": "练习册第9页",
        "time_slot": "18:00-18:15",
        "estimated_minutes": 15,
        "order_index": 1
      },
      {
        "task_content": "复习乘法表",
        "time_slot": "18:15-18:30",
        "estimated_minutes": 15,
        "order_index": 2
      },
      {
        "task_content": "完成应用题",
        "time_slot": "18:30-18:45",
        "estimated_minutes": 15,
        "order_index": 3
      }
    ],
    "customization": "针对乘法表记忆困难",
    "difficulty": "乘法表记忆",
    "solution": "听乘法口诀歌",
    "confidence_index": 3
  }'

# 详细学习计划
curl -X POST "http://localhost:8011/api/v1/planning/plans/plans" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "task_name": "英语综合练习",
    "time_slot": "19:00 - 20:00",
    "subject": "英语",
    "sub_tasks": [
      {
        "task_content": "单词背诵",
        "time_slot": "19:00-19:20",
        "estimated_minutes": 20,
        "order_index": 1
      },
      {
        "task_content": "听力练习",
        "time_slot": "19:20-19:40",
        "estimated_minutes": 20,
        "order_index": 2
      },
      {
        "task_content": "口语练习",
        "time_slot": "19:40-20:00",
        "estimated_minutes": 20,
        "order_index": 3
      }
    ],
    "customization": "加强听说能力训练",
    "difficulty": "听力理解",
    "solution": "多听英语歌曲和故事",
    "confidence_index": 4,
    "plan_date": "2025-07-02T00:00:00Z",
    "status": "pending",
    "notes": "重点关注发音准确性"
  }'
```

#### **响应示例**

```json
{
  "success": true,
  "message": "成功创建学生1的数学学习计划",
  "plan_id": "plan_1719840000_1_math",
  "timestamp": "2025-07-01T12:00:00.000Z"
}
```

### 2.获取学习计划

**端点**: \`GET /api/v1/planning/plans/plans/{child\_id}\`

#### **cURL 示例**

```bash
# 获取所有学习计划
curl "http://localhost:8011/api/v1/planning/plans/plans/1"

# 按学科筛选
curl "http://localhost:8011/api/v1/planning/plans/plans/1?subject=数学&limit=10"

# 按状态筛选
curl "http://localhost:8011/api/v1/planning/plans/plans/1?status=pending&limit=20"

# 按时间范围筛选
curl "http://localhost:8011/api/v1/planning/plans/plans/1?start_date=2025-07-01T00:00:00Z&end_date=2025-07-07T23:59:59Z"

# 组合筛选条件
curl "http://localhost:8011/api/v1/planning/plans/plans/1?subject=英语&status=completed&start_date=2025-06-01T00:00:00Z&limit=15"
```

#### **响应示例**

```json
[
  {
    "plan_id": "plan_1719840000_1_math",
    "child_id": 1,
    "task_name": "数学作业",
    "time_slot": "18:00 - 18:45",
    "subject": "数学",
    "sub_tasks": [
      {
        "task_content": "练习册第9页",
        "time_slot": "18:00-18:15",
        "estimated_minutes": 15,
        "order_index": 1
      },
      {
        "task_content": "复习乘法表",
        "time_slot": "18:15-18:30",
        "estimated_minutes": 15,
        "order_index": 2
      },
      {
        "task_content": "完成应用题",
        "time_slot": "18:30-18:45",
        "estimated_minutes": 15,
        "order_index": 3
      }
    ],
    "customization": "针对乘法表记忆困难",
    "difficulty": "乘法表记忆",
    "solution": "听乘法口诀歌",
    "confidence_index": 3,
    "plan_date": "2025-07-01T00:00:00Z",
    "status": "pending",
    "created_at": "2025-07-01T12:00:00.000Z",
    "updated_at": "2025-07-01T12:00:00.000Z"
  },
  {
    "plan_id": "plan_1719843600_1_english",
    "child_id": 1,
    "task_name": "英语综合练习",
    "time_slot": "19:00 - 20:00",
    "subject": "英语",
    "sub_tasks": [
      {
        "task_content": "单词背诵",
        "time_slot": "19:00-19:20",
        "estimated_minutes": 20,
        "order_index": 1
      },
      {
        "task_content": "听力练习",
        "time_slot": "19:20-19:40",
        "estimated_minutes": 20,
        "order_index": 2
      }
    ],
    "customization": "加强听说能力训练",
    "difficulty": "听力理解",
    "solution": "多听英语歌曲和故事",
    "confidence_index": 4,
    "plan_date": "2025-07-02T00:00:00Z",
    "status": "in_progress",
    "notes": "重点关注发音准确性",
    "created_at": "2025-07-01T13:00:00.000Z",
    "updated_at": "2025-07-01T13:30:00.000Z"
  }
]
```

### 3.**获取计划详情**

**端点**: \`GET /api/v1/planning/plans/plans/detail/{plan\_id}\`

根据计划ID获取单个计划详情

* **plan\_id**: 计划ID

#### **cURL 示例**

```bash
# 获取特定计划详情
curl "http://localhost:8011/api/v1/planning/plans/plans/detail/plan_1719840000_1_math"
```

#### **响应示例**

```json
{
  "plan_id": "plan_1719840000_1_math",
  "child_id": 1,
  "task_name": "数学作业",
  "time_slot": "18:00 - 18:45",
  "subject": "数学",
  "sub_tasks": [
    {
      "task_content": "练习册第9页",
      "time_slot": "18:00-18:15",
      "estimated_minutes": 15,
      "order_index": 1
    },
    {
      "task_content": "复习乘法表",
      "time_slot": "18:15-18:30",
      "estimated_minutes": 15,
      "order_index": 2
    },
    {
      "task_content": "完成应用题",
      "time_slot": "18:30-18:45",
      "estimated_minutes": 15,
      "order_index": 3
    }
  ],
  "customization": "针对乘法表记忆困难",
  "difficulty": "乘法表记忆",
  "solution": "听乘法口诀歌",
  "confidence_index": 3,
  "plan_date": "2025-07-01T00:00:00Z",
  "status": "pending",
  "created_at": "2025-07-01T12:00:00.000Z",
  "updated_at": "2025-07-01T12:00:00.000Z"
}
```

### 4.**更新学习计划**

**端点**: \`PUT /api/v1/planning/plans/plans/{plan\_id}\`

#### **cURL 示例**

```bash
# 更新计划状态
curl -X PUT "http://localhost:8011/api/v1/planning/plans/plans/plan_1719840000_1_math" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "completed"
  }'

# 更新计划内容
curl -X PUT "http://localhost:8011/api/v1/planning/plans/plans/plan_1719840000_1_math" \
  -H "Content-Type: application/json" \
  -d '{
    "task_name": "数学作业（已调整）",
    "time_slot": "18:00 - 19:00",
    "confidence_index": 4,
    "notes": "增加了额外的练习时间"
  }'

# 更新子任务
curl -X PUT "http://localhost:8011/api/v1/planning/plans/plans/plan_1719840000_1_math" \
  -H "Content-Type: application/json" \
  -d '{
    "sub_tasks": [
      {
        "task_content": "练习册第9-10页",
        "time_slot": "18:00-18:20",
        "estimated_minutes": 20,
        "order_index": 1
      },
      {
        "task_content": "复习乘法表",
        "time_slot": "18:20-18:40",
        "estimated_minutes": 20,
        "order_index": 2
      },
      {
        "task_content": "完成应用题",
        "time_slot": "18:40-19:00",
        "estimated_minutes": 20,
        "order_index": 3
      }
    ]
  }'
```

#### **响应示例**

```json
{
  "success": true,
  "message": "成功更新计划plan_1719840000_1_math",
  "plan_id": "plan_1719840000_1_math",
  "timestamp": "2025-07-01T14:00:00.000Z"
}
```





### 5.**删除学习计划**

**端点**: \`DELETE /api/v1/planning/plans/plans\`

* **child\_id**: 学生ID（必需）

* **plan\_id**: 计划ID（可选，指定则删除特定计划）

* **start\_date**: 开始日期（可选）

* **end\_date**: 结束日期（可选）

* **subject**: 学科筛选（可选）

#### **cURL 示例**

```bash
# 删除特定计划
curl -X DELETE "http://localhost:8011/api/v1/planning/plans/plans" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "plan_id": "plan_1719840000_1_math"
  }'

# 删除指定时间范围的计划
curl -X DELETE "http://localhost:8011/api/v1/planning/plans/plans" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "start_date": "2025-06-01T00:00:00Z",
    "end_date": "2025-06-30T23:59:59Z"
  }'

# 删除特定学科的计划
curl -X DELETE "http://localhost:8011/api/v1/planning/plans/plans" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "subject": "数学",
    "start_date": "2025-07-01T00:00:00Z",
    "end_date": "2025-07-07T23:59:59Z"
  }'
```

#### **响应示例**

```json
{
  "success": true,
  "message": "成功删除学生1在指定条件下的学习计划",
  "deleted_conditions": {
    "child_id": 1,
    "plan_id": "plan_1719840000_1_math",
    "start_date": null,
    "end_date": null,
    "subject": null
  }
}
```

### 6.**获取计划统计**

**端点**: \`GET /api/v1/planning/plans/plans-statistics/{child\_id}\`

获取学生的计划统计信息

* **child\_id**: 学生ID

* **days**: 统计天数（1-365天）

* **subject**: 学科筛选（可选）

#### **cURL 示例**

```bash
# 获取7天计划统计
curl "http://localhost:8011/api/v1/planning/plans/plans-statistics/1?days=7"

# 获取30天特定学科统计
curl "http://localhost:8011/api/v1/planning/plans/plans-statistics/1?days=30&subject=数学"

# 获取一年统计
curl "http://localhost:8011/api/v1/planning/plans/plans-statistics/1?days=365"
```

#### **响应示例**

```json
{
  "child_id": 1,
  "period_days": 7,
  "subject": null,
  "total_plans": 15,
  "completed_plans": 12,
  "pending_plans": 2,
  "in_progress_plans": 1,
  "cancelled_plans": 0,
  "completion_rate": 80.0,
  "average_confidence_index": 3.6,
  "subject_breakdown": {
    "数学": {
      "total_plans": 6,
      "completed": 5,
      "completion_rate": 83.3,
      "avg_confidence": 3.4
    },
    "英语": {
      "total_plans": 5,
      "completed": 4,
      "completion_rate": 80.0,
      "avg_confidence": 3.8
    },
    "语文": {
      "total_plans": 4,
      "completed": 3,
      "completion_rate": 75.0,
      "avg_confidence": 3.5
    }
  },
  "trends": {
    "completion_trend": "improving",
    "confidence_trend": "stable",
    "plan_count_trend": "increasing"
  }
}
```

#

# SQLite 数据库表总结

#### 1. parents 表（家长信息）



表关系：通过 `child_parent_relationships` 中间表与 `children` 表建立多对多关系。

#### 2. children 表（小孩信息）



表关系：



* 通过 `child_parent_relationships` 中间表与 `parents` 表建立多对多关系；

* 与 `child_academic_records` 表建立一对多关系；

* 与 `daily_tasks` 表建立一对多关系。

#### 3. child\_parent\_relationships 表（小孩 - 家长关系）



表关系：作为中间表，实现 `children` 与 `parents` 表的多对多关系。

#### 4. child\_academic\_records 表（小孩学业记录）



表关系：与 `children` 表建立多对一关系（多个记录属于一个孩子）。

#### 5. daily\_tasks 表（每日任务）



表关系：



* 与 `children` 表建立多对一关系；

* 与 `task_plans` 表建立多对一关系；

* 与 `task_items` 表建立一对多关系。

#### 6. task\_items 表（任务子项）



表关系：



* 与 `daily_tasks` 表建立多对一关系；

* 与 `task_plans` 表建立多对一关系。

#### 7. task\_plans 表（任务计划）



表关系：



* 与 `daily_tasks` 表建立一对多关系；

* 与 `task_items` 表建立一对多关系。

#### 8. user\_plan\_actions 表（用户计划表操作记录）



表关系：与 `children` 表建立多对一关系（多个记录属于一个孩子）。

