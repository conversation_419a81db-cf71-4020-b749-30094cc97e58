#### 1.目录结构

```python
├── backend/                          # 后端核心模块
│   ├── models/                       # 数据模型定义
│   │   ├── camera_models.py          # 摄像头相关数据模型
│   ├── services/                     # 业务服务层
│       ├── camera_server.py             # 摄像头FastAPI路由
│   └── utils/                        # 工具模块
│       ├── camera_api.py            # 服务器端摄像头管理器
│       └── logging.py               # 日志配置
├── camera_api_client.py              # 摄像头API客户端库
├── frontend/                         # 前端界面
│   ├── camera_client.html           # 摄像头客户端界面
│   └── server.py                    # 前端服务器
└── captured_images/                 # 图像捕获存储目录
```

#### 2.基本使用

```python
from camera_api_client import CameraAPIClient
"""   
Args:
    base_url: API基础URL
    timeout: 请求超时时间（秒）
    max_retries: 最大重试次数
"""

with CameraAPIClient() as client:
    session_id = client.quick_setup("example_module", "示例摄像头")
    
    # 设置图像变换
    client.set_transform(session_id, rotation=90, zoom=1.5)
    
    # 获取变换后的图像
    transformed_image = client.get_frame(session_id, return_format='pil')
```

#### **3.与图像处理集成**

```python
from camera_api_client import CameraAPIClient
from image_processing_api_client import ImageProcessingAPIClient

# 创建客户端
camera_client = CameraAPIClient()
processing_client = ImageProcessingAPIClient()

with camera_client as cam, processing_client as proc:
    # 1. 设置摄像头
    session_id = cam.quick_setup("processing_module", "处理摄像头")
    
    # 2. 获取原始图像
    original_image = cam.get_frame(session_id, return_format='pil')
    
    if original_image:
        # 3. 图像预处理
        gray_result = proc.grayscale(original_image, method="weighted")
        
        if gray_result.success:
            # 4. 进一步处理
            blur_result = proc.blur(
                gray_result.get_image(), 
                method="gaussian", 
                kernel_size=5
            )
            
            # 5. 保存处理结果
            if blur_result.success:
                blur_result.save_image("output/processed_frame.jpg")
```

#### **4.主要类和方法**

```python
class CameraAPIClient:
    def __init__(self, base_url: str, timeout: int = 30, max_retries: int = 3)
    
    # 健康检查
    def health_check() -> Dict[str, Any]
    
    # 摄像头注册
    def register_cameras(client_id: str, cameras: List[Dict]) -> bool
    
    # 会话管理
    def create_session(client_id: str, camera_id: str) -> str
    def close_session(session_id: str) -> bool
    def get_session_status(session_id: str) -> Dict[str, Any]
    
    # 图像操作
    def update_frame(session_id: str, image: Union[str, Image.Image], test_mode: bool = False) -> bool
    def get_frame(session_id: str, apply_transforms: bool = True, return_format: str = 'base64') -> Union[str, Image.Image, None]
    
    # 图像变换
    def set_transform(session_id: str, rotation: int = 0, zoom: float = 1.0) -> bool
    
    # 图像捕获
    def capture_image(session_id: str, save_path: str = None, filename: str = None, apply_transforms: bool = True) -> Dict[str, Any]
    
    # 便捷方法
    def quick_setup(client_id: str, camera_name: str = "API摄像头", resolution: str = "640x480") -> str
```

```python
    def get_frame(self, session_id: str, apply_transforms: bool = True, 
                 return_format: str = 'base64') -> Optional[Union[str, bytes, Image.Image]]:
        """获取帧数据
        
        Args:
            session_id: 会话ID
            apply_transforms: 是否应用图像变换
            return_format: 返回格式 ('base64', 'bytes', 'pil')
        
        Returns:
            图像数据，格式根据return_format参数决定
        """
        try:
            params = {'apply_transforms': apply_transforms}
            result = self._request('GET', f'/frame/{session_id}', params=params)
            
            image_data = result.get('image_data')
            if not image_data:
                logger.warning(f"未获取到图像数据: {session_id}")
                return None
            
            # 根据要求的格式返回数据
            if return_format == 'base64':
                return image_data
            elif return_format == 'bytes':
                return base64.b64decode(image_data)
            elif return_format == 'pil':
                image_bytes = base64.b64decode(image_data)
                return Image.open(BytesIO(image_bytes))
            else:
                raise CameraAPIError(f"不支持的返回格式: {return_format}")
                
        except Exception as e:
            logger.error(f"获取帧数据失败: {e}")
            raise
    
```

 调用 `get_frame()` 示例代码对比：

1️⃣ 获取 base64 字符串，用于前端接口返回

```python
image_str = camera_client.get_frame(session_id, return_format='base64')
# 在 API 接口中作为 JSON 返回：
return {"image_base64": image_str}
```

2️⃣ 获取 bytes，保存为文件或交给 OpenCV

```python
image_bytes = camera_client.get_frame(session_id, return_format='bytes')
with open("frame.png", "wb") as f:
    f.write(image_bytes)
# 或传给 OpenCV
import numpy as np
import cv2
img_np = cv2.imdecode(np.frombuffer(image_bytes, np.uint8), cv2.IMREAD_COLOR)
```

3️⃣ 获取 PIL 图像，进行图像处理

```python
image_pil = camera_client.get_frame(session_id, return_format='pil')
image_pil = image_pil.convert("L").resize((256, 256))  # 转灰度图并缩放
image_pil.show()  # 显示图像
```
