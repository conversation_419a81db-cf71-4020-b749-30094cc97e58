# 多模态任务输入Web界面使用指南

## 🎯 功能概述

多模态任务输入功能已集成到AI Child Web界面中，支持三种输入方式：
- 📝 **文本输入**：直接输入任务描述
- 🎤 **语音输入**：通过语音录制输入任务
- 📷 **图片输入**：上传图片或拍照识别任务

## 🚀 快速开始

### 1. 启动服务

```bash
cd smart_friend1
python main.py
```

服务将在 `http://localhost:8014` 启动，并自动打开Web界面。

### 2. 访问界面

打开浏览器访问：`http://localhost:8014/static/aiChild.html`

## 📝 使用方法

### 文本输入

1. **选择文本输入选项卡**
   - 点击"📝 文本输入"选项卡

2. **输入任务描述**
   ```
   今天数学作业：完成练习册第15页，重点练习分数加减法，预计45分钟
   英语：Unit5单词抄写3遍，听力练习15分钟
   语文：背诵《静夜思》，练字20个字
   ```

3. **提交处理**
   - 点击"📝 提交文本任务"按钮
   - 系统将自动解析并存储任务

### 语音输入

1. **选择语音输入选项卡**
   - 点击"🎤 语音输入"选项卡

2. **开始录音**
   - 点击"🎙️ 开始录音"按钮
   - 对着麦克风说出任务内容
   - 例如："今天要做英语听力练习，还有数学口算题二十道"

3. **停止录音**
   - 点击"⏹️ 停止录音"按钮
   - 查看识别结果

4. **提交处理**
   - 确认识别结果无误后
   - 点击"🎤 提交语音任务"按钮

### 图片输入

1. **选择图片输入选项卡**
   - 点击"📷 图片输入"选项卡

2. **选择图片方式**

   **方式一：文件上传**
   - 点击上传区域选择图片文件
   - 或直接拖拽图片到上传区域
   - 支持JPG、PNG、GIF格式

   **方式二：摄像头拍照**
   - 点击"📹 使用摄像头"按钮
   - 允许浏览器访问摄像头
   - 点击"📸 拍照"按钮捕获图片

3. **预览确认**
   - 查看图片预览
   - 确认图片清晰可读

4. **提交处理**
   - 点击"📷 提交图片任务"按钮
   - 系统将识别图片中的文字并解析任务

## 📊 处理结果

### 结果显示

处理完成后，系统会显示：
- ✅ 处理状态（成功/失败）
- 📊 解析任务数量
- 💾 存储任务数量
- 📋 详细任务列表

### 任务信息

每个解析出的任务包含：
- **任务名称**：如"数学作业"
- **学科分类**：如"数学"、"英语"
- **任务描述**：具体内容
- **预计时长**：完成时间
- **难度等级**：1-5级
- **子任务列表**：具体执行项

## 🔧 API接口

### 文本输入API

```bash
POST /api/v1/task-input/text
Content-Type: application/json

{
    "child_id": 4,
    "text_content": "今天数学作业：完成练习册第15页"
}
```

### 语音输入API

```bash
POST /api/v1/task-input/voice
Content-Type: application/json

{
    "child_id": 4,
    "voice_text": "今天要做英语听力练习"
}
```

### 图片输入API

```bash
POST /api/v1/task-input/image
Content-Type: multipart/form-data

child_id: 4
image_file: [图片文件]
```

### Base64图片API

```bash
POST /api/v1/task-input/image-base64
Content-Type: application/x-www-form-urlencoded

child_id=4&image_base64=data:image/jpeg;base64,/9j/4AAQ...
```

## 🏥 健康检查

### 检查API状态

```bash
GET /api/v1/task-input/health
```

返回：
```json
{
    "status": "healthy",
    "services": {
        "doubao_service": "connected",
        "daily_task_service": "connected"
    },
    "supported_input_types": ["text", "voice", "image"]
}
```

### 查询支持类型

```bash
GET /api/v1/task-input/supported-types
```

## 🧪 测试验证

### 运行集成测试

```bash
cd smart_friend1
python test_multimodal_integration.py
```

测试内容：
- ✅ API健康检查
- ✅ 文本输入处理
- ✅ 语音输入处理
- ✅ 图片输入处理
- ✅ Base64图片处理

## ⚠️ 注意事项

### 浏览器权限

- **麦克风权限**：语音输入需要允许浏览器访问麦克风
- **摄像头权限**：拍照功能需要允许浏览器访问摄像头

### 文件格式

- **图片格式**：支持JPG、PNG、GIF、BMP
- **文件大小**：建议小于5MB
- **图片质量**：确保文字清晰可读

### 网络要求

- **稳定连接**：需要稳定的网络连接
- **API服务**：确保豆包AI服务正常运行
- **数据库**：确保数据库连接正常

## 🔍 故障排除

### 常见问题

1. **语音识别失败**
   - 检查麦克风权限
   - 确保环境安静
   - 说话清晰

2. **图片识别失败**
   - 确保图片清晰
   - 检查文字是否可读
   - 尝试重新拍照

3. **API调用失败**
   - 检查网络连接
   - 确认服务状态
   - 查看控制台错误

### 日志查看

- **浏览器控制台**：F12查看JavaScript错误
- **服务器日志**：查看FastAPI服务日志
- **API响应**：检查网络请求响应

## 📞 技术支持

如遇问题，请：
1. 查看浏览器控制台错误信息
2. 运行健康检查API
3. 执行集成测试脚本
4. 检查服务配置和依赖

---

*最后更新：2024年12月20日*
