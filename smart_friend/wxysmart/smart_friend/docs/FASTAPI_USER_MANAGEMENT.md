# FastAPI用户信息管理API文档

基于FastAPI架构的小孩用户信息管理系统，提供完整的RESTful API接口。

## 🏗️ 架构概述

### API架构设计
- **框架**: FastAPI
- **数据库**: SQLite + SQLAlchemy ORM
- **数据验证**: Pydantic v2
- **依赖注入**: FastAPI Depends
- **错误处理**: 统一HTTP异常处理
- **文档**: 自动生成OpenAPI文档

### 目录结构
```
smart_friend/
├── api/                           # API层
│   ├── __init__.py
│   ├── deps.py                    # 依赖注入
│   └── v1/                        # API v1版本
│       ├── __init__.py
│       ├── api.py                 # 路由汇总
│       └── endpoints/             # API端点
│           ├── __init__.py
│           ├── parents.py         # 家长管理API
│           ├── children.py        # 小孩管理API
│           ├── relationships.py   # 关系管理API
│           └── academic_records.py # 学业记录API
├── backend/                       # 业务逻辑层
│   ├── models/                    # 数据模型
│   └── services/                  # 业务服务
├── schemas/                       # API数据模型
│   └── child_schemas.py           # Pydantic模型
├── main.py                        # FastAPI应用入口
└── docs/                          # 文档
    └── FASTAPI_USER_MANAGEMENT.md # 本文档
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 初始化数据库
```bash
python backend/models/init_child_db.py create
```

### 3. 启动API服务
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8001
```

### 4. 访问API文档
- **Swagger UI**: http://localhost:8002/docs
- **ReDoc**: http://localhost:8002/redoc
- **OpenAPI JSON**: http://localhost:8002/api/v1/openapi.json

## 📋 API端点概览

### 家长管理 (`/api/v1/parents`)
- `POST /` - 创建家长
- `GET /{parent_id}` - 获取家长信息
- `GET /` - 获取家长列表
- `PUT /{parent_id}` - 更新家长信息
- `DELETE /{parent_id}` - 软删除家长
- `GET /{parent_id}/children` - 获取家长的所有小孩

### 小孩管理 (`/api/v1/children`)
- `POST /` - 创建小孩
- `GET /{child_id}` - 获取小孩信息
- `GET /` - 获取小孩列表
- `PUT /{child_id}` - 更新小孩信息
- `DELETE /{child_id}` - 软删除小孩
- `GET /{child_id}/parents` - 获取小孩的所有家长

### 关系管理 (`/api/v1/relationships`)
- `POST /` - 创建家长-小孩关系
- `DELETE /{relationship_id}` - 软删除关系
- `GET /child/{child_id}/parents` - 获取小孩的家长
- `GET /parent/{parent_id}/children` - 获取家长的小孩

### 学业记录管理 (`/api/v1/academic-records`)
- `POST /` - 创建学业记录
- `GET /{record_id}` - 获取学业记录
- `GET /child/{child_id}` - 获取小孩的学业记录
- `PUT /{record_id}` - 更新学业记录
- `DELETE /{record_id}` - 软删除学业记录

## 🔧 API使用示例

### 创建家长
```bash
curl -X POST "http://localhost:8002/api/v1/parents/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "张三",
    "gender": "male",
    "age": 35,
    "phone": "13800138000",
    "email": "<EMAIL>",
    "wechat_id": "zhangsan_wechat",
    "notes": "孩子的父亲"
  }'
```

### 创建小孩
```bash
curl -X POST "http://localhost:8002/api/v1/children/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "张小明",
    "nickname": "小明",
    "gender": "male",
    "age": 8,
    "academic_level": "primary_2",
    "school_name": "北京市某某小学",
    "height": 125.0,
    "weight": 25.0,
    "personality_traits": "活泼开朗",
    "learning_style": "视觉学习者",
    "behavior_level": "good",
    "favorite_subjects": "数学，科学"
  }'
```

### 创建家长-小孩关系
```bash
curl -X POST "http://localhost:8002/api/v1/relationships/" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "parent_id": 1,
    "relationship_type": "father",
    "is_primary_contact": true,
    "notes": "父子关系"
  }'
```

### 获取小孩信息（包含关联数据）
```bash
curl "http://localhost:8002/api/v1/children/1?include_relations=true"
```

## 🧪 测试

### 运行API测试
```bash
python test/test_fastapi_child_management.py
```

### 测试覆盖范围
- ✅ 家长CRUD操作
- ✅ 小孩CRUD操作
- ✅ 关系管理
- ✅ 学业记录管理
- ✅ 数据验证
- ✅ 错误处理
- ✅ 依赖注入

## 🎯 特性

### 数据验证
- 使用Pydantic进行请求/响应数据验证
- 支持字段类型检查、范围验证、枚举值验证
- 自动生成详细的错误信息

### 错误处理
- 统一的HTTP异常处理
- 详细的错误信息和状态码
- 日志记录和错误追踪

### 依赖注入
- 数据库会话管理
- 服务层依赖注入
- 连接状态检查

### 软删除
- 所有删除操作都是软删除
- 保留数据完整性
- 支持数据恢复

### 分页支持
- 列表查询支持分页
- 可配置的限制和偏移量
- 性能优化

## 📊 响应格式

### 成功响应
```json
{
  "id": 1,
  "name": "张小明",
  "nickname": "小明",
  "gender": "male",
  "age": 8,
  "academic_level": "primary_2",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00"
}
```

### 错误响应
```json
{
  "detail": "未找到ID为123的小孩"
}
```

## 🔒 安全考虑

### 数据验证
- 输入数据严格验证
- SQL注入防护
- XSS攻击防护

### CORS配置
- 跨域请求控制
- 生产环境域名限制

### 日志记录
- 操作日志记录
- 错误日志追踪
- 性能监控

## 📈 性能优化

### 数据库优化
- 连接池管理
- 查询优化
- 索引使用

### 缓存策略
- 会话缓存
- 查询结果缓存

### 异步处理
- FastAPI异步支持
- 非阻塞I/O操作
