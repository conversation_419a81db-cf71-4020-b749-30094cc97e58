sequenceDiagram
    participant Client as 🌐 客户端
    participant FastAPI as 🚀 FastAPI App
    participant Router as 🛣️ 路由器
    participant Endpoint as 📡 API端点
    participant Service as ⚙️ 服务层
    participant Schema as 📋 数据模型
    participant DB as 🗄️ InfluxDB
    
    Note over Client,DB: 示例：创建学习计划请求流程
    
    Client->>FastAPI: POST /api/v1/planning/plans
    Note right of Client: Content-Type: application/json<br/>{"child_id": 123, "task_name": "语文作业", ...}
    
    FastAPI->>FastAPI: CORS 中间件处理
    FastAPI->>Router: 路由匹配
    Router->>Endpoint: planning.create_plan()
    
    Endpoint->>Schema: StudyPlanCreate 数据验证
    Schema-->>Endpoint: 验证通过/失败
    
    alt 数据验证成功
        Endpoint->>Service: PlanningService.create_plan()
        Service->>Service: 生成 plan_id (UUID)
        Service->>Service: 准备 tags 和 fields
        Service->>DB: InfluxDB.write_point()
        DB-->>Service: 写入结果
        Service-->>Endpoint: plan_id / None
        
        alt 写入成功
            Endpoint-->>Router: {"success": true, "plan_id": "uuid", ...}
        else 写入失败
            Endpoint-->>Router: HTTPException(500, "创建失败")
        end
    else 数据验证失败
        Endpoint-->>Router: HTTPException(422, "数据格式错误")
    end
    
    Router-->>FastAPI: 响应数据
    FastAPI-->>Client: JSON 响应
    
    Note over Client,DB: 其他请求类型遵循类似流程
    
    rect rgb(240, 248, 255)
        Note over Client,DB: 查询流程 (GET)
        Client->>FastAPI: GET /api/v1/planning/plans/123
        FastAPI->>Endpoint: get_plans()
        Endpoint->>Service: PlanningService.get_plans()
        Service->>DB: InfluxDB.query_data()
        DB-->>Service: 原始数据
        Service->>Service: _process_query_results()
        Service-->>Endpoint: 处理后的计划列表
        Endpoint->>Schema: StudyPlanResponse 序列化
        Schema-->>Endpoint: 验证后的响应数据
        Endpoint-->>Client: JSON 响应
    end
