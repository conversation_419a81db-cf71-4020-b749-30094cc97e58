# ASR Utils - 模块化语音识别工具集

## 概述

ASR Utils 是一个采用面向对象设计和单一职责原则的模块化语音识别工具集。它将原来分散在 `asr.py`、`asr_thread.py` 和 `volcano_asr_client.py` 中的功能整合为一个统一的、可扩展的工具集。

## 设计原则

- **单一职责原则**：每个类只负责一个功能
- **开闭原则**：对扩展开放，对修改关闭
- **依赖倒置原则**：依赖抽象而非具体实现
- **接口隔离原则**：客户端不应依赖它不需要的接口

## 主要组件

### 1. 基础抽象类

#### ASRClientBase
所有ASR客户端的基类，定义了统一的接口：
- `connect()` - 连接到ASR服务
- `disconnect()` - 断开ASR服务连接
- `start_recognition()` - 启动语音识别
- `stop_recognition()` - 停止语音识别
- `send_audio()` - 发送音频数据

### 2. 具体ASR客户端实现

#### VolcanoASRClient
火山引擎ASR客户端，实现基于WebSocket的火山引擎语音识别服务。

#### DoubaoASRClient
豆包ASR客户端，实现基于WebSocket的豆包语音识别服务。

### 3. 功能组件

#### ASRConnectionManager
负责管理WebSocket连接的建立、维护和重连。

#### ASRTextProcessor
负责文本处理，包括：
- TTS文本预处理
- 智能标点符号添加
- 文本规范化

#### ASRAudioProcessor
负责音频处理，包括：
- 音频采集
- 格式转换
- 静音检测

### 4. 高级管理器

#### ASRManager
统一管理不同的ASR客户端，提供高级语音识别功能：
- 自动选择ASR服务
- 模拟模式支持
- 错误恢复
- 状态管理

#### WebVoiceRecognitionService
提供基于Web界面的语音识别功能。

### 5. 工厂类和工具

#### ASRClientFactory
根据配置创建不同类型的ASR客户端。

#### ASRUtils
提供便捷的ASR功能接口。

## 使用示例

### 基本使用

```python
from backend.utils.asr_utils import ASRUtils

# 创建ASR管理器
asr_manager = ASRUtils.create_manager()

# 定义回调函数
def on_recognition_result(text: str, is_final: bool):
    if is_final:
        print(f"最终识别结果: {text}")
    else:
        print(f"实时识别中: {text}")

# 启动语音识别
if asr_manager.start_recognition(on_recognition_result):
    print("语音识别已启动，请说话...")
    
    # 运行一段时间
    time.sleep(10)
    
    # 停止语音识别
    asr_manager.stop_recognition()
    
    # 获取识别结果
    results = asr_manager.get_recognition_results()
    print(f"所有识别结果: {results}")
```

### 使用特定ASR客户端

```python
from backend.utils.asr_utils import ASRClientFactory

# 创建火山引擎ASR客户端
client = ASRClientFactory.create_client(
    'volcano', 
    'your_app_key', 
    'your_access_key'
)

# 连接到服务
if client.connect():
    print("成功连接到ASR服务")
    
    # 定义回调函数
    def on_result(text: str, is_final: bool):
        print(f"识别结果: {text}, 是否最终: {is_final}")
    
    # 启动识别
    if client.start_recognition(on_result):
        print("语音识别已启动")
        
        # 发送音频数据
        # client.send_audio(audio_data)
        
        # 停止识别
        client.stop_recognition()
    
    # 断开连接
    client.disconnect()
```

### 文本处理

```python
from backend.utils.asr_utils import ASRUtils

# 添加标点符号
raw_text = "你好世界这是一个测试"
punctuated_text = ASRUtils.add_punctuation(raw_text)
print(f"添加标点后: {punctuated_text}")

# TTS预处理
tts_text = ASRUtils.preprocess_text_for_tts(
    "这是代码：```python\nprint('hello')\n```结束"
)
print(f"TTS预处理后: {tts_text}")
```

### Web语音识别服务

```python
from backend.utils.asr_utils import ASRUtils

# 创建Web语音识别服务
web_service = ASRUtils.create_web_service(host='0.0.0.0', port=5000)

# 启动服务
web_service.start_service()

# 开始语音识别
if web_service.start_recognition():
    print("Web语音识别已启动")
    
    # 获取识别结果
    results = web_service.get_voice_results()
    print(f"识别结果: {results}")
    
    # 停止识别
    web_service.stop_recognition()

# 停止服务
web_service.stop_service()
```

## 配置

在配置文件中设置ASR相关参数：

```ini
[API]
asr_service_type = volcano  # 或 doubao
asr_app_key = your_app_key
asr_access_key = your_access_key
```

## 扩展

### 添加新的ASR服务

1. 继承 `ASRClientBase` 类
2. 实现所有抽象方法
3. 在 `ASRClientFactory` 中添加新的服务类型

```python
class NewASRClient(ASRClientBase):
    def connect(self) -> bool:
        # 实现连接逻辑
        pass
    
    def disconnect(self) -> bool:
        # 实现断开连接逻辑
        pass
    
    # 实现其他抽象方法...
```

### 自定义文本处理

```python
class CustomTextProcessor(ASRTextProcessor):
    def add_punctuation(self, text: str) -> str:
        # 自定义标点符号添加逻辑
        return super().add_punctuation(text)
```

## 测试

运行测试：

```bash
# 运行所有测试
python -m pytest test/test_asr_utils.py -v

# 运行特定测试类
python -m pytest test/test_asr_utils.py::TestASRTextProcessor -v
```

## 迁移指南

### 从旧版本迁移

1. **替换导入**：
   ```python
   # 旧版本
   from utils.asr import ASRManager
   from utils.volcano_asr_client import VolcanoASRClient
   
   # 新版本
   from backend.utils.asr_utils import ASRManager, VolcanoASRClient
   ```

2. **使用工厂类**：
   ```python
   # 旧版本
   client = VolcanoASRClient(app_key, access_key)
   
   # 新版本
   client = ASRClientFactory.create_client('volcano', app_key, access_key)
   ```

3. **使用工具类**：
   ```python
   # 旧版本
   manager = ASRManager()
   
   # 新版本
   manager = ASRUtils.create_manager()
   ```

## 注意事项

1. 确保安装了所有必要的依赖包
2. 配置正确的API密钥
3. 在生产环境中使用时，注意错误处理和日志记录
4. 模拟模式仅用于测试，生产环境需要真实的ASR服务

## 依赖

- numpy
- sounddevice
- websocket-client
- jieba
- threading
- queue
- json
- struct
- uuid
- time
- re

## 许可证

本项目遵循项目主许可证。
