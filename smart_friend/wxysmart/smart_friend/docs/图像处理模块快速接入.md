#### 1.目录结构

```bash
backend/
├── models/image_processing_models.py          # 数据模型层
├── services/image_processing_service.py       # 服务层  
└── utils/image_processing_api.py           # API层

image_processing_api_client.py                 # 客户端封装库
```

#### **2.基础使用**

```python
from image_processing_api_client import ImageProcessingAPIClient

# 简单调用
with ImageProcessingAPIClient() as client:
    result = client.grayscale(image_data, method="weighted")
    if result.success:
        result.save_image("output.jpg")
```

```python
# 批量处理多张图像
results = client.batch_process(
    image_list=[img1, img2, img3],
    algorithm="blur",
    output_dir="output/",
    method="gaussian",
    kernel_size=5
)
```

#### 3.**与摄像头系统集成**

```python
from camera_api_client import CameraAPIClient
from image_processing_api_client import ImageProcessingAPIClient

# 获取摄像头图像并处理
with CameraAPIClient() as camera, ImageProcessingAPIClient() as processor:
    session_id = camera.quick_setup("my_module")
    camera_image = camera.get_frame(session_id, return_format='pil')
    
    result = processor.enhance(camera_image, enhancement_type="auto_enhance")
    if result.success:
        result.save_image("enhanced_camera_image.jpg")
```

#### 4.主要类和方法

### 1. ImageProcessingAPIError

**异常类**

- **功能**: 封装图像处理API调用过程中的各种异常
- **继承**: Python标准异常类Exception
- **用途**: 网络错误、参数错误、服务器错误等异常处理

### 2. ImageProcessingResult

**结果封装类**

- **功能**: 封装图像处理API的返回结果

- 主要属性

  :

  - `success`: 处理是否成功
  - `message`: 处理结果消息
  - `processed_image`: 处理后的图像数据(base64格式)
  - `processing_time`: 处理耗时(秒)
  - `original_size`: 原始图像尺寸
  - `processed_size`: 处理后图像尺寸
  - `algorithm_info`: 算法详细信息

**主要方法**:

- `get_image_data(format='base64')`: 获取多种格式的图像数据
- `save_image(filepath, quality=90)`: 保存处理后的图像到文件

### 3. ImageProcessingAPIClient

**主要客户端类**

- **功能**: 提供完整的图像预处理API接口调用功能
- **支持功能**: 图像灰度化、模糊处理、图像增强、健康检查等

**初始化方法**

```python
__init__(base_url="http://localhost:8003/api/v1/image-processing", timeout=30, max_retries=3)
```

```python
class ImageProcessingAPIClient:
    def __init__(self, base_url: str, timeout: int = 30, max_retries: int = 3)
    
    # 健康检查
    def health_check() -> Dict[str, Any]
    def get_available_methods() -> Dict[str, List[str]]
    
    # 图像处理算法
    def grayscale(image: Union[str, Image.Image], method: str = "weighted", **kwargs) -> ImageProcessingResult
    def blur(image: Union[str, Image.Image], method: str = "gaussian", kernel_size: int = 5, **kwargs) -> ImageProcessingResult
    def enhance(image: Union[str, Image.Image], enhancement_type: str, **kwargs) -> ImageProcessingResult
    
    # 批量处理
    def batch_process(images: List[Union[str, Image.Image]], processing_type: str, parameters: Dict) -> List[ImageProcessingResult]
    
    # 便捷方法
    def process_file(input_path: str, output_path: str, processing_type: str, **kwargs) -> bool
```

#### 基础接口方法

**health_check()**

- **功能**: 检查图像处理服务的运行状态
- **返回**: 包含服务状态信息的字典
- **用途**: 确保服务正常运行

**get_available_methods()**

- **功能**: 获取服务端支持的所有图像处理方法
- **返回**: 可用方法字典
- **用途**: 动态检查服务能力

**图像处理方法**

**grayscale(image_data, method="weighted", ...)**

- **功能**: 图像灰度化处理
- 参数:
  - `image_data`: 图像数据（文件路径、base64字符串、字节数据或PIL图像）
  - `method`: 灰度化方法 ("weighted", "average", "opencv", "luminosity")
  - `preserve_alpha`: 是否保留透明度通道
  - `return_format`: 返回格式 ("jpeg", "png")
  - `quality`: JPEG质量 (1-100)

**blur(image_data, method="gaussian", kernel_size=5, ...)**

- **功能**: 图像模糊处理
- 参数:
  - `image_data`: 图像数据
  - `method`: 模糊方法 ("gaussian", "box", "median")
  - `kernel_size`: 核大小 (3-31，必须为奇数)
  - `sigma_x`: X方向标准差 (0.1-10.0)
  - `sigma_y`: Y方向标准差

**enhance(image_data, enhancement_type, ...)**

- **功能**: 图像增强处理
- 参数:
  - `image_data`: 图像数据
  - `enhancement_type`: 增强类型 ("brightness", "contrast", "saturation", "gamma", "auto_enhance")
  - `brightness`: 亮度调整 (-100到100)
  - `contrast`: 对比度调整 (0.1到3.0)
  - `saturation`: 饱和度调整 (0.0到3.0)
  - `gamma`: 伽马值 (0.1到3.0)

**便捷方法**

**process_image_file(input_path, output_path, algorithm, **kwargs)

- **功能**: 一站式图像文件处理方法

- 参数

  - `input_path`: 输入图像文件路径
  - `output_path`: 输出图像文件路径
  - `algorithm`: 算法类型 ("grayscale", "blur", "enhancement")
  - `kwargs`: 算法特定参数

**batch_process(image_list, algorithm, output_dir=None, **kwargs)

- **功能**: 批量处理图像

- 参数

  - `image_list`: 图像数据列表
  - `algorithm`: 算法类型
  - `output_dir`: 输出目录（可选）
  - `**kwargs`: 算法特定参数

**上下文管理器支持**

**enter\() / exit\()**

- **功能**: 支持with语句自动管理资源
- **用途**: 自动关闭HTTP会话，释放网络连接