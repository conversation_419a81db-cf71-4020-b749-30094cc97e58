# 文件上传验证器
import os
import magic
import hashlib
import shutil
from typing import Optional, Tuple, Dict, Any
from pathlib import Path
from datetime import datetime, timezone
from fastapi import UploadFile, HTTPException

from .config import FileUploadConfig
from .schemas import FileConflictStrategy, ExistingFileInfo, FileConflictInfo


class FileUploadValidator:
    """文件上传验证器"""
    
    def __init__(self):
        self.config = FileUploadConfig()
        # 尝试初始化 python-magic
        try:
            self.magic = magic.Magic(mime=True)
            self.magic_available = True
        except Exception:
            self.magic = None
            self.magic_available = False
    
    async def validate_file(self, 
                          file: UploadFile, 
                          upload_path: str,
                          max_size: Optional[int] = None) -> Dict[str, Any]:
        """
        完整的文件验证
        
        Args:
            file: 上传的文件对象
            upload_path: 目标上传路径
            max_size: 自定义最大文件大小
            
        Returns:
            Dict: 验证结果和文件信息
            
        Raises:
            HTTPException: 验证失败时抛出异常
        """
        validation_result = {
            'valid': False,
            'filename': '',
            'safe_filename': '',
            'file_size': 0,
            'file_type': '',
            'mime_type': '',
            'file_hash': '',
            'upload_path': '',
            'errors': []
        }
        
        try:
            # 1. 基础文件信息验证
            await self._validate_basic_info(file, validation_result)
            
            # 2. 文件扩展名验证
            self._validate_extension(validation_result['filename'], validation_result)
            
            # 3. 路径验证
            self._validate_upload_path(upload_path, validation_result)
            
            # 4. 文件大小验证
            await self._validate_file_size(file, max_size, validation_result)
            
            # 5. MIME类型验证
            await self._validate_mime_type(file, validation_result)
            
            # 6. 文件内容验证
            if self.config.ENABLE_CONTENT_VALIDATION:
                await self._validate_file_content(file, validation_result)
            
            # 7. 生成文件哈希
            await self._generate_file_hash(file, validation_result)
            
            # 8. 生成安全文件名和路径
            self._generate_safe_paths(upload_path, validation_result)
            
            # 如果没有错误，标记为有效
            if not validation_result['errors']:
                validation_result['valid'] = True
            
            return validation_result
            
        except Exception as e:
            validation_result['errors'].append(f"验证过程中发生错误: {str(e)}")
            raise HTTPException(status_code=400, detail=validation_result['errors'])
    
    async def _validate_basic_info(self, file: UploadFile, result: Dict[str, Any]):
        """验证基础文件信息"""
        if not file.filename:
            result['errors'].append("文件名不能为空")
            return
        
        result['filename'] = file.filename
        result['safe_filename'] = self.config.get_safe_filename(file.filename)
        
        # 检查文件名长度
        if len(file.filename) > 255:
            result['errors'].append("文件名过长（最大255字符）")
        
        # 检查是否为空文件
        if file.size == 0:
            result['errors'].append("不能上传空文件")
    
    def _validate_extension(self, filename: str, result: Dict[str, Any]):
        """验证文件扩展名"""
        file_ext = Path(filename).suffix.lower()
        
        # 检查是否在危险扩展名黑名单中
        if file_ext in self.config.DANGEROUS_EXTENSIONS:
            result['errors'].append(f"不允许上传 {file_ext} 类型的文件（安全限制）")
            return
        
        # 检查是否在允许的扩展名白名单中
        if file_ext not in self.config.ALLOWED_EXTENSIONS:
            result['errors'].append(f"不支持的文件类型: {file_ext}")
            return
        
        result['file_type'] = self.config.get_file_category(file_ext)
    
    def _validate_upload_path(self, upload_path: str, result: Dict[str, Any]):
        """验证上传路径"""
        # 规范化路径
        normalized_path = os.path.normpath(upload_path).replace('\\', '/')
        
        # 检查路径遍历攻击
        if '..' in normalized_path or normalized_path.startswith('/'):
            result['errors'].append("检测到路径遍历攻击尝试")
            return
        
        # 检查是否在白名单路径中
        if not self.config.is_path_allowed(normalized_path):
            result['errors'].append(f"不允许上传到路径: {upload_path}")
            return
        
        result['upload_path'] = normalized_path
    
    async def _validate_file_size(self, file: UploadFile, max_size: Optional[int], result: Dict[str, Any]):
        """验证文件大小"""
        # 获取文件大小
        file_size = 0
        if hasattr(file, 'size') and file.size:
            file_size = file.size
        else:
            # 如果没有size属性，读取内容获取大小
            content = await file.read()
            file_size = len(content)
            await file.seek(0)  # 重置文件指针
        
        result['file_size'] = file_size
        
        # 确定最大文件大小限制
        if max_size:
            max_allowed_size = max_size
        else:
            file_ext = Path(result['filename']).suffix.lower()
            max_allowed_size = self.config.get_max_size_for_extension(file_ext)
        
        if file_size > max_allowed_size:
            result['errors'].append(
                f"文件大小超出限制: {file_size / 1024 / 1024:.2f}MB > {max_allowed_size / 1024 / 1024:.2f}MB"
            )
    
    async def _validate_mime_type(self, file: UploadFile, result: Dict[str, Any]):
        """验证MIME类型"""
        mime_type = file.content_type
        
        # 如果有python-magic，使用它来检测真实的MIME类型
        if self.magic_available:
            try:
                content = await file.read(1024)  # 读取前1KB用于检测
                await file.seek(0)  # 重置文件指针
                detected_mime = self.magic.from_buffer(content)
                if detected_mime:
                    mime_type = detected_mime
            except Exception:
                pass  # 如果检测失败，使用原始MIME类型
        
        result['mime_type'] = mime_type
        
        # 检查MIME类型是否在白名单中
        if mime_type not in self.config.ALLOWED_MIME_TYPES:
            result['errors'].append(f"不支持的文件类型: {mime_type}")
    
    async def _validate_file_content(self, file: UploadFile, result: Dict[str, Any]):
        """验证文件内容"""
        try:
            # 读取文件头部用于内容验证
            header = await file.read(512)
            await file.seek(0)  # 重置文件指针
            
            # 检查文件头部是否匹配扩展名
            file_ext = Path(result['filename']).suffix.lower()
            
            # 常见文件头部签名
            file_signatures = {
                '.jpg': [b'\xff\xd8\xff'],
                '.jpeg': [b'\xff\xd8\xff'],
                '.png': [b'\x89\x50\x4e\x47'],
                '.gif': [b'\x47\x49\x46\x38'],
                '.pdf': [b'\x25\x50\x44\x46'],
                '.zip': [b'\x50\x4b\x03\x04', b'\x50\x4b\x05\x06', b'\x50\x4b\x07\x08'],
                '.mp4': [b'\x00\x00\x00\x18\x66\x74\x79\x70', b'\x00\x00\x00\x20\x66\x74\x79\x70']
            }
            
            if file_ext in file_signatures:
                signatures = file_signatures[file_ext]
                if not any(header.startswith(sig) for sig in signatures):
                    result['errors'].append(f"文件内容与扩展名 {file_ext} 不匹配")
            
        except Exception as e:
            result['errors'].append(f"文件内容验证失败: {str(e)}")
    
    async def _generate_file_hash(self, file: UploadFile, result: Dict[str, Any]):
        """生成文件哈希值"""
        try:
            content = await file.read()
            await file.seek(0)  # 重置文件指针
            
            # 生成SHA256哈希
            file_hash = hashlib.sha256(content).hexdigest()
            result['file_hash'] = file_hash
            
        except Exception as e:
            result['errors'].append(f"生成文件哈希失败: {str(e)}")
    
    def _generate_safe_paths(self, upload_path: str, result: Dict[str, Any]):
        """生成安全的文件路径"""
        safe_filename = result['safe_filename']
        safe_upload_path = os.path.join(self.config.BASE_UPLOAD_DIR, upload_path)
        
        # 确保目录存在
        os.makedirs(safe_upload_path, exist_ok=True)
        
        # 生成完整的文件路径
        full_path = os.path.join(safe_upload_path, safe_filename)
        
        # 如果文件已存在，添加时间戳
        if os.path.exists(full_path):
            import time
            name, ext = os.path.splitext(safe_filename)
            timestamp = int(time.time())
            safe_filename = f"{name}_{timestamp}{ext}"
            full_path = os.path.join(safe_upload_path, safe_filename)
        
        result['safe_filename'] = safe_filename
        result['full_path'] = full_path
        result['relative_path'] = os.path.join(upload_path, safe_filename)

    async def check_file_conflict(self,
                                 filename: str,
                                 upload_path: str,
                                 file_content: bytes = None,
                                 conflict_strategy: FileConflictStrategy = FileConflictStrategy.RENAME) -> FileConflictInfo:
        """
        检查文件冲突

        Args:
            filename: 文件名
            upload_path: 上传路径
            file_content: 文件内容（用于内容比较）
            conflict_strategy: 冲突处理策略

        Returns:
            FileConflictInfo: 冲突信息
        """
        safe_upload_path = os.path.join(self.config.BASE_UPLOAD_DIR, upload_path)
        target_file_path = os.path.join(safe_upload_path, filename)

        conflict_info = FileConflictInfo(
            conflict_detected=False,
            existing_file=None,
            suggested_strategy=None,
            suggested_filename=None,
            conflict_reason=None
        )

        # 检查文件是否存在
        if os.path.exists(target_file_path):
            conflict_info.conflict_detected = True
            conflict_info.conflict_reason = f"文件 {filename} 已存在于目标路径"

            # 获取现有文件信息
            existing_file_info = self._get_existing_file_info(target_file_path, file_content)
            conflict_info.existing_file = existing_file_info

            # 根据策略提供建议
            if conflict_strategy == FileConflictStrategy.RENAME:
                suggested_name = self._generate_unique_filename(safe_upload_path, filename)
                conflict_info.suggested_filename = suggested_name
                conflict_info.suggested_strategy = FileConflictStrategy.RENAME
            elif conflict_strategy == FileConflictStrategy.REPLACE:
                conflict_info.suggested_strategy = FileConflictStrategy.REPLACE
            else:  # CANCEL
                conflict_info.suggested_strategy = FileConflictStrategy.CANCEL

        return conflict_info

    def _get_existing_file_info(self, file_path: str, new_file_content: bytes = None) -> ExistingFileInfo:
        """获取现有文件的详细信息"""
        try:
            stat = os.stat(file_path)

            # 计算现有文件的哈希值
            existing_hash = None
            is_identical = None

            try:
                with open(file_path, 'rb') as f:
                    existing_content = f.read()
                    existing_hash = hashlib.sha256(existing_content).hexdigest()

                    # 如果提供了新文件内容，比较是否相同
                    if new_file_content:
                        new_hash = hashlib.sha256(new_file_content).hexdigest()
                        is_identical = (existing_hash == new_hash)
            except Exception:
                pass

            return ExistingFileInfo(
                file_path=file_path,
                file_name=os.path.basename(file_path),
                file_size=stat.st_size,
                created_time=datetime.fromtimestamp(stat.st_ctime, timezone.utc),
                modified_time=datetime.fromtimestamp(stat.st_mtime, timezone.utc),
                file_hash=existing_hash,
                is_identical=is_identical
            )

        except Exception as e:
            # 如果获取文件信息失败，返回基本信息
            return ExistingFileInfo(
                file_path=file_path,
                file_name=os.path.basename(file_path),
                file_size=0,
                created_time=datetime.now(timezone.utc),
                modified_time=datetime.now(timezone.utc),
                file_hash=None,
                is_identical=None
            )

    def _generate_unique_filename(self, directory: str, filename: str) -> str:
        """生成唯一的文件名"""
        name, ext = os.path.splitext(filename)
        counter = 1

        # 首先尝试添加时间戳
        import time
        timestamp = int(time.time())
        new_filename = f"{name}_{timestamp}{ext}"

        # 如果时间戳文件名仍然存在，添加计数器
        while os.path.exists(os.path.join(directory, new_filename)):
            new_filename = f"{name}_{timestamp}_{counter:03d}{ext}"
            counter += 1

            # 防止无限循环
            if counter > 999:
                import uuid
                unique_id = str(uuid.uuid4())[:8]
                new_filename = f"{name}_{unique_id}{ext}"
                break

        return new_filename

    def create_backup_file(self, file_path: str) -> str:
        """创建文件备份"""
        try:
            backup_dir = os.path.join(os.path.dirname(file_path), '.backup')
            os.makedirs(backup_dir, exist_ok=True)

            filename = os.path.basename(file_path)
            name, ext = os.path.splitext(filename)

            # 生成备份文件名（包含时间戳）
            import time
            timestamp = int(time.time())
            backup_filename = f"{name}_backup_{timestamp}{ext}"
            backup_path = os.path.join(backup_dir, backup_filename)

            # 复制文件
            shutil.copy2(file_path, backup_path)

            return backup_path

        except Exception as e:
            raise Exception(f"创建备份文件失败: {str(e)}")


class FileUploadSecurity:
    """文件上传安全工具类"""
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名，移除危险字符"""
        import re
        # 移除或替换危险字符
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 移除控制字符
        sanitized = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', sanitized)
        # 限制长度
        if len(sanitized) > 255:
            name, ext = os.path.splitext(sanitized)
            sanitized = name[:255-len(ext)] + ext
        return sanitized
    
    @staticmethod
    def is_safe_path(path: str, base_dir: str) -> bool:
        """检查路径是否安全（防止路径遍历）"""
        try:
            # 解析绝对路径
            abs_base = os.path.abspath(base_dir)
            abs_path = os.path.abspath(os.path.join(base_dir, path))
            
            # 检查是否在基础目录内
            return abs_path.startswith(abs_base)
        except Exception:
            return False
