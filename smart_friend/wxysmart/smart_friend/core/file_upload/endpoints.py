# 文件上传API端点
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
import logging

from service.file_upload_service import FileUploadService
from .schemas import (
    FileConflictStrategy, FileUploadResponse, FileConflictCheckRequest,
    FileConflictCheckResponse
)
from core.file_upload.config import FileUploadConfig

logger = logging.getLogger(__name__)

router = APIRouter()


def get_file_upload_service() -> FileUploadService:
    """获取文件上传服务实例"""
    return FileUploadService()


@router.post("/upload", response_model=Dict[str, Any])
async def upload_file(
    file: UploadFile = File(...),
    upload_path: str = Form("uploads/temp", description="上传路径"),
    user_id: Optional[int] = Form(None, description="用户ID"),
    max_size: Optional[int] = Form(None, description="最大文件大小（字节）"),
    allowed_types: Optional[str] = Form(None, description="允许的文件类型，逗号分隔"),
    service: FileUploadService = Depends(get_file_upload_service)
):
    """
    单文件上传
    
    - **file**: 要上传的文件
    - **upload_path**: 上传路径（必须在白名单中）
    - **user_id**: 用户ID（可选）
    - **max_size**: 最大文件大小限制（字节）
    - **allowed_types**: 允许的文件扩展名，如 ".jpg,.png,.pdf"
    """
    try:
        # 处理允许的文件类型
        allowed_types_list = None
        if allowed_types:
            allowed_types_list = [t.strip() for t in allowed_types.split(',')]
        
        result = await service.upload_file(
            file=file,
            upload_path=upload_path,
            user_id=user_id,
            max_size=max_size,
            allowed_types=allowed_types_list
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传API错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"文件上传失败: {str(e)}"
        )


@router.post("/upload-multiple", response_model=Dict[str, Any])
async def upload_multiple_files(
    files: List[UploadFile] = File(...),
    upload_path: str = Form("uploads/temp", description="上传路径"),
    user_id: Optional[int] = Form(None, description="用户ID"),
    max_size: Optional[int] = Form(None, description="最大文件大小（字节）"),
    allowed_types: Optional[str] = Form(None, description="允许的文件类型，逗号分隔"),
    service: FileUploadService = Depends(get_file_upload_service)
):
    """
    多文件批量上传
    
    - **files**: 要上传的文件列表
    - **upload_path**: 上传路径（必须在白名单中）
    - **user_id**: 用户ID（可选）
    - **max_size**: 最大文件大小限制（字节）
    - **allowed_types**: 允许的文件扩展名，如 ".jpg,.png,.pdf"
    """
    try:
        if len(files) > 10:  # 限制批量上传数量
            raise HTTPException(
                status_code=400,
                detail="批量上传文件数量不能超过10个"
            )
        
        # 处理允许的文件类型
        allowed_types_list = None
        if allowed_types:
            allowed_types_list = [t.strip() for t in allowed_types.split(',')]
        
        result = await service.upload_multiple_files(
            files=files,
            upload_path=upload_path,
            user_id=user_id,
            max_size=max_size,
            allowed_types=allowed_types_list
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量文件上传API错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量文件上传失败: {str(e)}"
        )


@router.delete("/delete")
async def delete_file(
    file_path: str = Query(..., description="要删除的文件路径"),
    user_id: Optional[int] = Query(None, description="用户ID"),
    service: FileUploadService = Depends(get_file_upload_service)
):
    """
    删除文件
    
    - **file_path**: 要删除的文件路径
    - **user_id**: 用户ID（可选）
    """
    try:
        result = service.delete_file(file_path=file_path, user_id=user_id)
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件删除API错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"文件删除失败: {str(e)}"
        )


@router.get("/info")
async def get_file_info(
    file_path: str = Query(..., description="文件路径"),
    service: FileUploadService = Depends(get_file_upload_service)
):
    """
    获取文件信息
    
    - **file_path**: 文件路径
    """
    try:
        result = service.get_file_info(file_path=file_path)
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件信息API错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取文件信息失败: {str(e)}"
        )


@router.get("/list")
async def list_files(
    directory: str = Query("uploads", description="目录路径"),
    user_id: Optional[int] = Query(None, description="用户ID"),
    service: FileUploadService = Depends(get_file_upload_service)
):
    """
    列出目录中的文件
    
    - **directory**: 目录路径
    - **user_id**: 用户ID（可选）
    """
    try:
        result = service.list_files(directory=directory, user_id=user_id)
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"列出文件API错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"列出文件失败: {str(e)}"
        )


@router.get("/config")
async def get_upload_config():
    """
    获取文件上传配置信息
    """
    try:
        config = FileUploadConfig()
        
        return {
            "allowed_extensions": list(config.ALLOWED_EXTENSIONS),
            "allowed_mime_types": list(config.ALLOWED_MIME_TYPES),
            "max_file_sizes": config.MAX_FILE_SIZE,
            "allowed_upload_paths": config.ALLOWED_UPLOAD_PATHS,
            "file_type_categories": config.FILE_TYPE_CATEGORIES,
            "base_upload_dir": config.BASE_UPLOAD_DIR,
            "dangerous_extensions": list(config.DANGEROUS_EXTENSIONS)
        }
        
    except Exception as e:
        logger.error(f"获取上传配置API错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取上传配置失败: {str(e)}"
        )


@router.post("/validate")
async def validate_file_only(
    file: UploadFile = File(...),
    upload_path: str = Form("uploads/temp", description="上传路径"),
    max_size: Optional[int] = Form(None, description="最大文件大小（字节）"),
    service: FileUploadService = Depends(get_file_upload_service)
):
    """
    仅验证文件，不实际上传
    
    - **file**: 要验证的文件
    - **upload_path**: 目标上传路径
    - **max_size**: 最大文件大小限制（字节）
    """
    try:
        validation_result = await service.validator.validate_file(
            file=file,
            upload_path=upload_path,
            max_size=max_size
        )
        
        return {
            "valid": validation_result['valid'],
            "filename": validation_result['filename'],
            "safe_filename": validation_result['safe_filename'],
            "file_size": validation_result['file_size'],
            "file_type": validation_result['file_type'],
            "mime_type": validation_result['mime_type'],
            "errors": validation_result['errors']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件验证API错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"文件验证失败: {str(e)}"
        )


@router.get("/health")
async def health_check():
    """
    文件上传服务健康检查
    """
    try:
        import os
        from datetime import datetime, timezone
        
        config = FileUploadConfig()
        base_dir = config.BASE_UPLOAD_DIR
        
        # 检查基础上传目录
        base_dir_exists = os.path.exists(base_dir)
        base_dir_writable = os.access(base_dir, os.W_OK) if base_dir_exists else False
        
        # 检查各个允许的上传路径
        path_status = {}
        for path in config.ALLOWED_UPLOAD_PATHS:
            full_path = os.path.join(base_dir, path)
            path_status[path] = {
                "exists": os.path.exists(full_path),
                "writable": os.access(full_path, os.W_OK) if os.path.exists(full_path) else False
            }
        
        return {
            "service_status": "healthy",
            "base_upload_dir": base_dir,
            "base_dir_exists": base_dir_exists,
            "base_dir_writable": base_dir_writable,
            "allowed_paths_status": path_status,
            "conflict_strategies": [strategy.value for strategy in FileConflictStrategy],
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"文件上传健康检查错误: {str(e)}")
        return {
            "service_status": "error",
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@router.post("/upload-with-conflict-handling", response_model=FileUploadResponse)
async def upload_file_with_conflict_handling(
    file: UploadFile = File(...),
    upload_path: str = Form("uploads/temp", description="上传路径"),
    user_id: Optional[int] = Form(None, description="用户ID"),
    max_size: Optional[int] = Form(None, description="最大文件大小（字节）"),
    allowed_types: Optional[str] = Form(None, description="允许的文件类型，逗号分隔"),
    conflict_strategy: FileConflictStrategy = Form(FileConflictStrategy.RENAME, description="冲突处理策略"),
    create_backup: bool = Form(False, description="覆盖时是否创建备份"),
    check_content: bool = Form(True, description="是否检查文件内容相同性"),
    service: FileUploadService = Depends(get_file_upload_service)
):
    """
    带冲突处理的文件上传

    支持的冲突处理策略:
    - rename: 自动重命名（默认）
    - replace: 覆盖现有文件
    - cancel: 取消上传
    """
    try:
        # 处理允许的文件类型
        allowed_types_list = None
        if allowed_types:
            allowed_types_list = [t.strip() for t in allowed_types.split(',')]

        result = await service.upload_file_with_conflict_handling(
            file=file,
            upload_path=upload_path,
            user_id=user_id,
            max_size=max_size,
            allowed_types=allowed_types_list,
            conflict_strategy=conflict_strategy,
            create_backup=create_backup,
            check_content=check_content
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@router.post("/check-conflict", response_model=FileConflictCheckResponse)
async def check_file_conflict(
    request: FileConflictCheckRequest,
    service: FileUploadService = Depends(get_file_upload_service)
):
    """
    检查文件冲突（不执行上传）

    用于在上传前预检查是否存在文件冲突
    """
    try:
        result = await service.check_file_conflict_only(request)
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"冲突检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"冲突检查失败: {str(e)}")
