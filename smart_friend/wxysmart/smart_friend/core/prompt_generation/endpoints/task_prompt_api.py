# 任务计划表Prompt生成API端点
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional
import logging

from ..schemas import TaskPromptRequest, TaskPromptResponse, TemplateInfo
from ..service import PromptGenerationService
from ..template_manager import PromptTemplateManager
from service.daily_task_service import DailyTaskService

logger = logging.getLogger(__name__)

router = APIRouter()


def get_prompt_service() -> PromptGenerationService:
    """获取prompt生成服务依赖"""
    return PromptGenerationService()


def get_template_manager() -> PromptTemplateManager:
    """获取模板管理器依赖"""
    return PromptTemplateManager()


def get_daily_task_service() -> DailyTaskService:
    """获取每日任务服务依赖"""
    return DailyTaskService()


@router.post("/task-prompt", response_model=TaskPromptResponse)
async def generate_task_prompt(
    request: TaskPromptRequest,
    service: PromptGenerationService = Depends(get_prompt_service)
):
    """
    生成任务计划表的完整prompt
    
    基于儿童个人档案、历史学习情况、当日任务要求和昨日任务情况，
    生成符合prompt.txt格式的完整任务计划表prompt。
    
    - **child_id**: 儿童ID（必填）
    - **days_back**: 回溯天数，获取近期学习数据（1-30天，默认7天）
    - **include_yesterday_tasks**: 是否包含昨日任务情况（默认true）
    - **template_type**: 模板类型（task_prompt或simple_task，默认task_prompt）
    - **subject_filter**: 学科筛选，只获取特定学科的数据（可选）
    
    返回包含：
    - prompt1: 孩子个人肖像
    - prompt2: 历史学习情况
    - prompt3: 当日任务要求
    - prompt4: 昨日任务情况
    - final_prompt: 最终格式化的完整prompt
    - 原始数据用于调试
    """
    try:
        # 验证儿童ID
        if request.child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="儿童ID必须大于0"
            )
        
        # 验证模板类型
        valid_templates = ["task_prompt"]
        if request.template_type not in valid_templates:
            raise HTTPException(
                status_code=400,
                detail=f"模板类型必须是: {', '.join(valid_templates)}"
            )
        
        # 生成任务prompt
        result = service.generate_task_prompt(request)
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"未找到儿童ID {request.child_id} 的相关数据或生成失败"
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成任务prompt时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成任务prompt失败: {str(e)}"
        )


@router.get("/task-prompt/{child_id}", response_model=TaskPromptResponse)
async def generate_task_prompt_simple(
    child_id: int,
    days_back: int = Query(default=7, ge=1, le=30, description="回溯天数(1-30天)"),
    include_yesterday_tasks: bool = Query(default=True, description="是否包含昨日任务情况"),
    template_type: str = Query(default="task_prompt", description="模板类型"),
    subject_filter: Optional[str] = Query(None, description="学科筛选"),
    auto_soft_delete: bool = Query(default=True, description="是否自动软删除prompt4输入源任务"),
    service: PromptGenerationService = Depends(get_prompt_service),
    task_service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    简化的任务prompt生成接口（GET方式）

    通过URL参数快速生成任务计划表prompt，适用于简单的调用场景。

    - **child_id**: 儿童ID
    - **days_back**: 回溯天数（1-30天，默认7天）
    - **include_yesterday_tasks**: 是否包含昨日任务情况（默认true）
    - **template_type**: 模板类型（默认task_prompt）
    - **subject_filter**: 学科筛选（可选）
    - **auto_soft_delete**: 是否自动软删除prompt4输入源任务（默认true）
    """
    try:
        # 构建请求对象
        request = TaskPromptRequest(
            child_id=child_id,
            days_back=days_back,
            include_yesterday_tasks=include_yesterday_tasks,
            template_type=template_type,
            subject_filter=subject_filter
        )

        # 生成任务prompt
        result = service.generate_task_prompt(request)

        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"未找到儿童ID {child_id} 的相关数据或生成失败"
            )

        # 如果启用自动软删除，在生成prompt后立即软删除prompt4输入源任务
        if auto_soft_delete:
            logger.info(f"开始为儿童ID {child_id} 软删除prompt4输入源任务")
            soft_delete_result = await task_service.soft_delete_prompt4_tasks(child_id)

            if soft_delete_result.get("success"):
                logger.info(f"成功软删除prompt4输入源任务: {soft_delete_result.get('message')}")
                # 将软删除信息添加到返回结果中
                result.soft_delete_info = soft_delete_result.get("data", {})
            else:
                logger.warning(f"软删除prompt4输入源任务失败: {soft_delete_result.get('message')}")
                # 软删除失败不影响prompt生成，只记录警告

        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成任务prompt时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成任务prompt失败: {str(e)}"
        )


@router.get("/templates", response_model=list[TemplateInfo])
async def get_available_templates(
    template_manager: PromptTemplateManager = Depends(get_template_manager)
):
    """
    获取可用的prompt模板列表
    
    返回系统中所有可用的prompt模板信息，包括模板名称、描述和变量说明。
    """
    try:
        templates = template_manager.get_available_templates()
        variables = template_manager.get_template_variables()
        
        template_list = []
        for name, description in templates.items():
            template_info = TemplateInfo(
                name=name,
                description=description,
                variables=variables
            )
            template_list.append(template_info)
        
        return template_list
        
    except Exception as e:
        logger.error(f"获取模板列表时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取模板列表失败: {str(e)}"
        )


@router.get("/prompt-sections/{child_id}")
async def get_prompt_sections(
    child_id: int,
    days_back: int = Query(default=7, ge=1, le=30, description="回溯天数(1-30天)"),
    subject_filter: Optional[str] = Query(None, description="学科筛选"),
    service: PromptGenerationService = Depends(get_prompt_service)
):
    """
    获取prompt各个部分的详细内容
    
    分别返回prompt1-prompt4的详细内容，用于调试和预览。
    
    - **child_id**: 儿童ID
    - **days_back**: 回溯天数（1-30天，默认7天）
    - **subject_filter**: 学科筛选（可选）
    """
    try:
        # 构建请求对象
        request = TaskPromptRequest(
            child_id=child_id,
            days_back=days_back,
            include_yesterday_tasks=True,
            template_type="task_prompt",
            subject_filter=subject_filter
        )
        
        # 生成任务prompt
        result = service.generate_task_prompt(request)
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"未找到儿童ID {child_id} 的相关数据"
            )
        
        return {
            "child_id": child_id,
            "prompt1": result.prompt1,
            "prompt2": result.prompt2,
            "prompt3": result.prompt3,
            "prompt4": result.prompt4,
            "data_counts": {
                "today_homework_count": len(result.today_homework),
                "recent_completion_count": len(result.recent_completion),
                "yesterday_tasks_count": len(result.yesterday_tasks) if result.yesterday_tasks else 0
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取prompt部分时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取prompt部分失败: {str(e)}"
        )
