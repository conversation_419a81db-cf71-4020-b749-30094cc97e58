# Prompt生成API端点
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional
import logging

from ..schemas import PromptGenerationRequest, GeneratedPrompt
from ..service import PromptGenerationService

logger = logging.getLogger(__name__)

router = APIRouter()


def get_prompt_service() -> PromptGenerationService:
    """获取prompt生成服务依赖"""
    return PromptGenerationService()


@router.post("/generate", response_model=GeneratedPrompt)
async def generate_prompt(
    request: PromptGenerationRequest,
    service: PromptGenerationService = Depends(get_prompt_service)
):
    """
    生成综合学习prompt
    
    整合儿童个人档案、今日作业和近期学习表现，生成结构化的prompt供大模型使用。
    
    - **child_id**: 儿童ID（必填）
    - **days_back**: 回溯天数，获取近期学习数据（1-30天，默认7天）
    - **include_today_homework**: 是否包含今日作业（默认true）
    - **include_recent_completion**: 是否包含近期完成情况（默认true）
    - **subject_filter**: 学科筛选，只获取特定学科的数据（可选）
    - **prompt_template**: 自定义prompt模板（可选）
    
    返回包含：
    - 儿童个人档案摘要
    - 今日学习任务列表
    - 近期学习表现数据
    - 结构化的prompt文本
    - 数据统计摘要
    """
    try:
        # 验证儿童ID
        if request.child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="儿童ID必须大于0"
            )
        
        # 生成prompt
        result = service.generate_prompt(request)
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"未找到儿童ID {request.child_id} 的相关数据或生成失败"
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成prompt时发生错误 prompt api: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成prompt失败: {str(e)}"
        )


@router.get("/generate/{child_id}", response_model=GeneratedPrompt)
async def generate_prompt_simple(
    child_id: int,
    days_back: int = Query(default=7, ge=1, le=30, description="回溯天数(1-30天)"),
    include_today_homework: bool = Query(default=True, description="是否包含今日作业"),
    include_recent_completion: bool = Query(default=True, description="是否包含近期完成情况"),
    subject_filter: Optional[str] = Query(None, description="学科筛选"),
    service: PromptGenerationService = Depends(get_prompt_service)
):
    """
    简化的prompt生成接口（GET方式）
    
    通过URL参数快速生成prompt，适用于简单的调用场景。
    
    - **child_id**: 儿童ID
    - **days_back**: 回溯天数（1-30天，默认7天）
    - **include_today_homework**: 是否包含今日作业（默认true）
    - **include_recent_completion**: 是否包含近期完成情况（默认true）
    - **subject_filter**: 学科筛选（可选）
    """
    try:
        # 构建请求对象
        request = PromptGenerationRequest(
            child_id=child_id,
            days_back=days_back,
            include_today_homework=include_today_homework,
            include_recent_completion=include_recent_completion,
            subject_filter=subject_filter
        )
        
        # 生成prompt
        result = service.generate_prompt(request)
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"未找到儿童ID {child_id} 的相关数据或生成失败"
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成prompt时发生错误 prompt api: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成prompt失败: {str(e)}"
        )


@router.get("/preview/{child_id}")
async def preview_data(
    child_id: int,
    days_back: int = Query(default=7, ge=1, le=30, description="回溯天数(1-30天)"),
    subject_filter: Optional[str] = Query(None, description="学科筛选"),
    service: PromptGenerationService = Depends(get_prompt_service)
):
    """
    预览数据接口
    
    在生成prompt之前，预览将要使用的原始数据，用于调试和验证。
    
    - **child_id**: 儿童ID
    - **days_back**: 回溯天数（1-30天，默认7天）
    - **subject_filter**: 学科筛选（可选）
    """
    try:
        # 获取各部分数据
        child_profile = service._get_child_profile(child_id)
        today_homework = service._get_today_homework(child_id, subject_filter)
        recent_completion = service._get_recent_completion(child_id, days_back, subject_filter)
        
        if not child_profile:
            raise HTTPException(
                status_code=404,
                detail=f"未找到儿童ID {child_id} 的档案信息"
            )
        
        return {
            "child_profile": child_profile.model_dump() if child_profile else None,
            "today_homework": [h.model_dump() for h in today_homework],
            "recent_completion": [r.model_dump() for r in recent_completion],
            "data_counts": {
                "today_homework_count": len(today_homework),
                "recent_completion_count": len(recent_completion)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预览数据时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"预览数据失败: {str(e)}"
        )
