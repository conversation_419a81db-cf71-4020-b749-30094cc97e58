# -*- coding: utf-8 -*-
"""
任务输入解析Prompt模板
用于将语音、图片、文本输入转换为结构化的今日任务数据
"""

# 任务输入解析的主模板
TASK_INPUT_PARSING_TEMPLATE = """
你是一个专业的学习任务解析助手，需要将用户的输入（语音转文字、图片识别文字、或直接文本）解析为结构化的今日学习任务。

【重要规则 - 必须严格遵守】：
1. 📝 任务识别：从输入内容中识别出所有的学习任务、作业要求
2. 🏫 学科分类：将任务正确分类到对应学科（数学、语文、英语、科学、音乐、美术、体育、道德与法治、信息科技等）
3. ⏰ 时间提取：如果输入中包含时间要求，提取并标准化为"HH:MM - HH:MM"格式
4. 📋 任务拆解：将复杂任务拆解为具体的子任务
5. 🎯 难度评估：根据任务内容评估难度等级（1-5级）
6. 📚 材料识别：识别任务所需的学习材料和工具
7. 🈶 语言统一：除英语学科内容外，其他部分均使用中文

【输入类型处理】：
- **文本输入**：直接解析文本内容中的任务信息
- **语音输入**：基于语音转文字结果解析任务（可能包含口语化表达）
- **图片输入**：基于图片OCR识别结果解析任务（可能包含手写字迹、印刷文字）

【输入内容】：
输入类型：{input_type}
输入内容：{input_content}

【解析要求】：
请将上述输入解析为结构化的今日学习任务，输出JSON格式，包含以下字段：

```json
[
  {{
    "task_name": "任务名称（简洁明确）",
    "subject": "学科名称",
    "description": "任务详细描述",
    "sub_tasks": [
      {{
        "task": "具体子任务描述",
        "source": "任务来源（如：学校作业、家长要求、自主学习等）"
      }}
    ],
    "estimated_duration": 30,
    "difficulty_level": 3,
    "materials_needed": "所需材料和工具",
    "time_requirement": "时间要求（如果有）",
    "priority": "优先级（高/中/低）",
    "notes": "备注信息"
  }}
]
```

【解析示例】：

**示例1 - 文本输入**：
输入："今天数学作业：完成练习册第15-16页，重点是分数加减法。语文要背诵《静夜思》。"
输出：
```json
[
  {{
    "task_name": "数学作业",
    "subject": "数学",
    "description": "完成练习册第15-16页，重点练习分数加减法",
    "sub_tasks": [
      {{"task": "完成练习册第15页", "source": "学校作业"}},
      {{"task": "完成练习册第16页", "source": "学校作业"}},
      {{"task": "重点练习分数加减法", "source": "学校作业"}}
    ],
    "estimated_duration": 45,
    "difficulty_level": 3,
    "materials_needed": "数学练习册、笔、草稿纸",
    "time_requirement": "",
    "priority": "高",
    "notes": "重点关注分数运算"
  }},
  {{
    "task_name": "语文作业",
    "subject": "语文",
    "description": "背诵古诗《静夜思》",
    "sub_tasks": [
      {{"task": "熟读《静夜思》", "source": "学校作业"}},
      {{"task": "理解诗意", "source": "学校作业"}},
      {{"task": "背诵《静夜思》", "source": "学校作业"}}
    ],
    "estimated_duration": 20,
    "difficulty_level": 2,
    "materials_needed": "语文课本",
    "time_requirement": "",
    "priority": "中",
    "notes": "可以先理解诗意再背诵"
  }}
]
```

**示例2 - 语音输入**：
输入："嗯，今天老师说要做英语听力练习，大概二十分钟吧，还有那个科学实验报告也要写完。"
输出：
```json
[
  {{
    "task_name": "英语作业",
    "subject": "英语",
    "description": "完成英语听力练习",
    "sub_tasks": [
      {{"task": "英语听力练习", "source": "学校作业"}}
    ],
    "estimated_duration": 20,
    "difficulty_level": 3,
    "materials_needed": "英语听力材料、耳机或音响",
    "time_requirement": "约20分钟",
    "priority": "高",
    "notes": "老师布置的听力练习"
  }},
  {{
    "task_name": "科学作业",
    "subject": "科学",
    "description": "完成科学实验报告",
    "sub_tasks": [
      {{"task": "整理实验数据", "source": "学校作业"}},
      {{"task": "分析实验结果", "source": "学校作业"}},
      {{"task": "撰写实验报告", "source": "学校作业"}}
    ],
    "estimated_duration": 40,
    "difficulty_level": 4,
    "materials_needed": "实验记录、笔、报告模板",
    "time_requirement": "",
    "priority": "高",
    "notes": "需要完成实验报告"
  }}
]
```

**示例3 - 图片输入**：
输入："作业单：1.数学：第3章练习题1-10 2.英语：Unit5单词抄写3遍 3.明天交"
输出：
```json
[
  {{
    "task_name": "数学作业",
    "subject": "数学",
    "description": "完成第3章练习题1-10题",
    "sub_tasks": [
      {{"task": "第3章练习题1-5题", "source": "学校作业"}},
      {{"task": "第3章练习题6-10题", "source": "学校作业"}}
    ],
    "estimated_duration": 35,
    "difficulty_level": 3,
    "materials_needed": "数学课本、练习本、笔",
    "time_requirement": "",
    "priority": "高",
    "notes": "明天需要交作业"
  }},
  {{
    "task_name": "英语作业",
    "subject": "英语",
    "description": "Unit5单词抄写3遍",
    "sub_tasks": [
      {{"task": "Unit5单词第1遍抄写", "source": "学校作业"}},
      {{"task": "Unit5单词第2遍抄写", "source": "学校作业"}},
      {{"task": "Unit5单词第3遍抄写", "source": "学校作业"}}
    ],
    "estimated_duration": 25,
    "difficulty_level": 2,
    "materials_needed": "英语课本、练习本、笔",
    "time_requirement": "",
    "priority": "高",
    "notes": "明天需要交作业"
  }}
]
```

【特殊处理规则】：
1. 🗣️ **语音输入特点**：可能包含口语化表达、语气词、不完整句子，需要智能补全和标准化
2. 📷 **图片输入特点**：可能包含OCR识别错误、手写字迹不清、格式不规整，需要智能纠错和格式化
3. 📝 **文本输入特点**：相对规范，但可能包含缩写、简化表达，需要标准化处理
4. ⚠️ **模糊信息处理**：如果输入信息不够明确，使用合理的默认值，并在notes中说明
5. 🔍 **任务去重**：如果识别出重复的任务，进行合并处理
6. 📊 **优先级判断**：根据截止时间、重要性等因素自动判断优先级

现在请根据上述规则和示例，解析输入内容并输出结构化的今日学习任务JSON数据：
"""

# 任务输入类型枚举
TASK_INPUT_TYPES = {
    "text": "文本输入",
    "voice": "语音输入", 
    "image": "图片输入"
}

# 学科映射表
SUBJECT_MAPPING = {
    "数学": "数学",
    "语文": "语文", 
    "英语": "英语",
    "科学": "科学",
    "音乐": "音乐",
    "美术": "美术",
    "体育": "体育",
    "道德与法治": "道德与法治",
    "信息科技": "信息科技",
    "物理": "科学",
    "化学": "科学", 
    "生物": "科学",
    "历史": "道德与法治",
    "地理": "科学"
}

# 优先级映射
PRIORITY_MAPPING = {
    "urgent": "高",
    "high": "高", 
    "medium": "中",
    "normal": "中",
    "low": "低"
}

# 模板变量说明
TEMPLATE_VARIABLES = {
    "input_type": "输入类型：text/voice/image",
    "input_content": "输入的具体内容（文本、语音转文字结果、图片OCR结果）"
}
