# -*- coding: utf-8 -*-
"""
Prompt模板管理器
负责管理和生成各种类型的prompt模板
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from .schemas import (
    ChildProfileSummary, TodayHomeworkSummary, RecentHomeworkCompletion
)

logger = logging.getLogger(__name__)


class PromptTemplateManager:
    """Prompt模板管理器"""
    
    def __init__(self):
        """初始化模板管理器"""
        self.templates = self._load_templates()
    
    def _load_templates(self) -> Dict[str, str]:
        """加载预定义模板"""
        # 导入你设计的prompt模板
        try:
            from .prompt_template.task_prompt_template import TASK_PROMPT_TEMPLATE
            task_template = TASK_PROMPT_TEMPLATE
            logger.info("成功导入自定义prompt模板")
        except ImportError as e:
            logger.warning(f"无法导入自定义prompt模板: {e}，使用默认模板")
            task_template = """
基于以下信息，请为{child_name}生成今日个性化学习任务计划表：

{prompt1}

{prompt2}

{prompt3}

{prompt4}

请生成一个JSON格式的任务计划表，包含以下字段：
- task_name: 任务名称
- subject: 学科
- time_slot: 建议时间段
- difficulty_level: 难度等级(1-5)
- estimated_duration: 预计耗时(分钟)
- learning_objectives: 学习目标
- materials_needed: 所需材料
- parent_guidance: 家长指导建议

请确保任务安排符合孩子的学习特点和能力水平。
"""

        return {
            "task_prompt": task_template,
            "basic_prompt": """
学生基本信息：
- 姓名：{child_name}（{child_nickname}）
- 年龄：{child_age}岁
- 学业等级：{academic_level}
- 学习风格：{learning_style}
- 擅长科目：{good_at_subjects}
- 薄弱科目：{weak_at_subjects}

今日学习任务：{today_homework_count}项
近期学习记录：{recent_records_count}条
"""
        }
    
    def generate_task_prompt(self, 
                           child_profile: ChildProfileSummary,
                           recent_completion: List[RecentHomeworkCompletion],
                           yesterday_tasks: Optional[List[Dict[str, Any]]],
                           today_homework: List[TodayHomeworkSummary],
                           template_type: str = "task_prompt") -> str:
        """生成任务计划表的完整prompt"""
        try:
            # 生成各个部分的prompt
            prompt1 = self._generate_prompt1(child_profile)
            prompt2 = self._generate_prompt2(recent_completion)
            prompt3 = self._generate_prompt3(yesterday_tasks)
            prompt4 = self._generate_prompt4(today_homework)
            
            # 获取模板
            template = self.templates.get(template_type, self.templates["task_prompt"])
            
            # 填充模板
            final_prompt = template.format(
                child_name=child_profile.name or "学生",
                prompt1=prompt1,
                prompt2=prompt2,
                prompt3=prompt3,
                prompt4=prompt4
            )

            print("template manager final_prompt 模板")
            print(final_prompt) 

            return final_prompt
            
        except KeyError as e:
            logger.error(f"生成任务prompt时发生占位符错误: {e}，请检查模板和填充数据")
            return f"生成prompt时发生占位符错误 in template manager: {str(e)}"
        except Exception as e:
            logger.error(f"生成任务prompt时发生错误: {e}")
            return f"生成prompt时发生错误 in template manager: {str(e)}"
    
    def _generate_prompt1(self, child_profile: ChildProfileSummary) -> str:
        """生成孩子个人肖像prompt"""
        try:
            prompt = f"""
## 孩子个人肖像

**基本信息：**
- 姓名：{child_profile.name or '未知'}
- 昵称：{child_profile.nickname or '无'}
- 年龄：{child_profile.age or '未知'}岁
- 学业等级：{child_profile.academic_level or '未知'}
- 学校：{child_profile.school_name or '未知'}

**学习特征：**
- 学习风格：{child_profile.learning_style or '未知'}
- 注意力持续时间：{child_profile.attention_span_minutes or '未知'}分钟
- 性格特点：{child_profile.personality_traits or '未知'}

**学科偏好：**
- 喜欢的学科：{child_profile.favorite_subjects or '未知'}
- 不喜欢的学科：{child_profile.disliked_subjects or '未知'}
- 擅长的科目：{child_profile.good_at_subjects or '未知'}
- 薄弱的科目：{child_profile.weak_at_subjects or '未知'}
"""
            return prompt.strip()
            
        except Exception as e:
            logger.error(f"生成个人肖像prompt时发生错误: {e}")
            return "个人肖像信息获取失败"
    
    def _generate_prompt2(self, recent_completion: List[RecentHomeworkCompletion]) -> str:
        """生成历史学习情况prompt"""
        try:
            if not recent_completion:
                return "## 历史学习情况\n\n暂无近期学习记录。"
            
            prompt = "## 历史学习情况\n\n"
            
            # 按学科分组统计
            subject_stats = {}
            for record in recent_completion:
                subject = record.subject
                if subject not in subject_stats:
                    subject_stats[subject] = {
                        'count': 0,
                        'avg_completion': 0,
                        'avg_accuracy': 0,
                        'avg_concentration': 0,
                        'records': []
                    }
                
                subject_stats[subject]['count'] += 1
                subject_stats[subject]['records'].append(record)
                
                # 累计统计
                if record.completion_rate:
                    subject_stats[subject]['avg_completion'] += record.completion_rate
                if record.accuracy_rate:
                    subject_stats[subject]['avg_accuracy'] += record.accuracy_rate
                if record.concentration_level:
                    subject_stats[subject]['avg_concentration'] += record.concentration_level
            
            # 计算平均值
            for subject, stats in subject_stats.items():
                count = stats['count']
                stats['avg_completion'] = stats['avg_completion'] / count if count > 0 else 0
                stats['avg_accuracy'] = stats['avg_accuracy'] / count if count > 0 else 0
                stats['avg_concentration'] = stats['avg_concentration'] / count if count > 0 else 0
            
            # 生成各学科统计
            for subject, stats in subject_stats.items():
                prompt += f"**{subject}：**\n"
                prompt += f"- 学习次数：{stats['count']}次\n"
                prompt += f"- 平均完成率：{stats['avg_completion']:.1f}%\n"
                prompt += f"- 平均正确率：{stats['avg_accuracy']:.1f}%\n"
                prompt += f"- 平均专注度：{stats['avg_concentration']:.1f}/5\n\n"
            
            return prompt.strip()
            
        except Exception as e:
            logger.error(f"生成历史学习情况prompt时发生错误: {e}")
            return "## 历史学习情况\n\n历史学习情况获取失败"
    
    def _generate_prompt3(self, yesterday_tasks: Optional[List[Dict[str, Any]]]) -> str:
        """生成昨日任务情况prompt"""
        try:
            if not yesterday_tasks:
                return "## 昨日任务情况\n\n暂无昨日任务记录。"
            
            prompt = "## 昨日任务情况\n\n"
            
            for i, task in enumerate(yesterday_tasks, 1):
                prompt += f"**任务{i}：**\n"
                prompt += f"- 任务名称：{task.get('task_name', '未知')}\n"
                prompt += f"- 学科：{task.get('subject', '未知')}\n"
                prompt += f"- 完成状态：{task.get('status', '未知')}\n"
                prompt += f"- 完成率：{task.get('completion_rate', '未知')}%\n"
                if task.get('notes'):
                    prompt += f"- 备注：{task['notes']}\n"
                prompt += "\n"
            
            return prompt.strip()
            
        except Exception as e:
            logger.error(f"生成昨日任务情况prompt时发生错误: {e}")
            return "## 昨日任务情况\n\n昨日任务情况获取失败"
    
    def _generate_prompt4(self, today_homework: List[TodayHomeworkSummary]) -> str:
        """生成今日任务要求prompt"""
        try:
            if not today_homework:
                return "## 今日任务要求\n\n暂无今日学习任务。"
            
            prompt = "## 今日任务要求\n\n"
            
            for i, homework in enumerate(today_homework, 1):
                prompt += f"**任务{i}：**\n"
                prompt += f"- 任务名称：{homework.task_name or '未知'}\n"
                prompt += f"- 学科：{homework.subject}\n"
                prompt += f"- 建议时间段：{homework.time_slot or '未指定'}\n"
                
                if homework.sub_tasks:
                    prompt += "- 子任务：\n"
                    for sub_task in homework.sub_tasks:
                        if isinstance(sub_task, dict):
                            prompt += f"  • {sub_task.get('task', '未知任务')}\n"
                        else:
                            prompt += f"  • {sub_task}\n"
                
                if homework.difficulty:
                    prompt += f"- 难点：{homework.difficulty}\n"
                
                if homework.confidence_index:
                    prompt += f"- 执行信心指数：{homework.confidence_index}/10\n"
                
                prompt += "\n"
            
            return prompt.strip()
            
        except Exception as e:
            logger.error(f"生成今日任务要求prompt时发生错误: {e}")
            return "## 今日任务要求\n\n今日任务要求获取失败"
    
    def get_template(self, template_name: str) -> Optional[str]:
        """获取指定模板"""
        return self.templates.get(template_name)
    
    def add_template(self, template_name: str, template_content: str):
        """添加新模板"""
        self.templates[template_name] = template_content
    
    def list_templates(self) -> List[str]:
        """列出所有可用模板"""
        return list(self.templates.keys())
