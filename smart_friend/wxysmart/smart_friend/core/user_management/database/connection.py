# SQLite数据库连接管理 - 简化版
import logging
from typing import Optional, List
from contextlib import contextmanager
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.engine import Engine

from config.config import settings

logger = logging.getLogger(__name__)


class SQLiteManager:
    """SQLite数据库连接管理器 - 简化版"""

    def __init__(self, database_url: Optional[str] = None):
        """
        初始化SQLite管理器

        Args:
            database_url: 数据库URL，如果不提供则使用配置中的默认值
        """
        self.database_url = database_url or settings.DATABASE_URL
        self.engine: Optional[Engine] = None
        self.SessionLocal: Optional[sessionmaker] = None
        self._initialize_engine()

    def _initialize_engine(self):
        """初始化SQLAlchemy引擎"""
        try:
            # 创建SQLAlchemy引擎
            self.engine = create_engine(
                self.database_url,
                connect_args={"check_same_thread": False},  # SQLite特定配置
                echo=False,  # 设置为True可以看到SQL语句
                pool_pre_ping=True,  # 连接前检查连接是否有效
            )

            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )

            logger.info(f"SQLite引擎初始化成功: {self.database_url}")

        except Exception as e:
            logger.error(f"SQLite引擎初始化失败: {e}")
            self.engine = None
            self.SessionLocal = None

    def check_connection(self) -> bool:
        """检查数据库连接状态"""
        if not self.engine:
            logger.error("SQLite引擎未初始化")
            return False

        try:
            # 尝试执行简单查询
            with self.engine.connect() as connection:
                result = connection.execute(text("SELECT 1"))
                result.fetchone()
            logger.debug("SQLite连接检查成功")
            return True

        except Exception as e:
            logger.error(f"SQLite连接检查失败: {e}")
            return False

    def get_session(self) -> Optional[Session]:
        """获取数据库会话"""
        if not self.SessionLocal:
            logger.error("会话工厂未初始化")
            return None

        try:
            return self.SessionLocal()
        except Exception as e:
            logger.error(f"创建数据库会话失败: {e}")
            return None

    @contextmanager
    def get_session_context(self):
        """获取数据库会话上下文管理器"""
        session = self.get_session()
        if not session:
            raise RuntimeError("无法创建数据库会话")

        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库会话执行失败: {e}")
            raise
        finally:
            session.close()

    def get_table_names(self) -> List[str]:
        """获取数据库中所有表名"""
        if not self.engine:
            logger.error("SQLite引擎未初始化")
            return []

        try:
            inspector = inspect(self.engine)
            table_names = inspector.get_table_names()
            logger.debug(f"获取到{len(table_names)}个表: {table_names}")
            return table_names

        except Exception as e:
            logger.error(f"获取表名失败: {e}")
            return []

    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("SQLite连接已关闭")


# 全局SQLite管理器实例
sqlite_manager = SQLiteManager()


def get_sqlite_manager() -> SQLiteManager:
    """获取SQLite管理器实例"""
    return sqlite_manager


def get_db_session() -> Optional[Session]:
    """获取数据库会话（兼容性函数）"""
    return sqlite_manager.get_session()


@contextmanager
def get_db_session_context():
    """获取数据库会话上下文管理器（兼容性函数）"""
    with sqlite_manager.get_session_context() as session:
        yield session


def check_db_connection() -> bool:
    """检查数据库连接（兼容性函数）"""
    return sqlite_manager.check_connection()


def init_db() -> bool:
    """初始化数据库（兼容性函数）"""
    try:
        # 检查连接
        if not sqlite_manager.check_connection():
            logger.error("数据库连接失败，无法初始化")
            return False

        logger.info("数据库初始化成功")
        return True

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False
