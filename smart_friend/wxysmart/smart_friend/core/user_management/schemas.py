# Pydantic 模型 - 小孩和家长信息的API请求/响应模型
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum


class GenderEnum(str, Enum):
    """性别枚举"""
    MALE = "male"
    FEMALE = "female"


class RelationshipEnum(str, Enum):
    """家长与小孩关系枚举"""
    FATHER = "father"
    MOTHER = "mother"
    GRANDFATHER = "grandfather"
    GRANDMOTHER = "grandmother"
    OTHER = "other"


class AcademicLevelEnum(str, Enum):
    """中国教育体系学业等级枚举"""
    PRIMARY_1 = "primary_1"                        # 小学一年级
    PRIMARY_2 = "primary_2"                        # 小学二年级
    PRIMARY_3 = "primary_3"                        # 小学三年级
    PRIMARY_4 = "primary_4"                        # 小学四年级
    PRIMARY_5 = "primary_5"                        # 小学五年级
    PRIMARY_6 = "primary_6"                        # 小学六年级


class BehaviorLevelEnum(str, Enum):
    """行为表现等级枚举"""
    EXCELLENT = "excellent"    # 优秀
    GOOD = "good"             # 良好
    AVERAGE = "average"       # 一般
    NEEDS_IMPROVEMENT = "needs_improvement"  # 需要改进
    CONCERNING = "concerning"  # 令人担忧


# ==================== 家长相关模型 ====================

class ParentBase(BaseModel):
    """家长基础模型"""
    name: str = Field(..., description="家长姓名", max_length=100)
    gender: Optional[GenderEnum] = Field(None, description="性别")
    age: Optional[int] = Field(None, description="年龄", ge=18, le=100)
    phone: Optional[str] = Field(None, description="手机号码", max_length=20)
    email: Optional[str] = Field(None, description="邮箱地址", max_length=100)
    wechat_id: Optional[str] = Field(None, description="微信号", max_length=50)
    notes: Optional[str] = Field(None, description="备注信息")


class ParentCreate(ParentBase):
    """创建家长请求模型"""
    pass


class ParentUpdate(BaseModel):
    """更新家长请求模型"""
    name: Optional[str] = Field(None, description="家长姓名", max_length=100)
    gender: Optional[GenderEnum] = Field(None, description="性别")
    age: Optional[int] = Field(None, description="年龄", ge=18, le=100)
    phone: Optional[str] = Field(None, description="手机号码", max_length=20)
    email: Optional[str] = Field(None, description="邮箱地址", max_length=100)
    wechat_id: Optional[str] = Field(None, description="微信号", max_length=50)
    notes: Optional[str] = Field(None, description="备注信息")


class ParentResponse(ParentBase):
    """家长响应模型"""
    id: int = Field(..., description="家长ID")
    is_active: bool = Field(..., description="是否激活")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# ==================== 小孩相关模型 ====================

class ChildBase(BaseModel):
    """小孩基础模型"""
    name: str = Field(..., description="小孩姓名", max_length=100)
    nickname: Optional[str] = Field(None, description="昵称", max_length=50)
    gender: Optional[GenderEnum] = Field(None, description="性别")
    birth_date: Optional[datetime] = Field(None, description="出生日期")
    age: Optional[int] = Field(None, description="年龄", ge=0, le=18)
    academic_level: Optional[AcademicLevelEnum] = Field(None, description="学业等级")
    school_name: Optional[str] = Field(None, description="学校名称", max_length=200)
    height: Optional[float] = Field(None, description="身高(cm)", ge=0, le=300)
    weight: Optional[float] = Field(None, description="体重(kg)", ge=0, le=200)
    overall_health_status: Optional[str] = Field("good", description="整体健康状况")
    allergies: Optional[str] = Field(None, description="过敏史")
    chronic_conditions: Optional[str] = Field(None, description="慢性疾病")
    medications: Optional[str] = Field(None, description="正在服用的药物")
    personality_traits: Optional[str] = Field(None, description="性格特点")
    learning_style: Optional[str] = Field(None, description="学习风格", max_length=100)
    behavior_level: Optional[BehaviorLevelEnum] = Field(BehaviorLevelEnum.AVERAGE, description="行为表现等级")
    attention_span_minutes: Optional[int] = Field(None, description="注意力持续时间(分钟)", ge=0, le=300)
    hobbies: Optional[str] = Field(None, description="兴趣爱好")
    favorite_subjects: Optional[str] = Field(None, description="喜欢的学科")
    disliked_subjects: Optional[str] = Field(None, description="不喜欢的学科")
    good_at_subjects: Optional[str] = Field(None, description="擅长的科目")
    weak_at_subjects: Optional[str] = Field(None, description="不擅长的科目")
    notes: Optional[str] = Field(None, description="备注信息")


class ChildCreate(ChildBase):
    """创建小孩请求模型"""
    pass


class ChildUpdate(BaseModel):
    """更新小孩请求模型"""
    name: Optional[str] = Field(None, description="小孩姓名", max_length=100)
    nickname: Optional[str] = Field(None, description="昵称", max_length=50)
    gender: Optional[GenderEnum] = Field(None, description="性别")
    birth_date: Optional[datetime] = Field(None, description="出生日期")
    age: Optional[int] = Field(None, description="年龄", ge=0, le=18)
    academic_level: Optional[AcademicLevelEnum] = Field(None, description="学业等级")
    school_name: Optional[str] = Field(None, description="学校名称", max_length=200)
    height: Optional[float] = Field(None, description="身高(cm)", ge=0, le=300)
    weight: Optional[float] = Field(None, description="体重(kg)", ge=0, le=200)
    overall_health_status: Optional[str] = Field(None, description="整体健康状况")
    allergies: Optional[str] = Field(None, description="过敏史")
    chronic_conditions: Optional[str] = Field(None, description="慢性疾病")
    medications: Optional[str] = Field(None, description="正在服用的药物")
    personality_traits: Optional[str] = Field(None, description="性格特点")
    learning_style: Optional[str] = Field(None, description="学习风格", max_length=100)
    behavior_level: Optional[BehaviorLevelEnum] = Field(None, description="行为表现等级")
    attention_span_minutes: Optional[int] = Field(None, description="注意力持续时间(分钟)", ge=0, le=300)
    hobbies: Optional[str] = Field(None, description="兴趣爱好")
    favorite_subjects: Optional[str] = Field(None, description="喜欢的学科")
    disliked_subjects: Optional[str] = Field(None, description="不喜欢的学科")
    good_at_subjects: Optional[str] = Field(None, description="擅长的科目")
    weak_at_subjects: Optional[str] = Field(None, description="不擅长的科目")
    notes: Optional[str] = Field(None, description="备注信息")


class ChildResponse(ChildBase):
    """小孩响应模型"""
    id: int = Field(..., description="小孩ID")
    is_active: bool = Field(..., description="是否激活")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# ==================== 关系相关模型 ====================

class ChildParentRelationshipBase(BaseModel):
    """小孩-家长关系基础模型"""
    child_id: int = Field(..., description="小孩ID")
    parent_id: int = Field(..., description="家长ID")
    relationship_type: RelationshipEnum = Field(..., description="关系类型")
    is_primary_contact: bool = Field(False, description="是否为主要联系人")
    notes: Optional[str] = Field(None, description="关系备注")


class ChildParentRelationshipCreate(ChildParentRelationshipBase):
    """创建小孩-家长关系请求模型"""
    pass


class ChildParentRelationshipUpdate(BaseModel):
    """更新小孩-家长关系请求模型"""
    relationship_type: Optional[RelationshipEnum] = Field(None, description="关系类型")
    is_primary_contact: Optional[bool] = Field(None, description="是否为主要联系人")
    notes: Optional[str] = Field(None, description="关系备注")


class ChildParentRelationshipResponse(ChildParentRelationshipBase):
    """小孩-家长关系响应模型"""
    id: int = Field(..., description="关系ID")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


# ==================== 学业记录相关模型 ====================

class ChildAcademicRecordBase(BaseModel):
    """小孩学业记录基础模型"""
    child_id: int = Field(..., description="小孩ID")
    academic_year: str = Field(..., description="学年(如2023-2024)", max_length=20)
    semester: str = Field(..., description="学期(如上学期/下学期)", max_length=20)
    record_date: datetime = Field(..., description="记录日期")
    record_type: str = Field(..., description="记录类型(期中/期末/月考/作业/其他)", max_length=50)
    subject: str = Field(..., description="学科名称", max_length=100)
    subject_category: Optional[str] = Field(None, description="学科类别(主科/副科)", max_length=50)
    score: Optional[float] = Field(None, description="得分", ge=0)
    max_score: Optional[float] = Field(None, description="满分", ge=0)
    percentage: Optional[float] = Field(None, description="得分率(%)", ge=0, le=100)
    grade_level: Optional[str] = Field(None, description="等级(A/B/C/D或优/良/中/差)", max_length=10)
    class_rank: Optional[int] = Field(None, description="班级排名", ge=1)
    grade_rank: Optional[int] = Field(None, description="年级排名", ge=1)
    notes: Optional[str] = Field(None, description="备注信息")


class ChildAcademicRecordCreate(ChildAcademicRecordBase):
    """创建小孩学业记录请求模型"""
    pass


class ChildAcademicRecordUpdate(BaseModel):
    """更新小孩学业记录请求模型"""
    academic_year: Optional[str] = Field(None, description="学年", max_length=20)
    semester: Optional[str] = Field(None, description="学期", max_length=20)
    record_date: Optional[datetime] = Field(None, description="记录日期")
    record_type: Optional[str] = Field(None, description="记录类型", max_length=50)
    subject: Optional[str] = Field(None, description="学科名称", max_length=100)
    subject_category: Optional[str] = Field(None, description="学科类别", max_length=50)
    score: Optional[float] = Field(None, description="得分", ge=0)
    max_score: Optional[float] = Field(None, description="满分", ge=0)
    percentage: Optional[float] = Field(None, description="得分率(%)", ge=0, le=100)
    grade_level: Optional[str] = Field(None, description="等级", max_length=10)
    class_rank: Optional[int] = Field(None, description="班级排名", ge=1)
    grade_rank: Optional[int] = Field(None, description="年级排名", ge=1)
    notes: Optional[str] = Field(None, description="备注信息")


class ChildAcademicRecordResponse(ChildAcademicRecordBase):
    """小孩学业记录响应模型"""
    id: int = Field(..., description="学业记录ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# ==================== 综合响应模型 ====================

class ChildWithRelationsResponse(ChildResponse):
    """包含关系的小孩响应模型"""
    parents: List[ParentResponse] = Field(default_factory=list, description="关联的家长列表")
    academic_records: List[ChildAcademicRecordResponse] = Field(default_factory=list, description="学业记录列表")


class ParentWithChildrenResponse(ParentResponse):
    """包含小孩的家长响应模型"""
    children: List[ChildResponse] = Field(default_factory=list, description="关联的小孩列表")
