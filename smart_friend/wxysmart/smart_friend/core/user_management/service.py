# 用户信息管理业务逻辑层
from typing import List, Optional

from .services.user_service import ChildService as BaseChildService
from .schemas import (
    ParentCreate, ParentUpdate, ParentResponse,
    ChildCreate, ChildUpdate, ChildResponse,
    ChildParentRelationshipCreate, ChildParentRelationshipResponse,
    ChildAcademicRecordCreate, ChildAcademicRecordUpdate, ChildAcademicRecordResponse
)


class UserManagementService:
    """用户信息管理服务类"""
    
    def __init__(self):
        self.base_service = BaseChildService()
    
    # ==================== 家长管理 ====================
    
    def create_parent(self, parent_data: ParentCreate) -> Optional[ParentResponse]:
        """创建家长"""
        parent = self.base_service.create_parent(parent_data)
        if parent:
            return ParentResponse.model_validate(parent)
        return None
    
    def get_parent(self, parent_id: int) -> Optional[ParentResponse]:
        """获取家长信息"""
        try:
            parent = self.base_service.get_parent(parent_id)
            if parent:
                return ParentResponse.model_validate(parent)
            return None
        except Exception as e:
            print(f"获取家长信息失败: {e}")
            return None
    
    def get_parents(self, skip: int = 0, limit: int = 100, is_active: bool = True) -> List[ParentResponse]:
        """获取家长列表"""
        try:
            parents = self.base_service.get_parents(skip=skip, limit=limit, is_active=is_active)
            return [ParentResponse.model_validate(parent) for parent in parents]
        except Exception as e:
            print(f"获取家长列表失败: {e}")
            return []
    
    def update_parent(self, parent_id: int, parent_data: ParentUpdate) -> Optional[ParentResponse]:
        """更新家长信息"""
        try:
            parent = self.base_service.update_parent(parent_id, parent_data)
            if parent:
                return ParentResponse.model_validate(parent)
            return None
        except Exception as e:
            print(f"更新家长信息失败: {e}")
            return None
    
    def delete_parent(self, parent_id: int) -> bool:
        """软删除家长"""
        try:
            return self.base_service.delete_parent(parent_id)
        except Exception as e:
            print(f"删除家长失败: {e}")
            return False
    
    # ==================== 小孩管理 ====================
    
    def create_child(self, child_data: ChildCreate) -> Optional[ChildResponse]:
        """创建小孩"""
        try:
            child = self.base_service.create_child(child_data)
            if child:
                return ChildResponse.model_validate(child)
            return None
        except Exception as e:
            print(f"创建小孩失败: {e}")
            return None
    
    def get_child(self, child_id: int, include_relations: bool = False) -> Optional[ChildResponse]:
        """获取小孩信息"""
        try:
            child = self.base_service.get_child(child_id, include_relations=include_relations)
            if child:
                return ChildResponse.model_validate(child)
            return None
        except Exception as e:
            print(f"获取小孩信息失败: {e}")
            return None
    
    def get_children(self, skip: int = 0, limit: int = 100, is_active: bool = True) -> List[ChildResponse]:
        """获取小孩列表"""
        try:
            children = self.base_service.get_children(skip=skip, limit=limit, is_active=is_active)
            return [ChildResponse.model_validate(child) for child in children]
        except Exception as e:
            print(f"获取小孩列表失败: {e}")
            return []
    
    def update_child(self, child_id: int, child_data: ChildUpdate) -> Optional[ChildResponse]:
        """更新小孩信息"""
        try:
            child = self.base_service.update_child(child_id, child_data)
            if child:
                return ChildResponse.model_validate(child)
            return None
        except Exception as e:
            print(f"更新小孩信息失败: {e}")
            return None
    
    def delete_child(self, child_id: int) -> bool:
        """软删除小孩"""
        try:
            return self.base_service.delete_child(child_id)
        except Exception as e:
            print(f"删除小孩失败: {e}")
            return False
    
    # ==================== 关系管理 ====================
    
    def create_relationship(self, relationship_data: ChildParentRelationshipCreate) -> Optional[ChildParentRelationshipResponse]:
        """创建家长-小孩关系"""
        relationship = self.base_service.create_child_parent_relationship(relationship_data)
        if relationship:
            return ChildParentRelationshipResponse.model_validate(relationship)
        return None
    
    def delete_relationship(self, relationship_id: int) -> bool:
        """软删除关系"""
        try:
            return self.base_service.delete_child_parent_relationship(relationship_id)
        except Exception as e:
            print(f"删除关系失败: {e}")
            return False
    
    def get_child_parents(self, child_id: int) -> List[dict]:
        """获取小孩的家长"""
        try:
            parents = self.base_service.get_child_parents(child_id)
            return [
                {
                    "id": parent.id,
                    "name": parent.name,
                    "gender": parent.gender,
                    "age": parent.age,
                    "phone": parent.phone,
                    "email": parent.email,
                    "wechat_id": parent.wechat_id
                }
                for parent in parents
            ]
        except Exception as e:
            print(f"获取小孩家长失败: {e}")
            return []
    
    def get_parent_children(self, parent_id: int) -> List[dict]:
        """获取家长的小孩"""
        try:
            children = self.base_service.get_parent_children(parent_id)
            return [
                {
                    "id": child.id,
                    "name": child.name,
                    "nickname": child.nickname,
                    "gender": child.gender,
                    "age": child.age,
                    "academic_level": child.academic_level,
                    "school_name": child.school_name
                }
                for child in children
            ]
        except Exception as e:
            print(f"获取家长小孩失败: {e}")
            return []
    
    # ==================== 学业记录管理 ====================
    
    def create_academic_record(self, record_data: ChildAcademicRecordCreate) -> Optional[ChildAcademicRecordResponse]:
        """创建学业记录"""
        try:
            record = self.base_service.create_academic_record(record_data)
            if record:
                return ChildAcademicRecordResponse.model_validate(record)
            return None
        except Exception as e:
            print(f"创建学业记录失败: {e}")
            return None
    
    def get_academic_record(self, record_id: int) -> Optional[ChildAcademicRecordResponse]:
        """获取学业记录"""
        try:
            record = self.base_service.get_academic_record(record_id)
            if record:
                return ChildAcademicRecordResponse.model_validate(record)
            return None
        except Exception as e:
            print(f"获取学业记录失败: {e}")
            return None
    
    def get_child_academic_records(
        self, 
        child_id: int, 
        subject: Optional[str] = None, 
        academic_year: Optional[str] = None, 
        limit: int = 100
    ) -> List[ChildAcademicRecordResponse]:
        """获取小孩的学业记录"""
        try:
            records = self.base_service.get_child_academic_records(
                child_id=child_id,
                subject=subject,
                academic_year=academic_year,
                limit=limit
            )
            return [ChildAcademicRecordResponse.model_validate(record) for record in records]
        except Exception as e:
            print(f"获取小孩学业记录失败: {e}")
            return []
    
    def update_academic_record(self, record_id: int, record_data: ChildAcademicRecordUpdate) -> Optional[ChildAcademicRecordResponse]:
        """更新学业记录"""
        try:
            record = self.base_service.update_academic_record(record_id, record_data)
            if record:
                return ChildAcademicRecordResponse.model_validate(record)
            return None
        except Exception as e:
            print(f"更新学业记录失败: {e}")
            return None
    
    def delete_academic_record(self, record_id: int) -> bool:
        """软删除学业记录"""
        try:
            return self.base_service.delete_academic_record(record_id)
        except Exception as e:
            print(f"删除学业记录失败: {e}")
            return False


# 创建服务实例
user_management_service = UserManagementService()
