# 家长管理API端点
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
import logging

from ..service import user_management_service, UserManagementService
from ..schemas import (
    ParentCreate, ParentUpdate, ParentResponse, 
    ParentWithChildrenResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


def get_user_management_service() -> UserManagementService:
    """获取用户管理服务依赖"""
    return user_management_service


@router.post("/", response_model=ParentResponse, status_code=status.HTTP_201_CREATED)
async def create_parent(
    parent_data: ParentCreate,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    创建家长

    - **name**: 家长姓名（必填）
    - **gender**: 性别（可选）
    - **age**: 年龄（可选，18-100岁）
    - **phone**: 手机号码（可选）
    - **email**: 邮箱地址（可选）
    - **wechat_id**: 微信号（可选）
    - **notes**: 备注信息（可选）
    """
    try:
        parent = service.create_parent(parent_data)
        if not parent:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建家长失败，请检查输入数据"
            )

        return parent

    except HTTPException:
        raise
    except Exception as e:
        error_msg = str(e)
        logger.error(f"创建家长时发生错误: {e}")

        # 检查是否是唯一约束错误
        if "UNIQUE constraint failed" in error_msg:
            if "parents.email" in error_msg:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="邮箱地址已存在，请使用其他邮箱"
                )
            elif "parents.phone" in error_msg:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="手机号码已存在，请使用其他手机号"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="数据冲突，请检查邮箱或手机号是否已存在"
                )

        # 其他数据库错误
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建家长失败，请稍后重试"
        )


@router.get("/{parent_id}", response_model=ParentResponse)
async def get_parent(
    parent_id: int,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    根据ID获取家长信息
    
    - **parent_id**: 家长ID
    """
    try:
        parent = service.get_parent(parent_id)
        if not parent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到ID为{parent_id}的家长"
            )
        
        return parent
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取家长信息时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取家长信息失败: {str(e)}"
        )


@router.get("/", response_model=List[ParentResponse])
async def get_parents(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    is_active: bool = Query(True, description="是否只返回激活的家长"),
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    获取家长列表
    
    - **skip**: 跳过的记录数（分页用）
    - **limit**: 返回的记录数（1-1000）
    - **is_active**: 是否只返回激活的家长
    """
    try:
        parents = service.get_parents(skip=skip, limit=limit, is_active=is_active)
        return parents
        
    except Exception as e:
        logger.error(f"获取家长列表时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取家长列表失败: {str(e)}"
        )


@router.put("/{parent_id}", response_model=ParentResponse)
async def update_parent(
    parent_id: int,
    parent_data: ParentUpdate,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    更新家长信息
    
    - **parent_id**: 家长ID
    - 其他字段均为可选，只更新提供的字段
    """
    try:
        parent = service.update_parent(parent_id, parent_data)
        if not parent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到ID为{parent_id}的家长或更新失败"
            )
        
        return parent
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新家长信息时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新家长信息失败: {str(e)}"
        )


@router.delete("/{parent_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_parent(
    parent_id: int,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    软删除家长（将is_active设为False）
    
    - **parent_id**: 家长ID
    """
    try:
        success = service.delete_parent(parent_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到ID为{parent_id}的家长或删除失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除家长时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除家长失败: {str(e)}"
        )


@router.get("/{parent_id}/children", response_model=List[dict])
async def get_parent_children(
    parent_id: int,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    获取家长的所有小孩
    
    - **parent_id**: 家长ID
    """
    try:
        children = service.get_parent_children(parent_id)
        return children
        
    except Exception as e:
        logger.error(f"获取家长小孩信息时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取家长小孩信息失败: {str(e)}"
        )
