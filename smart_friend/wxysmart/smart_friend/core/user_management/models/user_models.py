# SQLAlchemy ORM 模型 - 小孩和家长信息
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime
from enum import Enum as PyEnum
from typing import Optional, List
import logging

Base = declarative_base()

# 设置日志
logger = logging.getLogger(__name__)


class GenderEnum(PyEnum):
    """性别枚举"""
    MALE = "male"
    FEMALE = "female"

class RelationshipEnum(PyEnum):
    """家长与小孩关系枚举"""
    FATHER = "father"
    MOTHER = "mother"
    GRANDFATHER = "grandfather"
    GRANDMOTHER = "grandmother"
    OTHER = "other"


class AcademicLevelEnum(PyEnum):
    """中国教育体系学业等级枚举"""
    PRIMARY_1 = "primary_1"                        # 小学一年级
    PRIMARY_2 = "primary_2"                        # 小学二年级
    PRIMARY_3 = "primary_3"                        # 小学三年级
    PRIMARY_4 = "primary_4"                        # 小学四年级
    PRIMARY_5 = "primary_5"                        # 小学五年级
    PRIMARY_6 = "primary_6"                        # 小学六年级


class BehaviorLevelEnum(PyEnum):
    """行为表现等级枚举"""
    EXCELLENT = "excellent"    # 优秀
    GOOD = "good"             # 良好
    AVERAGE = "average"       # 一般
    NEEDS_IMPROVEMENT = "needs_improvement"  # 需要改进
    CONCERNING = "concerning"  # 令人担忧


class Parent(Base):
    """家长信息表"""
    __tablename__ = "parents"

    # 基本信息
    id = Column(Integer, primary_key=True, autoincrement=True, comment="家长ID")
    name = Column(String(100), nullable=False, comment="家长姓名")
    gender = Column(String(20), nullable=True, comment="性别")
    age = Column(Integer, nullable=True, comment="年龄")
    phone = Column(String(20), nullable=True, unique=True, comment="手机号码")
    email = Column(String(100), nullable=True, unique=True, comment="邮箱地址")
    wechat_id = Column(String(50), nullable=True, comment="微信号")

    # 系统信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    notes = Column(Text, nullable=True, comment="备注信息")

    # 关系
    children = relationship("Child", back_populates="parents", secondary="child_parent_relationships")

    @classmethod
    def create(cls, session, **kwargs) -> Optional['Parent']:
        """创建家长记录"""
        try:
            parent = cls(**kwargs)
            session.add(parent)
            session.flush()  # 获取ID但不提交
            return parent
        except SQLAlchemyError as e:
            logger.error(f"创建家长失败: {e}")
            session.rollback()
            # 对于唯一约束错误，重新抛出异常让上层处理
            if "UNIQUE constraint failed" in str(e):
                raise e
            return None

    @classmethod
    def get_by_id(cls, session, parent_id: int) -> Optional['Parent']:
        """根据ID获取家长"""
        try:
            return session.query(cls).filter(cls.id == parent_id).first()
        except SQLAlchemyError as e:
            logger.error(f"获取家长失败: {e}")
            return None

    @classmethod
    def get_all(cls, session, skip: int = 0, limit: int = 100, is_active: bool = True) -> List['Parent']:
        """获取家长列表"""
        try:
            query = session.query(cls).filter(cls.is_active == is_active)
            return query.offset(skip).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"获取家长列表失败: {e}")
            return []

    def update(self, session, **kwargs) -> bool:
        """更新家长信息"""
        try:
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            self.updated_at = datetime.now()
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"更新家长失败: {e}")
            session.rollback()
            return False

    def soft_delete(self, session) -> bool:
        """软删除家长"""
        try:
            self.is_active = False
            self.updated_at = datetime.now()
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"软删除家长失败: {e}")
            session.rollback()
            return False


class Child(Base):
    """小孩信息表"""
    __tablename__ = "children"

    # 基本信息
    id = Column(Integer, primary_key=True, autoincrement=True, comment="小孩ID")
    name = Column(String(100), nullable=False, comment="小孩姓名")
    nickname = Column(String(50), nullable=True, comment="昵称")
    gender = Column(String(20), nullable=True, comment="性别")
    birth_date = Column(DateTime, nullable=True, comment="出生日期")
    age = Column(Integer, nullable=True, comment="年龄")

    # 学业信息
    academic_level = Column(String(50), nullable=True, comment="学业等级")
    school_name = Column(String(200), nullable=True, comment="学校名称")

    # 身体特征
    height = Column(Float, nullable=True, comment="身高(cm)")
    weight = Column(Float, nullable=True, comment="体重(kg)")

    # 健康状况
    overall_health_status = Column(String(20), default="good", comment="整体健康状况")
    allergies = Column(Text, nullable=True, comment="过敏史")
    chronic_conditions = Column(Text, nullable=True, comment="慢性疾病")
    medications = Column(Text, nullable=True, comment="正在服用的药物")

    # 行为特征
    personality_traits = Column(Text, nullable=True, comment="性格特点")
    learning_style = Column(String(100), nullable=True, comment="学习风格")
    behavior_level = Column(String(20), default="average", comment="行为表现等级")
    attention_span_minutes = Column(Integer, nullable=True, comment="注意力持续时间(分钟)")

    # 兴趣爱好
    hobbies = Column(Text, nullable=True, comment="兴趣爱好")
    favorite_subjects = Column(Text, nullable=True, comment="喜欢的学科")
    disliked_subjects = Column(Text, nullable=True, comment="不喜欢的学科")
    good_at_subjects = Column(Text, nullable=True, comment="擅长的科目")
    weak_at_subjects = Column(Text, nullable=True, comment="不擅长的科目")

    # 系统信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    notes = Column(Text, nullable=True, comment="备注信息")

    # 关系
    parents = relationship("Parent", back_populates="children", secondary="child_parent_relationships")
    academic_records = relationship("ChildAcademicRecord", back_populates="child")

    @classmethod
    def create(cls, session, **kwargs) -> Optional['Child']:
        """创建小孩记录"""
        try:
            child = cls(**kwargs)
            session.add(child)
            session.flush()  # 获取ID但不提交
            return child
        except SQLAlchemyError as e:
            logger.error(f"创建小孩失败: {e}")
            session.rollback()
            return None

    @classmethod
    def get_by_id(cls, session, child_id: int) -> Optional['Child']:
        """根据ID获取小孩"""
        try:
            return session.query(cls).filter(cls.id == child_id).first()
        except SQLAlchemyError as e:
            logger.error(f"获取小孩失败: {e}")
            return None

    @classmethod
    def get_all(cls, session, skip: int = 0, limit: int = 100, is_active: bool = True) -> List['Child']:
        """获取小孩列表"""
        try:
            query = session.query(cls).filter(cls.is_active == is_active)
            return query.offset(skip).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"获取小孩列表失败: {e}")
            return []

    def update(self, session, **kwargs) -> bool:
        """更新小孩信息"""
        try:
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            self.updated_at = datetime.now()
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"更新小孩失败: {e}")
            session.rollback()
            return False

    def soft_delete(self, session) -> bool:
        """软删除小孩"""
        try:
            self.is_active = False
            self.updated_at = datetime.now()
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"软删除小孩失败: {e}")
            session.rollback()
            return False


class ChildParentRelationship(Base):
    """小孩-家长关系表"""
    __tablename__ = "child_parent_relationships"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="关系ID")
    child_id = Column(Integer, ForeignKey("children.id"), nullable=False, comment="小孩ID")
    parent_id = Column(Integer, ForeignKey("parents.id"), nullable=False, comment="家长ID")
    relationship_type = Column(String(20), nullable=False, comment="关系类型")
    is_primary_contact = Column(Boolean, default=False, comment="是否为主要联系人")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    notes = Column(Text, nullable=True, comment="关系备注")

    @classmethod
    def create(cls, session, **kwargs) -> Optional['ChildParentRelationship']:
        """创建关系记录"""
        try:
            relationship = cls(**kwargs)
            session.add(relationship)
            session.flush()  # 获取ID但不提交
            return relationship
        except SQLAlchemyError as e:
            logger.error(f"创建关系失败: {e}")
            session.rollback()
            return None

    @classmethod
    def get_by_id(cls, session, relationship_id: int) -> Optional['ChildParentRelationship']:
        """根据ID获取关系"""
        try:
            return session.query(cls).filter(cls.id == relationship_id).first()
        except SQLAlchemyError as e:
            logger.error(f"获取关系失败: {e}")
            return None

    @classmethod
    def get_all(cls, session, skip: int = 0, limit: int = 100, is_active: bool = True) -> List['ChildParentRelationship']:
        """获取关系列表"""
        try:
            query = session.query(cls).filter(cls.is_active == is_active)
            return query.offset(skip).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"获取关系列表失败: {e}")
            return []

    @classmethod
    def get_by_child_id(cls, session, child_id: int, is_active: bool = True) -> List['ChildParentRelationship']:
        """根据小孩ID获取关系列表"""
        try:
            query = session.query(cls).filter(
                cls.child_id == child_id,
                cls.is_active == is_active
            )
            return query.all()
        except SQLAlchemyError as e:
            logger.error(f"根据小孩ID获取关系失败: {e}")
            return []

    @classmethod
    def get_by_parent_id(cls, session, parent_id: int, is_active: bool = True) -> List['ChildParentRelationship']:
        """根据家长ID获取关系列表"""
        try:
            query = session.query(cls).filter(
                cls.parent_id == parent_id,
                cls.is_active == is_active
            )
            return query.all()
        except SQLAlchemyError as e:
            logger.error(f"根据家长ID获取关系失败: {e}")
            return []

    def update(self, session, **kwargs) -> bool:
        """更新关系信息"""
        try:
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            self.updated_at = datetime.now()
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"更新关系失败: {e}")
            session.rollback()
            return False

    def soft_delete(self, session) -> bool:
        """软删除关系"""
        try:
            self.is_active = False
            self.updated_at = datetime.now()
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"软删除关系失败: {e}")
            session.rollback()
            return False


class ChildAcademicRecord(Base):
    """小孩学业记录表"""
    __tablename__ = "child_academic_records"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="学业记录ID")
    child_id = Column(Integer, ForeignKey("children.id"), nullable=False, comment="小孩ID")

    # 记录基本信息
    academic_year = Column(String(20), nullable=False, comment="学年(如2023-2024)")
    semester = Column(String(20), nullable=False, comment="学期(如上学期/下学期)")
    record_date = Column(DateTime, nullable=False, comment="记录日期")
    record_type = Column(String(50), nullable=False, comment="记录类型(期中/期末/月考/作业/其他)")

    # 学科信息
    subject = Column(String(100), nullable=False, comment="学科名称")
    subject_category = Column(String(50), nullable=True, comment="学科类别(主科/副科)")

    # 成绩信息
    score = Column(Float, nullable=True, comment="得分")
    max_score = Column(Float, nullable=True, comment="满分")
    percentage = Column(Float, nullable=True, comment="得分率(%)")
    grade_level = Column(String(10), nullable=True, comment="等级(A/B/C/D或优/良/中/差)")
    class_rank = Column(Integer, nullable=True, comment="班级排名")
    grade_rank = Column(Integer, nullable=True, comment="年级排名")

    # 系统信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    notes = Column(Text, nullable=True, comment="备注信息")

    # 关系
    child = relationship("Child", back_populates="academic_records")

    @classmethod
    def create(cls, session, **kwargs) -> Optional['ChildAcademicRecord']:
        """创建学业记录"""
        try:
            record = cls(**kwargs)
            session.add(record)
            session.flush()  # 获取ID但不提交
            return record
        except SQLAlchemyError as e:
            logger.error(f"创建学业记录失败: {e}")
            session.rollback()
            return None

    @classmethod
    def get_by_id(cls, session, record_id: int) -> Optional['ChildAcademicRecord']:
        """根据ID获取学业记录"""
        try:
            return session.query(cls).filter(cls.id == record_id).first()
        except SQLAlchemyError as e:
            logger.error(f"获取学业记录失败: {e}")
            return None

    @classmethod
    def get_all(cls, session, skip: int = 0, limit: int = 100, is_active: bool = True) -> List['ChildAcademicRecord']:
        """获取学业记录列表"""
        try:
            query = session.query(cls).filter(cls.is_active == is_active)
            return query.offset(skip).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"获取学业记录列表失败: {e}")
            return []

    @classmethod
    def get_by_child_id(cls, session, child_id: int, subject: Optional[str] = None,
                       academic_year: Optional[str] = None, limit: int = 100,
                       is_active: bool = True) -> List['ChildAcademicRecord']:
        """根据小孩ID获取学业记录"""
        try:
            query = session.query(cls).filter(
                cls.child_id == child_id,
                cls.is_active == is_active
            )

            if subject:
                query = query.filter(cls.subject == subject)
            if academic_year:
                query = query.filter(cls.academic_year == academic_year)

            return query.order_by(cls.record_date.desc()).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"根据小孩ID获取学业记录失败: {e}")
            return []

    def update(self, session, **kwargs) -> bool:
        """更新学业记录"""
        try:
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            self.updated_at = datetime.now()
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"更新学业记录失败: {e}")
            session.rollback()
            return False

    def soft_delete(self, session) -> bool:
        """软删除学业记录"""
        try:
            self.is_active = False
            self.updated_at = datetime.now()
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"软删除学业记录失败: {e}")
            session.rollback()
            return False


class UserPlanAction(Base):
    """用户计划表操作记录表（简化版）"""
    __tablename__ = "user_plan_actions"

    # 基本信息
    id = Column(Integer, primary_key=True, autoincrement=True, comment="操作记录ID")
    table_id = Column(String(50), nullable=False, comment="表ID（每天的表ID不同）")
    operation_time = Column(DateTime, default=func.now(), nullable=False, comment="操作时间")
    user_operation = Column(Text, nullable=False, comment="用户操作（string）")
    user_id = Column(Integer, ForeignKey("children.id"), nullable=False, comment="用户ID（关联小孩表）")

    # 关系
    user = relationship("Child", foreign_keys=[user_id])

    @classmethod
    def create(cls, session, **kwargs) -> Optional['UserPlanAction']:
        """创建用户操作记录"""
        try:
            action = cls(**kwargs)
            session.add(action)
            session.flush()  # 获取ID但不提交
            return action
        except SQLAlchemyError as e:
            logger.error(f"创建用户操作记录失败: {e}")
            session.rollback()
            return None

    @classmethod
    def get_by_id(cls, session, action_id: int) -> Optional['UserPlanAction']:
        """根据ID获取操作记录"""
        try:
            return session.query(cls).filter(cls.id == action_id).first()
        except SQLAlchemyError as e:
            logger.error(f"获取操作记录失败: {e}")
            return None

    @classmethod
    def get_by_user_id(cls, session, user_id: int, limit: int = 100) -> List['UserPlanAction']:
        """根据用户ID获取操作记录列表"""
        try:
            query = session.query(cls).filter(cls.user_id == user_id)
            return query.order_by(cls.operation_time.desc()).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"根据用户ID获取操作记录失败: {e}")
            return []

    @classmethod
    def get_by_table_id(cls, session, table_id: str, limit: int = 100) -> List['UserPlanAction']:
        """根据表ID获取操作记录列表"""
        try:
            query = session.query(cls).filter(cls.table_id == table_id)
            return query.order_by(cls.operation_time.desc()).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"根据表ID获取操作记录失败: {e}")
            return []

    def update(self, session, **kwargs) -> bool:
        """更新操作记录"""
        try:
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"更新操作记录失败: {e}")
            session.rollback()
            return False
