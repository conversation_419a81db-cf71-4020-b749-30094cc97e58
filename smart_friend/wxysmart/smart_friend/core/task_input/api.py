# FastAPI 路由 - 用户任务输入记录管理
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from core.user_management.database.connection import get_db_session_context
from .models.task_input_models import UserTaskInput
from .schemas import (
    UserTaskInputCreate,
    UserTaskInputUpdate,
    UserTaskInputResponse,
    UserTaskInputWithOperatorInfo,
    UserTaskInputListResponse,
    UserTaskInputQuery,
    UserInputStatsResponse,
    OperatorTypeEnum,
    InputMethodEnum
)

router = APIRouter(prefix="/api/v1/task-inputs", tags=["用户任务输入记录"])


def get_db():
    """获取数据库会话"""
    with get_db_session_context() as session:
        yield session


@router.post("/", response_model=UserTaskInputResponse, summary="创建用户任务输入记录")
async def create_task_input(
    input_data: UserTaskInputCreate,
    db: Session = Depends(get_db)
):
    """创建新的用户任务输入记录"""
    try:
        # 创建记录
        record = UserTaskInput.create_input_record(
            session=db,
            operator_type=input_data.operator_type.value,
            operator_id=input_data.operator_id,
            child_id=input_data.child_id,
            content=input_data.content,
            input_method=input_data.input_method.value,
            input_time=input_data.input_time,
            notes=input_data.notes
        )
        
        if not record:
            raise HTTPException(status_code=400, detail="创建记录失败")
        
        db.commit()
        return record
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建记录失败: {str(e)}")


@router.get("/{record_id}", response_model=UserTaskInputWithOperatorInfo, summary="获取单个记录")
async def get_task_input(
    record_id: int,
    db: Session = Depends(get_db)
):
    """根据ID获取用户任务输入记录"""
    record = UserTaskInput.get_by_id(db, record_id)
    if not record:
        raise HTTPException(status_code=404, detail="记录不存在")
    
    # 获取操作者信息
    operator_info = record.get_operator_info(db)
    
    response_data = record.to_dict()
    response_data['operator_info'] = operator_info
    
    return response_data


@router.get("/", response_model=UserTaskInputListResponse, summary="获取记录列表")
async def list_task_inputs(
    child_id: Optional[int] = Query(None, description="孩子ID"),
    operator_type: Optional[OperatorTypeEnum] = Query(None, description="操作用户类型"),
    operator_id: Optional[int] = Query(None, description="操作用户ID"),
    input_method: Optional[InputMethodEnum] = Query(None, description="输入方式"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_db)
):
    """获取用户任务输入记录列表"""
    try:
        records = []
        
        if child_id and start_time and end_time:
            # 按时间范围查询
            records = UserTaskInput.get_by_time_range(db, child_id, start_time, end_time)
        elif child_id and input_method:
            # 按输入方式查询
            records = UserTaskInput.get_by_input_method(db, child_id, input_method.value, limit)
        elif operator_type and operator_id:
            # 按操作者查询
            records = UserTaskInput.get_by_operator(db, operator_type.value, operator_id, limit)
        elif child_id:
            # 按孩子ID查询
            records = UserTaskInput.get_by_child_id(db, child_id, limit)
        else:
            raise HTTPException(status_code=400, detail="请提供至少一个查询条件")
        
        # 应用偏移量和限制
        total = len(records)
        records = records[offset:offset + limit]
        
        return {
            "total": total,
            "items": records
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")


@router.put("/{record_id}", response_model=UserTaskInputResponse, summary="更新记录")
async def update_task_input(
    record_id: int,
    update_data: UserTaskInputUpdate,
    db: Session = Depends(get_db)
):
    """更新用户任务输入记录"""
    try:
        record = UserTaskInput.get_by_id(db, record_id)
        if not record:
            raise HTTPException(status_code=404, detail="记录不存在")
        
        # 更新记录
        update_fields = update_data.dict(exclude_unset=True)
        success = record.update_record(db, **update_fields)
        
        if not success:
            raise HTTPException(status_code=400, detail="更新失败")
        
        db.commit()
        return record
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")


@router.delete("/{record_id}", summary="删除记录")
async def delete_task_input(
    record_id: int,
    db: Session = Depends(get_db)
):
    """软删除用户任务输入记录"""
    try:
        record = UserTaskInput.get_by_id(db, record_id)
        if not record:
            raise HTTPException(status_code=404, detail="记录不存在")
        
        success = record.soft_delete(db)
        if not success:
            raise HTTPException(status_code=400, detail="删除失败")
        
        db.commit()
        return {"message": "记录已删除"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.get("/stats/child/{child_id}", response_model=UserInputStatsResponse, summary="获取孩子输入统计")
async def get_child_input_stats(
    child_id: int,
    db: Session = Depends(get_db)
):
    """获取指定孩子的输入统计信息"""
    try:
        # 获取所有记录
        records = UserTaskInput.get_by_child_id(db, child_id, limit=10000)
        
        if not records:
            raise HTTPException(status_code=404, detail="未找到相关记录")
        
        # 统计输入方式
        method_stats = {}
        for record in records:
            method = record.input_method
            method_stats[method] = method_stats.get(method, 0) + 1
        
        total_inputs = len(records)
        input_methods = []
        
        method_names = {1: "文本", 2: "语音", 3: "图片"}
        
        for method, count in method_stats.items():
            input_methods.append({
                "input_method": method,
                "input_method_name": method_names.get(method, "未知"),
                "count": count,
                "percentage": round((count / total_inputs) * 100, 2)
            })
        
        # 获取孩子信息
        from core.user_management.models.user_models import Child
        child = db.query(Child).filter(Child.id == child_id).first()
        child_name = child.name if child else "未知"
        
        return {
            "child_id": child_id,
            "child_name": child_name,
            "total_inputs": total_inputs,
            "input_methods": input_methods,
            "latest_input_time": records[0].input_time if records else None,
            "first_input_time": records[-1].input_time if records else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
