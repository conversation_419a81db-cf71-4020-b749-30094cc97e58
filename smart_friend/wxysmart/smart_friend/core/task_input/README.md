# 用户任务输入记录表 (UserTaskInput)

## 📋 概述

用户任务输入记录表用于记录用户（孩子或家长）输入的任务信息，支持多种输入方式（文本、语音、图片），并能够准确关联操作者和任务归属的孩子。

**核心特性**：
- 🔗 **跨表关联**：通过 `operator_type` 实现同一ID字段关联不同用户表
- 📝 **字符串存储**：采用简洁的文本格式存储用户输入内容
- 🎯 **多输入方式**：支持文本、语音、图片三种输入方式
- ⏰ **时序分析**：记录详细的输入时间用于行为分析

## 🗂️ 表结构

### 字段说明

| 字段名 | 类型 | 必选 | 默认值 | 说明 |
|--------|------|------|--------|------|
| id | INTEGER | 是 | 自增 | 自增主键，唯一标识记录，无业务含义 |
| operator_type | INTEGER | 是 | - | 区分操作用户类型（1=孩子，2=家长） |
| operator_id | INTEGER | 是 | - | 关联用户表的自增ID（孩子/家长表通用，靠operator_type区分表） |
| child_id | INTEGER | 是 | - | 明确任务归属的孩子（即使家长操作，也需关联孩子） |
| content | TEXT | 是 | - | 存储用户输入的原始内容（文本格式） |
| input_method | INTEGER | 是 | - | 输入方式枚举（1=文本，2=语音，3=图片） |
| input_time | DATETIME | 是 | CURRENT_TIMESTAMP | 记录输入时间，用于时序分析 |
| is_active | BOOLEAN | 否 | TRUE | 是否激活，用于软删除 |
| created_at | DATETIME | 否 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | 否 | CURRENT_TIMESTAMP | 更新时间 |
| notes | TEXT | 否 | NULL | 备注信息 |

### 🔗 表间关联逻辑

**核心设计思想**：通过 `operator_type` 字段实现"同一ID字段跨表关联"

| 操作者类型 | operator_type | operator_id 关联表 | 说明 |
|-----------|---------------|-------------------|------|
| 孩子操作 | 1 | `children.id` | 孩子自己输入任务 |
| 家长操作 | 2 | `parents.id` | 家长为孩子输入任务 |

**关联规则**：
- 无论谁操作，`child_id` 都明确指向任务归属的孩子
- `operator_id` 根据 `operator_type` 关联不同的用户表
- 这种设计避免了创建多个外键字段的复杂性

## 📊 枚举类型

### OperatorTypeEnum (操作用户类型)
```python
class OperatorTypeEnum(PyEnum):
    CHILD = 1   # 孩子操作
    PARENT = 2  # 家长操作
```

### InputMethodEnum (输入方式)
```python
class InputMethodEnum(PyEnum):
    TEXT = 1    # 文本输入
    VOICE = 2   # 语音输入
    IMAGE = 3   # 图片输入
```

## 🚀 使用方法

### 1. 创建记录

#### 文本输入示例
```python
from core.task_input.models.task_input_models import UserTaskInput, OperatorTypeEnum, InputMethodEnum
from core.user_management.database.connection import get_db_session_context

# 孩子输入文本任务
content = "今天的数学作业是第3章的练习题1-10题，预计需要60分钟完成，难度中等"

with get_db_session_context() as session:
    record = UserTaskInput.create_input_record(
        session=session,
        operator_type=OperatorTypeEnum.CHILD.value,  # 孩子操作
        operator_id=1,      # 孩子ID
        child_id=1,         # 任务归属孩子ID
        content=content,    # 文本内容
        input_method=InputMethodEnum.TEXT.value,
        notes="孩子主动输入的作业任务"
    )

    if record:
        session.commit()
        print(f"✅ 创建成功，记录ID: {record.id}")
```

#### 语音输入示例
```python
# 家长语音输入任务
content = "小明，记得完成英语单词背诵，今天要背20个新单词。语音文件：voice_20241210.wav"

record = UserTaskInput.create_input_record(
    session=session,
    operator_type=OperatorTypeEnum.PARENT.value,  # 家长操作
    operator_id=1,      # 家长ID
    child_id=1,         # 任务归属孩子ID
    content=content,    # 语音转文字内容
    input_method=InputMethodEnum.VOICE.value,
    notes="家长语音提醒"
)
```

#### 图片输入示例
```python
# 孩子拍照上传作业
content = "第五章 分数运算练习，1. 计算下列分数的和...。图片文件：homework_page1.jpg, homework_page2.jpg，共2页，科目：数学"

record = UserTaskInput.create_input_record(
    session=session,
    operator_type=OperatorTypeEnum.CHILD.value,   # 孩子操作
    operator_id=1,      # 孩子ID
    child_id=1,         # 任务归属孩子ID
    content=content,    # OCR识别的文本内容
    input_method=InputMethodEnum.IMAGE.value,
    notes="拍照上传作业"
)
```

### 2. 查询记录

#### 基础查询
```python
# 根据孩子ID查询最近的输入记录
records = UserTaskInput.get_by_child_id(session, child_id=1, limit=10)
print(f"找到 {len(records)} 条记录")

# 根据操作者查询（查看某个孩子的所有输入）
records = UserTaskInput.get_by_operator(
    session,
    operator_type=OperatorTypeEnum.CHILD.value,
    operator_id=1,
    limit=20
)

# 根据输入方式查询（查看语音输入记录）
voice_records = UserTaskInput.get_by_input_method(
    session,
    child_id=1,
    input_method=InputMethodEnum.VOICE.value,
    limit=10
)
```

#### 高级查询
```python
from datetime import datetime, timedelta

# 根据时间范围查询（查看最近7天的输入）
start_time = datetime.now() - timedelta(days=7)
end_time = datetime.now()
recent_records = UserTaskInput.get_by_time_range(
    session,
    child_id=1,
    start_time=start_time,
    end_time=end_time
)

# 获取记录详情（包含操作者信息）
record = UserTaskInput.get_by_id(session, record_id=1)
if record:
    operator_info = record.get_operator_info(session)
    print(f"操作者: {operator_info.get('name')} ({operator_info.get('type')})")
    print(f"内容: {record.content}")
```

### 3. 更新记录

```python
# 更新记录备注
record = UserTaskInput.get_by_id(session, record_id=1)
if record:
    success = record.update_record(session, notes="已处理完成")
    if success:
        session.commit()
        print("✅ 更新成功")

# 更新内容
success = record.update_record(session, content="更新后的任务内容")
```

### 4. 软删除记录

```python
# 软删除记录（设置is_active=False）
record = UserTaskInput.get_by_id(session, record_id=1)
if record:
    success = record.soft_delete(session)
    if success:
        session.commit()
        print("✅ 记录已删除")
```

## 📝 Content字段格式说明

**设计理念**：采用简洁的文本格式存储用户输入内容，便于存储、搜索和处理。

### 文本输入格式
```text
"今天的数学作业是第3章的练习题1-10题，预计需要60分钟完成，难度中等"
```
**特点**：直接存储用户输入的文本内容

### 语音输入格式
```text
"小明，记得完成英语单词背诵，今天要背20个新单词。语音文件：/uploads/voice/20241210_143022.wav，时长15.5秒，识别置信度95%"
```
**特点**：包含语音转文字内容和相关元数据

### 图片输入格式
```text
"第五章 分数运算练习，1. 计算下列分数的和...。图片文件：homework_page1.jpg, homework_page2.jpg，共2页，科目：数学，OCR识别完成"
```
**特点**：包含OCR识别的文本内容和图片信息

### 内容格式建议

#### 📋 任务描述类
```text
"[科目] [任务类型]：[具体内容]，[时间要求]，[难度等级]"
```
**示例**：`"数学 作业：完成第3章练习题1-10题，明天上交，难度中等"`

#### 🎤 语音转录类
```text
"[转录内容]。语音文件：[文件路径]，时长[X]秒"
```
**示例**：`"记得完成英语背诵。语音文件：voice_001.wav，时长12秒"`

#### 📷 图片识别类
```text
"[OCR内容]。图片文件：[文件列表]，共[X]页，科目：[科目名]"
```
**示例**：`"分数运算练习题。图片文件：math_hw_p1.jpg，共1页，科目：数学"`

## 🌐 API端点

### 基础CRUD操作
| 方法 | 端点 | 说明 | 参数 |
|------|------|------|------|
| POST | `/api/v1/task-inputs/` | 创建记录 | UserTaskInputCreate |
| GET | `/api/v1/task-inputs/{record_id}` | 获取单个记录 | record_id |
| GET | `/api/v1/task-inputs/` | 获取记录列表 | 查询参数 |
| PUT | `/api/v1/task-inputs/{record_id}` | 更新记录 | record_id, UserTaskInputUpdate |
| DELETE | `/api/v1/task-inputs/{record_id}` | 软删除记录 | record_id |

### 查询参数
- `child_id`: 孩子ID
- `operator_type`: 操作者类型（1=孩子，2=家长）
- `operator_id`: 操作者ID
- `input_method`: 输入方式（1=文本，2=语音，3=图片）
- `start_time`: 开始时间
- `end_time`: 结束时间
- `limit`: 返回记录数限制（默认100）
- `offset`: 偏移量（默认0）

### 统计分析
| 方法 | 端点 | 说明 |
|------|------|------|
| GET | `/api/v1/task-inputs/stats/child/{child_id}` | 获取孩子输入统计 |

## 🔧 数据库管理

### 表创建和迁移
```bash
# 创建用户任务输入表
python migrations/add_user_task_inputs_table.py migrate

# 回滚表（如需要）
python migrations/add_user_task_inputs_table.py rollback
```

### 数据库索引
表创建时自动添加以下索引以提高查询性能：

| 索引名 | 字段 | 用途 |
|--------|------|------|
| `idx_user_task_inputs_child_id` | child_id | 按孩子查询 |
| `idx_user_task_inputs_operator` | operator_type, operator_id | 按操作者查询 |
| `idx_user_task_inputs_input_time` | input_time | 按时间查询 |
| `idx_user_task_inputs_input_method` | input_method | 按输入方式查询 |
| `idx_user_task_inputs_active` | is_active | 软删除过滤 |

## 📚 使用示例

### 完整示例代码
```bash
# 运行完整示例
python examples/task_input_example.py

# 运行综合测试
python examples/test_all_updates.py
```

### 快速开始
```python
# 1. 导入必要模块
from core.task_input.models.task_input_models import UserTaskInput, OperatorTypeEnum, InputMethodEnum
from core.user_management.database.connection import get_db_session_context

# 2. 创建记录
with get_db_session_context() as session:
    record = UserTaskInput.create_input_record(
        session=session,
        operator_type=OperatorTypeEnum.CHILD.value,
        operator_id=1,
        child_id=1,
        content="今天的作业内容",
        input_method=InputMethodEnum.TEXT.value
    )
    session.commit()

# 3. 查询记录
records = UserTaskInput.get_by_child_id(session, child_id=1)
```

## ⚠️ 注意事项

### 数据完整性
1. **用户关联**: 确保 `operator_id` 对应的用户在相应表中存在
2. **孩子关联**: 确保 `child_id` 在 `children` 表中存在
3. **枚举值**: 使用预定义的枚举值，避免无效数据

### 内容格式
1. **文本格式**: `content` 字段现在使用文本格式，不再是JSON
2. **内容长度**: 建议控制单条内容长度，避免过长文本
3. **特殊字符**: 注意处理特殊字符和换行符

### 性能优化
1. **分页查询**: 大量数据查询时使用 `limit` 和 `offset` 参数
2. **索引利用**: 查询时尽量使用已建立索引的字段
3. **软删除**: 查询时注意 `is_active=True` 的过滤条件

### 时间处理
1. **时区**: 所有时间字段使用UTC时间
2. **显示**: 前端显示时需要转换为本地时间
3. **查询**: 时间范围查询时注意时区一致性

## 🚀 扩展建议

1. **全文搜索**: 为 `content` 字段添加全文搜索功能
2. **内容分析**: 基于内容进行关键词提取和分类
3. **行为分析**: 基于输入时间和方式进行用户行为分析
4. **智能推荐**: 根据历史输入推荐任务模板
5. **批量操作**: 支持批量导入和导出功能
