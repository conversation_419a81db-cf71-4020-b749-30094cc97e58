# Pydantic模型 - 用户任务输入记录请求/响应格式
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class OperatorTypeEnum(int, Enum):
    """操作用户类型枚举"""
    CHILD = 1    # 孩子操作
    PARENT = 2   # 家长操作


class InputMethodEnum(int, Enum):
    """输入方式枚举"""
    TEXT = 1     # 文本输入
    VOICE = 2    # 语音输入
    IMAGE = 3    # 图片输入


class UserTaskInputCreate(BaseModel):
    """创建用户任务输入记录请求模型"""
    operator_type: OperatorTypeEnum = Field(..., description="操作用户类型（1=孩子，2=家长）")
    operator_id: int = Field(..., gt=0, description="关联用户表的自增ID")
    child_id: int = Field(..., gt=0, description="任务归属的孩子ID")
    content: str = Field(..., description="用户输入的原始内容")
    input_method: InputMethodEnum = Field(..., description="输入方式（1=文本，2=语音，3=图片）")
    input_time: Optional[datetime] = Field(None, description="输入时间，不提供则使用当前时间")
    notes: Optional[str] = Field(None, max_length=1000, description="备注信息")

    @validator('content')
    def validate_content(cls, v):
        """验证内容不能为空"""
        if not v:
            raise ValueError('内容不能为空')
        return v

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class UserTaskInputUpdate(BaseModel):
    """更新用户任务输入记录请求模型"""
    content: Optional[str] = Field(None, description="用户输入的原始内容")
    notes: Optional[str] = Field(None, max_length=1000, description="备注信息")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class UserTaskInputResponse(BaseModel):
    """用户任务输入记录响应模型"""
    id: int
    operator_type: int
    operator_id: int
    child_id: int
    content: str
    input_method: int
    input_time: datetime
    is_active: bool
    created_at: datetime
    updated_at: datetime
    notes: Optional[str] = None

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class UserTaskInputWithOperatorInfo(UserTaskInputResponse):
    """包含操作者信息的用户任务输入记录响应模型"""
    operator_info: Optional[Dict[str, Any]] = Field(None, description="操作者信息")


class UserTaskInputListResponse(BaseModel):
    """用户任务输入记录列表响应模型"""
    total: int = Field(..., description="总记录数")
    items: List[UserTaskInputResponse] = Field(..., description="记录列表")


class UserTaskInputQuery(BaseModel):
    """用户任务输入记录查询参数模型"""
    child_id: Optional[int] = Field(None, gt=0, description="孩子ID")
    operator_type: Optional[OperatorTypeEnum] = Field(None, description="操作用户类型")
    operator_id: Optional[int] = Field(None, gt=0, description="操作用户ID")
    input_method: Optional[InputMethodEnum] = Field(None, description="输入方式")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    limit: Optional[int] = Field(100, ge=1, le=1000, description="返回记录数限制")
    offset: Optional[int] = Field(0, ge=0, description="偏移量")

    @validator('end_time')
    def validate_time_range(cls, v, values):
        """验证时间范围"""
        if v and 'start_time' in values and values['start_time']:
            if v <= values['start_time']:
                raise ValueError('结束时间必须大于开始时间')
        return v

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class InputMethodStatsResponse(BaseModel):
    """输入方式统计响应模型"""
    input_method: int
    input_method_name: str
    count: int
    percentage: float


class UserInputStatsResponse(BaseModel):
    """用户输入统计响应模型"""
    child_id: int
    child_name: str
    total_inputs: int
    input_methods: List[InputMethodStatsResponse]
    latest_input_time: Optional[datetime] = None
    first_input_time: Optional[datetime] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
