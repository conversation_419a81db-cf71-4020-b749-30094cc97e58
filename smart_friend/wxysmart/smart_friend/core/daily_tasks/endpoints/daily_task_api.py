# 每日任务API端点
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
import logging
import json

from ..schemas import (
    DailyTaskCreate, DailyTaskCreateSimple, DailyTaskUpdate, DailyTaskResponse,
    TaskStatusUpdate, TaskStatistics, YesterdayTaskSummary,
    DeleteTaskRequest, BatchTaskCreate, TaskQueryParams, TaskItemCreate, TaskItemResponse
)
from service.daily_task_service import DailyTaskService
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()


def get_daily_task_service() -> DailyTaskService:
    """获取每日任务服务依赖"""
    return DailyTaskService()


class TaskPlanConfirmRequest(BaseModel):
    """任务计划确认请求模型"""
    child_id: int
    task_plan: List[Dict[str, Any]]  # 前端任务计划数据


class SubTaskOperationRequest(BaseModel):
    """子任务操作请求模型"""
    task_name: str
    sub_task_content: str
    sub_task_source: str = "前端操作"
    time_slot: Optional[str] = None


@router.post("/tasks/simple", response_model=DailyTaskResponse)
async def create_task_simple(
    task_data: DailyTaskCreateSimple,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    创建每日任务（简化版，只需三个必填字段）

    - **child_id**: 学生ID
    - **task_name**: 任务名称
    - **description**: 任务描述（可选）
    """
    try:
        # 创建任务
        result = service.create_task(
            child_id=task_data.child_id,
            task_name=task_data.task_name,
            description=task_data.description or ""
        )

        if not result:
            raise HTTPException(
                status_code=500,
                detail="创建任务失败"
            )

        return DailyTaskResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建任务时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"创建任务失败: {str(e)}"
        )


@router.post("/tasks", response_model=DailyTaskResponse)
async def create_task(
    task_data: DailyTaskCreate,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    创建每日任务（完整版）

    - **child_id**: 学生ID
    - **task_name**: 任务名称
    - **description**: 任务描述（可选）
    - 其他字段均为可选
    """
    try:
        # 转换为字典格式
        task_dict = task_data.model_dump(exclude_unset=True)

        # 提取必填字段
        child_id = task_dict.pop('child_id')
        task_name = task_dict.pop('task_name')
        description = task_dict.pop('description', "")

        # 处理子任务
        if 'sub_tasks' in task_dict:
            task_dict['sub_tasks'] = [
                sub_task.model_dump() if hasattr(sub_task, 'model_dump') else sub_task
                for sub_task in task_dict['sub_tasks']
            ]

        # 创建任务
        result = service.create_task(child_id, task_name, description, **task_dict)

        if not result:
            raise HTTPException(
                status_code=500,
                detail="创建任务失败"
            )

        return DailyTaskResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建任务时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"创建任务失败: {str(e)}"
        )


@router.get("/tasks/{task_id}", response_model=DailyTaskResponse)
async def get_task(
    task_id: int,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    根据ID获取任务详情
    
    - **task_id**: 任务ID
    """
    try:
        result = service.get_task_by_id(task_id)
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"未找到ID为 {task_id} 的任务"
            )
        
        return DailyTaskResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取任务失败: {str(e)}"
        )


@router.get("/tasks/child/{child_id}/date/{task_date}", response_model=List[DailyTaskResponse])
async def get_tasks_by_date(
    child_id: int,
    task_date: date,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    获取指定学生指定日期的任务
    
    - **child_id**: 学生ID
    - **task_date**: 任务日期（格式：YYYY-MM-DD）
    """
    try:
        results = service.get_tasks_by_child_and_date(child_id, task_date)
        return [DailyTaskResponse(**result) for result in results]
        
    except Exception as e:
        logger.error(f"获取任务列表时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取任务列表失败: {str(e)}"
        )


@router.get("/tasks/child/{child_id}", response_model=List[DailyTaskResponse])
async def get_tasks_by_range(
    child_id: int,
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    subject: Optional[str] = Query(None, description="学科筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    获取指定学生指定日期范围的任务
    
    - **child_id**: 学生ID
    - **start_date**: 开始日期（可选，默认7天前）
    - **end_date**: 结束日期（可选，默认今天）
    - **subject**: 学科筛选（可选）
    - **status**: 状态筛选（可选：pending, in_progress, completed, cancelled, overdue）
    """
    try:
        # 设置默认日期范围
        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - timedelta(days=6)
        
        results = service.get_tasks_by_date_range(child_id, start_date, end_date, subject, status)
        return [DailyTaskResponse(**result) for result in results]
        
    except Exception as e:
        logger.error(f"获取任务列表时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取任务列表失败: {str(e)}"
        )


@router.get("/tasks/child/{child_id}/yesterday", response_model=List[YesterdayTaskSummary])
async def get_yesterday_tasks(
    child_id: int,
    subject: Optional[str] = Query(None, description="学科筛选"),
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    获取昨日任务情况（专门为prompt4服务）
    
    - **child_id**: 学生ID
    - **subject**: 学科筛选（可选）
    """
    try:
        results = service.get_yesterday_tasks(child_id, subject)
        return [YesterdayTaskSummary(**result) for result in results]
        
    except Exception as e:
        logger.error(f"获取昨日任务时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取昨日任务失败: {str(e)}"
        )


@router.put("/tasks/{task_id}", response_model=dict)
async def update_task(
    task_id: int,
    update_data: DailyTaskUpdate,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    更新任务信息
    
    - **task_id**: 任务ID
    - 其他字段为可选更新字段
    """
    try:
        # 转换为字典格式，排除未设置的字段
        update_dict = update_data.model_dump(exclude_unset=True)
        
        # 处理子任务
        if 'sub_tasks' in update_dict:
            update_dict['sub_tasks'] = [
                sub_task.model_dump() if hasattr(sub_task, 'model_dump') else sub_task
                for sub_task in update_dict['sub_tasks']
            ]
        
        success = service.update_task(task_id, update_dict)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail=f"未找到ID为 {task_id} 的任务或更新失败"
            )
        
        return {"message": "任务更新成功", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新任务时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"更新任务失败: {str(e)}"
        )


@router.patch("/tasks/{task_id}/status", response_model=dict)
async def update_task_status(
    task_id: int,
    status_data: TaskStatusUpdate,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    更新任务状态
    
    - **task_id**: 任务ID
    - **status**: 新状态
    - **completion_percentage**: 完成百分比（可选）
    """
    try:
        success = service.update_task_status(
            task_id, 
            status_data.status.value, 
            status_data.completion_percentage
        )
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail=f"未找到ID为 {task_id} 的任务或更新失败"
            )
        
        return {"message": "任务状态更新成功", "task_id": task_id, "status": status_data.status.value}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新任务状态时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"更新任务状态失败: {str(e)}"
        )


@router.delete("/tasks/{task_id}", response_model=dict)
async def delete_task(
    task_id: int,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    删除任务（软删除）
    
    - **task_id**: 任务ID
    """
    try:
        success = service.delete_task(task_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail=f"未找到ID为 {task_id} 的任务或删除失败"
            )
        
        return {"message": "任务删除成功", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除任务失败: {str(e)}"
        )


@router.get("/tasks/child/{child_id}/statistics", response_model=TaskStatistics)
async def get_task_statistics(
    child_id: int,
    days: int = Query(7, ge=1, le=365, description="统计天数(1-365天)"),
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    获取任务统计信息
    
    - **child_id**: 学生ID
    - **days**: 统计天数（1-365天，默认7天）
    """
    try:
        result = service.get_task_statistics(child_id, days)
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"未找到学生ID {child_id} 的任务数据"
            )
        
        return TaskStatistics(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务统计时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取任务统计失败: {str(e)}"
        )


@router.post("/tasks/confirm-plan", response_model=dict)
async def confirm_task_plan(
    request: TaskPlanConfirmRequest,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    确认任务计划并存储到数据库

    将前端个性化任务计划展示的任务相关信息存入数据库的task_items中，
    同时将生成计划使用的prompt4中的任务is_activate列设为0

    - **child_id**: 学生ID
    - **task_plan**: 前端任务计划数据
    """
    try:
        logger.info(f"开始确认任务计划，学生ID: {request.child_id}")

        # 调用服务层处理任务计划确认
        result = await service.confirm_task_plan(request.child_id, request.task_plan)

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "任务计划确认失败")
            )

        return {
            "success": True,
            "message": "任务计划确认成功",
            "data": result.get("data", {})
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认任务计划时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"确认任务计划失败: {str(e)}"
        )


@router.post("/tasks/{task_name}/subtasks", response_model=dict)
async def add_subtask(
    task_name: str,
    request: SubTaskOperationRequest,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    添加子任务

    - **task_name**: 主任务名称
    - **sub_task_content**: 子任务内容
    - **sub_task_source**: 子任务来源
    - **time_slot**: 时间段（可选）
    """
    try:
        logger.info(f"添加子任务到任务: {task_name}")

        result = await service.add_subtask(
            task_name=task_name,
            sub_task_content=request.sub_task_content,
            sub_task_source=request.sub_task_source,
            time_slot=request.time_slot
        )

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "添加子任务失败")
            )

        return {
            "success": True,
            "message": "子任务添加成功",
            "data": result.get("data", {})
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加子任务时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"添加子任务失败: {str(e)}"
        )


@router.delete("/tasks/{task_name}/subtasks", response_model=dict)
async def remove_subtask(
    task_name: str,
    request: SubTaskOperationRequest,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    删除子任务（软删除）

    - **task_name**: 主任务名称
    - **sub_task_content**: 子任务内容
    """
    try:
        logger.info(f"删除子任务: {request.sub_task_content} 从任务: {task_name}")

        result = await service.remove_subtask(
            task_name=task_name,
            sub_task_content=request.sub_task_content
        )

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "删除子任务失败")
            )

        return {
            "success": True,
            "message": "子任务删除成功",
            "data": result.get("data", {})
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除子任务时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除子任务失败: {str(e)}"
        )
