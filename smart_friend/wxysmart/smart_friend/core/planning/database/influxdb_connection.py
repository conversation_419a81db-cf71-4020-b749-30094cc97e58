# InfluxDB连接管理
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime, timezone
from influxdb_client import InfluxDBC<PERSON>, Point, WritePrecision
from influxdb_client.client.write_api import SYNCHRONOUS
from influxdb_client.client.exceptions import InfluxDBError

from config.config import settings

logger = logging.getLogger(__name__)


class InfluxDBManager:
    """InfluxDB连接管理器"""
    
    def __init__(self):
        self.client: Optional[InfluxDBClient] = None
        self.write_api = None
        self.query_api = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化InfluxDB客户端"""
        if not settings.INFLUXDB_ENABLED:
            logger.warning("InfluxDB未启用")
            return
        
        try:
            self.client = InfluxDBClient(
                url=settings.INFLUXDB_URL,
                token=settings.INFLUXDB_TOKEN,
                org=settings.INFLUXDB_ORG
            )
            self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
            self.query_api = self.client.query_api()
            logger.info("InfluxDB客户端初始化成功")
        except Exception as e:
            logger.error(f"InfluxDB客户端初始化失败: {e}")
            self.client = None
    
    def check_connection(self) -> bool:
        """检查InfluxDB连接状态"""
        if not self.client:
            return False
        
        try:
            # 尝试ping服务器
            health = self.client.health()
            return health.status == "pass"
        except Exception as e:
            logger.error(f"InfluxDB连接检查失败: {e}")
            return False
    
    def write_point(self, measurement: str, tags: Dict[str, str], 
                   fields: Dict[str, Any], timestamp: Optional[datetime] = None) -> bool:
        """写入单个数据点"""
        if not self.client or not self.write_api:
            logger.error("InfluxDB客户端未初始化")
            return False
        
        try:
            point = Point(measurement)
            
            # 添加标签
            for key, value in tags.items():
                point = point.tag(key, value)
            
            # 添加字段
            for key, value in fields.items():
                point = point.field(key, value)
            
            # 设置时间戳
            if timestamp:
                point = point.time(timestamp, WritePrecision.NS)
            
            self.write_api.write(bucket=settings.INFLUXDB_BUCKET, record=point)
            logger.debug(f"成功写入数据点到measurement: {measurement}")
            return True
            
        except InfluxDBError as e:
            logger.error(f"写入InfluxDB失败: {e}")
            return False
        except Exception as e:
            logger.error(f"写入数据点时发生未知错误: {e}")
            return False
    
    def write_points(self, points: List[Point]) -> bool:
        """批量写入数据点"""
        if not self.client or not self.write_api:
            logger.error("InfluxDB客户端未初始化")
            return False
        
        try:
            self.write_api.write(bucket=settings.INFLUXDB_BUCKET, record=points)
            logger.debug(f"成功批量写入{len(points)}个数据点")
            return True
            
        except InfluxDBError as e:
            logger.error(f"批量写入InfluxDB失败: {e}")
            return False
        except Exception as e:
            logger.error(f"批量写入数据点时发生未知错误: {e}")
            return False
    
    def query_data(self, flux_query: str) -> List[Dict[str, Any]]:
        """执行Flux查询"""
        if not self.client or not self.query_api:
            logger.error("InfluxDB客户端未初始化")
            return []
        
        try:
            result = self.query_api.query(flux_query, org=settings.INFLUXDB_ORG)
            
            data = []
            for table in result:
                for record in table.records:
                    data.append({
                        'time': record.get_time(),
                        'measurement': record.get_measurement(),
                        'field': record.get_field(),
                        'value': record.get_value(),
                        'tags': record.values
                    })
            
            logger.debug(f"查询返回{len(data)}条记录")
            return data
            
        except InfluxDBError as e:
            logger.error(f"查询InfluxDB失败: {e}")
            return []
        except Exception as e:
            logger.error(f"查询数据时发生未知错误: {e}")
            return []
    
    def delete_data(self, start: datetime, stop: datetime, 
                   predicate: str = "") -> bool:
        """删除指定时间范围的数据"""
        if not self.client:
            logger.error("InfluxDB客户端未初始化")
            return False
        
        try:
            delete_api = self.client.delete_api()
            delete_api.delete(
                start=start,
                stop=stop,
                predicate=predicate,
                bucket=settings.INFLUXDB_BUCKET,
                org=settings.INFLUXDB_ORG
            )
            logger.info(f"成功删除时间范围 {start} 到 {stop} 的数据")
            return True
            
        except InfluxDBError as e:
            logger.error(f"删除InfluxDB数据失败: {e}")
            return False
        except Exception as e:
            logger.error(f"删除数据时发生未知错误: {e}")
            return False
    
    def close(self):
        """关闭InfluxDB连接"""
        if self.client:
            self.client.close()
            logger.info("InfluxDB连接已关闭")


# 全局InfluxDB管理器实例
influxdb_manager = InfluxDBManager()


def get_influxdb_manager() -> InfluxDBManager:
    """获取InfluxDB管理器实例"""
    return influxdb_manager
