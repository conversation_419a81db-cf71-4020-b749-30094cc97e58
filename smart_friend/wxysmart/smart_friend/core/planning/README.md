# 测试规划模块 (Test Planning Module)

## 📋 概述

这个模块包含了学伴1.0项目中与每日学习数据存储、管理和分析相关的所有功能。基于InfluxDB时序数据库，实现了完整的每日学习情况数据格式，支持专注度、作业完成情况、时间管理、积分奖励、学科强弱势等多维度数据分析。

## 📁 文件结构

```
ai_child/api/test_planning/
├── database/                    # 数据库相关
│   ├── __init__.py
│   ├── influxdb_connection.py   # InfluxDB连接管理器
│   └── setup_influxdb.py        # InfluxDB初始化脚本
├── services/                    # 业务服务层
│   ├── __init__.py
│   └── daily_learning_service.py # 每日学习数据服务
├── endpoints/                   # API端点
│   ├── __init__.py
│   ├── daily_learning.py        # 每日学习API
│   ├── feedback.py
│   ├── planner.py
│   ├── produce_summary.py
│   └── tasks.py
├── tests/                       # 测试文件
│   ├── __init__.py
│   ├── test_influxdb_integration.py  # 集成测试
│   └── test_enhanced_learning_api.py # API测试
├── docs/                        # 文档
│   ├── InfluxDB_API_使用说明.md
│   ├── InfluxDB数据格式说明.md
│   └── 每日学习情况数据格式说明.md
├── __init__.py
├── schemas.py                   # 数据模型定义
├── service.py                   # 服务入口
├── run_tests.py                 # 统一测试启动脚本
└── README.md                    # 本文档
```

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的依赖：
```bash
pip install influxdb-client fastapi uvicorn pydantic
```

### 2. 配置InfluxDB

在 `ai_child/config.py` 中配置InfluxDB连接信息：
```python
INFLUXDB_ENABLED = True
INFLUXDB_URL = "http://**************:8086"
INFLUXDB_TOKEN = "your_token_here"
INFLUXDB_ORG = "your_org"
INFLUXDB_BUCKET = "daily_learning"
```

### 3. 初始化数据库

```bash
cd /root/projects/smart_friend
python ai_child/api/test_planning/database/setup_influxdb.py
```

### 4. 启动服务器

```bash
cd /root/projects/smart_friend
uvicorn ai_child.main:app --reload
```

### 5. 运行测试

```bash
cd /root/projects/smart_friend
python ai_child/api/test_planning/run_tests.py
```

## 📊 数据格式

### 核心数据结构

基于每日学习情况图片，支持以下数据维度：

#### 专注度相关
- `concentration_level`: 专注程度 (1-5)
- `internal_interruptions`: 内部中断次数（分心）
- `desk_leaving_times`: 离开课桌次数

#### 作业完成情况
- `homework_completion_rate`: 作业完成率 (%)
- `completion_rate`: 总体完成率 (%)
- `accuracy_rate`: 正确率 (%)

#### 完成耗时
- `total_duration_minutes`: 总耗时（分钟）
- `subject_duration_minutes`: 单科耗时（分钟）
- `task_duration_minutes`: 任务耗时（分钟）

#### 时间管理
- `is_ahead_schedule`: 是否提前完成
- `is_behind_schedule`: 是否延迟完成
- `schedule_deviation_minutes`: 与计划偏差（分钟）
- `schedule_summary`: 时间管理总结

#### 积分奖励
- `points_earned`: 获得积分
- `bonus_points`: 奖励积分

#### 学科强弱势
- `is_weak_subject`: 是否薄弱学科
- `is_strong_subject`: 是否强势学科
- `subject_performance_level`: 学科表现等级 (1-5)

## 🔧 API使用

### 创建学习记录

```bash
curl -X POST "http://localhost:8000/api/v1/daily-learning/learning-records" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "subject": "数学",
    "activity_type": "homework",
    "concentration_level": 4,
    "internal_interruptions": 2,
    "homework_completion_rate": 95.0,
    "total_duration_minutes": 120.0,
    "is_strong_subject": true,
    "points_earned": 85,
    "schedule_summary": "比计划延迟15分钟完成"
  }'
```

### 获取学习记录

```bash
curl "http://localhost:8000/api/v1/daily-learning/learning-records/1?subject=数学&limit=10"
```

### 获取学习统计

```bash
curl "http://localhost:8000/api/v1/daily-learning/learning-statistics/1?days=7"
```

## 🧪 测试

### 集成测试

测试数据库连接和基本功能：
```bash
python ai_child/api/test_planning/tests/test_influxdb_integration.py
```

### API测试

测试完整的API功能：
```bash
python ai_child/api/test_planning/tests/test_enhanced_learning_api.py
```

### 统一测试

运行所有测试：
```bash
python ai_child/api/test_planning/run_tests.py
```

## 📈 数据分析

### 支持的分析维度

1. **专注度分析**: 分心频率趋势、离开课桌行为模式
2. **时间管理分析**: 各学科耗时分布、提前/延迟完成模式
3. **学科表现分析**: 强势/薄弱学科识别、跨学科能力对比
4. **积分激励分析**: 积分获得趋势、奖励积分触发条件
5. **学习行为分析**: 求助频率模式、提问积极性

### Flux查询示例

获取专注度趋势：
```flux
from(bucket: "daily_learning")
  |> range(start: -30d)
  |> filter(fn: (r) => r._measurement == "daily_learning")
  |> filter(fn: (r) => r.child_id == "1")
  |> filter(fn: (r) => r._field == "concentration_level")
  |> aggregateWindow(every: 1d, fn: mean)
```

## 🔍 故障排除

### 常见问题

1. **InfluxDB连接失败**
   - 检查URL、Token、组织名称配置
   - 确认InfluxDB服务运行状态
   - 验证网络连接

2. **Bucket不存在**
   - 运行 `setup_influxdb.py` 创建bucket
   - 检查Token权限设置

3. **API测试失败**
   - 确认服务器已启动
   - 检查端口8000是否被占用
   - 查看服务器日志

### 日志查看

服务器日志会显示详细的错误信息，包括：
- InfluxDB连接状态
- 数据写入/查询结果
- API请求处理情况

## 📚 相关文档

- [每日学习情况数据格式说明](docs/每日学习情况数据格式说明.md)
- [InfluxDB API使用说明](docs/InfluxDB_API_使用说明.md)
- [InfluxDB数据格式说明](docs/InfluxDB数据格式说明.md)

## 🤝 贡献

1. 遵循现有代码风格
2. 添加适当的测试
3. 更新相关文档
4. 确保所有测试通过

## 📄 许可证

本项目遵循学伴1.0项目的许可证协议。
