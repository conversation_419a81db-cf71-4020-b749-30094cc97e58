# 每日学习数据InfluxDB操作接口
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional
from datetime import datetime, timezone, timedelta
import logging

from core.planning.schemas import (
    DailyLearningCreate,
    DailyLearningResponse,
    LearningStatistics,
    DeleteLearningRequest
)
from service.daily_learning_service import DailyLearningService
from core.planning.database.influxdb_connection import get_influxdb_manager

logger = logging.getLogger(__name__)

router = APIRouter()


def get_daily_learning_service() -> DailyLearningService:
    """获取每日学习服务实例"""
    return DailyLearningService()


@router.post("/learning-records", response_model=dict)
async def create_learning_record(
    learning_data: DailyLearningCreate,
    service: DailyLearningService = Depends(get_daily_learning_service)
):
    """
    创建每日学习记录
    - **child_id**: 孩子ID
    - **subject**: 学科名称
    - **activity_type**: 活动类型（lesson, exercise, test, project, game）
    - **study_duration_minutes**: 学习时长（分钟）
    - **completion_rate**: 完成率（0-100）
    - **accuracy_rate**: 正确率（0-100）
    - **score**: 得分
    - **enjoyment_rating**: 享受程度（1-5）
    - **notes**: 学习笔记
    """
    logger.info(f"📚 API请求: 添加学习记录 - 学生ID: {learning_data.child_id}, 学科: {learning_data.subject}, 活动类型: {learning_data.activity_type}")
    logger.debug(f"学习记录详情: 时长{learning_data.study_duration_minutes}分钟, 完成率{learning_data.completion_rate}%, 正确率{learning_data.accuracy_rate}%")

    try:
        # 检查InfluxDB连接
        logger.debug("检查 InfluxDB 连接状态")
        if not service.influxdb.check_connection():
            logger.error("InfluxDB 服务不可用")
            raise HTTPException(
                status_code=503,
                detail="InfluxDB服务不可用，请检查连接配置"
            )

        # 转换为字典格式
        learning_dict = learning_data.model_dump(exclude_unset=True)
        logger.debug("开始调用服务层添加学习记录")

        # 添加学习记录
        success = service.add_learning_record(
            child_id=learning_data.child_id,
            subject=learning_data.subject,
            learning_data=learning_dict
        )

        if not success:
            logger.error(f"服务层添加学习记录失败 - 学生ID: {learning_data.child_id}")
            raise HTTPException(
                status_code=500,
                detail="添加学习记录失败"
            )

        response = {
            "success": True,
            "message": f"成功添加孩子{learning_data.child_id}的{learning_data.subject}学习记录",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        logger.info(f"✅ API响应: 添加学习记录成功 - 学生ID: {learning_data.child_id}, 学科: {learning_data.subject}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API异常: 创建学习记录失败 - 学生ID: {learning_data.child_id}, 错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"创建学习记录失败: {str(e)}"
        )


@router.get("/learning-records/{child_id}", response_model=List[DailyLearningResponse])
async def get_learning_records(
    child_id: int,
    subject: Optional[str] = Query(None, description="学科筛选"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数限制"),
    service: DailyLearningService = Depends(get_daily_learning_service)
):
    """
    获取孩子的学习记录
    - **child_id**: 孩子ID
    - **subject**: 学科筛选（可选）
    - **start_date**: 开始日期（可选，默认30天前）
    - **end_date**: 结束日期（可选，默认当前时间）
    - **limit**: 返回记录数限制（1-1000）
    """
    logger.info(f"📖 API请求: 获取学习记录 - 学生ID: {child_id}, 学科: {subject}, 限制: {limit}")
    logger.debug(f"查询参数 - 开始日期: {start_date}, 结束日期: {end_date}")

    try:
        # 检查InfluxDB连接
        if not service.influxdb.check_connection():
            raise HTTPException(
                status_code=503,
                detail="InfluxDB服务不可用，请检查连接配置"
            )
        
        # 获取学习记录
        records = service.get_learning_records(
            child_id=child_id,
            subject=subject,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )
        
        # 转换为响应格式
        response_records = []
        for record in records:
            response_record = DailyLearningResponse(**record)
            response_records.append(response_record)
        
        return response_records
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学习记录时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取学习记录失败: {str(e)}"
        )


@router.delete("/learning-records", response_model=dict)
async def delete_learning_records(
    delete_request: DeleteLearningRequest,
    service: DailyLearningService = Depends(get_daily_learning_service)
):
    """
    删除指定时间范围的学习记录
    
    - **child_id**: 孩子ID
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    - **subject**: 学科筛选（可选）
    """
    try:
        # 检查InfluxDB连接
        if not service.influxdb.check_connection():
            raise HTTPException(
                status_code=503,
                detail="InfluxDB服务不可用，请检查连接配置"
            )
        
        # 验证日期范围
        if delete_request.start_date >= delete_request.end_date:
            raise HTTPException(
                status_code=400,
                detail="开始日期必须早于结束日期"
            )
        
        # 删除学习记录
        success = service.delete_learning_records(
            child_id=delete_request.child_id,
            start_date=delete_request.start_date,
            end_date=delete_request.end_date,
            subject=delete_request.subject
        )
        
        if not success:
            raise HTTPException(
                status_code=500,
                detail="删除学习记录失败"
            )
        
        return {
            "success": True,
            "message": f"成功删除孩子{delete_request.child_id}在指定时间范围内的学习记录",
            "deleted_period": {
                "start_date": delete_request.start_date.isoformat(),
                "end_date": delete_request.end_date.isoformat(),
                "subject": delete_request.subject
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除学习记录时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除学习记录失败: {str(e)}"
        )


@router.get("/learning-statistics/{child_id}", response_model=LearningStatistics)
async def get_learning_statistics(
    child_id: int,
    days: int = Query(7, ge=1, le=365, description="统计天数"),
    subject: Optional[str] = Query(None, description="学科筛选"),
    service: DailyLearningService = Depends(get_daily_learning_service)
):
    """
    获取孩子的学习统计信息
    
    - **child_id**: 孩子ID
    - **days**: 统计天数（1-365天）
    - **subject**: 学科筛选（可选）
    """
    try:
        # 检查InfluxDB连接
        if not service.influxdb.check_connection():
            raise HTTPException(
                status_code=503,
                detail="InfluxDB服务不可用，请检查连接配置"
            )
        
        # 获取统计信息
        statistics = service.get_learning_statistics(
            child_id=child_id,
            days=days,
            subject=subject
        )
        
        if not statistics:
            raise HTTPException(
                status_code=404,
                detail="未找到学习统计数据"
            )
        
        return LearningStatistics(**statistics)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学习统计信息时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取学习统计信息失败: {str(e)}"
        )


@router.get("/health", response_model=dict)
async def health_check(
    service: DailyLearningService = Depends(get_daily_learning_service)
):
    """
    InfluxDB健康检查
    """
    try:
        is_connected = service.influxdb.check_connection()
        
        return {
            "influxdb_status": "healthy" if is_connected else "unhealthy",
            "connected": is_connected,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"健康检查时发生错误: {e}")
        return {
            "influxdb_status": "error",
            "connected": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
