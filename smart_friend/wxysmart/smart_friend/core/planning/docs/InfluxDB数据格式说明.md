# InfluxDB数据格式详细说明

## 概述

我们的系统将每日学习数据存储在InfluxDB时间序列数据库中。每个学习记录作为一个数据点（Point）存储，包含测量名称、标签、字段和时间戳。

## 数据结构

### 1. Measurement (测量名称)
```
measurement = "daily_learning"
```
所有学习记录都使用统一的测量名称 `daily_learning`。

### 2. Tags (标签) - 用于索引和分组

标签是字符串类型，用于快速查询和分组：

#### 必需标签：
- **`child_id`**: 孩子ID (字符串)
- **`subject`**: 学科名称 (如: "数学", "语文", "英语")
- **`date`**: 记录日期 (格式: "YYYY-MM-DD")

#### 可选标签：
- **`activity_type`**: 活动类型
  - `"lesson"` - 课程学习
  - `"exercise"` - 练习
  - `"test"` - 测试
  - `"project"` - 项目
  - `"game"` - 游戏
- **`difficulty_level`**: 难度等级 ("1" 到 "5")

### 3. Fields (字段) - 实际数值数据

字段包含具体的学习指标数据：

#### 学习时间和完成度 (浮点数)：
- **`study_duration_minutes`**: 学习时长（分钟）
- **`completion_rate`**: 完成率（0-100）
- **`accuracy_rate`**: 正确率（0-100）

#### 评分数据 (浮点数)：
- **`score`**: 得分
- **`max_score`**: 满分

#### 情感和态度评价 (整数 1-5)：
- **`enjoyment_rating`**: 享受程度
- **`difficulty_rating`**: 难度感受
- **`concentration_level`**: 专注程度

#### 学习行为指标 (整数)：
- **`questions_asked`**: 提问次数
- **`help_requests`**: 求助次数
- **`breaks_taken`**: 休息次数

#### 文本信息 (字符串)：
- **`notes`**: 学习笔记
- **`feedback`**: 反馈信息

### 4. Timestamp (时间戳)
- 使用UTC时间
- 纳秒精度
- 如果未提供，使用当前时间

## 实际存储示例

### 示例1：数学练习记录
```
measurement: daily_learning
tags:
  child_id: "1"
  subject: "数学"
  date: "2025-06-11"
  activity_type: "exercise"
  difficulty_level: "3"
fields:
  study_duration_minutes: 45.0
  completion_rate: 85.5
  accuracy_rate: 92.0
  score: 88.0
  max_score: 100.0
  enjoyment_rating: 4
  difficulty_rating: 3
  concentration_level: 4
  questions_asked: 2
  help_requests: 1
  breaks_taken: 1
  notes: "今天的数学练习做得不错，对分数运算有了更好的理解"
  feedback: "继续保持，可以尝试更难的题目"
timestamp: 2025-06-11T10:30:00.000000000Z
```

### 示例2：语文课程记录
```
measurement: daily_learning
tags:
  child_id: "2"
  subject: "语文"
  date: "2025-06-11"
  activity_type: "lesson"
  difficulty_level: "2"
fields:
  study_duration_minutes: 60.0
  completion_rate: 100.0
  enjoyment_rating: 5
  concentration_level: 4
  notes: "学习了古诗词，很有趣"
timestamp: 2025-06-11T14:00:00.000000000Z
```

## Line Protocol格式

InfluxDB内部使用Line Protocol格式存储数据：

```
daily_learning,child_id=1,subject=数学,date=2025-06-11,activity_type=exercise,difficulty_level=3 study_duration_minutes=45.0,completion_rate=85.5,accuracy_rate=92.0,score=88.0,max_score=100.0,enjoyment_rating=4i,difficulty_rating=3i,concentration_level=4i,questions_asked=2i,help_requests=1i,breaks_taken=1i,notes="今天的数学练习做得不错",feedback="继续保持" 1686484200000000000
```

## 查询示例

### 1. 获取特定孩子的所有记录
```flux
from(bucket: "daily_learning")
  |> range(start: -30d)
  |> filter(fn: (r) => r._measurement == "daily_learning")
  |> filter(fn: (r) => r.child_id == "1")
```

### 2. 获取数学学科的记录
```flux
from(bucket: "daily_learning")
  |> range(start: -7d)
  |> filter(fn: (r) => r._measurement == "daily_learning")
  |> filter(fn: (r) => r.child_id == "1")
  |> filter(fn: (r) => r.subject == "数学")
```

### 3. 计算平均学习时长
```flux
from(bucket: "daily_learning")
  |> range(start: -7d)
  |> filter(fn: (r) => r._measurement == "daily_learning")
  |> filter(fn: (r) => r._field == "study_duration_minutes")
  |> filter(fn: (r) => r.child_id == "1")
  |> mean()
```

## 数据类型说明

| 字段类型 | InfluxDB类型 | 说明 |
|---------|-------------|------|
| Tags | String | 用于索引，支持快速查询和分组 |
| Float Fields | Float64 | 数值数据，支持数学运算 |
| Integer Fields | Int64 | 整数数据，如评分、次数 |
| String Fields | String | 文本数据，如笔记、反馈 |
| Timestamp | Time | 纳秒精度的UTC时间戳 |

## 索引和性能

### 高效查询的标签设计：
1. **child_id** - 最常用的查询维度
2. **subject** - 按学科分组查询
3. **date** - 按日期范围查询
4. **activity_type** - 按活动类型筛选

### 查询性能优化：
- 使用标签进行筛选（而非字段）
- 限制时间范围
- 使用适当的limit限制返回数据量

## 数据保留策略

当前配置：
- **保留期限**: 无限期（可根据需要调整）
- **精度**: 纳秒级时间戳
- **压缩**: InfluxDB自动压缩

## 备份和恢复

建议定期备份InfluxDB数据：
```bash
# 导出数据
influx backup /path/to/backup

# 恢复数据
influx restore /path/to/backup
```

## 注意事项

1. **标签值不要过多**: 避免高基数标签（如用户ID作为标签值过多）
2. **字段类型一致性**: 同一字段在不同记录中应保持相同类型
3. **时间戳精度**: 使用UTC时间，避免时区问题
4. **数据验证**: 写入前验证数据格式和范围
5. **批量写入**: 对于大量数据，使用批量写入提高性能
