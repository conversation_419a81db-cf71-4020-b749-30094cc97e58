# 每日学习情况数据格式说明

## 概述

基于提供的每日学习情况结构图，我们设计了完整的InfluxDB数据格式，涵盖专注度、作业完成情况、时间管理、积分奖励、学科强弱势等多个维度。

## 数据结构图

```
每日学习情况
├── 专注度
│   ├── 内部中断 → 分心
│   └── 内部中断 → 离开课桌
├── 作业完成率
├── 完成耗时
│   ├── 总耗时
│   ├── 单科耗时
│   └── 任务耗时
├── 作业提前/延迟总结
├── 积分奖励
├── 薄弱学科
└── 强势学科
```

## InfluxDB存储格式

### Measurement
```
measurement = "daily_learning"
```

### Tags (标签)
```json
{
  "child_id": "1",
  "subject": "数学",
  "date": "2025-06-11",
  "activity_type": "homework",
  "difficulty_level": "3"
}
```

### Fields (字段)

#### 1. 专注度相关
```json
{
  "concentration_level": 4,           // 专注程度 (1-5)
  "internal_interruptions": 2,       // 内部中断次数（分心）
  "desk_leaving_times": 1            // 离开课桌次数
}
```

#### 2. 作业完成情况
```json
{
  "homework_completion_rate": 95.0,  // 作业完成率 (%)
  "completion_rate": 90.0,           // 总体完成率 (%)
  "accuracy_rate": 88.0              // 正确率 (%)
}
```

#### 3. 完成耗时
```json
{
  "total_duration_minutes": 120.0,    // 总耗时（分钟）
  "subject_duration_minutes": 45.0,   // 单科耗时（分钟）
  "task_duration_minutes": 30.0,      // 任务耗时（分钟）
  "study_duration_minutes": 45.0      // 学习时长（分钟）
}
```

#### 4. 时间管理
```json
{
  "is_ahead_schedule": false,          // 是否提前完成
  "is_behind_schedule": true,          // 是否延迟完成
  "schedule_deviation_minutes": 15.0   // 与计划偏差（分钟）
}
```

#### 5. 积分奖励
```json
{
  "points_earned": 85,    // 获得积分
  "bonus_points": 10      // 奖励积分
}
```

#### 6. 学科强弱势
```json
{
  "is_weak_subject": false,           // 是否薄弱学科
  "is_strong_subject": true,          // 是否强势学科
  "subject_performance_level": 4      // 学科表现等级 (1-5)
}
```

#### 7. 评分数据
```json
{
  "score": 88.0,
  "max_score": 100.0
}
```

#### 8. 情感态度
```json
{
  "enjoyment_rating": 4,      // 享受程度 (1-5)
  "difficulty_rating": 3,     // 难度感受 (1-5)
  "motivation_level": 4       // 学习动机 (1-5)
}
```

#### 9. 学习行为
```json
{
  "questions_asked": 3,       // 提问次数
  "help_requests": 1,         // 求助次数
  "breaks_taken": 2           // 休息次数
}
```

#### 10. 文本信息
```json
{
  "notes": "今天数学作业完成得不错，但有些分心",
  "feedback": "数学是强势学科，继续保持",
  "schedule_summary": "比计划延迟15分钟完成，主要原因是中途分心2次"
}
```

## 完整示例记录

### 数学作业记录
```json
{
  "measurement": "daily_learning",
  "tags": {
    "child_id": "1",
    "subject": "数学",
    "date": "2025-06-11",
    "activity_type": "homework",
    "difficulty_level": "3"
  },
  "fields": {
    // 专注度
    "concentration_level": 4,
    "internal_interruptions": 2,
    "desk_leaving_times": 1,
    
    // 完成情况
    "homework_completion_rate": 95.0,
    "completion_rate": 90.0,
    "accuracy_rate": 88.0,
    
    // 耗时
    "total_duration_minutes": 120.0,
    "subject_duration_minutes": 45.0,
    "task_duration_minutes": 30.0,
    
    // 时间管理
    "is_ahead_schedule": false,
    "is_behind_schedule": true,
    "schedule_deviation_minutes": 15.0,
    
    // 积分
    "points_earned": 85,
    "bonus_points": 10,
    
    // 学科强弱势
    "is_weak_subject": false,
    "is_strong_subject": true,
    "subject_performance_level": 4,
    
    // 评分
    "score": 88.0,
    "max_score": 100.0,
    
    // 情感态度
    "enjoyment_rating": 4,
    "difficulty_rating": 3,
    "motivation_level": 4,
    
    // 行为
    "questions_asked": 3,
    "help_requests": 1,
    "breaks_taken": 2,
    
    // 文本
    "notes": "今天数学作业完成得不错，但有些分心，需要提高专注度",
    "feedback": "数学是强势学科，继续保持。建议减少分心次数",
    "schedule_summary": "比计划延迟15分钟完成，主要原因是中途分心2次，离开课桌1次"
  },
  "timestamp": "2025-06-11T10:30:00.000000000Z"
}
```

## API使用示例

### 创建学习记录
```bash
curl -X POST "http://localhost:8000/api/v1/daily-learning/learning-records" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "subject": "数学",
    "activity_type": "homework",
    "concentration_level": 4,
    "internal_interruptions": 2,
    "desk_leaving_times": 1,
    "homework_completion_rate": 95.0,
    "total_duration_minutes": 120.0,
    "subject_duration_minutes": 45.0,
    "is_behind_schedule": true,
    "schedule_deviation_minutes": 15.0,
    "points_earned": 85,
    "is_strong_subject": true,
    "subject_performance_level": 4,
    "schedule_summary": "比计划延迟15分钟完成，主要原因是中途分心2次"
  }'
```

### 获取学习记录
```bash
curl "http://localhost:8000/api/v1/daily-learning/learning-records/1?subject=数学&limit=10"
```

## 数据分析维度

### 1. 专注度分析
- 分心频率趋势
- 离开课桌行为模式
- 专注度与学习效果关联

### 2. 时间管理分析
- 各学科耗时分布
- 提前/延迟完成模式
- 时间规划能力评估

### 3. 学科表现分析
- 强势/薄弱学科识别
- 学科表现等级变化
- 跨学科能力对比

### 4. 积分激励分析
- 积分获得趋势
- 奖励积分触发条件
- 激励效果评估

### 5. 学习行为分析
- 求助频率模式
- 提问积极性
- 休息频率与效果

## 查询示例

### 获取专注度趋势
```flux
from(bucket: "daily_learning")
  |> range(start: -30d)
  |> filter(fn: (r) => r._measurement == "daily_learning")
  |> filter(fn: (r) => r.child_id == "1")
  |> filter(fn: (r) => r._field == "concentration_level" or r._field == "internal_interruptions")
  |> aggregateWindow(every: 1d, fn: mean)
```

### 分析学科强弱势
```flux
from(bucket: "daily_learning")
  |> range(start: -7d)
  |> filter(fn: (r) => r._measurement == "daily_learning")
  |> filter(fn: (r) => r._field == "subject_performance_level")
  |> group(columns: ["subject"])
  |> mean()
```

### 时间管理效率分析
```flux
from(bucket: "daily_learning")
  |> range(start: -30d)
  |> filter(fn: (r) => r._measurement == "daily_learning")
  |> filter(fn: (r) => r._field == "schedule_deviation_minutes")
  |> aggregateWindow(every: 1d, fn: mean)
```

这种数据格式设计完全基于您提供的每日学习情况结构图，能够全面记录和分析孩子的学习行为、时间管理、专注度等多个维度，为个性化教育提供数据支撑。
