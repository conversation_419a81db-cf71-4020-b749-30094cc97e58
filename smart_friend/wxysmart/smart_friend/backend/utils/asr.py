"""
ASR - 语音识别管理模块

本模块提供了一个高性能、可靠的语音识别管理类，用于实时流式麦克风语音转写。
设计用于 AI Agent 系统调用，支持双向流式处理，实现边录音边识别的自然交互体验。

主要功能特性:
1. 实时流式语音识别 - 边录音边识别，低延迟响应
2. 智能静音检测 - 自动识别用户停顿
3. 多线程异步处理 - 高性能且不阻塞主线程
4. 自动错误恢复 - 网络异常自动重连
5. 状态监控与可视化 - 提供识别状态和结果展示

使用示例:
    from utils.asr import ASRManager

    # 创建ASR管理器实例
    asr_manager = ASRManager()

    # 定义回调函数处理识别结果
    def on_recognition_result(text, is_final):
        if is_final:
            print(f"最终识别结果: {text}")
        else:
            print(f"实时识别中: {text}")

    # 启动语音识别
    asr_manager.start_recognition(on_recognition_result)

    # ... 应用逻辑 ...

    # 停止语音识别
    asr_manager.stop_recognition()
"""

import sounddevice as sd
import numpy as np
import time
import re
import queue
import jieba
import jieba.posseg as posseg
from collections import defaultdict
import threading
import websocket
import json
import uuid
from typing import Callable, Optional, Dict, List, Union, Any, Tuple

# 导入豆包ASR客户端
try:
    from utils.doubao_asr_client import DoubaoASRClient
except ImportError as e:
    print(f"导入 DoubaoASRClient 失败: {e}")
    DoubaoASRClient = None

# 火山引擎ASR客户端 (如果有的话)
try:
    from utils.volcano_asr_client import VolcanoASRClient
except ImportError:
    VolcanoASRClient = None

# 导入配置管理、日志管理和TTS模块
try:
    from utils.config_manager import get_config
except ImportError as e:
    print(f"导入 config_manager 失败: {e}")
    def get_config(path=None):
        return {}

try:
    from utils.logging_manager import get_logger
except ImportError as e:
    print(f"导入 logging_manager 失败: {e}")
    import logging
    def get_logger(name):
        return logging.getLogger(name)

# 获取日志记录器
logger = get_logger("ASRManager")


class ASRManager:
    """
    语音识别管理器 (ASRManager)

    提供高级语音识别功能的管理类 ，专为 AI Agent 系统设计，支持实时流式语音识别。
    该实现基于火山引擎(豆包)的语音识别API，通过WebSocket实现双向流式处理。

    核心特性:
    ---------
    1. 流式处理: 支持音频数据的实时流式输入和识别结果的实时流式输出
    2. 智能静音检测: 自动识别用户说话暂停，优化交互体验
    3. 多线程安全: 所有关键操作都使用线程锁保护，支持多线程环境
    4. 错误自愈: 网络异常、服务中断等情况下自动重试和恢复
    5. 状态监控: 提供完整的状态查询和结果获取接口

    技术规格:
    ---------
    - 采样率: 默认16000Hz (可配置)
    - 音频格式: 16位PCM
    - 通道数: 单通道 (可配置)
    - 流式响应: 实时识别结果和最终结果分别回调

    使用场景:
    ---------
    - 人机语音交互系统
    - 语音助手和问答系统
    - 会议记录和转写
    - 实时字幕生成
    - 语音控制应用

    架构设计:
    ---------
    - 音频采集层: 通过sounddevice库实时捕获麦克风音频
    - 数据处理层: 进行音频预处理、静音检测等
    - 网络通信层: 维护与ASR服务的WebSocket连接
    - 结果处理层: 处理识别结果、添加标点、实施回调
    - 状态管理层: 管理运行状态、错误恢复、资源释放

    线程模型:
    ---------
    - 主线程: 处理API调用，维护状态
    - 音频处理线程: 处理音频数据，发送到ASR服务
    - WebSocket监听线程: 接收ASR服务返回的结果

    错误处理:
    ---------
    - 网络连接错误: 自动重连，最多3次尝试
    - 服务响应超时: 自动重启连接
    - 内部处理错误: 记录日志，维持稳定性

    属性:
    -----
        is_recognizing (bool): 指示是否正在进行语音识别
        last_result (str): 最近一次的最终识别结果
        current_stream (str): 当前正在进行的流式识别的实时结果
        results (list): 所有最终识别结果的历史记录
    """

    def __init__(self,
                 sample_rate: int = 16000,
                 channels: int = 1,
                 chunk_duration: float = 0.6,
                 silence_threshold: float = 0.5,  # 提高静音阈值，使系统对声音更不敏感
                 silence_duration: float = 2.0,
                 config_path: str = None,
                 timeout: float = 30.0):
        """
        初始化语音识别管理器

        参数:
            sample_rate (int): 音频采样率，默认16000Hz
            channels (int): 音频通道数，默认1（单声道）
            chunk_duration (float): 每个音频块的持续时间（秒），默认0.6秒
            silence_threshold (float): 静音检测阈值，默认0.01
            silence_duration (float): 静音持续时间阈值（秒），默认2.0秒
            config_path (str): 配置文件路径，默认None（使用系统默认配置）
            timeout (float): 网络操作超时时间（秒），默认30.0秒
        """
        # 音频参数设置
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_duration = chunk_duration
        self.chunk_size = int(sample_rate * chunk_duration)
        self.silence_threshold = silence_threshold
        self.silence_duration = silence_duration
        self.timeout = timeout

        # 加载配置
        self.config = get_config(config_path)

        # 状态变量
        self.is_recognizing = False      # 是否正在进行语音识别
        self.is_paused = False           # 是否暂停识别
        self.stream = None               # 音频流对象
        self.audio_queue = queue.Queue()  # 音频数据队列
        self.results = []                # 历史识别结果
        self.last_result = ""            # 最近一次最终识别结果
        self.current_stream = ""         # 当前正在进行的识别的实时结果
        self.error_count = 0             # 错误计数
        self.max_retries = 3             # 最大重试次数
        self.retry_delay = 2.0           # 重试延迟（秒）
        self.session_id = str(uuid.uuid4())  # 当前识别会话ID

        # 多线程控制
        self.audio_processing_thread = None  # 音频处理线程
        self.ws_monitoring_thread = None     # WebSocket监控线程
        self.lock = threading.RLock()        # 可重入线程锁
        self.recognition_callback = None     # 识别结果回调函数

        # WebSocket连接状态
        self.ws_client = None            # WebSocket客户端对象
        self.ws_connected = False        # WebSocket连接状态

        # 初始化ASR客户端 (支持火山引擎/豆包)
        self._initialize_asr_client()

        # 调试状态
        self.debug_mode = False          # 调试模式开关

    def _initialize_asr_client(self):
        """初始化ASR客户端，支持火山引擎或豆包ASR服务"""
        try:
            # 尝试从配置获取API密钥信息
            asr_app_key = self.config.get('API', 'asr_app_key', '')
            asr_access_key = self.config.get('API', 'asr_access_key', '')
            asr_service_type = self.config.get(
                'API', 'asr_service_type', 'volcano').lower()

            if not asr_app_key or not asr_access_key:
                logger.warning("配置文件中缺少ASR API密钥，将使用模拟功能")
                self.asr_client = None
                self.use_mock = True
                return

            # 根据服务类型初始化不同的客户端
            if asr_service_type == 'volcano' and VolcanoASRClient:
                self.asr_client = VolcanoASRClient(
                    app_key=asr_app_key,
                    access_key=asr_access_key,
                    sample_rate=self.sample_rate,
                    channels=self.channels
                )
                self.use_mock = False
                logger.info("火山引擎ASR客户端初始化成功")
            else:
                # 默认使用豆包ASR
                self.asr_client = DoubaoASRClient(
                    app_key=asr_app_key,
                    access_key=asr_access_key,
                    sample_rate=self.sample_rate,
                    channels=self.channels
                )
                self.use_mock = False
                logger.info("豆包ASR客户端初始化成功")
        except Exception as e:
            logger.warning(f"ASR客户端初始化失败: {str(e)}，将使用模拟功能")
            self.asr_client = None
            self.use_mock = True

    def start_recognition(self, callback: Optional[Callable[[str, bool], None]] = None) -> bool:
        """
        启动语音识别，开始从麦克风采集音频并进行实时识别

        参数:
            callback (Callable[[str, bool], None], optional): 识别结果回调函数
                                                           第一个参数是识别文本
                                                           第二个参数表示是否为最终结果

        返回:
            bool: 是否成功启动识别

        异常:
            RuntimeError: 启动失败时抛出
        """
        with self.lock:
            if self.is_recognizing:
                logger.warning("已经在进行语音识别，请先停止当前识别")
                return False

            self.recognition_callback = callback
            self.is_recognizing = False  # 先设置为False，连接成功后再设置为True
            self.is_paused = False
            self.error_count = 0
            self.session_id = str(uuid.uuid4())  # 生成新的会话ID

            # 清空队列
            while not self.audio_queue.empty():
                try:
                    self.audio_queue.get_nowait()
                except queue.Empty:
                    break

            try:
                # 1. 设置状态
                logger.info(f"开始启动语音识别服务，会话ID: {self.session_id}")

                # 2. 初始化ASR服务连接
                if not self.use_mock:
                    # 连接ASR服务
                    if not self.asr_client.is_connected:
                        logger.info("尝试连接ASR服务...")
                        connect_result = self.asr_client.connect()
                        if not connect_result:
                            logger.warning("无法连接到ASR服务，切换到模拟模式")
                            self.use_mock = True
                        else:
                            logger.info("ASR服务连接成功")

                    # 启动语音识别
                    if not self.use_mock:
                        def on_asr_result(text, is_final):
                            """内部回调处理ASR结果"""
                            if self.debug_mode:
                                logger.debug(
                                    f"收到ASR结果: {text}, is_final={is_final}")

                            if is_final:
                                if text:
                                    # 添加标点符号处理
                                    processed_text = self.process_text_with_punctuation(
                                        text)
                                    logger.info(f"最终识别结果: {processed_text}")

                                    with self.lock:
                                        self.last_result = processed_text
                                        self.results.append(processed_text)
                                        self.current_stream = ""  # 清空实时结果

                                    # 调用外部回调函数
                                    if self.recognition_callback is not None:
                                        try:
                                            self.recognition_callback(
                                                processed_text, True)
                                        except Exception as e:
                                            logger.error(
                                                f"调用识别结果回调函数时出错: {str(e)}")
                            else:
                                # 实时流识别结果
                                with self.lock:
                                    self.current_stream = text

                                # 调用外部回调函数，标记为非最终结果
                                if self.recognition_callback is not None and text:
                                    try:
                                        self.recognition_callback(text, False)
                                    except Exception as e:
                                        logger.error(
                                            f"调用流式识别回调函数时出错: {str(e)}")

                        # 启动ASR识别
                        logger.info("启动ASR识别...")
                        if not self.asr_client.start_recognition(callback=on_asr_result):
                            logger.warning("启动语音识别失败，切换到模拟模式")
                            self.use_mock = True
                        else:
                            logger.info("ASR识别启动成功")

                # 3. 启动音频流
                logger.info("启动麦克风音频流...")
                self.stream = sd.InputStream(
                    samplerate=self.sample_rate,
                    channels=self.channels,
                    callback=self._audio_callback,
                    blocksize=self.chunk_size
                )
                self.stream.start()

                # 4. 启动处理线程
                self.audio_processing_thread = threading.Thread(
                    target=self._process_audio_stream,
                    daemon=True,
                    name="ASR-AudioProcessing"
                )
                self.audio_processing_thread.start()

                # 5. 启动WebSocket监控线程(如果不是模拟模式)
                if not self.use_mock and hasattr(self.asr_client, 'ws_client') and self.asr_client.ws_client:
                    self.ws_monitoring_thread = threading.Thread(
                        target=self._monitor_websocket,
                        daemon=True,
                        name="ASR-WebSocketMonitor"
                    )
                    self.ws_monitoring_thread.start()

                # 6. 设置状态为已启动
                self.is_recognizing = True
                logger.info("成功启动语音识别" + (" (模拟模式)" if self.use_mock else ""))
                return True

            except Exception as e:
                self.is_recognizing = False
                logger.error(f"启动语音识别失败: {str(e)}")
                # 尝试断开连接
                if not self.use_mock:
                    try:
                        if self.asr_client and self.asr_client.is_connected:
                            self.asr_client.disconnect()
                    except Exception as disconnect_error:
                        logger.error(f"断开ASR连接时出错: {str(disconnect_error)}")
                raise RuntimeError(f"启动语音识别失败: {str(e)}")

    def stop_recognition(self) -> bool:
        """
        停止语音识别，关闭麦克风和ASR服务连接

        返回:
            bool: 是否成功停止识别
        """
        with self.lock:
            if not self.is_recognizing:
                logger.warning("当前没有进行语音识别")
                return False

            logger.info("正在停止语音识别...")
            self.is_recognizing = False

            # 1. 停止音频流
            if self.stream is not None:
                try:
                    self.stream.stop()
                    self.stream.close()
                except Exception as e:
                    logger.error(f"关闭音频流时出错: {str(e)}")
                finally:
                    self.stream = None

            # 2. 如果不是模拟模式，关闭ASR连接
            if not self.use_mock:
                # 结束ASR识别会话
                if self.asr_client and self.asr_client.is_recognizing:
                    try:
                        self.asr_client.stop_recognition()
                    except Exception as e:
                        logger.error(f"结束ASR识别会话时出错: {str(e)}")

                # 断开ASR服务连接
                if self.asr_client and self.asr_client.is_connected:
                    try:
                        self.asr_client.disconnect()
                    except Exception as e:
                        logger.error(f"断开ASR服务连接时出错: {str(e)}")

            # 3. 等待处理线程结束
            threads_to_join = []
            if self.audio_processing_thread and self.audio_processing_thread.is_alive():
                threads_to_join.append(self.audio_processing_thread)

            if self.ws_monitoring_thread and self.ws_monitoring_thread.is_alive():
                threads_to_join.append(self.ws_monitoring_thread)

            for thread in threads_to_join:
                try:
                    thread.join(timeout=5.0)
                except Exception as e:
                    logger.error(f"等待线程 {thread.name} 结束时出错: {str(e)}")

            # 4. 清理状态
            self.audio_processing_thread = None
            self.ws_monitoring_thread = None

            # 5. 重置所有状态变量，确保下次可以正常启动
            self.is_paused = False
            self.error_count = 0
            self.current_stream = ""
            self.last_result = ""

            # 清空音频队列
            while not self.audio_queue.empty():
                try:
                    self.audio_queue.get_nowait()
                except queue.Empty:
                    break

            logger.info("成功停止语音识别，所有状态已重置")
            return True

    def restart_recognition(self, callback: Optional[Callable[[str, bool], None]] = None) -> bool:
        """
        重启语音识别服务

        参数:
            callback (Callable[[str, bool], None], optional): 识别结果回调函数
                                                          不提供则使用原有回调函数

        返回:
            bool: 是否成功重启识别
        """
        with self.lock:
            logger.info("正在重启语音识别...")
            self.stop_recognition()
            time.sleep(1.0)  # 等待资源完全释放
            return self.start_recognition(callback=callback if callback is not None else self.recognition_callback)

    def pause_recognition(self) -> bool:
        """
        暂停语音识别，但保持麦克风和连接活跃

        返回:
            bool: 是否成功暂停识别
        """
        with self.lock:
            if not self.is_recognizing:
                logger.warning("当前没有进行语音识别")
                return False

            if self.is_paused:
                logger.warning("已经处于暂停状态")
                return False

            self.is_paused = True
            logger.info("已暂停语音识别")
            return True

    def resume_recognition(self) -> bool:
        """
        恢复已暂停的语音识别

        返回:
            bool: 是否成功恢复识别
        """
        with self.lock:
            if not self.is_recognizing:
                logger.warning("语音识别未启动，无法恢复")
                return False

            if not self.is_paused:
                logger.warning("当前非暂停状态，无需恢复")
                return False

            self.is_paused = False
            logger.info("已恢复语音识别")
            return True

    def get_recognition_status(self) -> Dict[str, Any]:
        """
        获取当前语音识别的状态信息

        返回:
            Dict[str, Any]: 包含当前状态的字典，包括:
                - is_recognizing: 是否正在识别
                - is_paused: 是否暂停
                - session_id: 当前会话ID
                - error_count: 错误计数
                - last_result: 最近一次识别结果
                - current_stream: 当前实时识别结果
                - use_mock: 是否使用模拟模式
        """
        with self.lock:
            return {
                "is_recognizing": self.is_recognizing,
                "is_paused": self.is_paused,
                "session_id": self.session_id,
                "error_count": self.error_count,
                "last_result": self.last_result,
                "current_stream": self.current_stream,
                "use_mock": self.use_mock
            }

    def get_recognition_results(self) -> List[str]:
        """
        获取所有语音识别结果历史

        返回:
            List[str]: 所有识别结果的列表
        """
        with self.lock:
            return self.results.copy()

    def get_last_result(self) -> str:
        """
        获取最近一次的最终语音识别结果

        返回:
            str: 最近一次的最终识别结果
        """
        with self.lock:
            return self.last_result

    def get_current_stream(self) -> str:
        """
        获取当前正在进行的实时识别结果

        返回:
            str: 当前的实时识别结果（非最终结果）
        """
        with self.lock:
            return self.current_stream

    def clear_results(self) -> None:
        """
        清空所有识别结果历史
        """
        with self.lock:
            self.results = []
            self.last_result = ""
            self.current_stream = ""

    def set_debug_mode(self, enabled: bool) -> None:
        """
        设置调试模式

        参数:
            enabled (bool): 是否启用调试模式
        """
        self.debug_mode = enabled
        logger.info(f"调试模式已{'启用' if enabled else '禁用'}")

    def display_recognition_status(self) -> str:
        """
        生成并返回当前识别状态的文本表示，用于调试显示

        返回:
            str: 格式化的状态信息文本
        """
        with self.lock:
            status = self.get_recognition_status()

            lines = [
                "===== ASR识别状态 =====",
                f"识别状态: {'运行中' if status['is_recognizing'] else '已停止'}",
                f"暂停状态: {'已暂停' if status['is_paused'] else '未暂停'}",
                f"会话ID: {status['session_id']}",
                f"模拟模式: {'是' if status['use_mock'] else '否'}",
                f"错误计数: {status['error_count']}",
                "----------------------",
                f"最新识别结果: {status['last_result']}",
                f"当前流式结果: {status['current_stream']}",
                "======================="
            ]

            return "\n".join(lines)

    def send_audio_data(self, audio_data: np.ndarray) -> bool:
        """
        发送外部音频数据进行识别

        适用于不使用麦克风，而是从外部源获取音频数据的场景

        参数:
            audio_data (np.ndarray): 音频数据，应为numpy数组
                                  格式应与初始化时指定的采样率和通道数匹配

        返回:
            bool: 是否成功发送数据
        """
        if not self.is_recognizing or self.is_paused:
            logger.warning("语音识别未启动或处于暂停状态，无法发送音频数据")
            return False

        try:
            self.audio_queue.put(audio_data.copy())
            return True
        except Exception as e:
            logger.error(f"发送音频数据失败: {str(e)}")
            return False

    def _audio_callback(self, indata, frames, time_info, status):
        """
        音频数据回调函数，用于采集麦克风数据

        参数:
            indata: 输入音频数据
            frames: 帧数
            time_info: 时间信息
            status: 状态信息
        """
        if status:
            logger.warning(f"音频回调状态: {status}")

        if self.is_recognizing and not self.is_paused:
            try:
                self.audio_queue.put(indata.copy())
            except Exception as e:
                logger.error(f"处理音频数据时出错: {str(e)}")

    def _is_silence(self, audio_data: np.ndarray) -> bool:
        """
        检测音频数据是否为静音
        使用更复杂的算法来判断静音，避免误判

        参数:
            audio_data (np.ndarray): 音频数据

        返回:
            bool: 是否为静音
        """
        # 计算音频的RMS值（均方根值，更能反映人耳感知的音量）
        rms = np.sqrt(np.mean(np.square(audio_data)))

        # 计算音频的峰值
        peak = np.max(np.abs(audio_data))

        # 同时考虑RMS值和峰值，使静音检测更准确
        # 提高阈值，使系统对声音更不敏感
        is_silent = (rms < self.silence_threshold * 0.7) and (peak < self.silence_threshold)

        # 如果处于调试模式，记录音量信息
        if self.debug_mode and not is_silent:
            logger.debug(f"音频音量: RMS={rms:.6f}, 峰值={peak:.6f}, 阈值={self.silence_threshold}")

        return is_silent

    def _process_audio_stream(self):
        """
        处理音频数据的线程函数
        负责从音频队列获取数据，处理后发送到ASR服务
        """
        # 音频处理所需的变量
        audio_buffer = []  # 音频数据缓存
        last_speech_time = time.time()  # 上次检测到语音的时间（用于所有模式）
        is_speaking = False  # 是否正在说话
        speech_duration = 0  # 说话持续时间
        silence_count = 0  # 静音计数器
        silence_duration = 0  # 静音持续时间（用于所有模式）

        # 模拟需要的语音命令
        mock_commands = [
            "床前明月光下一句是什么",
            "今天天气怎么样",
            "帮我看看这是什么",
            "启动语音识别系统",
            "现在几点了",
            "你能帮我做什么"
        ]

        logger.info("音频处理线程已启动")

        while self.is_recognizing:
            try:
                # 从队列中获取音频数据，设置超时以便定期检查is_recognizing状态
                try:
                    speech_chunk = self.audio_queue.get(timeout=0.5).flatten()
                except queue.Empty:
                    continue

                # 如果处于暂停状态，则跳过处理
                if self.is_paused:
                    continue

                # 模拟模式处理
                if self.use_mock:
                    # 检测是否为静音
                    current_is_silence = self._is_silence(speech_chunk)
                    current_time = time.time()

                    if not current_is_silence:
                        # 检测到语音
                        if not is_speaking:
                            # 刚开始说话
                            is_speaking = True
                            logger.debug("检测到语音输入...")

                        # 累计语音持续时间
                        speech_duration += self.chunk_duration
                        last_speech_time = current_time
                        audio_buffer.append(speech_chunk)
                        silence_count = 0

                        # 模拟实时流结果
                        if speech_duration > 1.0 and len(audio_buffer) % 5 == 0:
                            # 每累积5个音频块，模拟一次流式结果
                            stream_result = "正在识别中" + "." * \
                                (len(audio_buffer) % 4 + 1)
                            with self.lock:
                                self.current_stream = stream_result

                            # 调用回调函数，标记为非最终结果
                            if self.recognition_callback is not None:
                                try:
                                    self.recognition_callback(
                                        stream_result, False)
                                except Exception as e:
                                    logger.error(f"调用模拟流识别回调函数时出错: {str(e)}")
                    elif is_speaking:
                        # 之前在说话，现在是静音
                        silence_count += 1
                        # 检查静音持续时间
                        if current_time - last_speech_time >= self.silence_duration:
                            # 静音超过阈值，生成模拟结果
                            if len(audio_buffer) > 10 and speech_duration > 1.0:  # 至少说话1秒钟
                                # 生成一个随机的语音识别结果
                                mock_result = np.random.choice(mock_commands)
                                logger.info(f"模拟语音识别结果: {mock_result}")

                                # 处理并回调结果
                                processed_result = self.process_text_with_punctuation(
                                    mock_result)

                                with self.lock:
                                    self.last_result = processed_result
                                    self.results.append(processed_result)
                                    self.current_stream = ""  # 清空实时结果

                                # 调用回调函数
                                if self.recognition_callback is not None:
                                    try:
                                        self.recognition_callback(
                                            processed_result, True)
                                    except Exception as e:
                                        logger.error(
                                            f"调用模拟识别回调函数时出错: {str(e)}")

                            # 重置状态
                            audio_buffer = []
                            is_speaking = False
                            speech_duration = 0
                            silence_count = 0
                else:
                    # 真实ASR模式处理
                    if self.asr_client and self.asr_client.is_connected and self.asr_client.is_recognizing:
                        try:
                            # 检测是否为静音
                            current_is_silence = self._is_silence(speech_chunk)
                            current_time = time.time()

                            # 如果不是静音，更新最后语音时间
                            if not current_is_silence:
                                last_speech_time = current_time
                                # 如果音量超过阈值，记录调试信息
                                if self.debug_mode:
                                    logger.debug(
                                        f"发送非静音音频数据，音量: {np.max(np.abs(speech_chunk)):.4f}, RMS: {np.sqrt(np.mean(np.square(speech_chunk))):.4f}")
                            elif self.debug_mode:
                                # 记录静音持续时间
                                silence_duration = current_time - last_speech_time
                                if silence_duration > 1.0:  # 只记录超过1秒的静音
                                    logger.debug(f"检测到静音，持续时间: {silence_duration:.2f}秒")

                            # 发送音频数据到ASR服务
                            send_result = self.asr_client.send_audio(
                                speech_chunk)

                            # 如果发送失败，增加错误计数
                            if not send_result:
                                logger.warning("发送音频数据失败")
                                self.error_count += 1

                        except Exception as e:
                            logger.error(f"发送音频数据时出错: {str(e)}")
                            self.error_count += 1

                            # 如果错误次数过多，尝试重新连接
                            if self.error_count >= self.max_retries:
                                self._handle_connection_error()

            except Exception as e:
                logger.error(f"处理音频数据时出错: {str(e)}")
                self.error_count += 1

        logger.info("音频处理线程已结束")

    def _handle_connection_error(self):
        """处理连接错误，尝试重新连接ASR服务"""
        logger.warning(f"错误次数过多，尝试重新连接ASR服务...")
        try:
            # 确保完全断开旧连接
            if self.asr_client and self.asr_client.is_connected:
                logger.info("尝试断开ASR连接以便重新连接...")
                self.asr_client.disconnect()

                # 等待连接完全关闭
                time.sleep(1.0)

            # 等待后重新连接
            logger.info(f"等待 {self.retry_delay} 秒后尝试重新连接...")
            time.sleep(self.retry_delay)

            logger.info("重新连接ASR服务...")
            if self.asr_client and self.asr_client.connect():
                logger.info("重新连接成功，启动识别...")
                self.asr_client.start_recognition(
                    callback=lambda text, is_final: self._handle_asr_result(
                        text, is_final)
                )
                self.error_count = 0
                logger.info("重新连接ASR服务成功")
            else:
                logger.warning("重新连接ASR服务失败，切换到模拟模式")
                self.use_mock = True
        except Exception as reconnect_error:
            logger.error(f"重新连接ASR服务时出错: {str(reconnect_error)}")
            logger.info("切换到模拟模式")
            self.use_mock = True

    def _handle_websocket_close(self, close_status_code=None, close_reason=None):
        """
        处理WebSocket连接关闭事件

        参数:
            close_status_code: WebSocket关闭状态码
            close_reason: WebSocket关闭原因
        """
        logger.warning(
            f"WebSocket连接已关闭: 状态码={close_status_code}, 原因={close_reason}")

        # 如果不是主动关闭且还在识别过程中，尝试重连
        if self.is_recognizing and not self.use_mock:
            logger.info("检测到WebSocket异常关闭，尝试重新连接...")
            self._handle_connection_error()

    def _monitor_websocket(self):
        """
        监控WebSocket连接状态的线程函数
        定期检查连接是否健康，如果不健康则尝试重连
        """
        logger.info("WebSocket监控线程已启动")
        check_interval = 5.0  # 每5秒检查一次

        while self.is_recognizing:
            try:
                # 检查WebSocket连接是否健康
                if not self.use_mock and self.asr_client and hasattr(self.asr_client, 'ws_client'):
                    ws = self.asr_client.ws_client
                    if ws is None or not ws.sock or not ws.sock.connected:
                        logger.warning("检测到WebSocket连接不健康")
                        self._handle_connection_error()
            except Exception as e:
                logger.error(f"监控WebSocket时出错: {str(e)}")

            # 休眠一段时间
            time.sleep(check_interval)

        logger.info("WebSocket监控线程已结束")

    def _handle_asr_result(self, text: str, is_final: bool):
        """
        处理ASR返回的识别结果

        参数:
            text: 识别文本
            is_final: 是否为最终结果
        """
        if is_final and text:
            # 最终结果，添加标点符号处理
            text = self.process_text_with_punctuation(text)

            with self.lock:
                self.last_result = text
                self.results.append(text)
                self.current_stream = ""  # 清空实时结果

            # 调用回调函数
            if self.recognition_callback is not None:
                try:
                    self.recognition_callback(text, True)
                except Exception as e:
                    logger.error(f"调用回调函数时出错: {str(e)}")
        elif text:
            # 非最终结果，更新当前流式结果
            with self.lock:
                self.current_stream = text

            # 调用回调函数，标记为非最终结果
            if self.recognition_callback is not None:
                try:
                    self.recognition_callback(text, False)
                except Exception as e:
                    logger.error(f"调用回调函数时出错: {str(e)}")

    def process_text_with_punctuation(self, text: str) -> str:
        """
        对识别文本进行处理，智能添加标点符号

        参数:
            text (str): 原始文本

        返回:
            str: 处理后的文本
        """
        # 去除可能存在的多余空格
        text = text.strip()

        # 如果文本为空，直接返回
        if not text:
            return text

        # 使用jieba分词和词性标注
        return self._process_with_jieba(text)

    def _process_with_jieba(self, text: str) -> str:
        """
        使用jieba进行分词和标点添加

        参数:
            text (str): 原始文本

        返回:
            str: 处理后的文本
        """
        # 先初始化jieba
        try:
            jieba.initialize()  # 确保jieba已初始化

            # 添加自定义词典
            custom_words = [
                "LifeBuddy", "豆包", "豆包大模型", "大语言模型",
                "火山引擎", "火山语音", "GPT", "大模型"
            ]
            for word in custom_words:
                jieba.add_word(word)

            # 进行词性标注
            words = posseg.cut(text)

            # 遍历词性标注结果，根据词性添加标点
            punctuated_text = ""
            prev_word_type = None
            for word, flag in words:
                # 根据词性和上下文添加标点
                if flag in ['v', 'vn'] and prev_word_type in ['n', 'r', 'uj']:
                    punctuated_text += "，" + word
                elif flag in ['w']:  # 标点符号
                    punctuated_text += word
                else:
                    # 直接添加词汇
                    if punctuated_text and not punctuated_text.endswith("，") and not punctuated_text.endswith("。"):
                        punctuated_text += word
                    else:
                        punctuated_text += word

                prev_word_type = flag

            # 如果文本不以标点结尾，添加句号
            if punctuated_text and not re.search(r'[，。！？、]$', punctuated_text):
                punctuated_text += "。"

            return punctuated_text
        except Exception as e:
            logger.warning(f"使用jieba处理文本出错: {str(e)}")
            # 出错时返回原文本
            if text and not re.search(r'[，。！？、]$', text):
                text += "。"
            return text
