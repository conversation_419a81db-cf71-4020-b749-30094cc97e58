"""
快速ASR连接管理器
优化连接速度，减少重连延迟，提高连接效率
"""

import time
import threading
import logging
import websocket
from typing import Callable, Optional, Dict, Any

logger = logging.getLogger(__name__)


class FastASRConnectionManager:
    """
    快速ASR连接管理器
    专门优化连接速度和重连效率
    """
    
    def __init__(self, 
                 url: str, 
                 headers: Dict[str, str],
                 connection_timeout: float = 2.0,  # 快速连接超时
                 max_retries: int = 3):
        """
        初始化快速连接管理器
        
        Args:
            url: WebSocket连接URL
            headers: 连接请求头
            connection_timeout: 连接超时时间（秒）
            max_retries: 最大重试次数
        """
        self.url = url
        self.headers = headers
        self.connection_timeout = connection_timeout
        self.max_retries = max_retries
        
        # 连接状态
        self.ws = None
        self.ws_thread = None
        self.is_connected = False
        self.connection_lock = threading.RLock()
        self.stop_event = threading.Event()
        
        # 连接统计
        self.connection_attempts = 0
        self.successful_connections = 0
        self.failed_connections = 0
        self.last_connection_time = 0
        
        # 回调函数
        self.on_open_callback = None
        self.on_message_callback = None
        self.on_error_callback = None
        self.on_close_callback = None
    
    def set_callbacks(self, 
                     on_open: Optional[Callable] = None,
                     on_message: Optional[Callable] = None,
                     on_error: Optional[Callable] = None,
                     on_close: Optional[Callable] = None):
        """设置回调函数"""
        self.on_open_callback = on_open
        self.on_message_callback = on_message
        self.on_error_callback = on_error
        self.on_close_callback = on_close
    
    def fast_connect(self) -> bool:
        """
        快速连接到ASR服务
        
        Returns:
            bool: 是否成功连接
        """
        with self.connection_lock:
            start_time = time.time()
            
            # 如果已经连接，直接返回
            if self.is_connected and self.ws and self.ws.sock:
                try:
                    # 快速检查连接状态
                    self.ws.sock.ping()
                    logger.info("ASR连接已存在且有效")
                    return True
                except:
                    logger.info("现有连接无效，需要重新连接")
                    self._force_close()
            
            # 快速清理旧连接
            self._force_close()
            
            # 尝试快速连接
            for attempt in range(self.max_retries):
                self.connection_attempts += 1
                
                try:
                    logger.info(f"快速连接尝试 {attempt + 1}/{self.max_retries}")
                    
                    # 重置状态
                    self.is_connected = False
                    self.stop_event.clear()
                    
                    # 创建WebSocket连接
                    self.ws = websocket.WebSocketApp(
                        self.url,
                        header=self.headers,
                        on_open=self._on_open,
                        on_message=self._on_message,
                        on_error=self._on_error,
                        on_close=self._on_close
                    )
                    
                    # 启动WebSocket线程
                    self.ws_thread = threading.Thread(
                        target=self.ws.run_forever,
                        daemon=True
                    )
                    self.ws_thread.start()
                    
                    # 快速等待连接建立
                    connection_start = time.time()
                    while not self.is_connected and (time.time() - connection_start) < self.connection_timeout:
                        if self.stop_event.is_set():
                            break
                        time.sleep(0.05)  # 非常短的检查间隔
                    
                    if self.is_connected:
                        self.successful_connections += 1
                        self.last_connection_time = time.time() - start_time
                        logger.info(f"快速连接成功，耗时: {self.last_connection_time:.3f}秒")
                        return True
                    else:
                        logger.warning(f"连接尝试 {attempt + 1} 超时")
                        self._force_close()
                        
                        # 只在非最后一次尝试时等待
                        if attempt < self.max_retries - 1:
                            time.sleep(0.1)  # 极短的重试间隔
                
                except Exception as e:
                    logger.error(f"连接尝试 {attempt + 1} 失败: {e}")
                    self._force_close()
                    
                    # 只在非最后一次尝试时等待
                    if attempt < self.max_retries - 1:
                        time.sleep(0.1)
            
            self.failed_connections += 1
            logger.error(f"快速连接失败，总耗时: {time.time() - start_time:.3f}秒")
            return False
    
    def _force_close(self):
        """强制关闭连接，不等待"""
        try:
            self.is_connected = False
            self.stop_event.set()
            
            if self.ws:
                try:
                    self.ws.close()
                except:
                    pass
                self.ws = None
            
            # 不等待线程结束，让它自然结束
            self.ws_thread = None
            
        except Exception as e:
            logger.debug(f"强制关闭连接时出错: {e}")
    
    def disconnect(self):
        """断开连接"""
        with self.connection_lock:
            self._force_close()
            logger.info("ASR连接已断开")
    
    def _on_open(self, ws):
        """WebSocket连接建立回调"""
        self.is_connected = True
        if self.on_open_callback:
            try:
                self.on_open_callback(ws)
            except Exception as e:
                logger.error(f"on_open回调出错: {e}")
    
    def _on_message(self, ws, message):
        """WebSocket消息接收回调"""
        if self.on_message_callback:
            try:
                self.on_message_callback(ws, message)
            except Exception as e:
                logger.error(f"on_message回调出错: {e}")
    
    def _on_error(self, ws, error):
        """WebSocket错误回调"""
        logger.error(f"WebSocket错误: {error}")
        if self.on_error_callback:
            try:
                self.on_error_callback(ws, error)
            except Exception as e:
                logger.error(f"on_error回调出错: {e}")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket关闭回调"""
        self.is_connected = False
        logger.info(f"WebSocket连接已关闭: {close_status_code} {close_msg}")
        if self.on_close_callback:
            try:
                self.on_close_callback(ws, close_status_code, close_msg)
            except Exception as e:
                logger.error(f"on_close回调出错: {e}")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            "is_connected": self.is_connected,
            "connection_attempts": self.connection_attempts,
            "successful_connections": self.successful_connections,
            "failed_connections": self.failed_connections,
            "last_connection_time": self.last_connection_time,
            "success_rate": self.successful_connections / max(self.connection_attempts, 1) * 100
        }


class FastVolcanoASRClient:
    """
    使用快速连接管理器的火山引擎ASR客户端
    """

    def __init__(self, app_key: str, access_key: str, **kwargs):
        """初始化快速ASR客户端"""
        self.app_key = app_key
        self.access_key = access_key
        self.resource_id = kwargs.get('resource_id', 'volc.bigasr.sauc.duration')
        self.model_name = kwargs.get('model_name', 'bigmodel')
        self.sample_rate = kwargs.get('sample_rate', 16000)
        self.channels = kwargs.get('channels', 1)
        self.format = kwargs.get('format', 'pcm')

        # WebSocket URL
        self.ws_url = "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel"

        # 生成连接ID
        import uuid
        self.connect_id = str(uuid.uuid4())

        # 准备请求头
        headers = {
            "X-Api-App-Key": self.app_key,
            "X-Api-Access-Key": self.access_key,
            "X-Api-Resource-Id": self.resource_id,
            "X-Api-Connect-Id": self.connect_id
        }

        # 创建快速连接管理器
        self.connection_manager = FastASRConnectionManager(
            url=self.ws_url,
            headers=headers,
            connection_timeout=2.0,  # 2秒快速连接超时
            max_retries=3
        )

        # 设置回调
        self.connection_manager.set_callbacks(
            on_open=self._on_open,
            on_message=self._on_message,
            on_error=self._on_error,
            on_close=self._on_close
        )

        # 状态
        self.is_connected = False
        self.is_recognizing = False
        self.result_callback = None

        # 音频队列和线程
        import queue
        self.audio_queue = queue.Queue()
        self.audio_sender_thread = None
        self.sequence_number = 0

        # 识别结果
        self.results = []
        self.last_result = ""
    
    def connect(self) -> bool:
        """快速连接到ASR服务"""
        success = self.connection_manager.fast_connect()
        self.is_connected = success
        return success
    
    def disconnect(self):
        """断开连接"""
        if self.is_recognizing:
            self.stop_recognition()
        self.connection_manager.disconnect()
        self.is_connected = False
        self.is_recognizing = False

    def start_recognition(self, callback: Optional[Callable[[str, bool], None]] = None) -> bool:
        """
        启动语音识别会话

        Args:
            callback: 识别结果回调函数

        Returns:
            bool: 是否成功启动识别会话
        """
        if not self.is_connected:
            logger.error("未连接到ASR服务，请先调用connect()")
            return False

        if self.is_recognizing:
            logger.warning("已经在进行识别，请先调用stop_recognition()")
            return False

        try:
            # 更新回调函数
            if callback:
                self.result_callback = callback

            # 清空队列和结果
            while not self.audio_queue.empty():
                try:
                    self.audio_queue.get_nowait()
                except:
                    break

            self.results = []
            self.last_result = ""
            self.sequence_number = 0

            # 发送初始化参数
            success = self._send_init_parameters()
            if not success:
                logger.error("发送初始化参数失败")
                return False

            # 标记为识别状态
            self.is_recognizing = True

            # 启动音频发送线程
            import threading
            self.audio_sender_thread = threading.Thread(
                target=self._audio_sender_thread,
                daemon=True
            )
            self.audio_sender_thread.start()

            # logger.info("成功启动语音识别会话")
            return True

        except Exception as e:
            logger.error(f"启动语音识别会话失败: {e}")
            return False

    def stop_recognition(self) -> bool:
        """
        停止语音识别会话

        Returns:
            bool: 是否成功停止识别会话
        """
        if not self.is_recognizing:
            logger.warning("当前未在进行识别")
            return True

        try:
            logger.info("正在停止语音识别会话...")

            # 发送结束标志
            self._send_audio_end_signal()

            # 等待一段时间接收最后的识别结果
            time.sleep(0.5)

            # 标记为非识别状态
            self.is_recognizing = False

            # 等待音频发送线程结束
            if self.audio_sender_thread and self.audio_sender_thread.is_alive():
                self.audio_sender_thread.join(timeout=1.0)

            logger.info("语音识别会话已停止")
            return True

        except Exception as e:
            logger.error(f"停止语音识别会话失败: {e}")
            self.is_recognizing = False
            return False

    def send_audio(self, audio_data: bytes) -> bool:
        """
        发送音频数据

        Args:
            audio_data: 音频数据

        Returns:
            bool: 是否成功发送
        """
        if not self.is_recognizing:
            logger.warning("未在识别状态，无法发送音频数据")
            return False

        try:
            # 将音频数据放入队列
            self.audio_queue.put(audio_data, timeout=1.0)
            return True
        except:
            logger.warning("音频队列已满，丢弃音频数据")
            return False

    def finalize_recognition(self) -> bool:
        """
        结束识别并获取最终结果

        Returns:
            bool: 是否成功结束识别
        """
        return self.stop_recognition()

    def _send_init_parameters(self) -> bool:
        """发送初始化参数 - 使用火山引擎二进制协议"""
        try:
            ws = self.connection_manager.ws
            if not ws or not ws.sock:
                return False

            # 构造请求参数 - 与标准客户端完全一致
            import uuid
            import json
            import struct

            params = {
                "user": {
                    "uid": str(uuid.uuid4())
                },
                "audio": {
                    "format": self.format,
                    "rate": self.sample_rate,
                    "bits": 16,
                    "channel": self.channels
                },
                "request": {
                    "model_name": self.model_name,
                    "enable_itn": True,
                    "enable_punc": True,
                    "enable_ddc": True
                }
            }

            # 将参数转换为JSON字符串
            params_json = json.dumps(params)
            payload = params_json.encode('utf-8')

            # 创建header - 与标准客户端完全一致
            header = bytearray(4)
            header[0] = 0x11  # 协议版本
            header[1] = (0x01 << 4) | 0x00  # MESSAGE_TYPE_FULL_CLIENT_REQUEST + MESSAGE_FLAG_NONE
            header[2] = (0x01 << 4) | 0x00  # SERIALIZATION_JSON + COMPRESSION_NONE
            header[3] = 0x00  # 保留字段

            # 添加payload大小（4字节无符号整数，大端序）
            payload_size = struct.pack("!I", len(payload))

            # 发送消息
            import websocket
            ws.send(bytes(header) + payload_size + payload, websocket.ABNF.OPCODE_BINARY)
            logger.debug("已发送初始化参数")
            return True

        except Exception as e:
            logger.error(f"发送初始化参数失败: {e}")
            return False

    def _send_audio_end_signal(self) -> bool:
        """发送音频结束信号 - 使用火山引擎二进制协议"""
        try:
            ws = self.connection_manager.ws
            if not ws or not ws.sock:
                return False

            # 创建header - 与标准客户端完全一致
            import struct
            header = bytearray(4)
            header[0] = 0x11  # 协议版本
            header[1] = (0x02 << 4) | 0x02  # MESSAGE_TYPE_AUDIO_ONLY_REQUEST + MESSAGE_FLAG_LAST_PACKET
            header[2] = (0x00 << 4) | 0x00  # SERIALIZATION_NONE + COMPRESSION_NONE
            header[3] = 0x00  # 保留字段

            # 空payload
            payload_size = struct.pack("!I", 0)

            # 发送消息
            import websocket
            ws.send(bytes(header) + payload_size, websocket.ABNF.OPCODE_BINARY)
            logger.debug("已发送音频结束信号")
            return True

        except Exception as e:
            logger.error(f"发送音频结束信号失败: {e}")
            return False

    def _audio_sender_thread(self):
        """音频数据发送线程"""
        logger.info("音频发送线程已启动")

        while self.is_connected and self.is_recognizing:
            try:
                # 从队列中获取音频数据
                try:
                    audio_chunk = self.audio_queue.get(timeout=0.5)
                except:
                    continue

                # 发送音频数据
                self._send_audio_chunk(audio_chunk)

            except Exception as e:
                logger.error(f"发送音频数据时出错: {e}")
                break

        logger.info("音频发送线程已结束")

    def _send_audio_chunk(self, audio_data: bytes) -> bool:
        """发送音频数据块 - 使用火山引擎二进制协议"""
        try:
            ws = self.connection_manager.ws
            if not ws or not ws.sock:
                return False

            # 创建header - 与标准客户端完全一致
            import struct
            header = bytearray(4)
            header[0] = 0x11  # 协议版本
            header[1] = (0x02 << 4) | 0x00  # MESSAGE_TYPE_AUDIO_ONLY_REQUEST + MESSAGE_FLAG_NONE
            header[2] = (0x00 << 4) | 0x00  # SERIALIZATION_NONE + COMPRESSION_NONE
            header[3] = 0x00  # 保留字段

            # 添加payload大小（4字节无符号整数，大端序）
            payload_size = struct.pack("!I", len(audio_data))

            # 发送消息
            import websocket
            ws.send(bytes(header) + payload_size + audio_data, websocket.ABNF.OPCODE_BINARY)
            self.sequence_number += 1
            return True

        except Exception as e:
            logger.error(f"发送音频数据块失败: {e}")
            return False

    def _on_open(self, ws):
        """连接建立回调"""
        logger.info("快速ASR连接已建立")
        self.is_connected = True

    def _on_message(self, ws, message):
        """消息接收回调 - 处理火山引擎ASR的二进制协议"""
        try:
            # 确保消息是二进制
            if not isinstance(message, bytes):
                logger.warning(f"接收到非二进制消息: {message}")
                return

            # 消息至少要有4字节的header
            if len(message) < 4:
                logger.warning(f"消息太短: {len(message)}字节")
                return

            # 解析header
            header = message[:4]

            # 提取消息类型和标志
            message_type = (header[1] >> 4) & 0x0F
            message_flags = header[1] & 0x0F

            # 处理识别结果消息
            if message_type == 0x0B:  # MESSAGE_TYPE_FULL_SERVER_RESPONSE
                self._parse_asr_response(message, message_flags)
            elif message_type == 0x0F:  # MESSAGE_TYPE_ERROR_MESSAGE
                self._parse_error_message(message)
            else:
                logger.debug(f"未知消息类型: {message_type}")

        except Exception as e:
            logger.error(f"处理WebSocket消息时出错: {e}")

    def _parse_asr_response(self, message: bytes, flags: int):
        """解析ASR识别结果"""
        try:
            # 跳过header，获取payload
            payload = message[4:]

            if len(payload) == 0:
                return

            # 解析JSON payload
            import json
            try:
                data = json.loads(payload.decode('utf-8'))
            except:
                logger.warning("无法解析识别结果JSON")
                return

            # 提取识别结果
            if 'result' in data:
                result = data['result']
                text = result.get('text', '')
                is_final = flags & 0x04 != 0  # 检查final标志位

                if text:
                    logger.debug(f"识别结果: {text}, is_final: {is_final}")

                    # 更新结果
                    if is_final:
                        self.results.append(text)
                        self.last_result = text

                    # 调用回调函数
                    if self.result_callback:
                        try:
                            self.result_callback(text, is_final)
                        except Exception as e:
                            logger.error(f"回调函数执行出错: {e}")

        except Exception as e:
            logger.error(f"解析ASR响应时出错: {e}")

    def _parse_error_message(self, message: bytes):
        """解析错误消息"""
        try:
            payload = message[4:]
            if len(payload) > 0:
                error_msg = payload.decode('utf-8', errors='ignore')
                logger.error(f"ASR服务错误: {error_msg}")
        except Exception as e:
            logger.error(f"解析错误消息时出错: {e}")

    def _on_error(self, ws, error):
        """错误回调"""
        logger.error(f"快速ASR连接错误: {error}")

    def _on_close(self, ws, close_status_code, close_msg):
        """连接关闭回调"""
        logger.info("快速ASR连接已关闭")
        self.is_connected = False
        self.is_recognizing = False

    def get_stats(self) -> Dict[str, Any]:
        """获取连接统计"""
        return self.connection_manager.get_connection_stats()
