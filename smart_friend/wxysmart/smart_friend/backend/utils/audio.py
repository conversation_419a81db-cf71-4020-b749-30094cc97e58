import pygame
import os
import time
import tempfile
import threading
import warnings
import queue  # 直接导入queue模块
from pydub.playback import play
from pydub import AudioSegment

# 导入日志管理模块
from utils.logging_manager import get_logger

# 获取日志记录器
logger = get_logger("AudioPlayer")

# 抑制pydub的警告
warnings.filterwarnings("ignore", category=RuntimeWarning, module="pydub.utils")

# 抑制pygame的欢迎消息
os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'


class AudioPlayer:
    """
    音频播放器类
    
    提供音频播放功能，支持文件播放和流式播放。
    """
    def __init__(self):
        """初始化音频播放器"""
        # 初始化pygame
        try:
            pygame.mixer.init()
            self.initialized = True
        except Exception as e:
            print(f"初始化音频播放器失败: {e}")
            self.initialized = False
        
        # 流式播放相关
        self.stream_active = False
        self.audio_queue = queue.Queue()
        self.stream_thread = None
    
    def play_file(self, file_path: str) -> bool:
        """
        播放音频文件
        
        Args:
            file_path (str): 音频文件路径
            
        Returns:
            bool: 是否成功播放
        """
        if not self.initialized:
            print("音频播放器未初始化")
            return False
            
        try:
            # 确保pygame已初始化
            if not pygame.mixer.get_init():
                pygame.mixer.init()
                
            # 加载并播放音频
            sound = pygame.mixer.Sound(file_path)
            sound.play()
            
            # 等待播放完成
            pygame.time.wait(int(sound.get_length() * 1000))
            
            return True
        except Exception as e:
            print(f"播放音频文件失败: {e}")
            return False

    def stop_playback(self):
        """
        停止当前播放

        立即停止正在进行的音频播放。
        """
        try:
            # 设置停止事件
            if hasattr(self, 'stop_event'):
                self.stop_event.set()

            # 直接停止pygame播放
            if pygame.mixer.get_init() and pygame.mixer.music.get_busy():
                pygame.mixer.music.stop()
                logger.info("已停止音频播放")

            # 停止流式播放
            self.stop_stream_playback()

            return True
        except Exception as e:
            logger.error(f"停止播放失败: {e}")
            return False

    def start_stream_playback(self):
        """
        启动流式播放线程

        创建并启动一个新线程来处理流式音频播放。
        如果已经在播放中，此方法不会重复启动。
        """
        if self.is_playing:
            logger.debug("流式播放已在运行中，忽略启动请求")
            return

        self.stop_event.clear()
        self.is_playing = True
        self.playback_thread = threading.Thread(target=self._stream_playback_worker)
        self.playback_thread.daemon = True
        self.playback_thread.start()
        logger.info("开始流式播放")

    def stop_stream_playback(self):
        """
        停止流式播放

        安全地停止正在进行的流式播放，清空队列并等待播放线程结束。
        如果没有在播放中，此方法不会执行任何操作。
        """
        if not self.is_playing:
            logger.debug("流式播放未在运行中，忽略停止请求")
            return

        self.stop_event.set()
        if self.playback_thread and self.playback_thread.is_alive():
            self.playback_thread.join(timeout=1.0)
        self.is_playing = False

        # 清空队列
        while not self.stream_queue.empty():
            try:
                self.stream_queue.get_nowait()
            except queue.Empty:
                break

        logger.info("停止流式播放")

    def add_audio_chunk(self, audio_data: bytes):
        """
        添加音频数据块到播放队列

        将音频数据添加到流式播放队列中，等待播放线程处理。
        注意：此方法是线程安全的。

        Args:
            audio_data (bytes): 音频数据字节
        """
        self.stream_queue.put(audio_data)

    def _stream_playback_worker(self):
        """
        流式播放工作线程（内部方法）

        在单独的线程中运行，从队列获取音频数据并播放。
        使用临时文件作为中间存储来播放音频块。
        线程会在播放完所有队列数据后继续等待新数据，直到被显式停止。
        """
        with tempfile.TemporaryDirectory() as temp_dir:
            chunk_id = 0

            while not self.stop_event.is_set():
                try:
                    # 非阻塞方式获取数据，超时后继续循环检查停止事件
                    try:
                        audio_data = self.stream_queue.get(timeout=0.5)
                    except queue.Empty:
                        continue

                    # 如果没有数据，跳过
                    if not audio_data:
                        continue

                    # 创建临时文件并写入音频数据
                    temp_path = os.path.join(temp_dir, f"chunk_{chunk_id}.mp3")
                    with open(temp_path, "wb") as f:
                        f.write(audio_data)

                    # 播放当前块
                    try:
                        pygame.mixer.music.load(temp_path)
                        pygame.mixer.music.play()

                        # 等待播放完成或停止事件
                        while pygame.mixer.music.get_busy() and not self.stop_event.is_set():
                            time.sleep(0.1)
                    except Exception as e:
                        logger.error(f"播放音频块失败: {e}")

                    chunk_id += 1

                except Exception as e:
                    logger.error(f"流式播放线程异常: {e}")
                    if self.stop_event.is_set():
                        break
                    time.sleep(0.5)  # 出现异常时暂停一下


# 直接使用AudioPlayer的示例
if __name__ == "__main__":
    # 创建音频播放器
    audio_player = AudioPlayer()

    # 示例1：播放文件
    print("正在播放音频文件...")
    audio_player.play_file("example.mp3")  # 请确保此文件存在

    # 示例2：流式播放
    print("开始流式播放...")
    audio_player.start_stream_playback()

    # 模拟添加音频块
    # 通常这些数据会来自网络或其他源
    for i in range(5):
        print(f"添加音频块 {i}...")
        # 此处应为真实的音频数据
        # audio_player.add_audio_chunk(音频数据)
        time.sleep(1)

    # 停止流式播放
    print("停止流式播放...")
    audio_player.stop_stream_playback()
