"""
VolcanoASRClient - 火山引擎大模型流式语音识别客户端

基于WebSocket协议实现实时双向流式语音识别，支持以下功能：
1. 建立WebSocket连接并进行鉴权
2. 发送初始参数配置
3. 流式发送音频数据
4. 接收并解析识别结果
5. 自动处理连接状态和错误恢复

参考：火山引擎语音技术官方文档
"""

import json
import time
import uuid
import struct
import threading
import queue
import numpy as np
import websocket
from typing import Callable, Optional, Dict, List, Union, Any, Tuple

# 导入日志管理模块
try:
    from utils.logging_manager import get_logger
    logger = get_logger("VolcanoASRClient")
except ImportError:
    import logging
    logger = logging.getLogger("VolcanoASRClient")

# WebSocket协议常量
MESSAGE_TYPE_FULL_CLIENT_REQUEST = 0x1
MESSAGE_TYPE_AUDIO_ONLY_REQUEST = 0x2
MESSAGE_TYPE_FULL_SERVER_RESPONSE = 0x9
MESSAGE_TYPE_ERROR_MESSAGE = 0xF

# 消息标志
MESSAGE_FLAG_NONE = 0x0
MESSAGE_FLAG_LAST_PACKET = 0x2

# 序列化和压缩方法
SERIALIZATION_NONE = 0x0
SERIALIZATION_JSON = 0x1
COMPRESSION_NONE = 0x0
COMPRESSION_GZIP = 0x1


class VolcanoASRClient:
    """
    火山引擎大模型流式语音识别客户端

    实现基于WebSocket的实时双向流式语音识别
    """

    def __init__(self,
                 app_key: str,
                 access_key: str,
                 model_name: str = "bigmodel",
                 sample_rate: int = 16000,
                 channels: int = 1,
                 format: str = "pcm",
                 audio_chunk_duration: float = 0.2,  # 200ms
                 enable_punctuation: bool = True,
                 enable_itn: bool = True,
                 callback: Optional[Callable[[str, bool], None]] = None,
                 connection_timeout: float = 3.0,  # 减少连接超时时间
                 reconnect_attempts: int = 1000000,
                 reconnect_delay: float = 2.0,  # 2秒重连延迟，更稳定
                 heartbeat_interval: float = 30.0):
        """
        初始化火山引擎ASR客户端

        Args:
            app_key: 火山引擎控制台获取的APP ID
            access_key: 火山引擎控制台获取的Access Token
            model_name: 使用的语音识别模型名称，默认是"bigmodel"
            sample_rate: 音频采样率，默认16000Hz
            channels: 音频通道数，默认1（单声道）
            format: 音频格式，默认"pcm"
            audio_chunk_duration: 音频分块持续时间（秒），建议为0.2秒（200ms）
            enable_punctuation: 是否启用标点符号，默认True
            enable_itn: 是否启用文本规范化，默认True
            callback: 识别结果回调函数，接收识别文本和是否为最终结果标志
            connection_timeout: 连接超时时间（秒），默认10秒
            reconnect_attempts: 重连尝试次数，默认3次
            reconnect_delay: 重连延迟时间（秒），默认2秒
            heartbeat_interval: 心跳包发送间隔（秒），默认30秒
        """
        # 检查必要参数
        if not app_key:
            raise ValueError("必须提供火山引擎的APP ID")
        if not access_key:
            raise ValueError("必须提供火山引擎的Access Token")

        # API凭据
        self.app_key = app_key
        self.access_key = access_key

        # 音频参数
        self.sample_rate = sample_rate
        self.channels = channels
        self.format = format
        self.audio_chunk_duration = audio_chunk_duration
        self.audio_chunk_size = int(sample_rate * audio_chunk_duration)  # 每个音频块的样本数

        # 模型和识别参数
        self.model_name = model_name
        self.enable_punctuation = enable_punctuation
        self.enable_itn = enable_itn

        # WebSocket连接参数
        self.ws_url = "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel"  # 双向流式模式
        self.resource_id = "volc.bigasr.sauc.duration"  # 小时版，也可以使用并发版：volc.bigasr.sauc.concurrent

        # 连接和重试参数
        self.connection_timeout = connection_timeout
        self.reconnect_attempts = reconnect_attempts
        self.reconnect_delay = reconnect_delay
        self.heartbeat_interval = heartbeat_interval
        self.current_reconnect_attempt = 0

        # 状态变量
        self.is_connected = False
        self.is_recognizing = False
        self.results = []
        self.last_result = ""
        self.connect_id = str(uuid.uuid4())
        self.sequence_number = 0
        self.last_error = None
        self.last_activity_time = time.time()

        # 线程和同步
        self.ws = None
        self.ws_thread = None
        self.audio_queue = queue.Queue()
        self.audio_sender_thread = None
        self.heartbeat_thread = None
        self.lock = threading.RLock()
        self.result_callback = callback
        self.reconnect_event = threading.Event()
        self.stop_event = threading.Event()
        self.reconnecting = False  # 防止多个重连线程同时运行

    def connect(self) -> bool:
        """
        连接到火山引擎ASR服务

        Returns:
            bool: 是否成功连接
        """
        with self.lock:
            if self.is_connected:
                logger.warning("已经连接到ASR服务")
                return True

            # 重置重连计数器
            self.current_reconnect_attempt = 0
            self.reconnect_event.clear()
            self.stop_event.clear()

            return self._try_connect()

    def _try_connect(self) -> bool:
        """
        尝试连接到火山引擎ASR服务，包含重试逻辑

        Returns:
            bool: 是否成功连接
        """
        while self.current_reconnect_attempt <= self.reconnect_attempts and not self.stop_event.is_set():
            try:
                if self.current_reconnect_attempt > 0:
                    # logger.info(f"尝试重新连接 (第 {self.current_reconnect_attempt}/{self.reconnect_attempts} 次)...")
                    # 等待重连延迟时间
                    time.sleep(self.reconnect_delay)
              

                # 准备WebSocket连接的HTTP请求头
                headers = {
                    "X-Api-App-Key": self.app_key,
                    "X-Api-Access-Key": self.access_key,
                    "X-Api-Resource-Id": self.resource_id,
                    "X-Api-Connect-Id": self.connect_id
                }

                # 创建WebSocket连接
                self.ws = websocket.WebSocketApp(
                    self.ws_url,
                    header=headers,
                    on_open=self._on_open,
                    on_message=self._on_message,
                    on_error=self._on_error,
                    on_close=self._on_close
                )

                # 启动WebSocket线程
                self.ws_thread = threading.Thread(
                    target=self.ws.run_forever,
                    daemon=True
                )
                self.ws_thread.start()

                # 等待连接建立 - 优化检查频率
                connection_start_time = time.time()
                while not self.is_connected and (time.time() - connection_start_time) < self.connection_timeout:
                    if self.stop_event.is_set():
                        logger.info("连接过程被中断")
                        return False
                    time.sleep(0.1)  # 减少检查间隔，提高响应速度

                if self.is_connected:
                    # 连接成功，启动心跳线程
                    self._start_heartbeat_thread()
                    # logger.info("成功连接到火山引擎ASR服务")
                    return True
                else:
                    logger.error(f"连接到火山引擎ASR服务超时 (超过 {self.connection_timeout} 秒)")
                    self.current_reconnect_attempt += 1

            except Exception as e:
                self.last_error = str(e)
                logger.error(f"连接到火山引擎ASR服务失败: {str(e)}")
                self.current_reconnect_attempt += 1

        logger.error(f"连接失败，已达到最大重试次数 ({self.reconnect_attempts})")
        return False

    def _start_heartbeat_thread(self):
        """
        启动心跳线程，定期发送心跳包保持连接活跃
        """
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            return

        self.heartbeat_thread = threading.Thread(
            target=self._heartbeat_worker,
            daemon=True
        )
        self.heartbeat_thread.start()
        logger.debug("心跳线程已启动")

    def _heartbeat_worker(self):
        """
        心跳线程工作函数，定期发送心跳包
        """
        while self.is_connected and not self.stop_event.is_set():
            try:
                # 检查最后活动时间，如果超过心跳间隔，发送心跳包
                if time.time() - self.last_activity_time > self.heartbeat_interval:
                    self._send_heartbeat()
                    self.last_activity_time = time.time()

                # 等待一段时间
                time.sleep(5.0)  # 每5秒检查一次

            except Exception as e:
                logger.error(f"心跳线程出错: {str(e)}")
                time.sleep(1.0)  # 出错时短暂等待

        logger.debug("心跳线程已结束")

    def _send_heartbeat(self) -> bool:
        """
        发送心跳包

        Returns:
            bool: 是否成功发送
        """
        try:
            if not self.ws or not self.ws.sock or not self.ws.sock.connected:
                logger.warning("WebSocket连接已关闭，无法发送心跳包")
                return False

            # 创建一个空的ping帧
            self.ws.sock.ping()
            logger.debug("已发送心跳包")
            return True

        except Exception as e:
            logger.error(f"发送心跳包时出错: {str(e)}")
            return False

    def disconnect(self) -> bool:
        """
        断开与火山引擎ASR服务的连接

        Returns:
            bool: 是否成功断开连接
        """
        with self.lock:
            if not self.is_connected:
                logger.warning("当前未连接到ASR服务")
                return True

            try:
                logger.info("正在断开与火山引擎ASR服务的连接...")

                # 设置停止事件，阻止重连
                self.stop_event.set()

                # 如果正在识别，先结束识别
                if self.is_recognizing:
                    self.stop_recognition()

                # 关闭WebSocket连接
                if self.ws:
                    self.ws.close()
                    self.ws = None

                # 快速清理线程 - 减少等待时间
                if self.ws_thread and self.ws_thread.is_alive():
                    self.ws_thread.join(timeout=1.0)  # 减少到1秒
                    if self.ws_thread.is_alive():
                        logger.warning("WebSocket线程未能在1秒内结束，强制继续")

                if self.audio_sender_thread and self.audio_sender_thread.is_alive():
                    self.audio_sender_thread.join(timeout=0.5)  # 减少到0.5秒
                    if self.audio_sender_thread.is_alive():
                        logger.warning("音频发送线程未能在0.5秒内结束，强制继续")

                # 停止心跳线程
                if self.heartbeat_thread and self.heartbeat_thread.is_alive():
                    self.heartbeat_thread.join(timeout=0.5)  # 减少到0.5秒

                # 清理状态
                self.is_connected = False
                self.is_recognizing = False
                self.ws_thread = None
                self.audio_sender_thread = None
                self.heartbeat_thread = None

                logger.info("已断开与火山引擎ASR服务的连接")
                return True

            except Exception as e:
                logger.error(f"断开连接时出错: {str(e)}")
                self.is_connected = False
                return False

    def start_recognition(self, callback: Optional[Callable[[str, bool], None]] = None) -> bool:
        """
        启动语音识别会话

        Args:
            callback: 识别结果回调函数，接收识别文本和是否为最终结果标志

        Returns:
            bool: 是否成功启动识别会话
        """
        with self.lock:
            if not self.is_connected:
                logger.error("未连接到ASR服务，请先调用connect()")
                return False

            if self.is_recognizing:
                logger.warning("已经在进行识别，请先调用stop_recognition()")
                return False

            try:
                # 更新回调函数
                if callback:
                    self.result_callback = callback

                # 清空队列和结果
                while not self.audio_queue.empty():
                    try:
                        self.audio_queue.get_nowait()
                    except queue.Empty:
                        break

                self.results = []
                self.last_result = ""
                self.sequence_number = 0

                # 发送初始化参数
                success = self._send_init_parameters()
                if not success:
                    logger.error("发送初始化参数失败")
                    return False

                # 标记为识别状态
                self.is_recognizing = True

                # 启动音频发送线程
                self.audio_sender_thread = threading.Thread(
                    target=self._audio_sender_thread,
                    daemon=True
                )
                self.audio_sender_thread.start()

                # logger.info("成功启动语音识别会话")
                return True

            except Exception as e:
                logger.error(f"启动语音识别会话失败: {str(e)}")
                return False

    def stop_recognition(self) -> bool:
        """
        停止语音识别会话

        Returns:
            bool: 是否成功停止识别会话
        """
        with self.lock:
            if not self.is_connected:
                logger.warning("未连接到ASR服务")
                return False

            if not self.is_recognizing:
                # logger.warning("当前没有进行识别会话")
                return False

            try:
                # logger.info("正在停止语音识别会话...")

                # 发送结束标志（空的音频数据包，标记为最后一包）
                success = self._send_audio_end_signal()

                # 等待一段时间接收最后的识别结果
                time.sleep(1.0)

                # 标记识别结束
                self.is_recognizing = False

                # 等待音频发送线程结束
                if self.audio_sender_thread and self.audio_sender_thread.is_alive():
                    self.audio_sender_thread.join(timeout=3.0)

                # logger.info("已停止语音识别会话")
                return True

            except Exception as e:
                logger.error(f"停止识别会话时出错: {str(e)}")
                self.is_recognizing = False
                return False

    def send_audio(self, audio_data: Union[bytes, np.ndarray]) -> bool:
        """
        发送音频数据进行识别

        Args:
            audio_data: 音频数据，可以是bytes或numpy数组
                如果是numpy数组，应为float32类型，范围在[-1, 1]内

        Returns:
            bool: 是否成功发送数据
        """
        # logger.info(f"self.is_connected{self.is_connected},self.is_recognizing{self.is_recognizing}")
        if not self.is_connected or not self.is_recognizing:
            # logger.warning("未连接到ASR服务或未启动识别会话")
            return False

        try:
            # 如果是numpy数组，转换为bytes
            if isinstance(audio_data, np.ndarray):
                # 确保数据是float32类型
                if audio_data.dtype != np.float32:
                    audio_data = audio_data.astype(np.float32)

                # 确保数据范围在[-1, 1]内
                max_value = np.max(np.abs(audio_data))
                if max_value > 1.0:
                    audio_data = audio_data / max_value

                # 转换为16位整数
                audio_data = (audio_data * 32767).astype(np.int16)

                # 转换为bytes
                audio_data = audio_data.tobytes()

            # 将音频数据放入队列
            self.audio_queue.put(audio_data)
            return True

        except Exception as e:
            logger.error(f"发送音频数据时出错: {str(e)}")
            return False

    def get_results(self) -> List[str]:
        """
        获取所有语音识别结果

        Returns:
            List[str]: 所有识别结果的列表
        """
        with self.lock:
            return self.results.copy()

    def get_last_result(self) -> str:
        """
        获取最近一次的语音识别结果

        Returns:
            str: 最近一次的识别结果
        """
        with self.lock:
            return self.last_result

    def clear_results(self) -> None:
        """
        清空所有识别结果
        """
        with self.lock:
            self.results = []
            self.last_result = ""

    def _on_open(self, ws):
        """
        WebSocket连接建立回调

        Args:
            ws: WebSocket实例
        """
        # logger.info("WebSocket连接已建立")
        self.is_connected = True

    def _on_message(self, ws, message):
        """
        WebSocket消息接收回调

        Args:
            ws: WebSocket实例
            message: 接收到的消息
        """
        try:
            # 更新最后活动时间
            self.last_activity_time = time.time()

            # 确保消息是二进制
            if not isinstance(message, bytes):
                logger.warning(f"接收到非二进制消息: {message}")
                return

            # 消息至少要有4字节的header
            if len(message) < 4:
                logger.warning(f"消息太短: {len(message)}字节")
                return

            # 解析header
            header = message[:4]

            # 提取消息类型和标志
            message_type = (header[1] >> 4) & 0x0F
            message_flags = header[1] & 0x0F

            # 处理不同类型的消息
            if message_type == MESSAGE_TYPE_FULL_SERVER_RESPONSE:
                # 解析并处理识别结果
                self._parse_asr_response(message, message_flags)
            elif message_type == MESSAGE_TYPE_ERROR_MESSAGE:
                # 解析错误消息
                self._parse_error_message(message)

                # 检查是否需要重连
                if self.is_connected and not self.stop_event.is_set():
                    # 标记为断开连接
                    self.is_connected = False

                    # 如果正在识别，标记为停止识别
                    if self.is_recognizing:
                        self.is_recognizing = False

                    # 触发重连事件
                    self.reconnect_event.set()

                    # 启动重连线程
                    threading.Thread(
                        target=self._handle_reconnect,
                        daemon=True
                    ).start()
            else:
                logger.warning(f"未知消息类型: {message_type}")

        except Exception as e:
            logger.error(f"处理WebSocket消息时出错: {str(e)}")

    def _on_error(self, ws, error):
        """
        WebSocket错误回调

        Args:
            ws: WebSocket实例
            error: 错误信息
        """
        logger.error(f"WebSocket错误: {str(error)}")
        self.last_error = str(error)

        # 检查是否需要重连
        if self.is_connected and not self.stop_event.is_set() and not self.reconnecting:
            # 标记为断开连接
            self.is_connected = False

            # 如果正在识别，标记为停止识别
            if self.is_recognizing:
                self.is_recognizing = False

            # 触发重连事件
            self.reconnect_event.set()

            # 启动重连线程（防止多个重连线程同时运行）
            self.reconnecting = True
            threading.Thread(
                target=self._handle_reconnect,
                daemon=True
            ).start()

    def _on_close(self, ws, close_status_code=None, close_msg=None):
        """
        WebSocket关闭回调

        Args:
            ws: WebSocket实例
            close_status_code: 关闭状态码（可选）
            close_msg: 关闭消息（可选）
        """
        # 处理参数缺失的情况
        if close_status_code is None:
            close_status_code = "未知"
        if close_msg is None:
            close_msg = "连接关闭"

        # logger.info(f"WebSocket连接已关闭: {close_status_code} - {close_msg}")

        # 标记为断开连接
        self.is_connected = False
        self.is_recognizing = False

        # 检查是否是主动关闭
        if not self.stop_event.is_set() and not self.reconnect_event.is_set():
            # 触发重连事件
            self.reconnect_event.set()

            # 启动重连线程
            threading.Thread(
                target=self._handle_reconnect,
                daemon=True
            ).start()

    def _handle_reconnect(self):
        """
        处理重连逻辑
        """
        try:
            # 等待一段时间再重连
            time.sleep(self.reconnect_delay)

            # 检查是否已经停止
            if self.stop_event.is_set():
                logger.info("已停止，不再尝试重连")
                return

            # logger.info("尝试重新连接...")

            # 尝试重新连接
            self.current_reconnect_attempt += 1
            if self.current_reconnect_attempt <= self.reconnect_attempts:
                # 尝试重新连接
                success = self._try_connect()

                # 重连成功后，不自动重启识别，等待前端重新发起
                # 这样可以避免协议冲突和状态混乱
                if success:
                    # logger.info("重连成功，等待新的识别请求...")
                    # 重置状态，确保下次识别可以正常启动
                    self.is_recognizing = False
                    self.sequence_number = 0
                    self.results = []
                    self.last_result = ""
            else:
                logger.error(f"重连失败，已达到最大重试次数 ({self.reconnect_attempts})")

        except Exception as e:
            logger.error(f"重连过程中出错: {str(e)}")

        finally:
            # 清除重连事件
            self.reconnect_event.clear()

    def _create_header(self, message_type, message_flags, serialization, compression):
        """
        创建WebSocket消息header

        Args:
            message_type: 消息类型
            message_flags: 消息标志
            serialization: 序列化方法
            compression: 压缩方法

        Returns:
            bytes: 4字节的header
        """
        header = bytearray(4)

        # 第一个字节: 协议版本 (4位) + header大小 (4位)
        header[0] = (0x01 << 4) | 0x01  # 版本1，header大小为4字节

        # 第二个字节: 消息类型 (4位) + 消息标志 (4位)
        header[1] = (message_type << 4) | message_flags

        # 第三个字节: 序列化方法 (4位) + 压缩方法 (4位)
        header[2] = (serialization << 4) | compression

        # 第四个字节: 保留字段
        header[3] = 0x00

        return bytes(header)

    def _send_init_parameters(self) -> bool:
        """
        发送初始化参数

        Returns:
            bool: 是否成功发送
        """
        try:
            # 构造请求参数
            params = {
                "user": {
                    "uid": str(uuid.uuid4())
                },
                "audio": {
                    "format": self.format,
                    "rate": self.sample_rate,
                    "bits": 16,  # 16位采样
                    "channel": self.channels
                },
                "request": {
                    "model_name": self.model_name,
                    "enable_itn": self.enable_itn,
                    "enable_punc": self.enable_punctuation,
                    "enable_ddc": True  # 启用语义顺滑
                }
            }

            # 将参数转换为JSON字符串
            params_json = json.dumps(params)
            payload = params_json.encode('utf-8')

            # 创建header
            header = self._create_header(
                message_type=MESSAGE_TYPE_FULL_CLIENT_REQUEST,
                message_flags=MESSAGE_FLAG_NONE,
                serialization=SERIALIZATION_JSON,
                compression=COMPRESSION_NONE
            )

            # 添加payload大小（4字节无符号整数，大端序）
            payload_size = struct.pack("!I", len(payload))

            # 发送消息
            if self.ws and self.ws.sock and self.ws.sock.connected:
                self.ws.send(header + payload_size + payload, websocket.ABNF.OPCODE_BINARY)
                # logger.info("已发送初始化参数")
                return True
            else:
                logger.error("WebSocket连接未就绪")
                return False

        except Exception as e:
            logger.error(f"发送初始化参数时出错: {str(e)}")
            return False

    def _send_audio_chunk(self, audio_chunk: bytes, is_final: bool = False) -> bool:
        """
        发送单个音频块

        Args:
            audio_chunk: 音频数据块
            is_final: 是否为最后一个音频块

        Returns:
            bool: 是否成功发送
        """
        try:
            # 检查WebSocket连接状态
            if not self.ws or not self.ws.sock or not self.ws.sock.connected:
                # logger.warning("WebSocket连接已关闭，无法发送音频数据")
                return False

            # 检查音频数据
            if not audio_chunk or len(audio_chunk) == 0:
                logger.warning("音频数据块为空")
                return False

            # 设置消息标志
            message_flags = MESSAGE_FLAG_LAST_PACKET if is_final else MESSAGE_FLAG_NONE

            # 创建header
            header = self._create_header(
                message_type=MESSAGE_TYPE_AUDIO_ONLY_REQUEST,
                message_flags=message_flags,
                serialization=SERIALIZATION_NONE,
                compression=COMPRESSION_NONE
            )

            # 添加payload大小
            payload_size = struct.pack("!I", len(audio_chunk))

            # 发送消息
            self.ws.send(header + payload_size + audio_chunk, websocket.ABNF.OPCODE_BINARY)

            # 递增序列号
            self.sequence_number += 1

            return True

        except Exception as e:
            # logger.error(f"发送音频块时出错: {str(e)}")
            return False

    def _send_audio_end_signal(self) -> bool:
        """
        发送音频结束信号

        Returns:
            bool: 是否成功发送
        """
        try:
            # 创建header，标记为最后一包
            header = self._create_header(
                message_type=MESSAGE_TYPE_AUDIO_ONLY_REQUEST,
                message_flags=MESSAGE_FLAG_LAST_PACKET,
                serialization=SERIALIZATION_NONE,
                compression=COMPRESSION_NONE
            )

            # 空的payload
            payload_size = struct.pack("!I", 0)

            # 发送消息
            if self.ws and self.ws.sock and self.ws.sock.connected:
                self.ws.send(header + payload_size, websocket.ABNF.OPCODE_BINARY)
                # logger.info("已发送音频结束信号")
                return True
            else:
                logger.error("WebSocket连接未就绪")
                return False

        except Exception as e:
            logger.error(f"发送音频结束信号时出错: {str(e)}")
            return False

    def _audio_sender_thread(self):
        """
        音频发送线程

        从队列中获取音频数据并发送到ASR服务
        """
        # logger.info("音频发送线程已启动")

        # 错误计数器和最大重试次数
        error_count = 0
        max_errors = 5

        while self.is_connected and self.is_recognizing and not self.stop_event.is_set():
            try:
                # 从队列获取音频数据
                try:
                    audio_chunk = self.audio_queue.get(timeout=0.5)
                except queue.Empty:
                    continue

                # 更新最后活动时间
                self.last_activity_time = time.time()

                # 发送音频块
                retry_count = 0
                max_retries = 1000000
                success = False

                while retry_count < max_retries and not success:
                    if retry_count > 0:
                        logger.debug(f"重试发送音频块 (第 {retry_count}/{max_retries} 次)")
                        time.sleep(0.2)  # 短暂等待后重试

                    success = self._send_audio_chunk(audio_chunk)

                    if success:
                        # 重置错误计数器
                        error_count = 0
                        break

                    retry_count += 1

                if not success:
                    logger.warning(f"发送音频块失败，已尝试 {max_retries} 次")
                    error_count += 1

                    # 如果连续错误次数过多，触发重连
                    if error_count >= max_errors:
                        logger.error(f"连续 {error_count} 次发送失败，尝试重新连接")

                        # 标记为断开连接
                        self.is_connected = False
                        self.is_recognizing = False

                        # 触发重连事件
                        self.reconnect_event.set()

                        # 启动重连线程
                        threading.Thread(
                            target=self._handle_reconnect,
                            daemon=True
                        ).start()

                        break

            except Exception as e:
                logger.error(f"音频发送线程出错: {str(e)}")
                error_count += 1

                # 如果连续错误次数过多，触发重连
                if error_count >= max_errors:
                    logger.error(f"连续 {error_count} 次错误，尝试重新连接")

                    # 标记为断开连接
                    self.is_connected = False
                    self.is_recognizing = False

                    # 触发重连事件
                    self.reconnect_event.set()

                    # 启动重连线程
                    threading.Thread(
                        target=self._handle_reconnect,
                        daemon=True
                    ).start()

                    break

                # 短暂等待后继续
                time.sleep(0.5)

        # logger.info("音频发送线程已结束")

    def _parse_asr_response(self, message: bytes, message_flags: int):
        """
        解析ASR响应

        Args:
            message: 二进制消息
            message_flags: 消息标志
        """
        try:
            # 解析header后的数据
            offset = 4

            # 检查是否包含序列号
            has_sequence = (message_flags & 0x01) == 0x01
            if has_sequence:
                if len(message) < offset + 4:
                    logger.warning("消息中应包含序列号但长度不足")
                    return

                sequence_bytes = message[offset:offset+4]
                sequence = struct.unpack("!I", sequence_bytes)[0]
                offset += 4
                logger.debug(f"消息序列号: {sequence}")

            # 解析payload大小
            if len(message) < offset + 4:
                logger.warning("消息长度不足，无法读取payload大小")
                return

            payload_size_bytes = message[offset:offset+4]
            payload_size = struct.unpack("!I", payload_size_bytes)[0]
            offset += 4

            # 检查消息长度是否符合预期
            if len(message) < offset + payload_size:
                logger.warning(f"消息长度不足，期望{offset + payload_size}字节，实际{len(message)}字节")
                return

            # 提取payload
            payload = message[offset:offset + payload_size]

            # 解析JSON响应
            try:
                response_json = json.loads(payload)
                self._handle_asr_result(response_json, message_flags)
            except json.JSONDecodeError as e:
                logger.error(f"解析JSON响应失败: {str(e)}")

        except Exception as e:
            logger.error(f"解析ASR响应时出错: {str(e)}")

    def _parse_error_message(self, message: bytes):
        """
        解析错误消息

        Args:
            message: 二进制消息
        """
        try:
            # 解析header后的数据
            offset = 4

            # 解析错误码
            if len(message) < offset + 4:
                logger.warning("消息长度不足，无法读取错误码")
                return

            error_code_bytes = message[offset:offset+4]
            error_code = struct.unpack("!I", error_code_bytes)[0]
            offset += 4

            # 解析错误消息大小
            if len(message) < offset + 4:
                logger.warning("消息长度不足，无法读取错误消息大小")
                return

            error_message_size_bytes = message[offset:offset+4]
            error_message_size = struct.unpack("!I", error_message_size_bytes)[0]
            offset += 4

            # 检查消息长度是否符合预期
            if len(message) < offset + error_message_size:
                logger.warning(f"消息长度不足，期望{offset + error_message_size}字节，实际{len(message)}字节")
                return

            # 提取错误消息
            error_message_bytes = message[offset:offset + error_message_size]
            error_message = error_message_bytes.decode('utf-8')

            # logger.error(f"火山引擎ASR错误: 错误码={error_code}, 错误消息={error_message}")

        except Exception as e:
            logger.error(f"解析错误消息时出错: {str(e)}")

    def _handle_asr_result(self, response: Dict[str, Any], message_flags: int):
        """
        处理ASR结果

        Args:
            response: ASR响应JSON
            message_flags: 消息标志
        """
        try:
            # 检查是否有result字段
            if 'result' not in response:
                logger.debug("ASR响应中没有result字段")
                return

            result = response['result']

            # 获取识别文本
            text = result.get('text', '')

            # 判断是否为最终结果
            is_final = (message_flags & 0x02) == 0x02  # 检查bit1是否设置（表示最后一包）

            # 如果文本为空，不处理
            if not text:
                return

            # logger.info(f"🎤 ASR结果: {text}, 是否最终结果: {is_final}")

            with self.lock:
                self.last_result = text
                if is_final and text not in self.results:
                    self.results.append(text)

            # 回调通知
            if self.result_callback:
                try:
                    self.result_callback(text, is_final)
                except Exception as e:
                    logger.error(f"执行回调函数时出错: {str(e)}")

        except Exception as e:
            logger.error(f"处理ASR结果时出错: {str(e)}")