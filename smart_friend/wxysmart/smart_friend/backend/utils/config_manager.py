"""
配置管理模块

负责读取、解析和管理应用程序配置，提供安全的配置访问方法。
支持从配置文件和环境变量获取配置值，优先使用环境变量。
"""

import os
import configparser
import logging
from typing import Any, Dict, Optional, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ConfigManager")

# 默认配置文件路径
DEFAULT_CONFIG_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'config.ini')


class ConfigManager:
    """
    配置管理类
    
    负责加载、解析和提供对应用程序配置的访问。
    支持从文件和环境变量加载配置，并提供类型转换功能。
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为项目根目录下的config/config.ini
        """
        self.config_path = config_path or DEFAULT_CONFIG_PATH
        # 禁用插值功能，避免日志格式字符串等特殊格式导致的问题
        self.config = configparser.ConfigParser(interpolation=None)
        self.load_config()
    
    def load_config(self) -> bool:
        """
        加载配置文件
        
        如果配置文件不存在，尝试创建示例配置文件
        
        Returns:
            bool: 是否成功加载配置
        """
        try:
            # 检查配置文件是否存在
            if not os.path.exists(self.config_path):
                # 如果目标配置文件不存在，检查是否存在示例配置文件
                example_config_path = f"{self.config_path}.example"
                if os.path.exists(example_config_path):
                    logger.warning(f"配置文件 {self.config_path} 不存在，将基于示例文件创建")
                    
                    # 确保目标目录存在
                    config_dir = os.path.dirname(self.config_path)
                    if not os.path.exists(config_dir):
                        os.makedirs(config_dir)
                    
                    # 复制示例文件
                    with open(example_config_path, 'r', encoding='utf-8') as example_file:
                        with open(self.config_path, 'w', encoding='utf-8') as config_file:
                            config_file.write(example_file.read())
                    
                    logger.info(f"已根据示例文件创建配置文件: {self.config_path}")
                else:
                    logger.error(f"配置文件 {self.config_path} 不存在，且找不到示例文件")
                    return False
            
            # 读取配置文件
            self.config.read(self.config_path, encoding='utf-8')
            logger.info(f"成功加载配置文件: {self.config_path}")
            return True
        
        except Exception as e:
            logger.error(f"加载配置文件时出错: {str(e)}")
            return False
    
    def get(self, section: str, key: str, default: Any = None, env_var: Optional[str] = None) -> str:
        """
        获取配置值
        
        先检查环境变量，再检查配置文件，最后使用默认值
        
        Args:
            section: 配置节名称
            key: 配置键名称
            default: 默认值，如果配置不存在则返回此值
            env_var: 对应的环境变量名称，如不指定则自动构造为 LIFEBUDDY_{SECTION}_{KEY} 的格式
            
        Returns:
            str: 配置值
        """
        # 构造环境变量名称
        if env_var is None:
            env_var = f"LIFEBUDDY_{section.upper()}_{key.upper()}"
        
        # 首先检查环境变量
        env_value = os.environ.get(env_var)
        if env_value is not None:
            return env_value
        
        # 其次检查配置文件
        try:
            return self.config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return default
    
    def get_int(self, section: str, key: str, default: Optional[int] = None, env_var: Optional[str] = None) -> Optional[int]:
        """获取整型配置值"""
        value = self.get(section, key, default, env_var)
        if value is None:
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            logger.warning(f"配置项 {section}.{key} 值 '{value}' 不是有效的整数，使用默认值 {default}")
            return default
    
    def get_float(self, section: str, key: str, default: Optional[float] = None, env_var: Optional[str] = None) -> Optional[float]:
        """获取浮点型配置值"""
        value = self.get(section, key, default, env_var)
        if value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            logger.warning(f"配置项 {section}.{key} 值 '{value}' 不是有效的浮点数，使用默认值 {default}")
            return default
    
    def get_bool(self, section: str, key: str, default: Optional[bool] = None, env_var: Optional[str] = None) -> Optional[bool]:
        """获取布尔型配置值"""
        value = self.get(section, key, default, env_var)
        if value is None:
            return None
        
        # 转换为小写
        if isinstance(value, str):
            value = value.lower()
        
        # 判断是否为真值
        if value in ('true', 'yes', '1', 'y', 'on', True, 1):
            return True
        # 判断是否为假值
        elif value in ('false', 'no', '0', 'n', 'off', False, 0):
            return False
        else:
            logger.warning(f"配置项 {section}.{key} 值 '{value}' 不是有效的布尔值，使用默认值 {default}")
            return default
    
    def get_list(self, section: str, key: str, default: Optional[list] = None, env_var: Optional[str] = None, 
                 separator: str = ',') -> Optional[list]:
        """获取列表型配置值，以分隔符分割字符串"""
        value = self.get(section, key, default, env_var)
        if value is None:
            return default if default is not None else []
        
        if isinstance(value, str):
            # 分割字符串并去除每项的前后空格
            return [item.strip() for item in value.split(separator) if item.strip()]
        return default if default is not None else []
    
    def get_all(self) -> Dict[str, Dict[str, str]]:
        """
        获取所有配置项
        
        Returns:
            Dict: 包含所有配置项的字典
        """
        result = {}
        for section in self.config.sections():
            result[section] = {}
            for key, value in self.config.items(section):
                result[section][key] = value
        return result
    
    def set(self, section: str, key: str, value: str) -> bool:
        """
        设置配置值并保存到文件
        
        Args:
            section: 配置节名称
            key: 配置键名称
            value: 配置值
            
        Returns:
            bool: 是否成功设置
        """
        try:
            # 确保节存在
            if not self.config.has_section(section):
                self.config.add_section(section)
            
            # 设置值
            self.config.set(section, key, str(value))
            
            # 保存到文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                self.config.write(f)
            
            logger.info(f"成功更新配置 {section}.{key} = {value}")
            return True
        
        except Exception as e:
            logger.error(f"设置配置值时出错: {str(e)}")
            return False


# 单例模式实现
_config_instance = None

def get_config(config_path: Optional[str] = None) -> ConfigManager:
    """
    获取配置管理器实例（单例模式）
    
    Args:
        config_path: 配置文件路径，仅在首次调用时有效
        
    Returns:
        ConfigManager: 配置管理器实例
    """
    global _config_instance
    if _config_instance is None:
        _config_instance = ConfigManager(config_path)
    return _config_instance


if __name__ == "__main__":
    # 测试配置管理器
    config = get_config()
    print("API配置:")
    print(f"ASR App Key: {config.get('API', 'asr_app_key', '默认值')}")
    print(f"Vision API Key: {config.get('API', 'vision_api_key', '默认值')}")
    print(f"TTS App ID: {config.get('API', 'tts_app_id', '默认值')}")
    
    print("\n系统配置:")
    print(f"调试模式: {config.get_bool('SYSTEM', 'debug', False)}")
    print(f"最大历史记录: {config.get_int('DIALOGUE', 'max_history', 10)}")