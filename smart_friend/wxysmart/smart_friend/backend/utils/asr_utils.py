"""
ASR Utils - 模块化语音识别工具集

本模块采用面向对象设计和单一职责原则，将ASR相关功能整合为一个统一的工具集。
每个类负责单一职责，通过继承和组合实现功能扩展。

主要组件:
1. ASRClientBase - ASR客户端基类
2. VolcanoASRClient - 火山引擎ASR客户端
3. DoubaoASRClient - 豆包ASR客户端  
4. ASRManager - ASR管理器
5. WebVoiceRecognitionService - Web语音识别服务
6. ASRTextProcessor - 文本处理器
7. ASRAudioProcessor - 音频处理器
8. ASRConnectionManager - 连接管理器

设计原则:
- 单一职责原则：每个类只负责一个功能
- 开闭原则：对扩展开放，对修改关闭
- 依赖倒置原则：依赖抽象而非具体实现
- 接口隔离原则：客户端不应依赖它不需要的接口
"""

import json
import time
import uuid
import struct
import threading
import queue
import numpy as np
import re
from abc import ABC, abstractmethod
from typing import Callable, Optional, Dict, List, Union, Any, Tuple
from datetime import datetime
from collections import defaultdict

# 可选依赖导入
try:
    import websocket
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False
    print("警告: websocket-client 未安装，WebSocket功能将不可用")

try:
    import sounddevice as sd
    SOUNDDEVICE_AVAILABLE = True
except ImportError:
    SOUNDDEVICE_AVAILABLE = False
    print("警告: sounddevice 未安装，音频采集功能将不可用")

try:
    import jieba
    import jieba.posseg as posseg
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    print("警告: jieba 未安装，中文分词功能将不可用")

# 导入日志管理模块
try:
    from utils.logging_manager import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

# 导入配置管理模块
try:
    from utils.config_manager import get_config
except ImportError:
    import configparser
    def get_config(path=None):
        """简单的配置读取函数"""
        if not path:
            return {}
        try:
            config = configparser.ConfigParser()
            config.read(path)
            return config
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            return {}

# 获取日志记录器
logger = get_logger("ASRUtils")


# ==================== 基础抽象类 ====================

class ASRClientBase(ABC):
    """
    ASR客户端基类
    
    定义所有ASR客户端必须实现的接口，遵循接口隔离原则
    """
    
    def __init__(self, app_key: str, access_key: str, **kwargs):
        """
        初始化ASR客户端基类
        
        Args:
            app_key: 应用密钥
            access_key: 访问密钥
            **kwargs: 其他配置参数
        """
        if not app_key:
            raise ValueError("必须提供APP ID")
        if not access_key:
            raise ValueError("必须提供Access Token")
            
        self.app_key = app_key
        self.access_key = access_key
        self.is_connected = False
        self.is_recognizing = False
        self.results = []
        self.last_result = ""
        self.lock = threading.RLock()
        
    @abstractmethod
    def connect(self) -> bool:
        """连接到ASR服务"""
        pass
        
    @abstractmethod
    def disconnect(self) -> bool:
        """断开ASR服务连接"""
        pass
        
    @abstractmethod
    def start_recognition(self, callback: Optional[Callable[[str, bool], None]] = None) -> bool:
        """启动语音识别"""
        pass
        
    @abstractmethod
    def stop_recognition(self) -> bool:
        """停止语音识别"""
        pass
        
    @abstractmethod
    def send_audio(self, audio_data: Union[bytes, np.ndarray]) -> bool:
        """发送音频数据"""
        pass
        
    def get_results(self) -> List[str]:
        """获取所有识别结果"""
        with self.lock:
            return self.results.copy()
            
    def get_last_result(self) -> str:
        """获取最近一次识别结果"""
        with self.lock:
            return self.last_result
            
    def clear_results(self) -> None:
        """清空识别结果"""
        with self.lock:
            self.results = []
            self.last_result = ""


class ASRConnectionManager:
    """
    ASR连接管理器

    负责管理WebSocket连接的建立、维护和重连
    """

    def __init__(self, url: str, headers: Dict[str, str],
                 reconnect_attempts: int = 3, reconnect_delay: float = 2.0):
        """
        初始化连接管理器

        Args:
            url: WebSocket连接URL
            headers: 连接请求头
            reconnect_attempts: 重连尝试次数
            reconnect_delay: 重连延迟时间
        """
        self.url = url
        self.headers = headers
        self.reconnect_attempts = reconnect_attempts
        self.reconnect_delay = reconnect_delay
        self.current_attempt = 0
        self.ws = None
        self.ws_thread = None
        self.is_connected = False
        self.stop_event = threading.Event()
        self.connection_lock = threading.RLock()

        # 连接状态监控
        self.connection_start_time = None
        self.last_activity_time = None
        self.total_connections = 0
        self.failed_connections = 0

    def create_connection(self, on_open=None, on_message=None,
                         on_error=None, on_close=None) -> bool:
        """
        创建WebSocket连接

        Args:
            on_open: 连接建立回调
            on_message: 消息接收回调
            on_error: 错误回调
            on_close: 连接关闭回调

        Returns:
            bool: 是否成功建立连接
        """
        with self.connection_lock:
            try:
                # 记录连接尝试
                self.total_connections += 1
                self.connection_start_time = time.time()

                # 如果已经连接，先关闭旧连接
                if self.is_connected or self.ws:
                    logger.info("检测到现有连接，先关闭旧连接...")
                    self._force_close_connection()

                logger.info(f"正在创建WebSocket连接到: {self.url} (第{self.total_connections}次尝试)")
                self.ws = websocket.WebSocketApp(
                    self.url,
                    header=self.headers,
                    on_open=on_open,
                    on_message=on_message,
                    on_error=on_error,
                    on_close=on_close
                )

                # 启动WebSocket线程
                self.ws_thread = threading.Thread(
                    target=self.ws.run_forever,
                    daemon=True
                )
                self.ws_thread.start()

                # 等待连接建立
                retry_count = 0
                while not self.is_connected and retry_count < 10:
                    if self.stop_event.is_set():
                        logger.warning("连接过程被中断")
                        self.failed_connections += 1
                        return False
                    time.sleep(0.3)
                    retry_count += 1

                if self.is_connected:
                    self.last_activity_time = time.time()
                    connection_time = time.time() - self.connection_start_time
                    logger.info(f"WebSocket连接建立成功，耗时: {connection_time:.2f}秒")
                else:
                    logger.error("WebSocket连接建立超时")
                    self.failed_connections += 1

                return self.is_connected

            except Exception as e:
                logger.error(f"创建WebSocket连接失败: {e}")
                self.failed_connections += 1
                return False

    def close_connection(self) -> bool:
        """
        关闭WebSocket连接

        Returns:
            bool: 是否成功关闭连接
        """
        with self.connection_lock:
            try:
                logger.info("正在关闭WebSocket连接...")
                self.stop_event.set()
                self.is_connected = False

                # 关闭WebSocket连接
                if self.ws:
                    try:
                        self.ws.close()
                        logger.info("WebSocket连接已关闭")
                    except Exception as e:
                        logger.warning(f"关闭WebSocket时出错: {e}")

                # 等待线程结束
                if self.ws_thread and self.ws_thread.is_alive():
                    self.ws_thread.join(timeout=3.0)
                    if self.ws_thread.is_alive():
                        logger.warning("WebSocket线程未能在超时时间内结束")

                self.ws = None
                self.ws_thread = None
                logger.info("WebSocket连接资源已清理")
                return True

            except Exception as e:
                logger.error(f"关闭WebSocket连接失败: {e}")
                return False

    def _force_close_connection(self):
        """
        强制关闭连接，不等待线程结束
        """
        try:
            self.stop_event.set()
            self.is_connected = False

            if self.ws:
                try:
                    self.ws.close()
                except:
                    pass
                self.ws = None

            self.ws_thread = None
            logger.info("强制关闭连接完成")

        except Exception as e:
            logger.warning(f"强制关闭连接时出错: {e}")

    def send_message(self, message: Union[str, bytes]) -> bool:
        """
        发送消息

        Args:
            message: 要发送的消息

        Returns:
            bool: 是否成功发送
        """
        try:
            if not self.is_connected or not self.ws:
                logger.error("WebSocket未连接，无法发送消息")
                return False

            if isinstance(message, str):
                self.ws.send(message)
            else:
                self.ws.send(message, opcode=websocket.ABNF.OPCODE_BINARY)

            return True

        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            return False

    def get_connection_stats(self) -> Dict[str, Any]:
        """
        获取连接统计信息

        Returns:
            Dict: 连接统计信息
        """
        current_time = time.time()
        return {
            "is_connected": self.is_connected,
            "total_connections": self.total_connections,
            "failed_connections": self.failed_connections,
            "success_rate": (self.total_connections - self.failed_connections) / max(self.total_connections, 1) * 100,
            "connection_duration": current_time - self.connection_start_time if self.connection_start_time else 0,
            "last_activity": current_time - self.last_activity_time if self.last_activity_time else None,
            "url": self.url
        }
            
    def close_connection(self) -> bool:
        """
        关闭WebSocket连接
        
        Returns:
            bool: 是否成功关闭连接
        """
        try:
            self.stop_event.set()
            if self.ws:
                self.ws.close()
            if hasattr(self, 'ws_thread') and self.ws_thread.is_alive():
                self.ws_thread.join(timeout=5.0)
            self.is_connected = False
            return True
        except Exception as e:
            logger.error(f"关闭WebSocket连接失败: {e}")
            return False
            
    def send_message(self, message: bytes, opcode=websocket.ABNF.OPCODE_BINARY) -> bool:
        """
        发送消息
        
        Args:
            message: 要发送的消息
            opcode: WebSocket操作码
            
        Returns:
            bool: 是否成功发送
        """
        try:
            if self.ws and self.ws.sock and self.ws.sock.connected:
                self.ws.send(message, opcode)
                return True
            return False
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False


class ASRTextProcessor:
    """
    ASR文本处理器
    
    负责对识别结果进行后处理，包括标点符号添加、文本规范化等
    """
    
    def __init__(self):
        """初始化文本处理器"""
        self.custom_words = [
            "LifeBuddy", "豆包", "豆包大模型", "大语言模型",
            "火山引擎", "火山语音", "GPT", "大模型"
        ]
        self._initialize_jieba()
        
    def _initialize_jieba(self):
        """初始化jieba分词器"""
        try:
            jieba.initialize()
            for word in self.custom_words:
                jieba.add_word(word)
        except Exception as e:
            logger.warning(f"初始化jieba失败: {e}")
            
    def preprocess_text_for_tts(self, text: str) -> str:
        """
        预处理文本以优化TTS输出
        
        Args:
            text: 原始文本
            
        Returns:
            str: 处理后的文本
        """
        if not text:
            return text
            
        # 移除Markdown代码块标记
        text = re.sub(r'```[\w]*\n|```', ' ', text)
        
        # 标点符号替换映射
        replacements = {
            '，': '，', '。': '。', '、': ' ', '；': '，', '：': ' ',
            '"': ' ', '"': ' ', ''': ' ', ''': ' ', '【': ' ', '】': ' ',
            '（': ' ', '）': ' ', '《': ' ', '》': ' ', '—': ' ', '-': ' ',
            '_': ' ', '*': ' ', '#': ' ', '`': ' ', '+': '加', '=': '等于',
            '/': ' ', '\\': ' ', '|': ' ', '<': ' ', '>': ' ',
            '.': '。', ',': '，', '?': '？', '!': '！', ';': '，', ':': ' ',
            '(': ' ', ')': ' ', '[': ' ', ']': ' ', '{': ' ', '}': ' '
        }
        
        for char, replacement in replacements.items():
            text = text.replace(char, replacement)
            
        # 移除连续空格并去除首尾空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
        
    def add_punctuation(self, text: str) -> str:
        """
        智能添加标点符号
        
        Args:
            text: 原始文本
            
        Returns:
            str: 添加标点后的文本
        """
        text = text.strip()
        if not text:
            return text
            
        try:
            # 使用jieba进行词性标注
            words = posseg.cut(text)
            punctuated_text = ""
            prev_word_type = None
            
            for word, flag in words:
                # 根据词性和上下文添加标点
                if flag in ['v', 'vn'] and prev_word_type in ['n', 'r', 'uj']:
                    punctuated_text += "，" + word
                elif flag in ['w']:  # 标点符号
                    punctuated_text += word
                else:
                    if punctuated_text and not punctuated_text.endswith(("，", "。")):
                        punctuated_text += word
                    else:
                        punctuated_text += word
                prev_word_type = flag
                
            # 如果文本不以标点结尾，添加句号
            if punctuated_text and not re.search(r'[，。！？、]$', punctuated_text):
                punctuated_text += "。"
                
            return punctuated_text
            
        except Exception as e:
            logger.warning(f"使用jieba处理文本出错: {e}")
            # 出错时返回原文本并添加句号
            if text and not re.search(r'[，。！？、]$', text):
                text += "。"
            return text


class ASRAudioProcessor:
    """
    ASR音频处理器
    
    负责音频数据的采集、预处理、静音检测等
    """
    
    def __init__(self, sample_rate: int = 16000, channels: int = 1,
                 chunk_duration: float = 0.6, silence_threshold: float = 0.5):
        """
        初始化音频处理器
        
        Args:
            sample_rate: 采样率
            channels: 通道数
            chunk_duration: 音频块持续时间
            silence_threshold: 静音检测阈值
        """
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_duration = chunk_duration
        self.chunk_size = int(sample_rate * chunk_duration)
        self.silence_threshold = silence_threshold
        self.audio_queue = queue.Queue()
        self.stream = None
        
    def start_audio_capture(self, callback: Optional[Callable] = None) -> bool:
        """
        开始音频采集

        Args:
            callback: 音频数据回调函数

        Returns:
            bool: 是否成功开始采集
        """
        if not SOUNDDEVICE_AVAILABLE:
            logger.warning("sounddevice未安装，无法进行音频采集")
            return False

        try:
            self.stream = sd.InputStream(
                samplerate=self.sample_rate,
                channels=self.channels,
                callback=callback or self._default_audio_callback,
                blocksize=self.chunk_size
            )
            self.stream.start()
            logger.info("音频采集已开始")
            return True
        except Exception as e:
            logger.error(f"开始音频采集失败: {e}")
            return False
            
    def stop_audio_capture(self) -> bool:
        """
        停止音频采集
        
        Returns:
            bool: 是否成功停止采集
        """
        try:
            if self.stream:
                self.stream.stop()
                self.stream.close()
                self.stream = None
            logger.info("音频采集已停止")
            return True
        except Exception as e:
            logger.error(f"停止音频采集失败: {e}")
            return False
            
    def _default_audio_callback(self, indata, frames, time_info, status):
        """默认音频回调函数"""
        if status:
            logger.warning(f"音频回调状态: {status}")
        try:
            self.audio_queue.put(indata.copy())
        except Exception as e:
            logger.error(f"处理音频数据时出错: {e}")
            
    def is_silence(self, audio_data: np.ndarray) -> bool:
        """
        检测音频是否为静音
        
        Args:
            audio_data: 音频数据
            
        Returns:
            bool: 是否为静音
        """
        # 计算RMS值和峰值
        rms = np.sqrt(np.mean(np.square(audio_data)))
        peak = np.max(np.abs(audio_data))
        
        # 同时考虑RMS值和峰值
        is_silent = (rms < self.silence_threshold * 0.7) and (peak < self.silence_threshold)
        
        return is_silent
        
    def convert_audio_format(self, audio_data: Union[bytes, np.ndarray]) -> bytes:
        """
        转换音频格式
        
        Args:
            audio_data: 输入音频数据
            
        Returns:
            bytes: 转换后的音频数据
        """
        if isinstance(audio_data, np.ndarray):
            # 确保数据是float32类型
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
                
            # 确保数据范围在[-1, 1]内
            max_value = np.max(np.abs(audio_data))
            if max_value > 1.0:
                audio_data = audio_data / max_value
                
            # 转换为16位整数
            audio_data = (audio_data * 32767).astype(np.int16)
            
            # 转换为bytes
            return audio_data.tobytes()
        
        return audio_data if isinstance(audio_data, bytes) else b''


# ==================== 具体ASR客户端实现 ====================

class VolcanoASRClient(ASRClientBase):
    """
    火山引擎ASR客户端

    实现基于WebSocket的火山引擎语音识别服务
    """

    def __init__(self, app_key: str, access_key: str,
                 model_name: str = "bigmodel", sample_rate: int = 16000,
                 channels: int = 1, **kwargs):
        """
        初始化火山引擎ASR客户端

        Args:
            app_key: 火山引擎APP ID
            access_key: 火山引擎Access Token
            model_name: 模型名称
            sample_rate: 采样率
            channels: 通道数
        """
        super().__init__(app_key, access_key)

        self.model_name = model_name
        self.sample_rate = sample_rate
        self.channels = channels
        self.format = "pcm"

        # WebSocket连接参数
        self.ws_url = "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel"
        self.resource_id = "volc.bigasr.sauc.duration"
        self.connect_id = str(uuid.uuid4())

        # 连接管理器
        headers = {
            "X-Api-App-Key": self.app_key,
            "X-Api-Access-Key": self.access_key,
            "X-Api-Resource-Id": self.resource_id,
            "X-Api-Connect-Id": self.connect_id
        }
        self.connection_manager = ASRConnectionManager(self.ws_url, headers)

        # 音频队列和线程
        self.audio_queue = queue.Queue()
        self.audio_sender_thread = None
        self.result_callback = None
        self.sequence_number = 0

    def connect(self) -> bool:
        """连接到火山引擎ASR服务"""
        try:
            success = self.connection_manager.create_connection(
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )

            if success:
                self.is_connected = True
                logger.info("成功连接到火山引擎ASR服务")

            return success

        except Exception as e:
            logger.error(f"连接火山引擎ASR服务失败: {e}")
            return False

    def disconnect(self) -> bool:
        """断开火山引擎ASR服务连接"""
        try:
            logger.info("正在断开火山引擎ASR服务连接...")

            # 停止识别
            if self.is_recognizing:
                self.stop_recognition()

            # 关闭连接管理器
            success = self.connection_manager.close_connection()
            self.is_connected = False

            # 清理音频队列
            while not self.audio_queue.empty():
                try:
                    self.audio_queue.get_nowait()
                except queue.Empty:
                    break

            logger.info("已断开火山引擎ASR服务连接")
            return success

        except Exception as e:
            logger.error(f"断开火山引擎ASR服务连接失败: {e}")
            return False

    def start_recognition(self, callback: Optional[Callable[[str, bool], None]] = None) -> bool:
        """启动语音识别"""
        if not self.is_connected:
            logger.error("未连接到ASR服务")
            return False

        if self.is_recognizing:
            logger.warning("已在进行识别")
            return False

        try:
            self.result_callback = callback
            self.sequence_number = 0

            # 清空音频队列
            while not self.audio_queue.empty():
                try:
                    self.audio_queue.get_nowait()
                except queue.Empty:
                    break

            # 发送初始化参数
            if not self._send_init_parameters():
                return False

            self.is_recognizing = True

            # 启动音频发送线程
            self.audio_sender_thread = threading.Thread(
                target=self._audio_sender_worker,
                daemon=True
            )
            self.audio_sender_thread.start()

            logger.info("火山引擎语音识别已启动")
            return True

        except Exception as e:
            logger.error(f"启动火山引擎语音识别失败: {e}")
            return False

    def stop_recognition(self) -> bool:
        """停止语音识别"""
        if not self.is_recognizing:
            return True

        try:
            # 发送结束信号
            self._send_audio_end_signal()

            self.is_recognizing = False

            # 等待音频发送线程结束
            if self.audio_sender_thread and self.audio_sender_thread.is_alive():
                self.audio_sender_thread.join(timeout=3.0)

            logger.info("火山引擎语音识别已停止")
            return True

        except Exception as e:
            logger.error(f"停止火山引擎语音识别失败: {e}")
            return False

    def send_audio(self, audio_data: Union[bytes, np.ndarray]) -> bool:
        """发送音频数据"""
        if not self.is_connected or not self.is_recognizing:
            return False

        try:
            # 转换音频格式
            audio_processor = ASRAudioProcessor()
            audio_bytes = audio_processor.convert_audio_format(audio_data)

            # 放入队列
            self.audio_queue.put(audio_bytes)
            return True

        except Exception as e:
            logger.error(f"发送音频数据失败: {e}")
            return False

    def _on_open(self, ws):
        """WebSocket连接建立回调"""
        logger.info("火山引擎WebSocket连接已建立")
        self.connection_manager.is_connected = True

    def _on_message(self, ws, message):
        """WebSocket消息接收回调"""
        try:
            if not isinstance(message, bytes) or len(message) < 4:
                return

            # 解析消息头
            header = message[:4]
            message_type = (header[1] >> 4) & 0x0F
            message_flags = header[1] & 0x0F

            if message_type == 9:  # 服务器响应
                self._parse_asr_response(message, message_flags)
            elif message_type == 15:  # 错误消息
                self._parse_error_message(message)

        except Exception as e:
            logger.error(f"处理火山引擎WebSocket消息失败: {e}")

    def _on_error(self, ws, error):
        """WebSocket错误回调"""
        logger.error(f"火山引擎WebSocket错误: {error}")

    def _on_close(self, ws, close_status_code=None, close_msg=None):
        """WebSocket关闭回调"""
        logger.info(f"火山引擎WebSocket连接已关闭: {close_status_code} - {close_msg}")
        self.connection_manager.is_connected = False

    def _send_init_parameters(self) -> bool:
        """发送初始化参数"""
        try:
            params = {
                "user": {"uid": str(uuid.uuid4())},
                "audio": {
                    "format": self.format,
                    "rate": self.sample_rate,
                    "bits": 16,
                    "channel": self.channels
                },
                "request": {
                    "model_name": self.model_name,
                    "enable_itn": True,
                    "enable_punc": True,
                    "enable_ddc": True
                }
            }

            params_json = json.dumps(params)
            payload = params_json.encode('utf-8')

            # 创建消息头
            header = self._create_header(1, 0, 1, 0)  # full_client_request, JSON
            payload_size = struct.pack("!I", len(payload))

            message = header + payload_size + payload
            return self.connection_manager.send_message(message)

        except Exception as e:
            logger.error(f"发送初始化参数失败: {e}")
            return False

    def _send_audio_end_signal(self) -> bool:
        """发送音频结束信号"""
        try:
            header = self._create_header(2, 2, 0, 0)  # audio_only_request, last_packet
            payload_size = struct.pack("!I", 0)
            message = header + payload_size
            return self.connection_manager.send_message(message)
        except Exception as e:
            logger.error(f"发送音频结束信号失败: {e}")
            return False

    def _create_header(self, message_type: int, message_flags: int,
                      serialization: int, compression: int) -> bytes:
        """创建消息头"""
        header = bytearray(4)
        header[0] = (0x01 << 4) | 0x01  # 版本1，header大小4字节
        header[1] = (message_type << 4) | message_flags
        header[2] = (serialization << 4) | compression
        header[3] = 0x00
        return bytes(header)

    def _audio_sender_worker(self):
        """音频发送工作线程"""
        logger.info("火山引擎音频发送线程已启动")

        while self.is_connected and self.is_recognizing:
            try:
                try:
                    audio_chunk = self.audio_queue.get(timeout=0.5)
                except queue.Empty:
                    continue

                # 发送音频块
                header = self._create_header(2, 0, 0, 0)  # audio_only_request
                payload_size = struct.pack("!I", len(audio_chunk))
                message = header + payload_size + audio_chunk

                if not self.connection_manager.send_message(message):
                    logger.warning("发送音频数据失败")

                self.sequence_number += 1

            except Exception as e:
                logger.error(f"音频发送线程错误: {e}")

        logger.info("火山引擎音频发送线程已结束")

    def _parse_asr_response(self, message: bytes, message_flags: int):
        """解析ASR响应"""
        try:
            offset = 4

            # 解析payload大小
            if len(message) < offset + 4:
                return

            payload_size = struct.unpack("!I", message[offset:offset+4])[0]
            offset += 4

            if len(message) < offset + payload_size:
                return

            payload = message[offset:offset + payload_size]

            # 解析JSON响应
            response_json = json.loads(payload)
            self._handle_asr_result(response_json, message_flags)

        except Exception as e:
            logger.error(f"解析火山引擎ASR响应失败: {e}")

    def _parse_error_message(self, message: bytes):
        """解析错误消息"""
        try:
            offset = 4
            if len(message) < offset + 8:
                return

            error_code = struct.unpack("!I", message[offset:offset+4])[0]
            offset += 4
            error_msg_size = struct.unpack("!I", message[offset:offset+4])[0]
            offset += 4

            if len(message) < offset + error_msg_size:
                return

            error_msg = message[offset:offset + error_msg_size].decode('utf-8')
            # logger.error(f"火山引擎ASR错误: 错误码={error_code}, 错误消息={error_msg}")

        except Exception as e:
            logger.error(f"解析错误消息失败: {e}")

    def _handle_asr_result(self, response: Dict[str, Any], message_flags: int):
        """处理ASR结果"""
        try:
            if 'result' not in response:
                return

            result = response['result']
            text = result.get('text', '')

            if not text:
                return

            # 判断是否为最终结果
            is_final = (message_flags & 0x02) == 0x02

            with self.lock:
                self.last_result = text
                if is_final and text not in self.results:
                    self.results.append(text)

            # 回调通知
            if self.result_callback:
                try:
                    self.result_callback(text, is_final)
                except Exception as e:
                    logger.error(f"执行回调函数失败: {e}")

        except Exception as e:
            logger.error(f"处理火山引擎ASR结果失败: {e}")


class DoubaoASRClient(ASRClientBase):
    """
    豆包ASR客户端

    实现基于WebSocket的豆包语音识别服务
    """

    def __init__(self, app_key: str, access_key: str,
                 model_name: str = "bigmodel", sample_rate: int = 16000,
                 channels: int = 1, language: str = "zh-CN", **kwargs):
        """
        初始化豆包ASR客户端

        Args:
            app_key: 豆包APP ID
            access_key: 豆包Access Token
            model_name: 模型名称
            sample_rate: 采样率
            channels: 通道数
            language: 识别语言
        """
        super().__init__(app_key, access_key)

        self.model_name = model_name
        self.sample_rate = sample_rate
        self.channels = channels
        self.language = language
        self.format = "pcm"
        self.codec = "raw"
        self.bits = 16

        # WebSocket连接参数
        self.ws_url = "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel"
        self.resource_id = "volc.bigasr.sauc.duration"
        self.request_id = str(uuid.uuid4())

        # 连接管理器
        headers = {
            "X-Api-App-Key": self.app_key,
            "X-Api-Access-Key": self.access_key,
            "X-Api-Resource-Id": self.resource_id,
            "X-Api-Connect-Id": self.request_id
        }
        self.connection_manager = ASRConnectionManager(self.ws_url, headers)

        # 音频队列和线程
        self.audio_queue = queue.Queue()
        self.audio_sender_thread = None
        self.result_callback = None
        self.sequence_number = 0

    def connect(self) -> bool:
        """连接到豆包ASR服务"""
        try:
            success = self.connection_manager.create_connection(
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )

            if success:
                self.is_connected = True
                logger.info("成功连接到豆包ASR服务")

            return success

        except Exception as e:
            logger.error(f"连接豆包ASR服务失败: {e}")
            return False

    def disconnect(self) -> bool:
        """断开豆包ASR服务连接"""
        try:
            logger.info("正在断开豆包ASR服务连接...")

            # 停止识别
            if self.is_recognizing:
                self.stop_recognition()

            # 关闭连接管理器
            success = self.connection_manager.close_connection()
            self.is_connected = False

            # 清理音频队列
            while not self.audio_queue.empty():
                try:
                    self.audio_queue.get_nowait()
                except queue.Empty:
                    break

            logger.info("已断开豆包ASR服务连接")
            return success

        except Exception as e:
            logger.error(f"断开豆包ASR服务连接失败: {e}")
            return False

    def start_recognition(self, callback: Optional[Callable[[str, bool], None]] = None) -> bool:
        """启动语音识别"""
        if not self.is_connected:
            logger.error("未连接到ASR服务")
            return False

        if self.is_recognizing:
            logger.warning("已在进行识别")
            return False

        try:
            self.result_callback = callback
            self.sequence_number = 0

            # 清空音频队列
            while not self.audio_queue.empty():
                try:
                    self.audio_queue.get_nowait()
                except queue.Empty:
                    break

            # 发送初始化参数
            if not self._send_init_parameters():
                return False

            self.is_recognizing = True

            # 启动音频发送线程
            self.audio_sender_thread = threading.Thread(
                target=self._audio_sender_worker,
                daemon=True
            )
            self.audio_sender_thread.start()

            logger.info("豆包语音识别已启动")
            return True

        except Exception as e:
            logger.error(f"启动豆包语音识别失败: {e}")
            return False

    def stop_recognition(self) -> bool:
        """停止语音识别"""
        if not self.is_recognizing:
            return True

        try:
            # 发送结束信号
            self._send_audio_end_signal()

            self.is_recognizing = False

            # 等待音频发送线程结束
            if self.audio_sender_thread and self.audio_sender_thread.is_alive():
                self.audio_sender_thread.join(timeout=3.0)

            logger.info("豆包语音识别已停止")
            return True

        except Exception as e:
            logger.error(f"停止豆包语音识别失败: {e}")
            return False

    def send_audio(self, audio_data: Union[bytes, np.ndarray]) -> bool:
        """发送音频数据"""
        if not self.is_connected or not self.is_recognizing:
            return False

        try:
            # 转换音频格式
            audio_processor = ASRAudioProcessor()
            audio_bytes = audio_processor.convert_audio_format(audio_data)

            # 放入队列
            self.audio_queue.put(audio_bytes)
            return True

        except Exception as e:
            logger.error(f"发送音频数据失败: {e}")
            return False

    def _on_open(self, _):
        """WebSocket连接建立回调"""
        logger.info("豆包WebSocket连接已建立")
        self.connection_manager.is_connected = True

    def _on_message(self, _, message):
        """WebSocket消息接收回调"""
        try:
            if not isinstance(message, bytes) or len(message) < 8:
                return

            # 解析消息头
            header = message[:4]
            message_type = header[1] >> 4
            message_flags = header[1] & 0x0F

            if message_type in [9, 1]:  # 服务器响应
                self._parse_asr_response(message, message_flags)
            elif message_type == 15:  # 错误消息
                self._parse_error_message(message)

        except Exception as e:
            logger.error(f"处理豆包WebSocket消息失败: {e}")

    def _on_error(self, _, error):
        """WebSocket错误回调"""
        logger.error(f"豆包WebSocket错误: {error}")

    def _on_close(self, _, close_status_code=None, close_msg=None):
        """WebSocket关闭回调"""
        logger.info(f"豆包WebSocket连接已关闭: {close_status_code} - {close_msg}")
        self.connection_manager.is_connected = False

    def _send_init_parameters(self) -> bool:
        """发送初始化参数"""
        try:
            params = {
                "user": {"uid": self.request_id},
                "audio": {
                    "format": self.format,
                    "codec": self.codec,
                    "rate": self.sample_rate,
                    "bits": self.bits,
                    "channel": self.channels,
                    "language": self.language
                },
                "request": {
                    "model_name": self.model_name,
                    "enable_itn": False,
                    "enable_ddc": False,
                    "enable_punc": True,
                    "show_utterances": True,
                    "result_type": "json"
                }
            }

            params_json = json.dumps(params, ensure_ascii=False)
            payload = params_json.encode('utf-8')

            # 创建消息头
            header = self._create_header(1, 0, 1, 0)  # full_client_request, JSON
            payload_size = struct.pack("!I", len(payload))

            message = header + payload_size + payload
            return self.connection_manager.send_message(message)

        except Exception as e:
            logger.error(f"发送初始化参数失败: {e}")
            return False

    def _send_audio_end_signal(self) -> bool:
        """发送音频结束信号"""
        try:
            header = self._create_header(2, 2, 0, 0)  # audio_only_request, last_packet
            payload_size = struct.pack("!I", 0)
            message = header + payload_size
            return self.connection_manager.send_message(message)
        except Exception as e:
            logger.error(f"发送音频结束信号失败: {e}")
            return False

    def _create_header(self, message_type: int, message_flags: int,
                      serialization: int, compression: int) -> bytes:
        """创建消息头"""
        header = bytearray(4)
        header[0] = (0x01 << 4) | 0x01  # 版本1，header大小4字节
        header[1] = (message_type << 4) | message_flags
        header[2] = (serialization << 4) | compression
        header[3] = 0x00
        return bytes(header)

    def _audio_sender_worker(self):
        """音频发送工作线程"""
        logger.info("豆包音频发送线程已启动")

        while self.is_connected and self.is_recognizing:
            try:
                try:
                    audio_chunk = self.audio_queue.get(timeout=0.5)
                except queue.Empty:
                    continue

                # 分块发送音频数据
                chunk_size = 3200  # 200ms at 16kHz
                for i in range(0, len(audio_chunk), chunk_size):
                    chunk = audio_chunk[i:i+chunk_size]

                    header = self._create_header(2, 0, 0, 0)  # audio_only_request
                    payload_size = struct.pack("!I", len(chunk))
                    message = header + payload_size + chunk

                    if not self.connection_manager.send_message(message):
                        logger.warning("发送音频数据失败")

                    time.sleep(0.01)  # 短暂延迟

                self.sequence_number += 1

            except Exception as e:
                logger.error(f"音频发送线程错误: {e}")

        logger.info("豆包音频发送线程已结束")

    def _parse_asr_response(self, message: bytes, message_flags: int):
        """解析ASR响应"""
        try:
            offset = 4

            # 检查是否有序列号
            has_sequence = (message_flags & 0x01) == 0x01
            if has_sequence:
                if len(message) < offset + 4:
                    return
                offset += 4

            # 解析payload大小
            if len(message) < offset + 4:
                return

            payload_size = struct.unpack("!I", message[offset:offset+4])[0]
            offset += 4

            if len(message) < offset + payload_size:
                return

            payload = message[offset:offset + payload_size]

            # 检查是否有长度前缀
            if len(payload) >= 4:
                prefix = payload[:4]
                try:
                    prefix_length = struct.unpack("!I", prefix)[0]
                    if len(payload) >= 4 + prefix_length and prefix_length > 0:
                        payload = payload[4:4+prefix_length]
                except:
                    pass

            # 解析JSON响应
            try:
                payload_str = payload.decode('utf-8', errors='replace')
                # 查找JSON开始位置
                start_idx = payload_str.find('{')
                if start_idx >= 0:
                    json_str = payload_str[start_idx:]
                    response_json = json.loads(json_str)
                    self._handle_asr_result(response_json, message_flags)
            except json.JSONDecodeError:
                logger.warning("无法解析JSON响应")

        except Exception as e:
            logger.error(f"解析豆包ASR响应失败: {e}")

    def _parse_error_message(self, message: bytes):
        """解析错误消息"""
        try:
            if len(message) >= 16:
                error_code = struct.unpack("!I", message[8:12])[0]
                error_msg_size = struct.unpack("!I", message[12:16])[0]

                if len(message) >= 16 + error_msg_size:
                    error_msg = message[16:16 + error_msg_size].decode('utf-8', errors='replace')
                    logger.error(f"豆包ASR错误: 错误码={error_code}, 错误消息={error_msg}")

        except Exception as e:
            logger.error(f"解析错误消息失败: {e}")

    def _handle_asr_result(self, response: Dict[str, Any], message_flags: int):
        """处理ASR结果"""
        try:
            if "result" in response and "text" in response["result"]:
                text = response["result"]["text"]

                if not text:
                    return

                # 判断是否为最终结果
                is_final = (message_flags & 0x02) == 0x02 or message_flags == 0x03

                with self.lock:
                    self.last_result = text
                    if is_final:
                        self.results.append(text)

                # 回调通知
                if self.result_callback:
                    try:
                        self.result_callback(text, is_final)
                    except Exception as e:
                        logger.error(f"执行回调函数失败: {e}")

        except Exception as e:
            logger.error(f"处理豆包ASR结果失败: {e}")


# ==================== ASR管理器 ====================

class ASRManager:
    """
    ASR管理器

    统一管理不同的ASR客户端，提供高级语音识别功能
    """

    def __init__(self, config_path: str = None,
                 sample_rate: int = 16000, channels: int = 1,
                 chunk_duration: float = 0.6, silence_threshold: float = 0.5,
                 silence_duration: float = 2.0):
        """
        初始化ASR管理器

        Args:
            config_path: 配置文件路径
            sample_rate: 采样率
            channels: 通道数
            chunk_duration: 音频块持续时间
            silence_threshold: 静音检测阈值
            silence_duration: 静音持续时间阈值
        """
        # 加载配置
        self.config = get_config(config_path)

        # 音频参数
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_duration = chunk_duration
        self.silence_threshold = silence_threshold
        self.silence_duration = silence_duration

        # 状态变量
        self.is_recognizing = False
        self.is_paused = False
        self.session_id = str(uuid.uuid4())
        self.results = []
        self.last_result = ""
        self.current_stream = ""
        self.error_count = 0
        self.max_retries = 3

        # 线程控制
        self.lock = threading.RLock()
        self.recognition_callback = None
        self.audio_processing_thread = None

        # 组件初始化
        self.asr_client = None
        self.audio_processor = ASRAudioProcessor(
            sample_rate, channels, chunk_duration, silence_threshold
        )
        self.text_processor = ASRTextProcessor()

        # 初始化ASR客户端
        self._initialize_asr_client()

    def _initialize_asr_client(self):
        """初始化ASR客户端"""
        # 从配置获取API密钥
        try:
            if hasattr(self.config, 'get') and hasattr(self.config, 'sections'):
                # ConfigParser对象
                asr_app_key = self.config.get('API', 'asr_app_key', fallback='')
                asr_access_key = self.config.get('API', 'asr_access_key', fallback='')
                asr_service_type = self.config.get('API', 'asr_service_type', fallback='volcano')
            else:
                # 字典对象
                api_section = self.config.get('API', {})
                asr_app_key = api_section.get('asr_app_key', '')
                asr_access_key = api_section.get('asr_access_key', '')
                asr_service_type = api_section.get('asr_service_type', 'volcano')
        except Exception as e:
            logger.warning(f"读取配置失败: {e}")
            asr_app_key = ''
            asr_access_key = ''
            asr_service_type = 'volcano'

        if asr_service_type:
            asr_service_type = asr_service_type.lower()

        if not asr_app_key or not asr_access_key:
            raise ValueError("配置文件中缺少ASR API密钥，无法初始化ASR客户端")

        # 根据服务类型初始化客户端
        if asr_service_type == 'volcano':
            self.asr_client = VolcanoASRClient(
                app_key=asr_app_key,
                access_key=asr_access_key,
                sample_rate=self.sample_rate,
                channels=self.channels
            )
        elif asr_service_type == 'doubao':
            self.asr_client = DoubaoASRClient(
                app_key=asr_app_key,
                access_key=asr_access_key,
                sample_rate=self.sample_rate,
                channels=self.channels
            )
        else:
            raise ValueError(f"不支持的ASR服务类型: {asr_service_type}")

        logger.info(f"{asr_service_type.upper()}ASR客户端初始化成功")

    def start_recognition(self, callback: Optional[Callable[[str, bool], None]] = None) -> bool:
        """
        启动语音识别

        Args:
            callback: 识别结果回调函数

        Returns:
            bool: 是否成功启动识别
        """
        with self.lock:
            if self.is_recognizing:
                logger.warning("已经在进行语音识别")
                return False

            self.recognition_callback = callback
            self.is_recognizing = False
            self.is_paused = False
            self.error_count = 0
            self.session_id = str(uuid.uuid4())

            try:
                logger.info(f"开始启动语音识别服务，会话ID: {self.session_id}")

                # 连接ASR服务
                if not self.asr_client.is_connected:
                    if not self.asr_client.connect():
                        logger.error("无法连接到ASR服务")
                        return False
                    else:
                        logger.info("ASR服务连接成功")

                # 启动语音识别
                def on_asr_result(text, is_final):
                    self._handle_asr_result(text, is_final)

                if not self.asr_client.start_recognition(callback=on_asr_result):
                    logger.error("启动语音识别失败")
                    return False

                # 启动音频采集
                if not self.audio_processor.start_audio_capture(self._audio_callback):
                    logger.error("启动音频采集失败")
                    return False

                # 启动音频处理线程
                self.audio_processing_thread = threading.Thread(
                    target=self._process_audio_stream,
                    daemon=True,
                    name="ASR-AudioProcessing"
                )
                self.audio_processing_thread.start()

                self.is_recognizing = True
                logger.info("成功启动语音识别")
                return True

            except Exception as e:
                self.is_recognizing = False
                logger.error(f"启动语音识别失败: {e}")
                return False

    def stop_recognition(self) -> bool:
        """
        停止语音识别

        Returns:
            bool: 是否成功停止识别
        """
        with self.lock:
            if not self.is_recognizing:
                logger.warning("当前没有进行语音识别")
                return False

            logger.info("正在停止语音识别...")
            self.is_recognizing = False

            # 停止音频采集
            self.audio_processor.stop_audio_capture()

            # 停止ASR服务
            if self.asr_client:
                if self.asr_client.is_recognizing:
                    self.asr_client.stop_recognition()
                if self.asr_client.is_connected:
                    self.asr_client.disconnect()

            # 等待处理线程结束
            if self.audio_processing_thread and self.audio_processing_thread.is_alive():
                self.audio_processing_thread.join(timeout=5.0)

            self.audio_processing_thread = None
            logger.info("成功停止语音识别")
            return True

    def pause_recognition(self) -> bool:
        """暂停语音识别"""
        with self.lock:
            if not self.is_recognizing:
                logger.warning("当前没有进行语音识别")
                return False

            if self.is_paused:
                logger.warning("已经处于暂停状态")
                return False

            self.is_paused = True
            logger.info("已暂停语音识别")
            return True

    def resume_recognition(self) -> bool:
        """恢复语音识别"""
        with self.lock:
            if not self.is_recognizing:
                logger.warning("语音识别未启动，无法恢复")
                return False

            if not self.is_paused:
                logger.warning("当前非暂停状态，无需恢复")
                return False

            self.is_paused = False
            logger.info("已恢复语音识别")
            return True

    def get_recognition_status(self) -> Dict[str, Any]:
        """获取识别状态"""
        with self.lock:
            return {
                "is_recognizing": self.is_recognizing,
                "is_paused": self.is_paused,
                "session_id": self.session_id,
                "error_count": self.error_count,
                "last_result": self.last_result,
                "current_stream": self.current_stream,
                "asr_connected": self.asr_client.is_connected if self.asr_client else False
            }

    def get_recognition_results(self) -> List[str]:
        """获取所有识别结果"""
        with self.lock:
            return self.results.copy()

    def get_last_result(self) -> str:
        """获取最近一次识别结果"""
        with self.lock:
            return self.last_result

    def get_current_stream(self) -> str:
        """获取当前实时识别结果"""
        with self.lock:
            return self.current_stream

    def clear_results(self) -> None:
        """清空识别结果"""
        with self.lock:
            self.results = []
            self.last_result = ""
            self.current_stream = ""

    def send_audio_data(self, audio_data: np.ndarray) -> bool:
        """发送外部音频数据"""
        if not self.is_recognizing or self.is_paused:
            logger.warning("语音识别未启动或处于暂停状态")
            return False

        try:
            self.audio_processor.audio_queue.put(audio_data.copy())
            return True
        except Exception as e:
            logger.error(f"发送音频数据失败: {e}")
            return False

    def _audio_callback(self, indata, frames, time_info, status):
        """音频数据回调函数"""
        if status:
            logger.warning(f"音频回调状态: {status}")

        if self.is_recognizing and not self.is_paused:
            try:
                self.audio_processor.audio_queue.put(indata.copy())
            except Exception as e:
                logger.error(f"处理音频数据时出错: {e}")

    def _handle_asr_result(self, text: str, is_final: bool):
        """处理ASR识别结果"""
        if is_final and text:
            # 添加标点符号处理
            processed_text = self.text_processor.add_punctuation(text)
            logger.info(f"最终识别结果: {processed_text}")

            with self.lock:
                self.last_result = processed_text
                self.results.append(processed_text)
                self.current_stream = ""

            # 调用外部回调函数
            if self.recognition_callback:
                try:
                    self.recognition_callback(processed_text, True)
                except Exception as e:
                    logger.error(f"调用识别结果回调函数时出错: {e}")
        else:
            # 实时流识别结果
            with self.lock:
                self.current_stream = text

            # 调用外部回调函数
            if self.recognition_callback and text:
                try:
                    self.recognition_callback(text, False)
                except Exception as e:
                    logger.error(f"调用流式识别回调函数时出错: {e}")

    def _process_audio_stream(self):
        """处理音频数据的线程函数"""
        logger.info("音频处理线程已启动")

        while self.is_recognizing:
            try:
                # 从队列获取音频数据
                try:
                    speech_chunk = self.audio_processor.audio_queue.get(timeout=0.5).flatten()
                except queue.Empty:
                    continue

                # 如果暂停，跳过处理
                if self.is_paused:
                    continue

                # 发送音频数据到ASR服务
                if (self.asr_client and self.asr_client.is_connected and
                    self.asr_client.is_recognizing):
                    try:
                        if not self.asr_client.send_audio(speech_chunk):
                            logger.warning("发送音频数据失败")
                            self.error_count += 1

                            if self.error_count >= self.max_retries:
                                self._handle_connection_error()

                    except Exception as e:
                        logger.error(f"发送音频数据时出错: {e}")
                        self.error_count += 1

            except Exception as e:
                logger.error(f"处理音频数据时出错: {e}")
                self.error_count += 1

        logger.info("音频处理线程已结束")

    def _handle_connection_error(self):
        """处理连接错误"""
        logger.warning("错误次数过多，尝试重新连接ASR服务...")
        try:
            # 确保完全断开旧连接
            if self.asr_client:
                if self.asr_client.is_connected:
                    logger.info("断开现有ASR连接...")
                    self.asr_client.disconnect()

                # 等待连接完全关闭
                time.sleep(1.0)

            # 等待重连延迟
            logger.info("等待重连延迟...")
            time.sleep(2.0)

            # 重新连接
            logger.info("尝试重新连接ASR服务...")
            if self.asr_client and self.asr_client.connect():
                logger.info("重新连接成功，启动识别...")
                self.asr_client.start_recognition(callback=self._handle_asr_result)
                self.error_count = 0
                logger.info("重新连接ASR服务成功")
            else:
                logger.error("重新连接ASR服务失败，停止语音识别")
                self.stop_recognition()

        except Exception as e:
            logger.error(f"重新连接ASR服务时出错: {e}")
            self.stop_recognition()


# ==================== Web语音识别服务 ====================

class WebVoiceRecognitionService:
    """
    Web语音识别服务

    提供基于Web界面的语音识别功能
    """

    def __init__(self, host: str = '0.0.0.0', port: int = 5000):
        """
        初始化Web语音识别服务

        Args:
            host: 服务监听地址
            port: 服务监听端口
        """
        self.host = host
        self.port = port
        self.is_running = False
        self.recognition_active = False
        self.voice_results_queue = queue.Queue()

        # ASR管理器（延迟初始化）
        self.asr_manager = None

        # 静音检测参数
        self.last_speech_time = time.time()
        self.silence_timeout = 1.5
        self.recognition_start_time = None
        self.is_listening = False
        self.last_recognized_text = ""

        logger.info("Web语音识别服务初始化完成")

    def start_service(self):
        """启动Web语音识别服务"""
        logger.info(f"启动Web语音识别服务，监听 {self.host}:{self.port}")
        self.is_running = True

        # 这里可以集成Flask-SocketIO等Web框架
        # 由于篇幅限制，这里只提供接口框架

    def stop_service(self):
        """停止Web语音识别服务"""
        logger.info("停止Web语音识别服务")
        self.is_running = False

        if self.asr_manager.is_recognizing:
            self.asr_manager.stop_recognition()

    def get_voice_results(self) -> List[Dict[str, Any]]:
        """获取语音识别结果"""
        results = []
        try:
            while not self.voice_results_queue.empty():
                result = self.voice_results_queue.get_nowait()
                results.append(result)
        except queue.Empty:
            pass
        return results

    def is_voice_service_active(self) -> bool:
        """检查语音识别服务是否活跃"""
        return self.is_running

    def start_recognition(self, config_path: str = None) -> bool:
        """开始语音识别"""
        try:
            # 延迟初始化ASR管理器
            if self.asr_manager is None:
                if config_path:
                    self.asr_manager = ASRManager(config_path=config_path)
                else:
                    logger.warning("未提供配置文件，Web语音识别服务需要有效的ASR配置")
                    return False

            def on_result(text: str, is_final: bool):
                if is_final and text.strip():
                    self.voice_results_queue.put({
                        'text': text,
                        'timestamp': datetime.now().isoformat(),
                        'source': 'web_voice_recognition'
                    })

            success = self.asr_manager.start_recognition(callback=on_result)
            if success:
                self.recognition_active = True
                self.is_listening = True
                self.recognition_start_time = time.time()
                self.last_speech_time = time.time()
                self.last_recognized_text = ""

            return success

        except Exception as e:
            logger.error(f"启动语音识别失败: {e}")
            return False

    def stop_recognition(self) -> bool:
        """停止语音识别"""
        try:
            success = True
            if self.asr_manager:
                success = self.asr_manager.stop_recognition()
            self.recognition_active = False
            self.is_listening = False
            self.recognition_start_time = None
            return success

        except Exception as e:
            logger.error(f"停止语音识别失败: {e}")
            return False


# ==================== 工厂类和工具函数 ====================

class ASRClientFactory:
    """
    ASR客户端工厂类

    根据配置创建不同类型的ASR客户端
    """

    @staticmethod
    def create_client(service_type: str, app_key: str, access_key: str, **kwargs) -> ASRClientBase:
        """
        创建ASR客户端

        Args:
            service_type: 服务类型 ('volcano' 或 'doubao')
            app_key: 应用密钥
            access_key: 访问密钥
            **kwargs: 其他参数

        Returns:
            ASRClientBase: ASR客户端实例

        Raises:
            ValueError: 不支持的服务类型
        """
        service_type = service_type.lower()

        if service_type == 'volcano':
            return VolcanoASRClient(app_key, access_key, **kwargs)
        elif service_type == 'doubao':
            return DoubaoASRClient(app_key, access_key, **kwargs)
        else:
            raise ValueError(f"不支持的ASR服务类型: {service_type}")

    @staticmethod
    def create_from_config(config_path: str = None) -> ASRClientBase:
        """
        从配置文件创建ASR客户端

        Args:
            config_path: 配置文件路径

        Returns:
            ASRClientBase: ASR客户端实例
        """
        config = get_config(config_path)

        service_type = config.get('API', 'asr_service_type', 'volcano')
        app_key = config.get('API', 'asr_app_key', '')
        access_key = config.get('API', 'asr_access_key', '')

        if not app_key or not access_key:
            raise ValueError("配置文件中缺少ASR API密钥")

        return ASRClientFactory.create_client(service_type, app_key, access_key)


class ASRUtils:
    """
    ASR工具类

    提供便捷的ASR功能接口
    """

    @staticmethod
    def create_manager(config_path: str = None, **kwargs) -> ASRManager:
        """
        创建ASR管理器

        Args:
            config_path: 配置文件路径
            **kwargs: 其他参数

        Returns:
            ASRManager: ASR管理器实例
        """
        return ASRManager(config_path=config_path, **kwargs)

    @staticmethod
    def create_web_service(host: str = '0.0.0.0', port: int = 5000) -> WebVoiceRecognitionService:
        """
        创建Web语音识别服务

        Args:
            host: 服务监听地址
            port: 服务监听端口

        Returns:
            WebVoiceRecognitionService: Web语音识别服务实例
        """
        return WebVoiceRecognitionService(host, port)

    @staticmethod
    def preprocess_text_for_tts(text: str) -> str:
        """
        预处理文本以优化TTS输出

        Args:
            text: 原始文本

        Returns:
            str: 处理后的文本
        """
        processor = ASRTextProcessor()
        return processor.preprocess_text_for_tts(text)

    @staticmethod
    def add_punctuation(text: str) -> str:
        """
        智能添加标点符号

        Args:
            text: 原始文本

        Returns:
            str: 添加标点后的文本
        """
        processor = ASRTextProcessor()
        return processor.add_punctuation(text)


# ==================== 使用示例 ====================

def example_usage():
    """
    使用示例

    展示如何使用ASR工具集的各个组件
    """

    # 示例1: 使用ASR管理器进行语音识别
    def example_asr_manager():
        """ASR管理器使用示例"""
        print("=== ASR管理器使用示例 ===")

        # 创建ASR管理器
        asr_manager = ASRUtils.create_manager()

        # 定义回调函数
        def on_recognition_result(text: str, is_final: bool):
            if is_final:
                print(f"最终识别结果: {text}")
            else:
                print(f"实时识别中: {text}")

        # 启动语音识别
        if asr_manager.start_recognition(on_recognition_result):
            print("语音识别已启动，请说话...")

            # 模拟运行一段时间
            time.sleep(10)

            # 停止语音识别
            asr_manager.stop_recognition()

            # 获取识别结果
            results = asr_manager.get_recognition_results()
            print(f"所有识别结果: {results}")
        else:
            print("启动语音识别失败")

    # 示例2: 使用特定ASR客户端
    def example_asr_client():
        """ASR客户端使用示例"""
        print("=== ASR客户端使用示例 ===")

        try:
            # 创建火山引擎ASR客户端
            client = ASRClientFactory.create_client(
                'volcano',
                'your_app_key',
                'your_access_key'
            )

            # 连接到服务
            if client.connect():
                print("成功连接到ASR服务")

                # 定义回调函数
                def on_result(text: str, is_final: bool):
                    print(f"识别结果: {text}, 是否最终: {is_final}")

                # 启动识别
                if client.start_recognition(on_result):
                    print("语音识别已启动")

                    # 这里可以发送音频数据
                    # client.send_audio(audio_data)

                    # 停止识别
                    client.stop_recognition()

                # 断开连接
                client.disconnect()
            else:
                print("连接ASR服务失败")

        except Exception as e:
            print(f"ASR客户端示例出错: {e}")

    # 示例3: 文本处理
    def example_text_processing():
        """文本处理使用示例"""
        print("=== 文本处理使用示例 ===")

        # 原始文本
        raw_text = "你好世界这是一个测试"

        # 添加标点符号
        punctuated_text = ASRUtils.add_punctuation(raw_text)
        print(f"原始文本: {raw_text}")
        print(f"添加标点后: {punctuated_text}")

        # TTS预处理
        tts_text = ASRUtils.preprocess_text_for_tts("这是代码：```python\nprint('hello')\n```结束")
        print(f"TTS预处理后: {tts_text}")

    # 运行示例
    try:
        example_text_processing()
        # example_asr_manager()  # 需要音频设备
        # example_asr_client()   # 需要有效的API密钥
    except Exception as e:
        print(f"运行示例时出错: {e}")


if __name__ == "__main__":
    """
    模块直接运行时的入口点
    """
    print("ASR Utils - 模块化语音识别工具集")
    print("=" * 50)

    # 运行使用示例
    example_usage()
