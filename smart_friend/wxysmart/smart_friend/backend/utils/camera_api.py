"""
服务器端摄像头相关的 FastAPI 路由和端点
提供摄像头操作的 REST API 接口，支持客户端摄像头连接
"""
from fastapi import APIRouter, HTTPException, Depends
from datetime import datetime, timezone
from typing import Optional

from backend.services.camera_manager_server import get_camera_manager, ServerCameraManager
from backend.utils.logging import get_camera_logger

# 导入数据模型
from backend.models.camera_models import (
    CameraCaptureRequest,
    CameraCaptureResponse,
    CameraInfo,
    CameraListResponse,
    CameraRegisterRequest,
    SessionCreateRequest,
    SessionResponse,
    FrameUpdateRequest,
    TransformRequest,
    FrameCaptureResponse,
    CameraHealthResponse
)

logger = get_camera_logger()
router = APIRouter()


@router.post("/register", response_model=SessionResponse)
async def register_client_cameras(
    request: CameraRegisterRequest,
    camera_manager: ServerCameraManager = Depends(get_camera_manager)
):
    """
    注册客户端摄像头
    
    客户端调用此接口注册其可用的摄像头设备
    """
    try:
        success = camera_manager.register_client_cameras(
            request.client_id, 
            request.cameras
        )
        
        if success:
            return SessionResponse(
                success=True,
                message=f"成功注册 {len(request.cameras)} 个摄像头"
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="摄像头注册失败"
            )
            
    except Exception as e:
        logger.error(f"注册客户端摄像头时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"注册摄像头失败: {str(e)}"
        )


@router.get("/cameras", response_model=CameraListResponse)
async def list_cameras(
    client_id: Optional[str] = None,
    camera_manager: ServerCameraManager = Depends(get_camera_manager)
):
    """
    获取摄像头列表
    
    - **client_id**: 可选，指定客户端ID则只返回该客户端的摄像头
    """
    try:
        if client_id:
            cameras_raw = camera_manager.get_client_cameras(client_id)
        else:
            cameras_raw = camera_manager.get_all_cameras()
        
        # 转换为 Pydantic 模型
        cameras = []
        for cam in cameras_raw:
            camera_info = CameraInfo(
                camera_id=cam.camera_id,
                name=cam.name,
                status=cam.status,
                resolution=cam.resolution,
                client_id=cam.client_id,
                stream_type=cam.stream_type
            )
            cameras.append(camera_info)
        
        available_count = len([c for c in cameras if c.status == "available"])
        
        return CameraListResponse(
            cameras=cameras,
            total_count=len(cameras),
            available_count=available_count
        )
        
    except Exception as e:
        logger.error(f"获取摄像头列表时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取摄像头列表失败: {str(e)}"
        )


@router.post("/session/create", response_model=SessionResponse)
async def create_camera_session(
    request: SessionCreateRequest,
    camera_manager: ServerCameraManager = Depends(get_camera_manager)
):
    """
    创建摄像头会话
    
    创建一个摄像头会话以开始视频流传输
    """
    try:
        session_id = camera_manager.create_camera_session(
            request.client_id,
            request.camera_id
        )
        
        if session_id:
            return SessionResponse(
                success=True,
                session_id=session_id,
                message="摄像头会话创建成功"
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="摄像头会话创建失败"
            )
            
    except Exception as e:
        logger.error(f"创建摄像头会话时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"创建摄像头会话失败: {str(e)}"
        )


@router.delete("/session/{session_id}", response_model=SessionResponse)
async def close_camera_session(
    session_id: str,
    camera_manager: ServerCameraManager = Depends(get_camera_manager)
):
    """关闭摄像头会话"""
    try:
        success = camera_manager.close_camera_session(session_id)
        
        if success:
            return SessionResponse(
                success=True,
                message="摄像头会话已关闭"
            )
        else:
            raise HTTPException(
                status_code=404,
                detail="会话不存在"
            )
            
    except Exception as e:
        logger.error(f"关闭摄像头会话时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"关闭摄像头会话失败: {str(e)}"
        )


@router.post("/frame/update", response_model=SessionResponse)
async def update_frame(
    request: FrameUpdateRequest,
    camera_manager: ServerCameraManager = Depends(get_camera_manager)
):
    """
    更新画面数据

    客户端调用此接口上传摄像头画面数据
    支持测试模式（test_mode=True时不保存数据，仅用于性能测试）
    """
    try:
        if request.test_mode:
            # 测试模式：只验证会话存在，不保存数据
            session_exists = camera_manager.get_session_status(request.session_id) is not None
            if session_exists:
                logger.debug(f"测试模式：接收到会话 {request.session_id} 的画面数据，数据长度: {len(request.frame_data)}")
                return SessionResponse(
                    success=True,
                    message="测试模式：画面数据接收成功（未保存）"
                )
            else:
                raise HTTPException(
                    status_code=404,
                    detail="会话不存在"
                )
        else:
            # 正常模式：保存数据
            success = camera_manager.update_session_frame(
                request.session_id,
                request.frame_data
            )

            if success:
                return SessionResponse(
                    success=True,
                    message="画面数据更新成功"
                )
            else:
                raise HTTPException(
                    status_code=404,
                    detail="会话不存在"
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新画面数据时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"更新画面数据失败: {str(e)}"
        )


@router.get("/frame/{session_id}", response_model=FrameCaptureResponse)
async def get_frame(
    session_id: str,
    apply_transforms: bool = True,
    camera_manager: ServerCameraManager = Depends(get_camera_manager)
):
    """
    获取会话的最新画面

    Args:
        session_id: 会话ID
        apply_transforms: 是否应用变换（旋转、缩放等），默认为True
    """
    try:
        # 获取原始画面数据
        frame_data = camera_manager.get_session_frame(session_id)

        if not frame_data:
            raise HTTPException(
                status_code=404,
                detail="会话不存在或无画面数据"
            )

        # 应用变换（如果需要）
        if apply_transforms:
            transformed_data = camera_manager.apply_transformations(session_id, frame_data)
            if transformed_data:
                frame_data = transformed_data
                logger.debug(f"已对会话 {session_id} 应用画面变换")
            else:
                logger.warning(f"会话 {session_id} 变换应用失败，返回原始画面")

        return FrameCaptureResponse(
            success=True,
            image_data=frame_data,
            timestamp=datetime.now(timezone.utc).isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取画面时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取画面失败: {str(e)}"
        )


@router.post("/transform", response_model=SessionResponse)
async def apply_transform(
    request: TransformRequest,
    camera_manager: ServerCameraManager = Depends(get_camera_manager)
):
    """
    应用画面变换

    设置旋转角度和缩放因子
    """
    try:
        success = True

        if request.rotation is not None:
            success &= camera_manager.set_session_rotation(
                request.session_id,
                request.rotation
            )

        if request.zoom is not None:
            success &= camera_manager.set_session_zoom(
                request.session_id,
                request.zoom
            )

        if success:
            return SessionResponse(
                success=True,
                message="画面变换设置成功"
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="画面变换设置失败"
            )

    except Exception as e:
        logger.error(f"应用画面变换时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"应用画面变换失败: {str(e)}"
        )


@router.get("/session/{session_id}/status")
async def get_session_status(
    session_id: str,
    camera_manager: ServerCameraManager = Depends(get_camera_manager)
):
    """获取会话状态"""
    try:
        status = camera_manager.get_session_status(session_id)

        if status:
            return status
        else:
            raise HTTPException(
                status_code=404,
                detail="会话不存在"
            )

    except Exception as e:
        logger.error(f"获取会话状态时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取会话状态失败: {str(e)}"
        )


@router.get("/health", response_model=CameraHealthResponse)
async def health_check(
    camera_manager: ServerCameraManager = Depends(get_camera_manager)
):
    """
    摄像头服务健康检查
    """
    try:
        all_cameras = camera_manager.get_all_cameras()
        active_sessions = len(camera_manager.sessions)

        return CameraHealthResponse(
            service_status="healthy",
            total_cameras=len(all_cameras),
            active_sessions=active_sessions,
            timestamp=datetime.now(timezone.utc).isoformat()
        )

    except Exception as e:
        logger.error(f"健康检查时发生错误: {e}")
        return CameraHealthResponse(
            service_status="error",
            total_cameras=0,
            active_sessions=0,
            timestamp=datetime.now(timezone.utc).isoformat()
        )


@router.post("/capture", response_model=CameraCaptureResponse)
async def capture_image(
    request: CameraCaptureRequest,
    camera_manager: ServerCameraManager = Depends(get_camera_manager)
):
    """
    捕获并保存图像

    从指定会话的摄像头捕获当前画面并保存到指定路径

    - **session_id**: 会话ID（必需）
    - **save_path**: 保存路径，不指定则使用默认路径 'captured_images'
    - **filename**: 文件名，不指定则使用北京时间自动生成
    - **apply_transforms**: 是否应用当前的变换设置（旋转、缩放等），默认为True

    返回捕获结果，包括文件路径、文件名、捕获时间、图像尺寸和文件大小
    """
    try:
        logger.info(f"收到图像捕获请求: session_id={request.session_id}, "
                   f"save_path={request.save_path}, filename={request.filename}, "
                   f"apply_transforms={request.apply_transforms}")

        # 调用相机管理器的捕获方法
        result = camera_manager.capture_image(
            session_id=request.session_id,
            save_path=request.save_path,
            filename=request.filename,
            apply_transforms=request.apply_transforms
        )

        if result["success"]:
            return CameraCaptureResponse(
                success=True,
                message=result["message"],
                file_path=result.get("file_path"),
                filename=result.get("filename"),
                capture_time=result.get("capture_time"),
                image_size=result.get("image_size"),
                file_size=result.get("file_size")
            )
        else:
            # 根据错误代码返回适当的HTTP状态码
            error_code = result.get("error_code", "UNKNOWN_ERROR")
            if error_code == "SESSION_NOT_FOUND":
                status_code = 404
            elif error_code == "NO_FRAME_DATA":
                status_code = 400
            else:
                status_code = 500

            raise HTTPException(
                status_code=status_code,
                detail=result["message"]
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"捕获图像时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"捕获图像失败: {str(e)}"
        )
