"""
WebRTC VAD实时语音活动检测器
使用WebRTC VAD进行实时语音活动检测，提高ASR识别的灵敏度和准确性
"""

import webrtcvad
import numpy as np
import logging
import time
from typing import List, Tuple, Optional, Callable
from collections import deque
import threading

logger = logging.getLogger(__name__)


class WebRTCVADDetector:
    """
    WebRTC VAD实时语音活动检测器
    
    参数说明：
    - detection_window: 检测时间窗口（秒），默认0.5秒
    - chunk_duration: 每个检测块的时长（毫秒），默认20ms
    - activation_threshold: 有效语音激活率阈值，默认40%
    - sample_rate: 音频采样率，支持8000, 16000, 32000, 48000Hz
    - aggressiveness: VAD敏感度，0-3，数值越高越敏感
    """
    
    def __init__(self,
                 detection_window: float = 0.5,  # 检测时间窗口：0.5秒
                 chunk_duration: int = 20,       # 检测块时长：20ms
                 activation_threshold: float = 0.4,  # 激活率阈值：40%
                 sample_rate: int = 16000,       # 采样率：16kHz
                 aggressiveness: int = 2):       # VAD敏感度：中等
        
        self.detection_window = detection_window
        self.chunk_duration = chunk_duration
        self.activation_threshold = activation_threshold
        self.sample_rate = sample_rate
        self.aggressiveness = aggressiveness
        
        # 验证参数
        if sample_rate not in [8000, 16000, 32000, 48000]:
            raise ValueError(f"不支持的采样率: {sample_rate}，支持的采样率: 8000, 16000, 32000, 48000")
        
        if aggressiveness not in [0, 1, 2, 3]:
            raise ValueError(f"VAD敏感度必须在0-3之间，当前值: {aggressiveness}")
        
        # 初始化WebRTC VAD
        self.vad = webrtcvad.Vad(aggressiveness)
        
        # 计算参数
        self.chunk_samples = int(sample_rate * chunk_duration / 1000)  # 每个chunk的样本数
        self.chunks_per_window = int(detection_window * 1000 / chunk_duration)  # 每个窗口的chunk数
        self.activation_chunks_threshold = int(self.chunks_per_window * activation_threshold)  # 激活chunk阈值
        
        # 状态管理
        self.chunk_buffer = deque(maxlen=self.chunks_per_window)  # chunk缓冲区
        self.activation_buffer = deque(maxlen=self.chunks_per_window)  # 激活状态缓冲区
        self.audio_cache = deque()  # 有效音频缓存
        self.is_voice_active = False  # 当前语音活动状态
        self.last_voice_time = 0  # 最后检测到语音的时间
        
        # 统计信息
        self.total_chunks = 0
        self.voice_chunks = 0
        self.detection_windows = 0
        self.voice_windows = 0
        
        # 回调函数
        self.voice_start_callback: Optional[Callable] = None
        self.voice_end_callback: Optional[Callable] = None
        self.audio_ready_callback: Optional[Callable[[bytes], None]] = None
        
        logger.info(f"WebRTC VAD检测器初始化完成:")
        logger.info(f"  检测窗口: {detection_window}s")
        logger.info(f"  chunk时长: {chunk_duration}ms")
        logger.info(f"  激活阈值: {activation_threshold*100}%")
        logger.info(f"  每窗口chunk数: {self.chunks_per_window}")
        logger.info(f"  激活chunk阈值: {self.activation_chunks_threshold}")
        logger.info(f"  采样率: {sample_rate}Hz")
        logger.info(f"  VAD敏感度: {aggressiveness}")
    
    def set_callbacks(self,
                     voice_start_callback: Optional[Callable] = None,
                     voice_end_callback: Optional[Callable] = None,
                     audio_ready_callback: Optional[Callable[[bytes], None]] = None):
        """设置回调函数"""
        self.voice_start_callback = voice_start_callback
        self.voice_end_callback = voice_end_callback
        self.audio_ready_callback = audio_ready_callback
    
    def process_audio_chunk(self, audio_data: bytes) -> bool:
        """
        处理音频数据块
        
        Args:
            audio_data: 音频数据（16位PCM格式）
            
        Returns:
            bool: 当前是否检测到语音活动
        """
        try:
            # 确保音频数据长度正确
            expected_bytes = self.chunk_samples * 2  # 16位 = 2字节
            if len(audio_data) != expected_bytes:
                # 如果数据长度不匹配，进行填充或截断
                if len(audio_data) < expected_bytes:
                    audio_data = audio_data + b'\x00' * (expected_bytes - len(audio_data))
                else:
                    audio_data = audio_data[:expected_bytes]
            
            # 使用WebRTC VAD检测语音活动
            is_speech = self.vad.is_speech(audio_data, self.sample_rate)
            
            # 更新缓冲区
            self.chunk_buffer.append(audio_data)
            self.activation_buffer.append(is_speech)
            
            # 更新统计
            self.total_chunks += 1
            if is_speech:
                self.voice_chunks += 1
            
            # 检查是否有完整的检测窗口
            if len(self.activation_buffer) == self.chunks_per_window:
                self._process_detection_window()
            
            return self.is_voice_active
            
        except Exception as e:
            logger.error(f"处理音频chunk时出错: {e}")
            return False
    
    def _process_detection_window(self):
        """处理完整的检测窗口"""
        try:
            # 计算当前窗口的激活chunk数量
            active_chunks = sum(self.activation_buffer)
            
            # 判断是否为有效语音
            is_voice_window = active_chunks >= self.activation_chunks_threshold
            
            # 更新统计
            self.detection_windows += 1
            if is_voice_window:
                self.voice_windows += 1
            
            # 状态变化检测
            previous_state = self.is_voice_active
            self.is_voice_active = is_voice_window
            
            if is_voice_window:
                self.last_voice_time = time.time()
                
                # 将当前窗口的音频数据加入缓存
                window_audio = b''.join(self.chunk_buffer)
                self.audio_cache.append(window_audio)
                
                # 如果有音频就绪回调，发送缓存的音频
                if self.audio_ready_callback:
                    self.audio_ready_callback(window_audio)
                
                # 语音开始回调
                if not previous_state and self.voice_start_callback:
                    self.voice_start_callback()
                    
                logger.debug(f"🎤 检测到语音活动: {active_chunks}/{self.chunks_per_window} chunks激活 "
                           f"({active_chunks/self.chunks_per_window*100:.1f}%)")
            else:
                # 语音结束回调
                if previous_state and self.voice_end_callback:
                    self.voice_end_callback()
                    
                logger.debug(f"⏸️ 未检测到语音活动: {active_chunks}/{self.chunks_per_window} chunks激活 "
                           f"({active_chunks/self.chunks_per_window*100:.1f}%)")
            
        except Exception as e:
            logger.error(f"处理检测窗口时出错: {e}")
    
    def get_cached_audio(self) -> bytes:
        """获取缓存的有效音频数据"""
        if not self.audio_cache:
            return b''
        
        # 合并所有缓存的音频
        cached_audio = b''.join(self.audio_cache)
        self.audio_cache.clear()
        return cached_audio
    
    def clear_cache(self):
        """清空音频缓存"""
        self.audio_cache.clear()
    
    def get_statistics(self) -> dict:
        """获取检测统计信息"""
        voice_rate = (self.voice_chunks / max(self.total_chunks, 1)) * 100
        window_voice_rate = (self.voice_windows / max(self.detection_windows, 1)) * 100
        
        return {
            "total_chunks": self.total_chunks,
            "voice_chunks": self.voice_chunks,
            "voice_chunk_rate": voice_rate,
            "detection_windows": self.detection_windows,
            "voice_windows": self.voice_windows,
            "voice_window_rate": window_voice_rate,
            "is_voice_active": self.is_voice_active,
            "last_voice_time": self.last_voice_time,
            "cache_size": len(self.audio_cache)
        }
    
    def reset(self):
        """重置检测器状态"""
        self.chunk_buffer.clear()
        self.activation_buffer.clear()
        self.audio_cache.clear()
        self.is_voice_active = False
        self.last_voice_time = 0
        
        # 重置统计
        self.total_chunks = 0
        self.voice_chunks = 0
        self.detection_windows = 0
        self.voice_windows = 0
        
        logger.info("WebRTC VAD检测器已重置")


class VADEnhancedAudioProcessor:
    """
    VAD增强的音频处理器
    结合WebRTC VAD检测和音频缓冲，提供更智能的音频处理
    """
    
    def __init__(self,
                 vad_detector: WebRTCVADDetector,
                 silence_timeout: float = 2.0,  # 静音超时时间
                 min_voice_duration: float = 0.3):  # 最小语音持续时间
        
        self.vad_detector = vad_detector
        self.silence_timeout = silence_timeout
        self.min_voice_duration = min_voice_duration
        
        # 状态管理
        self.is_processing = False
        self.voice_start_time = 0
        self.last_voice_time = 0
        
        # 音频缓冲
        self.audio_buffer = deque()
        self.buffer_lock = threading.RLock()
        
        # 设置VAD回调
        self.vad_detector.set_callbacks(
            voice_start_callback=self._on_voice_start,
            voice_end_callback=self._on_voice_end,
            audio_ready_callback=self._on_audio_ready
        )
        
        logger.info(f"VAD增强音频处理器初始化完成:")
        logger.info(f"  静音超时: {silence_timeout}s")
        logger.info(f"  最小语音时长: {min_voice_duration}s")
    
    def _on_voice_start(self):
        """语音开始回调"""
        self.voice_start_time = time.time()
        logger.debug("🎤 VAD检测到语音开始")
    
    def _on_voice_end(self):
        """语音结束回调"""
        voice_duration = time.time() - self.voice_start_time
        logger.debug(f"⏸️ VAD检测到语音结束，持续时间: {voice_duration:.2f}s")
    
    def _on_audio_ready(self, audio_data: bytes):
        """音频就绪回调"""
        with self.buffer_lock:
            self.audio_buffer.append(audio_data)
            self.last_voice_time = time.time()
    
    def process_audio(self, audio_data: bytes) -> bool:
        """
        处理音频数据
        
        Args:
            audio_data: 音频数据
            
        Returns:
            bool: 是否检测到语音活动
        """
        return self.vad_detector.process_audio_chunk(audio_data)
    
    def get_buffered_audio(self) -> bytes:
        """获取缓冲的音频数据"""
        with self.buffer_lock:
            if not self.audio_buffer:
                return b''
            
            buffered_audio = b''.join(self.audio_buffer)
            self.audio_buffer.clear()
            return buffered_audio
    
    def should_stop_recording(self) -> bool:
        """判断是否应该停止录音"""
        if not self.last_voice_time:
            return False
        
        silence_duration = time.time() - self.last_voice_time
        return silence_duration >= self.silence_timeout
    
    def get_voice_duration(self) -> float:
        """获取当前语音持续时间"""
        if not self.voice_start_time:
            return 0
        return time.time() - self.voice_start_time
    
    def is_valid_voice_session(self) -> bool:
        """判断是否为有效的语音会话"""
        return self.get_voice_duration() >= self.min_voice_duration
    
    def reset(self):
        """重置处理器状态"""
        with self.buffer_lock:
            self.audio_buffer.clear()
        
        self.voice_start_time = 0
        self.last_voice_time = 0
        self.vad_detector.reset()
        
        logger.info("VAD增强音频处理器已重置")
