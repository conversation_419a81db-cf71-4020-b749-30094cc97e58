"""
DoubaoASRClient - 豆包语音识别客户端

这个模块提供了一个基于豆包(火山引擎)API的流式双向语音识别客户端。
使用WebSocket协议实现实时的音频流传输和识别结果接收。

主要功能包括：
1. 建立WebSocket连接并进行鉴权
2. 发送初始参数配置
3. 流式发送音频数据
4. 接收并解析识别结果
5. 自动处理连接状态和错误恢复

使用示例:
    from doubao_asr_client import DoubaoASRClient
    
    # 创建ASR客户端实例
    client = DoubaoASRClient(app_key="your_app_key", access_key="your_access_key")
    
    # 定义回调函数处理识别结果
    def on_result(text, is_final):
        print(f"识别结果: {text}, 是否最终结果: {is_final}")
    
    # 连接到服务器
    client.connect()
    
    # 启动语音识别
    client.start_recognition(callback=on_result)
    
    # 发送音频数据
    client.send_audio(audio_data)
    
    # ... 应用逻辑 ...
    
    # 发送结束标志
    client.finalize_recognition()
    
    # 断开连接
    client.disconnect()
"""

import json
import time
import threading
import queue
import struct
import websocket
import numpy as np
from typing import Callable, Optional, Dict, List, Union, Any, Tuple

# 导入日志管理模块
from utils.logging_manager import get_logger

# 获取日志记录器
logger = get_logger("DoubaoASRClient")


class DoubaoASRClient:
    """
    豆包(火山引擎)流式双向语音识别客户端

    主要特性:
    - 基于WebSocket的流式双向通信
    - 支持实时音频发送和识别结果接收
    - 自动断线重连和错误恢复
    - 支持多种音频格式和参数设置

    属性:
        is_connected (bool): 指示是否已连接到服务器
        is_recognizing (bool): 指示是否正在进行识别
        last_result (str): 最近一次的识别结果
        results (list): 所有识别结果的历史记录
    """

    def __init__(self,
                 app_key: str,
                 access_key: str,
                 model_name: str = "bigmodel",
                 sample_rate: int = 16000,
                 channels: int = 1,
                 format: str = "pcm",
                 codec: str = "raw",
                 bits: int = 16,
                 language: str = "zh-CN",
                 audio_chunk_size: int = 3200,  # 200ms at 16kHz
                 reconnect_attempts: int = 3,
                 reconnect_delay: float = 2.0,
                 result_callback: Optional[Callable[[str, bool], None]] = None):
        """
        初始化豆包语音识别客户端

        Args:
            app_key (str): 火山引擎控制台获取的APP ID
            access_key (str): 火山引擎控制台获取的Access Token
            model_name (str): 使用的语音识别模型名称
            sample_rate (int): 音频采样率
            channels (int): 音频通道数
            format (str): 音频容器格式
            codec (str): 音频编码格式
            bits (int): 音频采样点位数
            language (str): 识别语言
            audio_chunk_size (int): 音频数据分块大小（字节数）
            reconnect_attempts (int): 连接断开时的重试次数
            reconnect_delay (float): 重试间隔时间（秒）
            result_callback (Callable): 识别结果回调函数，接收识别文本和是否为最终结果标志
        """
        # 检查必要参数
        if not app_key:
            raise ValueError("必须提供火山引擎的APP ID")
        if not access_key:
            raise ValueError("必须提供火山引擎的Access Token")
            
        # API凭据
        self.app_key = app_key
        self.access_key = access_key

        # 音频参数
        self.sample_rate = sample_rate
        self.channels = channels
        self.format = format
        self.codec = codec
        self.bits = bits
        self.language = language
        self.audio_chunk_size = audio_chunk_size

        # 模型参数
        self.model_name = model_name

        # 连接参数
        self.ws_url = "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel"  # 双向流式模式
        # 也可以使用流式输入模式: "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_nostream"
        # 可选: "volc.bigasr.sauc.concurrent"（并发版）
        self.resource_id = "volc.bigasr.sauc.duration"
        self.reconnect_attempts = reconnect_attempts
        self.reconnect_delay = reconnect_delay

        # 状态变量
        self.is_connected = False
        self.is_recognizing = False
        self.results = []
        self.last_result = ""
        self.error_count = 0
        self.request_id = ""

        # 线程和锁
        self.ws = None
        self.ws_thread = None
        self.send_thread = None
        self.lock = threading.RLock()
        self.audio_queue = queue.Queue()
        self.result_callback = result_callback

        # 协议参数
        self.sequence_number = 0

    def connect(self) -> bool:
        """
        连接到豆包语音识别服务器

        Returns:
            bool: 是否成功连接

        Raises:
            ConnectionError: 如果连接失败
        """
        with self.lock:
            if self.is_connected:
                logger.warning("已经连接到服务器")
                return True

            try:
                logger.info("正在连接到豆包语音识别服务器...")

                # 准备HTTP请求头
                headers = {
                    "X-Api-App-Key": self.app_key,
                    "X-Api-Access-Key": self.access_key,
                    "X-Api-Resource-Id": self.resource_id,  # 必需字段：资源ID
                    "X-Api-Connect-Id": self._generate_request_id()
                }

                logger.info(f"使用资源ID: {self.resource_id}")

                # 创建WebSocket连接
                self.ws = websocket.WebSocketApp(
                    self.ws_url,
                    header=headers,
                    on_open=self._on_open,
                    on_message=self._on_message,
                    on_error=self._on_error,
                    on_close=self._on_close
                )

                # 在单独的线程中运行WebSocket
                self.ws_thread = threading.Thread(
                    target=self.ws.run_forever,
                    daemon=True
                )
                self.ws_thread.start()

                # 等待连接建立
                retry_count = 0
                while not self.is_connected and retry_count < 10:
                    time.sleep(0.3)
                    retry_count += 1

                if not self.is_connected:
                    raise ConnectionError("无法连接到豆包语音识别服务器")

                logger.info("成功连接到豆包语音识别服务器")
                return True

            except Exception as e:
                logger.error(f"连接到豆包语音识别服务器失败: {str(e)}")
                self.is_connected = False
                raise ConnectionError(f"连接到豆包语音识别服务器失败: {str(e)}")

    def disconnect(self) -> bool:
        """
        断开与豆包语音识别服务器的连接

        Returns:
            bool: 是否成功断开连接
        """
        with self.lock:
            if not self.is_connected:
                logger.warning("当前未连接到服务器")
                return True

            try:
                if self.is_recognizing:
                    self.finalize_recognition()

                logger.info("正在断开与豆包语音识别服务器的连接...")

                if self.ws is not None:
                    self.ws.close()

                # 等待WebSocket线程结束
                if self.ws_thread is not None and self.ws_thread.is_alive():
                    self.ws_thread.join(timeout=5.0)

                # 等待发送线程结束
                if self.send_thread is not None and self.send_thread.is_alive():
                    self.send_thread.join(timeout=5.0)

                self.is_connected = False
                self.is_recognizing = False

                logger.info("已断开与豆包语音识别服务器的连接")
                return True

            except Exception as e:
                logger.error(f"断开连接时出错: {str(e)}")
                self.is_connected = False
                return False

    def start_recognition(self, callback: Optional[Callable[[str, bool], None]] = None) -> bool:
        """
        启动语音识别会话

        Args:
            callback (Callable[[str, bool], None], optional): 识别结果回调函数
                接收参数：文本结果、是否为最终结果

        Returns:
            bool: 是否成功启动识别会话

        Raises:
            RuntimeError: 如果未连接到服务器或发送初始参数失败
        """
        with self.lock:
            if not self.is_connected:
                logger.error("未连接到服务器，请先调用connect()")
                raise RuntimeError("未连接到服务器，请先调用connect()")

            if self.is_recognizing:
                logger.warning("已经在进行识别，请先调用finalize_recognition()")
                return False

            try:
                # 更新回调
                if callback is not None:
                    self.result_callback = callback

                # 清空队列
                while not self.audio_queue.empty():
                    try:
                        self.audio_queue.get_nowait()
                    except queue.Empty:
                        break

                # 重置序列号
                self.sequence_number = 0

                # 发送初始参数
                success = self._send_init_parameters()
                if not success:
                    raise RuntimeError("发送初始参数失败")

                self.is_recognizing = True
                self.results = []
                self.last_result = ""

                # 启动发送线程
                self.send_thread = threading.Thread(
                    target=self._audio_sender_thread,
                    daemon=True
                )
                self.send_thread.start()

                # logger.info("成功启动语音识别会话")
                return True

            except Exception as e:
                logger.error(f"启动语音识别会话失败: {str(e)}")
                self.is_recognizing = False
                raise RuntimeError(f"启动语音识别会话失败: {str(e)}")

    def send_audio(self, audio_data: Union[bytes, np.ndarray]) -> bool:
        """
        发送音频数据进行识别

        Args:
            audio_data (Union[bytes, np.ndarray]): 音频数据
                如果是numpy数组，应为float32类型，范围在[-1, 1]内

        Returns:
            bool: 是否成功发送数据
        """
        if not self.is_connected or not self.is_recognizing:
            logger.warning("未连接到服务器或未启动识别会话")
            return False

        try:
            # 如果是numpy数组，转换为bytes
            if isinstance(audio_data, np.ndarray):
                # 记录原始音频数据的最大音量
                max_volume = np.max(np.abs(audio_data))
                
                # 如果音量太低，记录日志但仍处理（不要直接丢弃）
                if max_volume < 0.01:  # 阈值可调整
                    logger.debug(f"音频音量较低: {max_volume:.4f}")
                
                # 确保数据是float32类型
                if audio_data.dtype != np.float32:
                    audio_data = audio_data.astype(np.float32)

                # 确保数据范围在[-1, 1]内
                if max_volume > 1.0:
                    audio_data = audio_data / max_volume

                # 可选：轻微增强非静音音频信号
                if 0.01 < max_volume < 0.3:  # 音量在低到中等范围内
                    gain_factor = min(2.0, 0.3 / max_volume)  # 最大增益2倍
                    audio_data = audio_data * gain_factor
                    logger.debug(f"对低音量音频应用增益: {gain_factor:.2f}倍")

                # 转换为16位整数
                audio_data = (audio_data * 32767).astype(np.int16)

                # 转换为bytes
                audio_data = audio_data.tobytes()

            # 检查数据是否为空
            if not audio_data or len(audio_data) == 0:
                logger.warning("尝试发送空的音频数据")
                return False

            # 将音频数据放入队列
            self.audio_queue.put(audio_data)
            return True

        except Exception as e:
            logger.error(f"发送音频数据时出错: {str(e)}")
            return False

    def finalize_recognition(self) -> bool:
        """
        结束当前的语音识别会话

        Returns:
            bool: 是否成功结束会话
        """
        with self.lock:
            if not self.is_connected:
                logger.warning("未连接到服务器")
                return False

            if not self.is_recognizing:
                logger.warning("当前没有进行识别会话")
                return False

            try:
                # 发送结束标志（空的音频数据包，但标记为最后一包）
                logger.info("正在发送识别会话结束标志...")

                # 构造最后一包的音频数据包
                header_bytes = self._create_audio_header(is_final=True)
                payload_size = struct.pack("!I", 0)  # 0字节的有效载荷

                # 发送数据
                if self.ws is not None and self.ws.sock and self.ws.sock.connected:
                    self.ws.send(header_bytes + payload_size,
                                 websocket.ABNF.OPCODE_BINARY)

                # 等待最后的结果
                time.sleep(1.0)

                self.is_recognizing = False

                # 等待发送线程结束
                if self.send_thread is not None and self.send_thread.is_alive():
                    self.send_thread.join(timeout=5.0)

                logger.info("已结束语音识别会话")
                return True

            except Exception as e:
                logger.error(f"结束识别会话时出错: {str(e)}")
                self.is_recognizing = False
                return False

    def get_results(self) -> List[str]:
        """
        获取所有语音识别结果

        Returns:
            List[str]: 所有识别结果的列表
        """
        with self.lock:
            return self.results.copy()

    def get_last_result(self) -> str:
        """
        获取最近一次的语音识别结果

        Returns:
            str: 最近一次的识别结果
        """
        with self.lock:
            return self.last_result

    def clear_results(self) -> None:
        """
        清空所有识别结果
        """
        with self.lock:
            self.results = []
            self.last_result = ""

    def _generate_request_id(self) -> str:
        """
        生成唯一的请求ID

        Returns:
            str: 请求ID
        """
        import uuid
        self.request_id = str(uuid.uuid4())
        return self.request_id

    def _on_open(self, ws):
        """
        WebSocket连接建立回调

        Args:
            ws: WebSocket实例
        """
        logger.info("WebSocket连接已建立")
        self.is_connected = True

    def _on_message(self, ws, message):
        """
        WebSocket消息接收回调

        Args:
            ws: WebSocket实例
            message: 接收到的消息
        """
        try:
            # 解析二进制消息
            if isinstance(message, bytes):
                # 提取header (4字节)
                if len(message) < 8:  # 至少需要header和payload_size
                    logger.warning(f"收到的消息太短: {len(message)}字节")
                    return

                header = message[:4]

                # 解析header
                protocol_version = (header[0] >> 4) & 0x0F
                message_type = header[1] >> 4
                message_specific_flags = header[1] & 0x0F
                message_serialization = header[2] >> 4
                message_compression = header[2] & 0x0F

                logger.debug(f"消息头: version={protocol_version}, type={message_type}, "
                             f"flags={message_specific_flags}, serialization={message_serialization}")

                # 提取序列号（如果存在）
                offset = 4
                sequence = None

                # 检查消息标志中的序列号标志(bit0, 最右)
                has_sequence = (message_specific_flags & 0x01) == 0x01
                if has_sequence:
                    if len(message) < offset + 4:
                        logger.warning("消息中应包含序列号但长度不足")
                        return
                    sequence_bytes = message[offset:offset+4]
                    sequence = struct.unpack("!I", sequence_bytes)[0]
                    offset += 4
                    logger.debug(f"消息序列号: {sequence}")

                # 提取payload_size
                if len(message) < offset + 4:
                    logger.warning(f"消息长度不足以包含payload_size")
                    return

                payload_size_bytes = message[offset:offset+4]
                payload_size = struct.unpack("!I", payload_size_bytes)[0]
                offset += 4

                logger.debug(f"接收到消息: type={message_type}, flags={message_specific_flags}, "
                             f"序列号={sequence}, payload_size={payload_size}")

                # 提取payload
                if len(message) < offset + payload_size:
                    logger.warning(
                        f"消息长度不符: 期望至少{offset + payload_size}字节，实际{len(message)}字节")
                    return

                payload = message[offset:offset+payload_size]

                # 检查payload是否以4字节长度前缀开始
                if message_serialization == 1 and len(payload) >= 4:
                    # 检查前4个字节是否可能是长度前缀
                    prefix = payload[:4]
                    if all(b in [0, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123] for b in prefix):
                        prefix_length = struct.unpack("!I", prefix)[0]
                        if len(payload) >= 4 + prefix_length and prefix_length > 0:
                            # 有长度前缀，跳过它
                            payload = payload[4:4+prefix_length]
                            logger.debug(
                                f"检测到JSON长度前缀: {prefix_length}，实际数据长度: {len(payload)}")

                # 检查消息类型
                if message_type == 9 or message_type == 1:  # 0b1001 - full server response 或 0b0001
                    # 根据序列化方法解析payload
                    if message_serialization == 1:  # JSON
                        try:
                            if payload:
                                # 检查payload是否包含有效的JSON数据
                                # 先尝试直接解析JSON
                                try:
                                    payload_str = payload.decode(
                                        'utf-8', errors='replace')
                                    result_json = json.loads(payload_str)
                                    self._process_recognition_result(
                                        result_json, message_specific_flags)
                                    logger.debug(
                                        f"成功处理消息类型 {message_type}，flags: {message_specific_flags}")
                                except UnicodeDecodeError:
                                    logger.error(f"无法解码为UTF-8: {payload!r}")
                                except json.JSONDecodeError as e:
                                    # 尝试查找JSON开始的位置
                                    try:
                                        payload_str = payload.decode(
                                            'utf-8', errors='replace')
                                        start_idx = payload_str.find('{')
                                        if start_idx >= 0:
                                            json_str = payload_str[start_idx:]
                                            result_json = json.loads(json_str)
                                            self._process_recognition_result(
                                                result_json, message_specific_flags)
                                            logger.debug(f"通过查找JSON开始位置成功解析")
                                        else:
                                            logger.error(
                                                f"找不到JSON开始位置: {payload_str[:100]}")
                                    except Exception as inner_e:
                                        logger.error(
                                            f"尝试解析JSON时出错: {inner_e}, 数据: {payload!r}")
                        except Exception as e:
                            logger.error(f"处理payload时发生异常: {e}")
                    else:
                        logger.warning(f"不支持的序列化方法: {message_serialization}")
                elif message_type == 15:  # 0b1111 - 错误消息
                    # 解析错误消息
                    if len(message) >= 16:  # 至少需要header, error code, error message size
                        error_code_bytes = message[8:12]
                        error_msg_size_bytes = message[12:16]
                        error_code = struct.unpack("!I", error_code_bytes)[0]
                        error_msg_size = struct.unpack(
                            "!I", error_msg_size_bytes)[0]

                        if len(message) >= 16 + error_msg_size:
                            error_msg = message[16:16 +
                                                error_msg_size].decode('utf-8', errors='replace')
                            logger.error(
                                f"服务器错误 (代码: {error_code}): {error_msg}")
                else:
                    logger.warning(
                        f"不支持的消息类型: {message_type} (十六进制: 0x{message_type:X})")
                    # 打印更多调试信息，帮助诊断问题
                    logger.debug(f"消息头信息: version={protocol_version}, flags={message_specific_flags}, "
                                 f"serialization={message_serialization}, compression={message_compression}")
                    if len(payload) > 0:
                        try:
                            logger.debug(f"Payload预览: {payload[:100]}")
                        except Exception:
                            pass

        except Exception as e:
            logger.error(f"处理WebSocket消息时出错: {str(e)}")

    def _on_error(self, ws, error):
        """
        WebSocket错误回调

        Args:
            ws: WebSocket实例
            error: 错误信息
        """
        logger.error(f"WebSocket错误: {str(error)}")
        self.error_count += 1

        # 尝试重新连接
        if self.error_count <= self.reconnect_attempts:
            logger.info(
                f"尝试重新连接 ({self.error_count}/{self.reconnect_attempts})...")
            time.sleep(self.reconnect_delay)
            self.disconnect()
            self.connect()
        else:
            logger.error(f"重连次数超过最大限制 ({self.reconnect_attempts})，放弃重连")
            self.is_connected = False

    def _on_close(self, ws, close_status_code, close_msg):
        """
        WebSocket关闭回调

        Args:
            ws: WebSocket实例
            close_status_code: 关闭状态码
            close_msg: 关闭消息
        """
        logger.info(f"WebSocket连接已关闭: {close_status_code} {close_msg}")
        self.is_connected = False
        self.is_recognizing = False

    def _create_init_header(self) -> bytes:
        """
        创建初始请求的header

        Returns:
            bytes: header字节数据
        """
        # 构建header的各个字段 (按照火山引擎文档)
        protocol_version = 0x01  # 0b0001 - 协议版本1
        header_size = 0x01      # 0b0001 - header size = 4 (1 x 4)
        message_type = 0x01     # 0b0001 - full client request
        message_specific_flags = 0x00  # 0b0000 - 非最后一包
        message_serialization = 0x01  # 0b0001 - JSON格式
        message_compression = 0x00  # 0b0000 - 无压缩
        reserved = 0x00  # 保留字段

        # 按照文档中的格式构建第一个字节
        byte0 = (protocol_version << 4) | header_size
        # 构建第二个字节
        byte1 = (message_type << 4) | message_specific_flags
        # 构建第三个字节
        byte2 = (message_serialization << 4) | message_compression
        # 第四个字节是保留字段
        byte3 = reserved

        # 组合成4字节header
        header_bytes = bytes([byte0, byte1, byte2, byte3])

        return header_bytes

    def _create_audio_header(self, is_final: bool = False) -> bytes:
        """
        创建音频数据包的header

        Args:
            is_final (bool): 是否为最后一包音频数据

        Returns:
            bytes: header字节数据
        """
        # 构建header的各个字段 (按照火山引擎文档)
        protocol_version = 0x01  # 0b0001 - 协议版本1
        header_size = 0x01      # 0b0001 - header size = 4 (1 x 4)
        message_type = 0x02     # 0b0010 - audio only request
        # 根据文档，最后一包的flags应为0b0010
        message_specific_flags = 0x02 if is_final else 0x00
        message_serialization = 0x00  # 0b0000 - 无序列化（raw bytes）
        message_compression = 0x00  # 0b0000 - 无压缩
        reserved = 0x00  # 保留字段

        # 按照文档中的格式构建第一个字节
        byte0 = (protocol_version << 4) | header_size
        # 构建第二个字节
        byte1 = (message_type << 4) | message_specific_flags
        # 构建第三个字节
        byte2 = (message_serialization << 4) | message_compression
        # 第四个字节是保留字段
        byte3 = reserved

        # 组合成4字节header
        header_bytes = bytes([byte0, byte1, byte2, byte3])

        return header_bytes

    def _send_init_parameters(self) -> bool:
        """
        发送初始参数

        Returns:
            bool: 是否成功发送初始参数
        """
        try:
            # 构造初始参数JSON (严格按照火山引擎文档)
            params = {
                "user": {
                    "uid": self._generate_request_id()  # 用户标识
                },
                "audio": {
                    "format": self.format,
                    "codec": self.codec,
                    "rate": self.sample_rate,
                    "bits": self.bits,
                    "channel": self.channels,
                    "language": self.language
                },
                "request": {
                    "model_name": self.model_name,
                    "enable_itn": False,  # 是否启用ITN
                    "enable_ddc": False,  # 是否启用顺滑
                    "enable_punc": True,  # 是否启用标点
                    "show_utterances": True,  # 输出语音停顿、分句信息
                    "result_type": "json"  # 结果返回方式
                }
            }

            # 序列化为JSON
            params_json = json.dumps(params, ensure_ascii=False)
            params_bytes = params_json.encode('utf-8')

            # 构造消息
            header_bytes = self._create_init_header()
            payload_size = struct.pack("!I", len(params_bytes))

            # 发送数据
            if self.ws is not None and self.ws.sock and self.ws.sock.connected:
                self.ws.send(header_bytes + payload_size +
                             params_bytes, websocket.ABNF.OPCODE_BINARY)
                logger.info("已发送初始参数")
                return True
            else:
                logger.error("WebSocket连接未就绪，无法发送初始参数")
                return False

        except Exception as e:
            logger.error(f"发送初始参数时出错: {str(e)}")
            return False

    def _audio_sender_thread(self):
        """
        音频数据发送线程
        """
        logger.info("音频发送线程已启动")

        while self.is_connected and self.is_recognizing:
            try:
                # 从队列中获取音频数据
                try:
                    audio_chunk = self.audio_queue.get(timeout=0.5)
                except queue.Empty:
                    continue

                # 发送音频数据
                self._send_audio_chunk(audio_chunk)

            except Exception as e:
                logger.error(f"发送音频数据时出错: {str(e)}")
                self.error_count += 1

                # 如果错误次数过多，尝试重新连接
                if self.error_count >= self.reconnect_attempts:
                    logger.warning(f"错误次数过多，尝试重新连接...")
                    self.disconnect()
                    time.sleep(self.reconnect_delay)
                    self.connect()
                    self.start_recognition(self.result_callback)
                    self.error_count = 0
                    break

        logger.info("音频发送线程已结束")

    def _send_audio_chunk(self, audio_chunk: bytes) -> bool:
        """
        发送单个音频数据块

        Args:
            audio_chunk (bytes): 音频数据块

        Returns:
            bool: 是否成功发送数据
        """
        try:
            # 将音频数据分割成适当大小的块
            for i in range(0, len(audio_chunk), self.audio_chunk_size):
                chunk = audio_chunk[i:i+self.audio_chunk_size]

                # 检查是否为最后一块
                is_final = False

                # 构造消息
                header_bytes = self._create_audio_header(is_final=is_final)
                payload_size = struct.pack("!I", len(chunk))

                # 发送数据
                if self.ws is not None and self.ws.sock and self.ws.sock.connected:
                    message = header_bytes + payload_size + chunk
                    self.ws.send(message, websocket.ABNF.OPCODE_BINARY)
                    logger.debug(f"已发送音频数据: {len(chunk)}字节")
                    self.sequence_number += 1
                else:
                    logger.error("WebSocket连接未就绪，无法发送音频数据")
                    return False

                # 短暂延迟，避免发送过快
                time.sleep(0.01)

            return True

        except Exception as e:
            logger.error(f"发送音频数据块时出错: {str(e)}")
            return False

    def _process_recognition_result(self, result_json: Dict[str, Any], message_flags: int):
        """
        处理识别结果

        Args:
            result_json (Dict[str, Any]): 识别结果JSON
            message_flags (int): 消息标志
        """
        logger.debug(
            f"处理识别结果，message_flags: {message_flags}, 数据: {json.dumps(result_json, ensure_ascii=False)[:200]}...")
        try:
            # 检查是否包含识别结果
            if "result" in result_json and "text" in result_json["result"]:
                text = result_json["result"]["text"]

                # 尝试两种可能的标志位模式
                # 根据文档，bit1表示是否是最后一包，最后一包的flags为0b0011
                # 但实际上服务器可能用0b0001表示非最终结果，0b0011表示最终结果
                is_final = (message_flags &
                            0x02) == 0x02 or message_flags == 0x03

                logger.debug(
                    f"识别结果: {text}, 标志位: {message_flags}, 是否最终结果: {is_final}")

                with self.lock:
                    self.last_result = text
                    if is_final:
                        self.results.append(text)

                # 调用回调函数
                if self.result_callback is not None:
                    try:
                        self.result_callback(text, is_final)
                    except Exception as e:
                        logger.error(f"调用回调函数时出错: {str(e)}")

            # 检查是否包含utterances
            if "result" in result_json and "utterances" in result_json["result"]:
                utterances = result_json["result"]["utterances"]
                for utterance in utterances:
                    if "text" in utterance:
                        logger.debug(f"识别分句: {utterance['text']}")

        except Exception as e:
            logger.error(f"处理识别结果时出错: {str(e)}")
