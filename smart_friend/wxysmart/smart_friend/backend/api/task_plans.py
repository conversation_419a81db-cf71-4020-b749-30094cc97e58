#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务计划API接口
"""

from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
import json
import logging
from typing import List, Dict, Any
from api.task_confirm_api import confirm_task_plan
from core.user_management.database.connection import get_db_session_context
from core.daily_tasks.models.daily_task_models import TaskPlan,TaskItem,DailyTask
from service.daily_task_service import DailyTaskService
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/task-plans", tags=["task-plans"])


class TaskPlanCreate(BaseModel):
    """创建任务计划的请求模型"""
    child_id: int  # 学生ID
    content: str  # JSON字符串格式的计划内容


class TaskPlanResponse(BaseModel):
    """任务计划响应模型"""
    id: int
    child_id: int
    content: str
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class SubTaskResponse(BaseModel):
    id:int
    sub_task_name: str
    time_slot: str

class DailyTaskResponse(BaseModel):
    id: int
    task_name: str
    time_slot: str
    subject: str
    sub_tasks: List[SubTaskResponse]
    customization: str
    difficulty: str
    solution: str
    confidence_index: int



class TaskPlanResultResponse(BaseModel):
    child_id: int
    content: List[DailyTaskResponse]
    id: int
    created_at: str
    updated_at: str


async def confirm_task_plan_1( child_id: int,task_plan:List[Dict[str, Any]],planID: int):
    """   
    确认任务计划接口

    将前端个性化任务计划展示的任务相关信息存入数据库，
    并返回带有任务ID的完整数据给前端
    """
    try:
        logger.info(f"收到任务计划确认请求，学生ID: {child_id}")


        service = DailyTaskService()
        result = await service.confirm_task_plan(child_id,task_plan,planID)

        if not result.get("success"):
            raise HTTPException(
                status_code=500, 
                detail=result.get("message", "任务计划确认失败")
            )

        # 获取存储后的任务数据（包含ID）
        tasks_result = await service.get_today_tasks_with_subtasks(child_id)
        if tasks_result.get("success"):
            result["tasks_with_ids"] = tasks_result.get("data", [])

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认任务计划失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"确认任务计划失败: {str(e)}"
        )



@router.post("/", response_model=TaskPlanResponse)
async def create_task_plan(plan_data: TaskPlanCreate):
    """
    创建新的任务计划
    """
    try:
        logger.info(f"收到创建任务计划请求: child_id={plan_data.child_id}, content长度={len(plan_data.content)}")

        # 验证content是否为有效的JSON
        try:
            json.loads(plan_data.content)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="计划内容必须是有效的JSON格式")

        with get_db_session_context() as session:
            logger.info(f"开始创建任务计划: child_id={plan_data.child_id}")

            # 创建任务计划
            task_plan = TaskPlan.create_plan(session, plan_data.child_id, plan_data.content)

            if not task_plan:
                logger.error("TaskPlan.create_plan 返回 None")
                raise HTTPException(status_code=500, detail="创建任务计划失败")

            logger.info(f"任务计划创建成功: id={task_plan.id}, child_id={task_plan.child_id}")
            session.commit()
            logger.info("数据库事务提交成功")

            # 返回创建的计划
            response = TaskPlanResponse(
                id=task_plan.id,
                child_id=task_plan.child_id,
                content=task_plan.content,
                created_at=task_plan.created_at.isoformat() if task_plan.created_at else "",
                updated_at=task_plan.updated_at.isoformat() if task_plan.updated_at else ""
            )

            latest_task_plan = session.query(TaskPlan).filter(
                TaskPlan.child_id == plan_data.child_id
            ).order_by(TaskPlan.created_at.desc()).first()

            task_plan_list: List[Dict[str, Any]] = json.loads(latest_task_plan.content)
            await  confirm_task_plan_1(plan_data.child_id,task_plan_list,latest_task_plan.id)

            logger.info(f"返回响应: id={response.id}, child_id={response.child_id}")
            return response
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建任务计划失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get("/{plan_id}", response_model=TaskPlanResponse)
async def get_task_plan(plan_id: int):
    """
    根据ID获取任务计划
    """
    try:
        with get_db_session_context() as session:
            task_plan = session.query(TaskPlan).filter(TaskPlan.id == plan_id).first()
            
            if not task_plan:
                raise HTTPException(status_code=404, detail="任务计划不存在")
            
            return TaskPlanResponse(
                id=task_plan.id,
                child_id=task_plan.child_id,
                content=task_plan.content,
                created_at=task_plan.created_at.isoformat() if task_plan.created_at else "",
                updated_at=task_plan.updated_at.isoformat() if task_plan.updated_at else ""
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务计划失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")



# @router.get("/getplanbychildid/{child_id}", response_model=TaskPlanResponse)
# async def get_task_planByChild_id(child_id: int):
#     """
#     根据ID获取任务计划
#     """
#     try:
#         with get_db_session_context() as session:
#             task_plan = session.query(TaskPlan).filter(TaskPlan.child_id == child_id).order_by(TaskPlan.created_at.desc()).first()
            
#             if not task_plan:
#                 raise HTTPException(status_code=404, detail="任务计划不存在")
            
#             return TaskPlanResponse(
#                 id=task_plan.id,
#                 child_id=task_plan.child_id,
#                 content=task_plan.content,
#                 created_at=task_plan.created_at.isoformat() if task_plan.created_at else "",
#                 updated_at=task_plan.updated_at.isoformat() if task_plan.updated_at else ""
#             )
            
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"获取任务计划失败: {e}")
#         raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get("/getplanbychildid/{child_id}", response_model=TaskPlanResultResponse)
async def get_task_planByChild_id(child_id: int):
    """
    根据ID获取任务计划，包含学科任务及子任务信息
    """
    try:




        with get_db_session_context() as session:
            # 获取对应小孩ID且最新的taskplan的id

    
            latest_task_plan = session.query(TaskPlan).filter(
                TaskPlan.child_id == child_id
            ).order_by(TaskPlan.created_at.desc()).first()
       


            if not latest_task_plan:
                raise HTTPException(status_code=404, detail="任务计划不存在")

            # 获取该计划下的所有DailyTask
            daily_tasks = session.query(DailyTask).filter(
                DailyTask.plan_id == latest_task_plan.id,
                DailyTask.is_active == True
            ).all()

            result = []
            for daily_task in daily_tasks:
                # 获取该DailyTask对应的所有子任务
                sub_tasks = session.query(TaskItem).filter(
                    TaskItem.daily_task_id == daily_task.id,
                    TaskItem.is_active == True
                ).all()

                sub_task_list = []
                for sub_task in sub_tasks:
                    sub_task_list.append(SubTaskResponse(
                        id =sub_task.id,
                        sub_task_name=sub_task.task_content,
                        time_slot=sub_task.time_slot
                    ))

                task_info = DailyTaskResponse(
                    id= daily_task.id,
                    task_name=daily_task.task_name,
                    time_slot=daily_task.time_slot,
                    subject=daily_task.subject,
                    sub_tasks=sub_task_list,
                    customization=daily_task.customization or "",
                    difficulty=daily_task.difficulty or "",
                    solution=daily_task.solution or "",
                    confidence_index=daily_task.confidence_index or 0
                )
                result.append(task_info)


            return TaskPlanResultResponse(
                child_id=child_id,
                content=result,
                id=latest_task_plan.id,
                created_at=latest_task_plan.created_at.isoformat() if latest_task_plan.created_at else "",
                updated_at=latest_task_plan.updated_at.isoformat() if latest_task_plan.updated_at else ""
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务计划失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.get("/", response_model=List[TaskPlanResponse])
async def list_task_plans(limit: int = 50, offset: int = 0):
    """
    获取任务计划列表
    """
    try:
        with get_db_session_context() as session:
            task_plans = session.query(TaskPlan)\
                .order_by(TaskPlan.created_at.desc())\
                .offset(offset)\
                .limit(limit)\
                .all()
            
            return [
                TaskPlanResponse(
                    id=plan.id,
                    child_id=plan.child_id,
                    content=plan.content,
                    created_at=plan.created_at.isoformat() if plan.created_at else "",
                    updated_at=plan.updated_at.isoformat() if plan.updated_at else ""
                )
                for plan in task_plans
            ]
            
    except Exception as e:
        logger.error(f"获取任务计划列表失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.put("/{plan_id}", response_model=TaskPlanResponse)
async def update_task_plan(plan_id: int, plan_data: TaskPlanCreate):
    """
    更新任务计划
    """
    try:
        # 验证content是否为有效的JSON
        try:
            json.loads(plan_data.content)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="计划内容必须是有效的JSON格式")
        
        with get_db_session_context() as session:
            task_plan = session.query(TaskPlan).filter(TaskPlan.id == plan_id).first()
            
            if not task_plan:
                raise HTTPException(status_code=404, detail="任务计划不存在")
            
            # 更新内容
            task_plan.content = plan_data.content
            session.commit()
            
            return TaskPlanResponse(
                id=task_plan.id,
                child_id=task_plan.child_id,
                content=task_plan.content,
                created_at=task_plan.created_at.isoformat() if task_plan.created_at else "",
                updated_at=task_plan.updated_at.isoformat() if task_plan.updated_at else ""
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新任务计划失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.delete("/{plan_id}")
async def delete_task_plan(plan_id: int):
    """
    删除任务计划
    """
    try:
        with get_db_session_context() as session:
            task_plan = session.query(TaskPlan).filter(TaskPlan.id == plan_id).first()
            
            if not task_plan:
                raise HTTPException(status_code=404, detail="任务计划不存在")
            
            session.delete(task_plan)
            session.commit()
            
            return {"message": "任务计划删除成功"}
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务计划失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.get("/{plan_id}/tasks")
async def get_plan_tasks(plan_id: int):
    """
    获取指定计划下的所有任务
    """
    try:
        with get_db_session_context() as session:
            # 检查计划是否存在
            task_plan = session.query(TaskPlan).filter(TaskPlan.id == plan_id).first()
            if not task_plan:
                raise HTTPException(status_code=404, detail="任务计划不存在")
            
            # 获取关联的任务
            from core.daily_tasks.models.daily_task_models import DailyTask, TaskItem
            
            daily_tasks = session.query(DailyTask)\
                .filter(DailyTask.plan_id == plan_id, DailyTask.is_active == True)\
                .all()
            
            task_items = session.query(TaskItem)\
                .filter(TaskItem.plan_id == plan_id, TaskItem.is_active == True)\
                .all()
            
            return {
                "plan_id": plan_id,
                "daily_tasks": [task.to_dict() for task in daily_tasks],
                "task_items": [item.to_dict() for item in task_items]
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取计划任务失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")



# 定义更新子任务时间的请求模型
class UpdateSubtaskTimeRequest(BaseModel):
    sub_task_id: int
    new_time: str
    time_type: str

@router.post("/updateSubtaskTime")
async def update_SubtaskTime(request: UpdateSubtaskTimeRequest):
    try:
        with get_db_session_context() as session:  # type: Session
            # 查询对应的子任务
            sub_task = session.query(TaskItem).filter(TaskItem.id == request.sub_task_id).first()
            if not sub_task:
                raise HTTPException(status_code=404, detail="未找到对应的子任务")

            # 解析现有的 timeslot
            if sub_task.time_slot:
                start_time, end_time = sub_task.time_slot.split(' - ')
            else:
                start_time = end_time = "00:00"

            # 根据时间种类更新时间
            if request.time_type == "start":
                start_time = request.new_time
            else:
                end_time = request.new_time

            # 更新 timeslot 字段
            sub_task.time_slot = f"{start_time} - {end_time}"
            # 更新 updated_at 字段
            sub_task.updated_at = datetime.now()

            # 提交更改到数据库
            session.commit()

            return {
                "success": True,
                "message": "子任务时间更新成功",
                "sub_task_id": request.sub_task_id
            }

    except SQLAlchemyError as e:
        session.rollback()
        raise HTTPException(status_code=500, detail=f"数据库操作出错: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
  
