#!/usr/bin/env python3
"""
检测API端点
基于testbd.py和testde.py实现坐姿检测和桌面检测的FastAPI端点
"""

import cv2
import sys
import os
import base64
import numpy as np
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
import logging

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
sys.path.insert(0, project_root)

# 导入检测模块
try:
    from body_detection import StandalonePoseAnalyzer
    from body_detection.desk import DesktopSegmentationDetector, DesktopLayoutAnalyzer
    print("✅ 成功导入检测模块")
except ImportError as e:
    print(f"❌ 导入检测模块失败: {e}")
    print("请确保在lyk虚拟环境下运行: conda activate lyk")

# 设置日志
logger = logging.getLogger(__name__)
router = APIRouter()

# 数据模型
class ImageDetectionRequest(BaseModel):
    """图像检测请求模型"""
    image: str  # base64编码的图像数据
    detection_type: Optional[str] = "both"  # "posture", "desktop", "both"

class PostureDetectionResult(BaseModel):
    """坐姿检测结果模型"""
    success: bool
    message: str
    result: Optional[Dict[str, Any]] = None
    analysis: Optional[Dict[str, Any]] = None

class DesktopDetectionResult(BaseModel):
    """桌面检测结果模型"""
    success: bool
    message: str
    overall_score: Optional[float] = None
    quality_level: Optional[str] = None
    learning_objects: Optional[int] = None
    total_objects: Optional[int] = None
    suggestions: Optional[list] = None
    detailed_analysis: Optional[Dict[str, Any]] = None

class CombinedDetectionResult(BaseModel):
    """综合检测结果模型"""
    success: bool
    message: str
    posture_result: Optional[PostureDetectionResult] = None
    desktop_result: Optional[DesktopDetectionResult] = None

# 全局检测器实例
posture_analyzer = None
desktop_detector = None
desktop_analyzer = None

def init_detectors():
    """初始化检测器"""
    global posture_analyzer, desktop_detector, desktop_analyzer
    
    try:
        # 初始化坐姿分析器
        posture_analyzer = StandalonePoseAnalyzer(
            head_tilt_threshold=0.13,        # 头部倾斜阈值
            shoulder_level_threshold=0.25,   # 肩膀水平阈值
            strict_shoulder_threshold=0.1,   # 严格肩膀阈值
            shoulder_symmetry_min=0.8,       # 肩膀对称性最小值
            shoulder_symmetry_max=1.2,       # 肩膀对称性最大值
            body_straightness_factor=1.3     # 身体挺直系数
        )
        logger.info("✅ 坐姿分析器初始化成功")
        
        # 初始化桌面检测器
        desktop_detector = DesktopSegmentationDetector(
            model_path="yolo11n-seg.pt",
            confidence_threshold=0.25
        )
        desktop_analyzer = DesktopLayoutAnalyzer()
        logger.info("✅ 桌面检测器初始化成功")
        
        return True
    except Exception as e:
        logger.error(f"❌ 检测器初始化失败: {e}")
        return False

def decode_base64_image(image_data: str) -> np.ndarray:
    """解码base64图像数据"""
    try:
        # 移除data:image/jpeg;base64,前缀（如果存在）
        if ',' in image_data:
            image_data = image_data.split(',')[1]
        
        # 解码base64
        image_bytes = base64.b64decode(image_data)
        
        # 转换为numpy数组
        nparr = np.frombuffer(image_bytes, np.uint8)
        
        # 解码为OpenCV图像
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            raise ValueError("无法解码图像数据")
            
        return image
    except Exception as e:
        raise ValueError(f"图像解码失败: {str(e)}")

@router.post("/posture-detection", response_model=PostureDetectionResult)
async def detect_posture(request: ImageDetectionRequest):
    """
    坐姿检测API端点
    
    基于testbd.py的实现，分析用户的坐姿状态
    """
    try:
        # 确保检测器已初始化
        if posture_analyzer is None:
            if not init_detectors():
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="坐姿检测器初始化失败"
                )
        
        # 解码图像
        frame = decode_base64_image(request.image)
        logger.info(f"✅ 图像解码成功，尺寸: {frame.shape}")
        
        # 执行坐姿分析
        logger.info("🔍 开始坐姿分析...")
        result, annotated_image = posture_analyzer.analyze_posture_from_image_with_visualization(frame)
        
        # 处理结果
        if result:
            logger.info("✅ 坐姿分析完成")

            # 清理和格式化结果数据，确保只返回有用的键值对
            cleaned_result = {}
            if isinstance(result, dict):
                for key, value in result.items():
                    # 过滤掉None值和空值
                    if value is not None and value != "":
                        # 格式化键名，使其更易读
                        clean_key = key.replace('_', ' ').title()
                        cleaned_result[clean_key] = value

            # 创建简洁的分析数据
            analysis_data = {
                "检测状态": "成功",
                "图像尺寸": f"{frame.shape[1]}x{frame.shape[0]}",
                "检测项目数": len(cleaned_result)
            }

            return PostureDetectionResult(
                success=True,
                message="坐姿检测成功",
                result=cleaned_result,
                analysis=analysis_data
            )
        else:
            logger.warning("⚠️ 坐姿分析返回空结果")
            return PostureDetectionResult(
                success=False,
                message="坐姿检测失败：未能检测到有效的姿态信息"
            )
            
    except ValueError as e:
        logger.error(f"❌ 图像处理错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"图像处理错误: {str(e)}"
        )
    except Exception as e:
        logger.error(f"❌ 坐姿检测失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"坐姿检测失败: {str(e)}"
        )

@router.post("/desktop-detection", response_model=DesktopDetectionResult)
async def detect_desktop(request: ImageDetectionRequest):
    """
    桌面检测API端点
    
    基于testde.py的实现，分析桌面布局和整洁度
    """
    try:
        # 确保检测器已初始化
        if desktop_detector is None or desktop_analyzer is None:
            if not init_detectors():
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="桌面检测器初始化失败"
                )
        
        # 解码图像
        frame = decode_base64_image(request.image)
        logger.info(f"✅ 图像解码成功，尺寸: {frame.shape}")
        
        # 执行分割检测
        logger.info("🔍 开始桌面物体检测...")
        objects = desktop_detector.detect_objects_with_segmentation(frame)
        logger.info(f"检测到 {len(objects)} 个物体")
        
        # 执行布局分析
        logger.info("🔍 开始桌面布局分析...")
        report = desktop_analyzer.evaluate_desktop_layout(objects, frame.shape[:2])
        
        # 提取建议
        suggestions = []
        for suggestion in report.priority_suggestions:
            suggestions.append(suggestion)
        
        logger.info("✅ 桌面检测完成")
        return DesktopDetectionResult(
            success=True,
            message="桌面检测成功",
            overall_score=float(report.overall_score),
            quality_level=report.quality_level.value,
            learning_objects=report.learning_objects,
            total_objects=report.total_objects,
            suggestions=suggestions,
            detailed_analysis={
                "objects_detected": len(objects),
                "image_size": f"{frame.shape[1]}x{frame.shape[0]}",
                "analysis_complete": True
            }
        )
            
    except ValueError as e:
        logger.error(f"❌ 图像处理错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"图像处理错误: {str(e)}"
        )
    except Exception as e:
        logger.error(f"❌ 桌面检测失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"桌面检测失败: {str(e)}"
        )

@router.post("/combined-detection", response_model=CombinedDetectionResult)
async def detect_combined(request: ImageDetectionRequest):
    """
    综合检测API端点
    
    同时进行坐姿检测和桌面检测
    """
    try:
        posture_result = None
        desktop_result = None
        
        # 根据请求类型执行检测
        if request.detection_type in ["posture", "both"]:
            try:
                posture_request = ImageDetectionRequest(image=request.image)
                posture_result = await detect_posture(posture_request)
            except Exception as e:
                logger.error(f"坐姿检测失败: {e}")
                posture_result = PostureDetectionResult(
                    success=False,
                    message=f"坐姿检测失败: {str(e)}"
                )
        
        if request.detection_type in ["desktop", "both"]:
            try:
                desktop_request = ImageDetectionRequest(image=request.image)
                desktop_result = await detect_desktop(desktop_request)
            except Exception as e:
                logger.error(f"桌面检测失败: {e}")
                desktop_result = DesktopDetectionResult(
                    success=False,
                    message=f"桌面检测失败: {str(e)}"
                )
        
        # 判断整体成功状态
        overall_success = True
        messages = []
        
        if posture_result:
            if posture_result.success:
                messages.append("坐姿检测成功")
            else:
                overall_success = False
                messages.append("坐姿检测失败")
        
        if desktop_result:
            if desktop_result.success:
                messages.append("桌面检测成功")
            else:
                overall_success = False
                messages.append("桌面检测失败")
        
        return CombinedDetectionResult(
            success=overall_success,
            message="; ".join(messages),
            posture_result=posture_result,
            desktop_result=desktop_result
        )
        
    except Exception as e:
        logger.error(f"❌ 综合检测失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"综合检测失败: {str(e)}"
        )

@router.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查检测器状态
        detectors_status = {
            "posture_analyzer": posture_analyzer is not None,
            "desktop_detector": desktop_detector is not None,
            "desktop_analyzer": desktop_analyzer is not None
        }
        
        # 如果检测器未初始化，尝试初始化
        if not all(detectors_status.values()):
            init_success = init_detectors()
            detectors_status.update({
                "posture_analyzer": posture_analyzer is not None,
                "desktop_detector": desktop_detector is not None,
                "desktop_analyzer": desktop_analyzer is not None,
                "init_attempted": True,
                "init_success": init_success
            })
        
        all_ready = all([
            posture_analyzer is not None,
            desktop_detector is not None,
            desktop_analyzer is not None
        ])
        
        return {
            "status": "healthy" if all_ready else "initializing",
            "detectors": detectors_status,
            "supported_detection_types": ["posture", "desktop", "both"],
            "ready": all_ready
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "ready": False
        }

# 在模块加载时初始化检测器
init_detectors()
