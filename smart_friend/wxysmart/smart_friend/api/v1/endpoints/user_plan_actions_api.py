"""
用户计划表操作记录API端点（简化版）
提供用户操作记录的创建、查询和管理功能
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
import logging

from service.user_plan_action_service import UserPlanActionService

logger = logging.getLogger(__name__)

router = APIRouter()


# Pydantic模型定义
class UserActionCreate(BaseModel):
    """创建用户操作记录的请求模型"""
    user_id: int = Field(..., description="用户ID")
    table_id: str = Field(..., description="表ID（每天的表ID不同）")
    user_operation: str = Field(..., description="用户操作（string）")


class UserActionResponse(BaseModel):
    """用户操作记录的响应模型"""
    id: int
    table_id: str
    operation_time: Optional[str]
    user_operation: str
    user_id: int


class ActionCreateResponse(BaseModel):
    """操作记录创建响应模型"""
    success: bool
    action_id: Optional[int] = None
    message: str


# API端点
@router.post("/actions", response_model=ActionCreateResponse, summary="创建用户操作记录")
async def create_user_action(action: UserActionCreate):
    """
    创建用户操作记录
    
    支持记录用户对计划表的各种操作
    """
    try:
        action_id = UserPlanActionService.create_action(
            user_id=action.user_id,
            table_id=action.table_id,
            user_operation=action.user_operation
        )
        
        if action_id:
            return ActionCreateResponse(
                success=True,
                action_id=action_id,
                message="操作记录创建成功"
            )
        else:
            raise HTTPException(status_code=500, detail="操作记录创建失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建用户操作记录异常: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/actions/{user_id}", response_model=List[UserActionResponse], summary="获取用户操作历史")
async def get_user_action_history(
    user_id: int,
    limit: int = Query(50, ge=1, le=200, description="返回记录数量限制")
):
    """
    获取用户操作历史
    
    返回指定用户的操作历史记录，按操作时间倒序排列
    """
    try:
        history = UserPlanActionService.get_user_action_history(user_id, limit)
        return [UserActionResponse(**record) for record in history]
        
    except Exception as e:
        logger.error(f"获取用户操作历史异常: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")



@router.get("/actions/table/{table_id}", response_model=List[UserActionResponse], summary="获取表操作历史")
async def get_table_action_history(
    table_id: str,
    limit: int = Query(50, ge=1, le=200, description="返回记录数量限制")
):
    """
    获取指定表的操作历史
    
    返回指定表的所有操作历史记录
    """
    try:
        history = UserPlanActionService.get_table_action_history(table_id, limit)
        return [UserActionResponse(**record) for record in history]
        
    except Exception as e:
        logger.error(f"获取表操作历史异常: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/utils/generate-table-id", summary="生成表ID")
async def generate_table_id(date_str: Optional[str] = Query(None, description="日期字符串，格式为YYYYMMDD")):
    """
    生成表ID
    
    用于生成每天唯一的表ID
    """
    try:
        table_id = UserPlanActionService.generate_table_id(date_str)
        return {"table_id": table_id}
        
    except Exception as e:
        logger.error(f"生成表ID异常: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
