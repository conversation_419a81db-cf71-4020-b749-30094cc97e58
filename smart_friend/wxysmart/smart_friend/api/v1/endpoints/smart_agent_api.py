#!/usr/bin/env python3
"""
智能学习Agent API
提供Agent服务的RESTful API接口
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field

from agent.smart_agent import get_agent
from agent.posture_tool import register_posture_tools
from agent.task_time_modifier import register_task_time_modifier_tools

logger = logging.getLogger(__name__)

# OpenManus integration for enhanced API responses
try:
    from service.openmanus_service import get_openmanus_service, is_openmanus_available
    from service.doubao_service import get_doubao_service
    OPENMANUS_INTEGRATION = True
    logger.info("✅ OpenManus integration available in smart_agent_api")
except ImportError:
    OPENMANUS_INTEGRATION = False
    logger.info("⚠️ OpenManus integration not available in smart_agent_api")

# 创建路由器
router = APIRouter(prefix="/smart-agent", tags=["smart-agent"])

# 初始化Agent并注册工具
def init_agent():
    """初始化Agent并注册所有工具"""
    agent = get_agent()

    # 注册坐姿检测工具
    register_posture_tools(agent)

    # 注册任务时间修改工具
    register_task_time_modifier_tools(agent)

    logger.info("SmartAgent已初始化并注册所有工具")
    return agent

# 全局初始化
_agent = init_agent()


# 请求模型
class VoiceInputRequest(BaseModel):
    """语音输入请求模型"""
    voice_text: str = Field(..., description="语音识别的文本内容")
    child_id: Optional[int] = Field(None, description="学生ID")


class PostureCheckRequest(BaseModel):
    """坐姿检查请求模型"""
    image_data: Optional[str] = Field(None, description="图像数据（base64编码）")
    child_id: Optional[int] = Field(None, description="学生ID")


# 响应模型
class AgentResponse(BaseModel):
    """Agent响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    child_id: Optional[int] = Field(None, description="学生ID")
    timestamp: Optional[str] = Field(None, description="时间戳")
    data: Optional[Dict[str, Any]] = Field(None, description="额外数据")
    intent_info: Optional[Dict[str, Any]] = Field(None, description="意图识别信息")


@router.post("/voice-input", response_model=AgentResponse)
async def process_voice_input(request: VoiceInputRequest):
    """
    处理语音输入
    
    这是Agent的主要入口，接收语音识别的文本，Agent会自动分析意图并选择合适的工具
    """
    try:
        logger.info(f"接收到语音输入: {request.voice_text[:50]}...")
        
        # 调用Agent处理语音输入
        result = await _agent.process_voice_input(
            voice_text=request.voice_text,
            child_id=request.child_id
        )
        
        # 准备响应数据
        response_data = None
        if 'posture_data' in result:
            response_data = result.get('posture_data')
        elif 'task_data' in result:
            response_data = result.get('task_data')

        # 创建响应对象
        response = AgentResponse(
            success=result.get('success', True),
            message=result.get('message', ''),
            child_id=request.child_id,
            data=response_data
        )

        # 如果有意图信息，添加到响应中
        if 'intent_info' in result:
            response.intent_info = result['intent_info']

        return response
        
    except Exception as e:
        logger.error(f"处理语音输入异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理语音输入失败: {str(e)}"
        )


@router.post("/posture-check", response_model=AgentResponse)
async def check_posture(request: PostureCheckRequest):
    """
    坐姿检测接口
    
    可以从摄像头或上传的图像检测学生坐姿
    """
    try:
        logger.info("接收到坐姿检测请求")
        
        if request.image_data:
            # 从图像检测坐姿
            result = await _agent.tools['check_posture_image']['function'](request.image_data)
        else:
            # 从摄像头检测坐姿
            result = await _agent.tools['check_posture_camera']['function']()
        
        return AgentResponse(
            success=result.get('success', True),
            message=result.get('message', ''),
            child_id=request.child_id,
            data=result.get('posture_data') if 'posture_data' in result else None
        )
        
    except Exception as e:
        logger.error(f"坐姿检测异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"坐姿检测失败: {str(e)}"
        )


@router.get("/posture-tips", response_model=AgentResponse)
async def get_posture_tips():
    """
    获取坐姿建议
    """
    try:
        result = _agent.tools['posture_tips']['function']()
        
        return AgentResponse(
            success=result.get('success', True),
            message=result.get('message', ''),
            child_id=None
        )
        
    except Exception as e:
        logger.error(f"获取坐姿建议异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取坐姿建议失败: {str(e)}"
        )


@router.get("/conversation-history/{child_id}")
async def get_conversation_history(child_id: int, limit: int = 10):
    """
    获取对话历史
    
    Args:
        child_id: 学生ID
        limit: 返回的历史记录数量限制
    """
    try:
        history = _agent.get_conversation_history(child_id=child_id, limit=limit)
        
        return {
            "success": True,
            "child_id": child_id,
            "history": history,
            "count": len(history)
        }
        
    except Exception as e:
        logger.error(f"获取对话历史异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取对话历史失败: {str(e)}"
        )


@router.delete("/conversation-history/{child_id}")
async def clear_conversation_history(child_id: int):
    """
    清空对话历史
    
    Args:
        child_id: 学生ID
    """
    try:
        _agent.clear_conversation_history(child_id=child_id)
        
        return {
            "success": True,
            "message": f"学生 {child_id} 的对话历史已清空",
            "child_id": child_id
        }
        
    except Exception as e:
        logger.error(f"清空对话历史异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清空对话历史失败: {str(e)}"
        )


@router.get("/status")
async def get_agent_status():
    """
    获取Agent状态信息
    """
    try:
        return {
            "status": "active",
            "tools_count": len(_agent.tools),
            "available_tools": list(_agent.tools.keys()),
            "conversation_count": len(_agent.conversation_history),
            "agent_type": "SmartLearningAgent"
        }
        
    except Exception as e:
        logger.error(f"获取Agent状态异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Agent状态失败: {str(e)}"
        )


@router.post("/reload")
async def reload_agent():
    """
    重新加载Agent
    """
    try:
        global _agent
        _agent = init_agent()
        
        return {
            "success": True,
            "message": "Agent重新加载成功",
            "tools_count": len(_agent.tools),
            "available_tools": list(_agent.tools.keys())
        }
        
    except Exception as e:
        logger.error(f"重新加载Agent异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重新加载Agent失败: {str(e)}"
        )


# OpenManus Enhanced API Endpoints (Added without removing existing code)

@router.post("/openmanus-enhanced-voice-input", response_model=AgentResponse)
async def openmanus_enhanced_voice_input(request: VoiceInputRequest):
    """
    OpenManus enhanced voice input processing with intelligent response generation
    Falls back to standard processing if OpenManus is not available
    """
    try:
        logger.info(f"OpenManus enhanced voice input: {request.voice_text[:50]}...")

        if OPENMANUS_INTEGRATION and is_openmanus_available():
            # Use OpenManus enhanced processing
            doubao_service = get_doubao_service()
            result = doubao_service.openmanus_enhanced_chat(
                user_input=request.voice_text,
                child_id=request.child_id
            )

            if result.get('success') and not result.get('fallback_used'):
                logger.info("✅ Using OpenManus enhanced voice processing")
                return AgentResponse(
                    success=True,
                    message=result.get('content', ''),
                    child_id=request.child_id,
                    timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    data={
                        'enhanced_by': result.get('enhanced_by', 'openmanus'),
                        'metadata': result.get('metadata', {}),
                        'processing_method': 'openmanus_enhanced'
                    },
                    intent_info=result.get('intent_info', {}),
                    summary=result.get('summary', '')
                )

        # Fallback to standard voice input processing
        logger.info("🔄 Using standard voice input processing")
        return await process_voice_input(request)

    except Exception as e:
        logger.error(f"OpenManus enhanced voice input error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Enhanced voice input processing failed: {str(e)}"
        )

@router.post("/openmanus-intent-classification")
async def openmanus_intent_classification(request: dict):
    """
    OpenManus enhanced intent classification
    """
    try:
        text = request.get("text", "")
        if not text:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Text is required for intent classification"
            )

        if OPENMANUS_INTEGRATION and is_openmanus_available():
            doubao_service = get_doubao_service()
            result = doubao_service.openmanus_classify_intent(text)

            if result.get('success'):
                logger.info("✅ Using OpenManus intent classification")
                return {
                    "success": True,
                    "intent": result.get('intent', 'unknown'),
                    "confidence": result.get('confidence', 0.0),
                    "intent_info": result.get('intent_info', {}),
                    "enhanced_by": result.get('enhanced_by', 'openmanus'),
                    "text": text
                }

        # Fallback to basic classification
        logger.info("🔄 Using basic intent classification")
        return {
            "success": True,
            "intent": "daily_chat",
            "confidence": 0.5,
            "intent_info": {"method": "fallback"},
            "enhanced_by": "standard",
            "text": text
        }

    except Exception as e:
        logger.error(f"Intent classification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Intent classification failed: {str(e)}"
        )

@router.post("/openmanus-create-plan")
async def openmanus_create_plan(request: dict):
    """
    OpenManus enhanced task planning
    """
    try:
        objective = request.get("objective", "")
        resources = request.get("resources", "")
        context = request.get("context", "")
        child_id = request.get("child_id")

        if not objective:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Objective is required for plan creation"
            )

        if OPENMANUS_INTEGRATION and is_openmanus_available():
            doubao_service = get_doubao_service()
            result = doubao_service.openmanus_create_plan(objective, resources, context)

            if result.get('success'):
                logger.info("✅ Using OpenManus task planning")
                return {
                    "success": True,
                    "plan": result.get('plan', {}),
                    "status": result.get('status', 'completed'),
                    "enhanced_by": result.get('enhanced_by', 'openmanus'),
                    "objective": objective,
                    "child_id": child_id
                }

        # Fallback to basic planning
        logger.info("🔄 Using basic task planning")
        return {
            "success": True,
            "plan": {
                "objective": objective,
                "basic_plan": f"Create a learning plan for: {objective}",
                "resources": resources,
                "context": context
            },
            "status": "completed",
            "enhanced_by": "standard",
            "objective": objective,
            "child_id": child_id
        }

    except Exception as e:
        logger.error(f"Task planning error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Task planning failed: {str(e)}"
        )

@router.get("/openmanus-status")
async def openmanus_status():
    """
    Get OpenManus integration status and capabilities
    """
    try:
        if OPENMANUS_INTEGRATION and is_openmanus_available():
            doubao_service = get_doubao_service()
            status_info = doubao_service.get_openmanus_status()

            return {
                "integration_available": True,
                "openmanus_status": status_info,
                "enhanced_features": [
                    "Enhanced voice input processing",
                    "Advanced intent classification",
                    "Intelligent task planning",
                    "Automatic summary generation",
                    "Context-aware responses"
                ],
                "fallback_available": True
            }
        else:
            return {
                "integration_available": False,
                "openmanus_status": {
                    "status": "unavailable",
                    "ready": False,
                    "message": "OpenManus integration not available"
                },
                "enhanced_features": [],
                "fallback_available": True
            }

    except Exception as e:
        logger.error(f"Status check error: {e}")
        return {
            "integration_available": False,
            "error": str(e),
            "fallback_available": True
        }

