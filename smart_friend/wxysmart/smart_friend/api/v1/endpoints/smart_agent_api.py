#!/usr/bin/env python3
"""
智能学习Agent API
提供Agent服务的RESTful API接口
"""

import logging
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field

from agent.smart_agent import get_agent
from agent.posture_tool import register_posture_tools
from agent.task_time_modifier import register_task_time_modifier_tools

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/smart-agent", tags=["smart-agent"])

# 初始化Agent并注册工具
def init_agent():
    """初始化Agent并注册所有工具"""
    agent = get_agent()

    # 注册坐姿检测工具
    register_posture_tools(agent)

    # 注册任务时间修改工具
    register_task_time_modifier_tools(agent)

    logger.info("SmartAgent已初始化并注册所有工具")
    return agent

# 全局初始化
_agent = init_agent()


# 请求模型
class VoiceInputRequest(BaseModel):
    """语音输入请求模型"""
    voice_text: str = Field(..., description="语音识别的文本内容")
    child_id: Optional[int] = Field(None, description="学生ID")


class PostureCheckRequest(BaseModel):
    """坐姿检查请求模型"""
    image_data: Optional[str] = Field(None, description="图像数据（base64编码）")
    child_id: Optional[int] = Field(None, description="学生ID")


# 响应模型
class AgentResponse(BaseModel):
    """Agent响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    child_id: Optional[int] = Field(None, description="学生ID")
    timestamp: Optional[str] = Field(None, description="时间戳")
    data: Optional[Dict[str, Any]] = Field(None, description="额外数据")
    intent_info: Optional[Dict[str, Any]] = Field(None, description="意图识别信息")


@router.post("/voice-input", response_model=AgentResponse)
async def process_voice_input(request: VoiceInputRequest):
    """
    处理语音输入
    
    这是Agent的主要入口，接收语音识别的文本，Agent会自动分析意图并选择合适的工具
    """
    try:
        logger.info(f"接收到语音输入: {request.voice_text[:50]}...")
        
        # 调用Agent处理语音输入
        result = await _agent.process_voice_input(
            voice_text=request.voice_text,
            child_id=request.child_id
        )
        
        # 准备响应数据
        response_data = None
        if 'posture_data' in result:
            response_data = result.get('posture_data')
        elif 'task_data' in result:
            response_data = result.get('task_data')

        # 创建响应对象
        response = AgentResponse(
            success=result.get('success', True),
            message=result.get('message', ''),
            child_id=request.child_id,
            data=response_data
        )

        # 如果有意图信息，添加到响应中
        if 'intent_info' in result:
            response.intent_info = result['intent_info']

        return response
        
    except Exception as e:
        logger.error(f"处理语音输入异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理语音输入失败: {str(e)}"
        )


@router.post("/posture-check", response_model=AgentResponse)
async def check_posture(request: PostureCheckRequest):
    """
    坐姿检测接口
    
    可以从摄像头或上传的图像检测学生坐姿
    """
    try:
        logger.info("接收到坐姿检测请求")
        
        if request.image_data:
            # 从图像检测坐姿
            result = await _agent.tools['check_posture_image']['function'](request.image_data)
        else:
            # 从摄像头检测坐姿
            result = await _agent.tools['check_posture_camera']['function']()
        
        return AgentResponse(
            success=result.get('success', True),
            message=result.get('message', ''),
            child_id=request.child_id,
            data=result.get('posture_data') if 'posture_data' in result else None
        )
        
    except Exception as e:
        logger.error(f"坐姿检测异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"坐姿检测失败: {str(e)}"
        )


@router.get("/posture-tips", response_model=AgentResponse)
async def get_posture_tips():
    """
    获取坐姿建议
    """
    try:
        result = _agent.tools['posture_tips']['function']()
        
        return AgentResponse(
            success=result.get('success', True),
            message=result.get('message', ''),
            child_id=None
        )
        
    except Exception as e:
        logger.error(f"获取坐姿建议异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取坐姿建议失败: {str(e)}"
        )


@router.get("/conversation-history/{child_id}")
async def get_conversation_history(child_id: int, limit: int = 10):
    """
    获取对话历史
    
    Args:
        child_id: 学生ID
        limit: 返回的历史记录数量限制
    """
    try:
        history = _agent.get_conversation_history(child_id=child_id, limit=limit)
        
        return {
            "success": True,
            "child_id": child_id,
            "history": history,
            "count": len(history)
        }
        
    except Exception as e:
        logger.error(f"获取对话历史异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取对话历史失败: {str(e)}"
        )


@router.delete("/conversation-history/{child_id}")
async def clear_conversation_history(child_id: int):
    """
    清空对话历史
    
    Args:
        child_id: 学生ID
    """
    try:
        _agent.clear_conversation_history(child_id=child_id)
        
        return {
            "success": True,
            "message": f"学生 {child_id} 的对话历史已清空",
            "child_id": child_id
        }
        
    except Exception as e:
        logger.error(f"清空对话历史异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清空对话历史失败: {str(e)}"
        )


@router.get("/status")
async def get_agent_status():
    """
    获取Agent状态信息
    """
    try:
        return {
            "status": "active",
            "tools_count": len(_agent.tools),
            "available_tools": list(_agent.tools.keys()),
            "conversation_count": len(_agent.conversation_history),
            "agent_type": "SmartLearningAgent"
        }
        
    except Exception as e:
        logger.error(f"获取Agent状态异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Agent状态失败: {str(e)}"
        )


@router.post("/reload")
async def reload_agent():
    """
    重新加载Agent
    """
    try:
        global _agent
        _agent = init_agent()
        
        return {
            "success": True,
            "message": "Agent重新加载成功",
            "tools_count": len(_agent.tools),
            "available_tools": list(_agent.tools.keys())
        }
        
    except Exception as e:
        logger.error(f"重新加载Agent异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重新加载Agent失败: {str(e)}"
        )



