# 豆包模型API端点
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any
import logging

from service.doubao_service import DoubaoService, DoubaoAPIException, get_doubao_service, create_doubao_service
from service.models.doubao_models import (
    ChatCompletionRequest, 
    ChatCompletionResponse, 
    SimpleChatRequest,
    ModelInfo,
    ConnectionStatus,
    DoubaoServiceStatus,
    DoubaoConfig,
    BatchChatRequest,
    BatchChatResponse,
    APIResponse,
    ErrorResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/chat/completions", response_model=ChatCompletionResponse)
async def chat_completion(
    request: ChatCompletionRequest,
    doubao_service: DoubaoService = Depends(get_doubao_service)
):
    """
    豆包模型聊天补全接口
    
    支持多轮对话，可以传入完整的对话历史。
    """
    try:
        result = doubao_service.chat_completion(
            messages=[msg.dict() for msg in request.messages],
            temperature=request.temperature,
            top_p=request.top_p,
            max_tokens=request.max_tokens,
            stream=request.stream
        )
        
        return ChatCompletionResponse(**result)
        
    except DoubaoAPIException as e:
        logger.error(f"豆包API调用失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"豆包模型服务不可用: {str(e)}"
        )
    except Exception as e:
        logger.error(f"聊天补全接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务内部错误: {str(e)}"
        )


@router.post("/chat/simple", response_model=ChatCompletionResponse)
async def simple_chat(
    request: SimpleChatRequest,
    doubao_service: DoubaoService = Depends(get_doubao_service)
):
    """
    简单聊天接口
    
    直接传入prompt，适用于单轮对话场景。
    """
    try:
        result = doubao_service.simple_chat(
            prompt=request.prompt,
            temperature=request.temperature,
            top_p=request.top_p,
            max_tokens=request.max_tokens
        )
        
        return ChatCompletionResponse(**result)
        
    except DoubaoAPIException as e:
        logger.error(f"豆包API调用失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"豆包模型服务不可用: {str(e)}"
        )
    except Exception as e:
        logger.error(f"简单聊天接口异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务内部错误: {str(e)}"
        )


@router.get("/model/info", response_model=ModelInfo)
async def get_model_info(
    doubao_service: DoubaoService = Depends(get_doubao_service)
):
    """
    获取当前模型信息
    """
    try:
        info = doubao_service.get_model_info()
        return ModelInfo(**info)
    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模型信息失败: {str(e)}"
        )


@router.get("/health", response_model=DoubaoServiceStatus)
async def health_check(
    doubao_service: DoubaoService = Depends(get_doubao_service)
):
    """
    健康检查接口
    
    检查豆包服务是否正常工作。
    """
    try:
        # 获取模型信息
        model_info = doubao_service.get_model_info()
        
        # 验证连接
        is_connected = doubao_service.validate_connection()
        
        from datetime import datetime
        current_time = datetime.now().isoformat()
        
        connection_status = ConnectionStatus(
            is_connected=is_connected,
            message="连接正常" if is_connected else "连接失败",
            timestamp=current_time
        )
        
        service_status = DoubaoServiceStatus(
            is_healthy=is_connected,
            model_info=ModelInfo(**model_info),
            connection_status=connection_status,
            last_check_time=current_time
        )
        
        return service_status
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"健康检查失败: {str(e)}"
        )


@router.post("/service/create", response_model=APIResponse)
async def create_service(config: DoubaoConfig):
    """
    创建新的豆包服务实例
    
    使用自定义配置创建服务实例，适用于需要使用不同配置的场景。
    """
    try:
        service = create_doubao_service(
            api_key=config.api_key,
            base_url=config.base_url,
            model_name=config.model_name
        )
        
        # 验证新服务
        is_valid = service.validate_connection()
        
        return APIResponse(
            success=is_valid,
            message="服务创建成功" if is_valid else "服务创建成功但连接验证失败",
            data={
                "model_name": service.model_name,
                "base_url": service.base_url,
                "connection_valid": is_valid
            }
        )
        
    except Exception as e:
        logger.error(f"创建服务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建服务失败: {str(e)}"
        )


@router.post("/test/connection", response_model=ConnectionStatus)
async def test_connection(
    doubao_service: DoubaoService = Depends(get_doubao_service)
):
    """
    测试连接接口
    
    测试与豆包API的连接是否正常。
    """
    try:
        is_connected = doubao_service.validate_connection()
        from datetime import datetime
        
        return ConnectionStatus(
            is_connected=is_connected,
            message="连接测试成功" if is_connected else "连接测试失败",
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"连接测试失败: {e}")
        return ConnectionStatus(
            is_connected=False,
            message=f"连接测试异常: {str(e)}",
            timestamp=datetime.now().isoformat()
        )
