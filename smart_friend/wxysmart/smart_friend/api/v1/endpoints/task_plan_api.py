# 任务计划API端点
from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import logging

from service.task_plan_service import TaskPlanService, get_task_plan_service
from core.daily_tasks.schemas import DailyTaskResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/task-plan", tags=["任务计划"])


@router.post("/save")
async def save_task_plan(
    task_plan_data: Dict[str, Any],
    service: TaskPlanService = Depends(get_task_plan_service)
):
    """
    保存前端生成的任务计划到数据库
    
    请求体格式：
    {
        "child_id": 2,
        "task_plan": [
            {
                "task_name": "数学作业",
                "time_slot": "18:00 - 18:45",
                "customization": "重点练习乘法运算",
                "difficulty": "乘法表记忆",
                "solution": "通过儿歌和游戏方式记忆乘法表",
                "confidence_index": 3,
                "sub_tasks": [
                    {
                        "sub_task_name": "练习册第9页",
                        "time_slot": "18:00 - 18:20"
                    },
                    {
                        "sub_task_name": "口算题卡20题",
                        "time_slot": "18:20 - 18:35"
                    }
                ]
            }
        ]
    }
    """
    try:
        logger.info(f"收到任务计划保存请求")
        
        # 验证必需字段
        if "child_id" not in task_plan_data:
            raise HTTPException(
                status_code=400,
                detail="缺少必需字段: child_id"
            )
        
        if "task_plan" not in task_plan_data:
            raise HTTPException(
                status_code=400,
                detail="缺少必需字段: task_plan"
            )
        
        child_id = task_plan_data["child_id"]
        task_plan = task_plan_data["task_plan"]
        
        # 验证学生ID
        if child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 验证任务计划数据
        if not isinstance(task_plan, list) or len(task_plan) == 0:
            raise HTTPException(
                status_code=400,
                detail="任务计划必须是非空数组"
            )
        
        # 调用服务层保存任务计划
        result = await service.save_task_plan(child_id, task_plan)
        
        if result["success"]:
            logger.info(f"成功保存学生{child_id}的任务计划，共{result['total_tasks']}个任务")
            return {
                "success": True,
                "message": f"成功保存任务计划，共{result['total_tasks']}个任务",
                "child_id": child_id,
                "total_tasks": result["total_tasks"],
                "saved_task_ids": result["saved_task_ids"],
                "soft_deleted_count": result["soft_deleted_count"]
            }
        else:
            logger.error(f"保存任务计划失败: {result['message']}")
            raise HTTPException(
                status_code=500,
                detail=f"保存任务计划失败: {result['message']}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存任务计划时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"保存任务计划失败: {str(e)}"
        )


@router.get("/child/{child_id}/today", response_model=List[DailyTaskResponse])
async def get_today_task_plan(
    child_id: int,
    service: TaskPlanService = Depends(get_task_plan_service)
):
    """
    获取指定学生今日的任务计划
    """
    try:
        logger.info(f"获取学生{child_id}的今日任务计划")
        
        # 验证学生ID
        if child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 获取今日任务计划
        tasks = await service.get_today_task_plan(child_id)
        
        return [DailyTaskResponse(**task) for task in tasks]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取今日任务计划时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取今日任务计划失败: {str(e)}"
        )


@router.delete("/task/{task_id}")
async def delete_task(
    task_id: int,
    service: TaskPlanService = Depends(get_task_plan_service)
):
    """
    删除指定任务（软删除）
    """
    try:
        logger.info(f"删除任务: {task_id}")
        
        # 验证任务ID
        if task_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="任务ID必须大于0"
            )
        
        # 删除任务
        result = await service.delete_task(task_id)
        
        if result["success"]:
            return {
                "success": True,
                "message": "任务删除成功",
                "task_id": task_id
            }
        else:
            raise HTTPException(
                status_code=404,
                detail=result["message"]
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除任务失败: {str(e)}"
        )


@router.put("/task/{task_id}")
async def update_task(
    task_id: int,
    task_data: Dict[str, Any],
    service: TaskPlanService = Depends(get_task_plan_service)
):
    """
    更新指定任务
    """
    try:
        logger.info(f"更新任务: {task_id}")
        
        # 验证任务ID
        if task_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="任务ID必须大于0"
            )
        
        # 更新任务
        result = await service.update_task(task_id, task_data)
        
        if result["success"]:
            return {
                "success": True,
                "message": "任务更新成功",
                "task_id": task_id,
                "updated_task": result["updated_task"]
            }
        else:
            raise HTTPException(
                status_code=404,
                detail=result["message"]
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新任务时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"更新任务失败: {str(e)}"
        )


@router.post("/task/{task_id}/subtask")
async def add_subtask(
    task_id: int,
    subtask_data: Dict[str, Any],
    service: TaskPlanService = Depends(get_task_plan_service)
):
    """
    为指定任务添加子任务
    """
    try:
        logger.info(f"为任务{task_id}添加子任务")
        
        # 验证任务ID
        if task_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="任务ID必须大于0"
            )
        
        # 添加子任务
        result = await service.add_subtask(task_id, subtask_data)
        
        if result["success"]:
            return {
                "success": True,
                "message": "子任务添加成功",
                "task_id": task_id,
                "subtask_id": result["subtask_id"]
            }
        else:
            raise HTTPException(
                status_code=404,
                detail=result["message"]
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加子任务时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"添加子任务失败: {str(e)}"
        )
