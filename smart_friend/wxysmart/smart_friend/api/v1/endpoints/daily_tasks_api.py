# -*- coding: utf-8 -*-
"""
当日任务管理API
提供当日任务的查询、创建、更新、删除功能
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from pydantic import BaseModel, Field

from service.daily_task_service import DailyTaskService

logger = logging.getLogger(__name__)

router = APIRouter()


class TaskCreateRequest(BaseModel):
    """任务创建请求模型"""
    child_id: int = Field(..., description="学生ID")
    task_name: str = Field(..., description="任务名称")
    description: str = Field("", description="任务描述")
    subject: str = Field(..., description="学科")
    task_date: Optional[str] = Field(None, description="任务日期 (YYYY-MM-DD)")
    time_slot: Optional[str] = Field(None, description="时间段")
    estimated_duration: Optional[int] = Field(None, description="预计时长(分钟)")
    difficulty: Optional[str] = Field(None, description="难点")
    confidence_index: Optional[int] = Field(None, description="信心指数(1-5)")


class TaskUpdateRequest(BaseModel):
    """任务更新请求模型"""
    task_name: Optional[str] = Field(None, description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    status: Optional[str] = Field(None, description="任务状态")
    completion_percentage: Optional[float] = Field(None, description="完成百分比")


class TaskStatusUpdateRequest(BaseModel):
    """任务状态更新请求模型"""
    status: str = Field(..., description="任务状态 (pending/in_progress/completed)")


def get_daily_task_service() -> DailyTaskService:
    """获取当日任务服务实例"""
    return DailyTaskService()


@router.get("/child/{child_id}/today")
async def get_today_tasks(
    child_id: int,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    获取指定学生的当日任务
    
    Args:
        child_id: 学生ID
        
    Returns:
        Dict: 包含任务列表的响应
    """
    try:
        logger.info(f"获取学生{child_id}的当日任务")
        
        # 验证学生ID
        if child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 获取今日任务
        today = date.today()
        tasks = service.get_tasks_by_date(child_id, today)
        
        # 转换为字典格式
        task_list = []
        if tasks:
            for task in tasks:
                task_dict = task.to_dict() if hasattr(task, 'to_dict') else task
                
                # 获取子任务
                if hasattr(task, 'id'):
                    sub_tasks = service.get_task_items(task.id)
                    task_dict['sub_tasks'] = [
                        item.to_dict() if hasattr(item, 'to_dict') else item 
                        for item in sub_tasks
                    ] if sub_tasks else []
                
                task_list.append(task_dict)
        
        logger.info(f"成功获取学生{child_id}的{len(task_list)}个当日任务")
        
        return {
            "success": True,
            "message": f"成功获取{len(task_list)}个当日任务",
            "child_id": child_id,
            "task_date": today.isoformat(),
            "tasks": task_list,
            "total_count": len(task_list)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取当日任务时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取当日任务失败: {str(e)}"
        )


@router.post("/create")
async def create_task(
    request: TaskCreateRequest,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    创建新任务
    
    Args:
        request: 任务创建请求
        
    Returns:
        Dict: 创建结果
    """
    try:
        logger.info(f"创建学生{request.child_id}的新任务: {request.task_name}")
        
        # 验证学生ID
        if request.child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 验证任务名称
        if not request.task_name.strip():
            raise HTTPException(
                status_code=400,
                detail="任务名称不能为空"
            )
        
        # 验证学科
        if not request.subject.strip():
            raise HTTPException(
                status_code=400,
                detail="学科不能为空"
            )
        
        # 准备任务数据
        task_data = {
            "child_id": request.child_id,
            "task_name": request.task_name,
            "description": request.description,
            "subject": request.subject,
            "task_date": datetime.strptime(request.task_date, "%Y-%m-%d") if request.task_date else datetime.now(),
            "time_slot": request.time_slot,
            "estimated_duration": request.estimated_duration,
            "difficulty": request.difficulty,
            "confidence_index": request.confidence_index,
            "status": "pending"
        }
        
        # 创建任务
        result = service.create_task(**task_data)
        
        if result:
            logger.info(f"成功创建任务: {request.task_name} (ID: {result.get('id')})")
            return {
                "success": True,
                "message": "任务创建成功",
                "task": result
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="任务创建失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建任务时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"创建任务失败: {str(e)}"
        )


@router.put("/{task_id}/status")
async def update_task_status(
    task_id: int,
    request: TaskStatusUpdateRequest,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    更新任务状态
    
    Args:
        task_id: 任务ID
        request: 状态更新请求
        
    Returns:
        Dict: 更新结果
    """
    try:
        logger.info(f"更新任务{task_id}状态为: {request.status}")
        
        # 验证任务ID
        if task_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="任务ID必须大于0"
            )
        
        # 验证状态值
        valid_statuses = ["pending", "in_progress", "completed"]
        if request.status not in valid_statuses:
            raise HTTPException(
                status_code=400,
                detail=f"无效的状态值，必须是: {', '.join(valid_statuses)}"
            )
        
        # 更新任务状态
        completion_percentage = 100.0 if request.status == "completed" else None
        
        result = service.update_task(
            task_id=task_id,
            status=request.status,
            completion_percentage=completion_percentage
        )
        
        if result:
            logger.info(f"成功更新任务{task_id}状态")
            return {
                "success": True,
                "message": "任务状态更新成功",
                "task": result
            }
        else:
            raise HTTPException(
                status_code=404,
                detail="任务不存在或更新失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新任务状态时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"更新任务状态失败: {str(e)}"
        )


@router.delete("/{task_id}")
async def delete_task(
    task_id: int,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    删除任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        Dict: 删除结果
    """
    try:
        logger.info(f"删除任务{task_id}")
        
        # 验证任务ID
        if task_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="任务ID必须大于0"
            )
        
        # 删除任务
        result = service.delete_task(task_id)
        
        if result:
            logger.info(f"成功删除任务{task_id}")
            return {
                "success": True,
                "message": "任务删除成功"
            }
        else:
            raise HTTPException(
                status_code=404,
                detail="任务不存在或删除失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"删除任务失败: {str(e)}"
        )


@router.get("/child/{child_id}/stats")
async def get_task_stats(
    child_id: int,
    service: DailyTaskService = Depends(get_daily_task_service)
):
    """
    获取学生任务统计信息
    
    Args:
        child_id: 学生ID
        
    Returns:
        Dict: 统计信息
    """
    try:
        logger.info(f"获取学生{child_id}的任务统计")
        
        # 验证学生ID
        if child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 获取今日任务
        today = date.today()
        tasks = service.get_tasks_by_date(child_id, today)
        
        # 计算统计信息
        total_tasks = len(tasks) if tasks else 0
        completed_tasks = len([t for t in tasks if t.status == 'completed']) if tasks else 0
        in_progress_tasks = len([t for t in tasks if t.status == 'in_progress']) if tasks else 0
        pending_tasks = len([t for t in tasks if t.status == 'pending']) if tasks else 0
        
        completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        
        return {
            "success": True,
            "child_id": child_id,
            "task_date": today.isoformat(),
            "stats": {
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "in_progress_tasks": in_progress_tasks,
                "pending_tasks": pending_tasks,
                "completion_rate": round(completion_rate, 1)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务统计时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取任务统计失败: {str(e)}"
        )
