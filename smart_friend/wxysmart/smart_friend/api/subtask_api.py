# 子任务操作API
from fastapi import APIRouter, HTTPException
from typing import Optional
import logging
from pydantic import BaseModel

from service.daily_task_service import DailyTaskService

logger = logging.getLogger(__name__)

router = APIRouter()

class AddSubTaskRequest(BaseModel):
    """添加子任务请求模型"""
    child_id: int

    plan_id: int
    task_name: str
    sub_task_content: str
    daily_task_id:int

    sub_task_source: str = "手动添加"
    time_slot: Optional[str] = None

class RemoveSubTaskRequest(BaseModel):
    """删除子任务请求模型"""
    child_id: int
    task_name: str  # 保留用于兼容性
    sub_task_content: str  # 保留用于兼容性
    sub_task_id: Optional[int] = None  # 新增子任务ID字段

@router.post("/add_subtask")
async def add_subtask(request: AddSubTaskRequest):
    """
    添加子任务接口
    
    为指定的主任务添加一个新的子任务
    """
    try:
        logger.info(f"添加子任务: {request.sub_task_content} 到任务: {request.task_name}")
        
        service = DailyTaskService()
        result = await service.add_subtask(
            task_name=request.task_name,
            sub_task_content=request.sub_task_content,
            sub_task_source=request.sub_task_source,

            time_slot=request.time_slot,
            plan_id=request.plan_id,
            daily_task_id=request.daily_task_id


        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "添加子任务失败")
            )
        
        return {
            "success": True,
            "message": "子任务添加成功",
            "data": result.get("data", {})
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加子任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"添加子任务失败: {str(e)}"
        )

@router.post("/remove_subtask")
async def remove_subtask(request: RemoveSubTaskRequest):
    """
    删除子任务接口

    将指定的子任务软删除（设置is_active=False）
    支持通过子任务ID或任务名称+子任务内容进行删除
    """
    try:
        if request.sub_task_id:
            logger.info(f"通过ID删除子任务: {request.sub_task_id}")
        else:
            logger.info(f"删除子任务: {request.sub_task_content} 从任务: {request.task_name}")

        service = DailyTaskService()
        result = await service.remove_subtask(
            task_name=request.task_name,
            sub_task_content=request.sub_task_content,
            sub_task_id=request.sub_task_id
        )

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "删除子任务失败")
            )

        return {
            "success": True,
            "message": "子任务删除成功",
            "data": result.get("data", {})
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除子任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除子任务失败: {str(e)}"
        )

@router.post("/restore_subtask")
async def restore_subtask(request: RemoveSubTaskRequest):
    """
    撤销删除子任务接口

    将已删除的子任务恢复（设置is_active=True）
    支持通过子任务ID或任务名称+子任务内容进行恢复
    """
    try:
        if request.sub_task_id:
            logger.info(f"通过ID撤销删除子任务: {request.sub_task_id}")
        else:
            logger.info(f"撤销删除子任务: {request.sub_task_content} 从任务: {request.task_name}")

        service = DailyTaskService()
        result = await service.restore_subtask(
            task_name=request.task_name,
            sub_task_content=request.sub_task_content,
            sub_task_id=request.sub_task_id
        )

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "撤销删除子任务失败")
            )

        return {
            "success": True,
            "message": "子任务恢复成功",
            "data": result.get("data", {})
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"撤销删除子任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"撤销删除子任务失败: {str(e)}"
        )

@router.get("/get_task_subtasks/{task_name}")
async def get_task_subtasks(task_name: str, child_id: int):
    """
    获取指定任务的所有子任务
    """
    try:
        logger.info(f"获取任务 {task_name} 的子任务列表")
        
        service = DailyTaskService()
        # 这里需要在service中添加获取子任务的方法
        # result = await service.get_task_subtasks(task_name, child_id)
        
        return {
            "success": True,
            "message": "获取子任务列表成功",
            "data": []  # 暂时返回空列表
        }
        
    except Exception as e:
        logger.error(f"获取子任务列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取子任务列表失败: {str(e)}"
        )
