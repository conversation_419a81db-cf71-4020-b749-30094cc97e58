#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化daily_tasks数据库表
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.exc import SQLAlchemyError
import logging
from core.user_management.database.connection import get_sqlite_manager
from core.user_management.models.user_models import Base
from core.daily_tasks.models.daily_task_models import DailyTask, TaskItem

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_daily_tasks_tables():
    """创建daily_tasks相关表"""
    try:
        sqlite_manager = get_sqlite_manager()
        
        if not sqlite_manager.engine:
            logger.error("数据库引擎未初始化")
            return False
        
        # 创建所有表（包括daily_tasks和task_items）
        Base.metadata.create_all(bind=sqlite_manager.engine)
        logger.info("成功创建daily_tasks相关数据表")
        
        # 验证表是否创建成功
        table_names = sqlite_manager.get_table_names()
        expected_tables = ['daily_tasks', 'task_items']
        
        for table in expected_tables:
            if table in table_names:
                logger.info(f"✓ 表 '{table}' 创建成功")
            else:
                logger.error(f"✗ 表 '{table}' 创建失败")
                return False
        
        return True
        
    except SQLAlchemyError as e:
        logger.error(f"创建daily_tasks表失败: {e}")
        return False


def check_daily_tasks_tables():
    """检查daily_tasks表状态"""
    try:
        sqlite_manager = get_sqlite_manager()
        
        # 检查连接
        if not sqlite_manager.check_connection():
            logger.error("数据库连接失败")
            return False
        
        logger.info("数据库连接正常")
        
        # 检查表
        table_names = sqlite_manager.get_table_names()
        expected_tables = ['daily_tasks', 'task_items']
        
        logger.info(f"数据库中的表: {table_names}")
        
        missing_tables = []
        for table in expected_tables:
            if table not in table_names:
                missing_tables.append(table)
        
        if missing_tables:
            logger.warning(f"缺少daily_tasks相关表: {missing_tables}")
            return False
        else:
            logger.info("所有daily_tasks相关表都存在")
            return True
            
    except Exception as e:
        logger.error(f"检查daily_tasks表失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("Daily Tasks 数据库表初始化工具")
    print("=" * 60)
    
    # 检查表是否存在
    if check_daily_tasks_tables():
        print("✅ daily_tasks相关表已存在，无需创建")
        return
    
    print("📋 daily_tasks相关表不存在，开始创建...")
    
    # 创建表
    if create_daily_tasks_tables():
        print("✅ daily_tasks相关表创建成功")
        
        # 再次检查
        if check_daily_tasks_tables():
            print("✅ 验证通过，所有表创建成功")
        else:
            print("❌ 验证失败，表创建可能有问题")
            sys.exit(1)
    else:
        print("❌ daily_tasks相关表创建失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
