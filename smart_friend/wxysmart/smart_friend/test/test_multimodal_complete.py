#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的多模态任务输入测试
使用真实的图片和语音数据测试所有功能
"""

import asyncio
import json
import logging
import base64
import os
import sys
from datetime import datetime, timezone
from PIL import Image

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_text_input():
    """测试文本输入"""
    print("\n" + "=" * 60)
    print("📝 测试文本输入")
    print("=" * 60)
    
    try:
        from service.multimodal_task_input_service import get_multimodal_task_input_service
        
        service = get_multimodal_task_input_service()
        test_child_id = 1
        
        # 测试复杂的文本输入
        text_content = """
今天的作业安排：
1. 数学：完成练习册第15-16页，重点练习分数加减法，预计45分钟
2. 英语：Unit5单词抄写3遍，听力练习15分钟
3. 语文：背诵《静夜思》，练字20个字
4. 科学：完成植物生长实验报告，包括观察记录和结论
5. 体育：跳绳100下，仰卧起坐20个
明天上午要交作业
"""
        
        print(f"输入文本内容:")
        print(text_content)
        
        result = await service.process_text_input(
            child_id=test_child_id,
            text_content=text_content
        )
        
        print(f"\n处理结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
        print(f"消息: {result['message']}")
        print(f"解析任务数: {result.get('total_tasks', 0)}")
        print(f"存储任务数: {result.get('stored_tasks', 0)}")
        
        if result['success'] and result['tasks']:
            print("\n📋 解析出的任务:")
            for i, task in enumerate(result['tasks'], 1):
                print(f"  {i}. {task.get('task_name', '未知')} - {task.get('subject', '未知')} - 难度{task.get('difficulty_level', 0)}/5")
                print(f"     预计时长: {task.get('estimated_duration', 0)}分钟")
                print(f"     优先级: {task.get('priority', '未知')}")
                if task.get('sub_tasks'):
                    for j, sub_task in enumerate(task['sub_tasks'], 1):
                        print(f"     {i}.{j} {sub_task.get('task', '')}")
        
        return result
        
    except Exception as e:
        logger.error(f"测试文本输入失败: {e}")
        return {"success": False, "error": str(e)}


async def test_voice_input():
    """测试语音输入"""
    print("\n" + "=" * 60)
    print("🎤 测试语音输入")
    print("=" * 60)
    
    try:
        from service.multimodal_task_input_service import get_multimodal_task_input_service
        
        service = get_multimodal_task_input_service()
        test_child_id = 1
        
        # 读取真实的语音测试数据
        voice_files = [
            "test_data/voice/voice_sample_1.txt",
            "test_data/voice/voice_sample_2.txt",
            "test_data/voice/voice_sample_3.txt"
        ]
        
        results = []
        
        for i, voice_file in enumerate(voice_files, 1):
            if os.path.exists(voice_file):
                with open(voice_file, 'r', encoding='utf-8') as f:
                    voice_text = f.read().strip()
                
                print(f"\n🎤 语音样本 {i}:")
                print(f"内容: {voice_text}")
                
                result = await service.process_voice_input(
                    child_id=test_child_id,
                    voice_text=voice_text
                )
                
                print(f"处理结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
                print(f"解析任务数: {result.get('total_tasks', 0)}")
                
                if result['success'] and result['tasks']:
                    print("解析出的任务:")
                    for j, task in enumerate(result['tasks'], 1):
                        print(f"  {j}. {task.get('task_name', '未知')} - {task.get('subject', '未知')}")
                
                results.append(result)
            else:
                print(f"❌ 语音文件不存在: {voice_file}")
        
        return results
        
    except Exception as e:
        logger.error(f"测试语音输入失败: {e}")
        return [{"success": False, "error": str(e)}]


async def test_image_input_multimodal():
    """测试多模态图片输入"""
    print("\n" + "=" * 60)
    print("🖼️ 测试多模态图片输入")
    print("=" * 60)
    
    try:
        from service.multimodal_task_input_service import get_multimodal_task_input_service
        
        service = get_multimodal_task_input_service()
        test_child_id = 1
        
        # 测试不同格式的图片
        image_tests = [
            {
                "name": "作业单1 (base64格式)",
                "file": "test_data/images/homework_1_base64.txt",
                "format": "base64"
            },
            {
                "name": "作业单2 (bytes格式)",
                "file": "test_data/images/20250619155018.jpg",
                "format": "bytes"
            },
            {
                "name": "时间表 (PIL格式)",
                "file": "test_data/images/schedule.jpg",
                "format": "pil"
            }
        ]
        
        results = []
        
        for test_case in image_tests:
            print(f"\n🖼️ 测试 {test_case['name']}:")
            
            if not os.path.exists(test_case['file']):
                print(f"❌ 文件不存在: {test_case['file']}")
                continue
            
            # 根据格式加载图片数据
            if test_case['format'] == 'base64':
                with open(test_case['file'], 'r') as f:
                    base64_data = f.read().strip()
                    if base64_data.startswith('data:image'):
                        image_data = base64_data.split(',')[1]
                    else:
                        image_data = base64_data
                        
            elif test_case['format'] == 'bytes':
                with open(test_case['file'], 'rb') as f:
                    image_data = f.read()
                    
            elif test_case['format'] == 'pil':
                image_data = Image.open(test_case['file'])
            
            print(f"图片格式: {test_case['format']}")
            print(f"数据类型: {type(image_data)}")
            
            result = await service.process_image_input(
                child_id=test_child_id,
                image_data=image_data,
                image_format=test_case['format']
            )
            
            print(f"处理结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
            print(f"消息: {result['message']}")
            print(f"解析任务数: {result.get('total_tasks', 0)}")
            print(f"存储任务数: {result.get('stored_tasks', 0)}")
            
            if result['success'] and result['tasks']:
                print("解析出的任务:")
                for i, task in enumerate(result['tasks'], 1):
                    print(f"  {i}. {task.get('task_name', '未知')} - {task.get('subject', '未知')}")
                    print(f"     描述: {task.get('description', '无')[:50]}...")
                    if task.get('sub_tasks'):
                        print(f"     子任务数: {len(task['sub_tasks'])}")
            
            results.append(result)
        
        return results
        
    except Exception as e:
        logger.error(f"测试多模态图片输入失败: {e}")
        return [{"success": False, "error": str(e)}]


async def test_format_conversion():
    """测试图片格式转换功能"""
    print("\n" + "=" * 60)
    print("🔄 测试图片格式转换")
    print("=" * 60)
    
    try:
        from service.multimodal_task_input_service import get_multimodal_task_input_service
        
        service = get_multimodal_task_input_service()
        
        # 测试不同格式转换
        test_image_path = "test_data/images/homework_1.jpg"
        
        if not os.path.exists(test_image_path):
            print(f"❌ 测试图片不存在: {test_image_path}")
            return {"success": False}
        
        # 1. 测试bytes转base64
        print("1. 测试 bytes → base64 转换:")
        with open(test_image_path, 'rb') as f:
            image_bytes = f.read()
        
        base64_result = service._convert_to_base64(image_bytes, "bytes")
        print(f"   结果: {'✅ 成功' if base64_result else '❌ 失败'}")
        if base64_result:
            print(f"   Base64长度: {len(base64_result)} 字符")
        
        # 2. 测试PIL转base64
        print("\n2. 测试 PIL → base64 转换:")
        try:
            pil_image = Image.open(test_image_path)
            pil_base64_result = service._convert_to_base64(pil_image, "pil")
            print(f"   结果: {'✅ 成功' if pil_base64_result else '❌ 失败'}")
            if pil_base64_result:
                print(f"   Base64长度: {len(pil_base64_result)} 字符")
        except Exception as e:
            print(f"   ❌ PIL转换失败: {e}")
        
        # 3. 测试自动格式检测
        print("\n3. 测试自动格式检测:")
        auto_result = service._convert_to_base64(image_bytes, "auto")
        print(f"   结果: {'✅ 成功' if auto_result else '❌ 失败'}")
        
        return {"success": True}
        
    except Exception as e:
        logger.error(f"测试格式转换失败: {e}")
        return {"success": False, "error": str(e)}


async def test_service_health():
    """测试服务健康状态"""
    print("\n" + "=" * 60)
    print("🏥 测试服务健康状态")
    print("=" * 60)
    
    try:
        from service.multimodal_task_input_service import get_multimodal_task_input_service
        
        service = get_multimodal_task_input_service()
        
        # 检查豆包服务
        doubao_status = service.doubao_service.validate_connection()
        print(f"豆包AI服务: {'✅ 正常' if doubao_status else '❌ 异常'}")
        
        # 检查PIL库
        from service.multimodal_task_input_service import PIL_AVAILABLE
        print(f"PIL图片处理: {'✅ 可用' if PIL_AVAILABLE else '❌ 不可用'}")
        
        # 检查测试数据
        test_files = [
            "test_data/images/homework_1.jpg",
            "test_data/images/homework_2.jpg", 
            "test_data/images/schedule.jpg",
            "test_data/voice/voice_sample_1.txt"
        ]
        
        print(f"\n📁 测试数据文件:")
        for file_path in test_files:
            exists = os.path.exists(file_path)
            print(f"   {file_path}: {'✅ 存在' if exists else '❌ 缺失'}")
        
        return {
            "doubao_status": doubao_status,
            "pil_available": PIL_AVAILABLE,
            "test_files_ready": all(os.path.exists(f) for f in test_files)
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {"error": str(e)}


async def main():
    """主测试函数"""
    
    print("🚀 开始完整的多模态任务输入测试")
    print("测试包括：文本、语音、图片（base64/bytes/PIL）三种输入方式")
    
    # 健康检查
    health_result = await test_service_health()
    
    if not health_result.get("doubao_status"):
        print("\n❌ 豆包服务连接异常，无法进行完整测试")
        return
    
    # 测试各种输入方式
    text_result = await test_text_input()
    voice_results = await test_voice_input()
    image_results = await test_image_input_multimodal()
    format_result = await test_format_conversion()
    
    # 输出测试摘要
    print("\n" + "=" * 80)
    print("🏆 测试摘要")
    print("=" * 80)
    
    print(f"📝 文本输入: {'✅ 成功' if text_result.get('success') else '❌ 失败'}")
    if text_result.get('success'):
        print(f"   解析任务数: {text_result.get('total_tasks', 0)}")
        print(f"   存储任务数: {text_result.get('stored_tasks', 0)}")
    
    print(f"\n🎤 语音输入: {len([r for r in voice_results if r.get('success')])} / {len(voice_results)} 成功")
    total_voice_tasks = sum(r.get('total_tasks', 0) for r in voice_results if r.get('success'))
    print(f"   总解析任务数: {total_voice_tasks}")
    
    print(f"\n🖼️ 图片输入: {len([r for r in image_results if r.get('success')])} / {len(image_results)} 成功")
    total_image_tasks = sum(r.get('total_tasks', 0) for r in image_results if r.get('success'))
    print(f"   总解析任务数: {total_image_tasks}")
    
    print(f"\n🔄 格式转换: {'✅ 成功' if format_result.get('success') else '❌ 失败'}")
    
    print(f"\n🏥 服务状态:")
    print(f"   豆包AI: {'✅ 正常' if health_result.get('doubao_status') else '❌ 异常'}")
    print(f"   PIL库: {'✅ 可用' if health_result.get('pil_available') else '❌ 不可用'}")
    print(f"   测试数据: {'✅ 完整' if health_result.get('test_files_ready') else '❌ 缺失'}")
    
    # 统计总数
    total_tasks = text_result.get('total_tasks', 0) + total_voice_tasks + total_image_tasks
    total_stored = text_result.get('stored_tasks', 0) + sum(r.get('stored_tasks', 0) for r in voice_results + image_results if r.get('success'))
    
    print(f"\n📊 总体统计:")
    print(f"   总解析任务数: {total_tasks}")
    print(f"   总存储任务数: {total_stored}")
    print(f"   存储成功率: {(total_stored/total_tasks*100):.1f}%" if total_tasks > 0 else "   存储成功率: 0%")
    
    if total_tasks > 0 and total_stored == total_tasks:
        print("\n🎉 所有测试完美通过！多模态任务输入系统运行正常！")
    elif total_tasks > 0:
        print("\n⚠️ 部分测试通过，系统基本可用，建议检查失败的部分")
    else:
        print("\n❌ 测试失败，请检查系统配置和依赖")


if __name__ == "__main__":
    asyncio.run(main())
