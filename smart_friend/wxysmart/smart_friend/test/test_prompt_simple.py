#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Prompt生成功能测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_import():
    """测试模块导入"""
    print("=" * 50)
    print("测试模块导入")
    print("=" * 50)
    
    try:
        from core.prompt_generation.schemas import (
            ChildProfileSummary, TodayHomeworkSummary, 
            RecentHomeworkCompletion, PromptGenerationRequest
        )
        print("✓ schemas模块导入成功")
        
        # 测试创建基本对象
        profile = ChildProfileSummary(
            id=1,
            name="测试小明",
            nickname="小明",
            age=8,
            academic_level="小学二年级",
            learning_style="视觉型学习者",
            good_at_subjects="数学",
            weak_at_subjects="语文"
        )
        print(f"✓ 创建儿童档案对象成功: {profile.name}")
        
        homework = TodayHomeworkSummary(
            subject="数学",
            task_name="加减法练习",
            time_slot="19:00-19:30",
            status="pending"
        )
        print(f"✓ 创建作业对象成功: {homework.subject} - {homework.task_name}")
        
        completion = RecentHomeworkCompletion(
            date="2024-01-15",
            subject="数学",
            completion_rate=95.0,
            accuracy_rate=88.0,
            concentration_level=4
        )
        print(f"✓ 创建完成记录对象成功: {completion.subject} - {completion.completion_rate}%")
        
        request = PromptGenerationRequest(
            child_id=1,
            days_back=7
        )
        print(f"✓ 创建请求对象成功: child_id={request.child_id}, days_back={request.days_back}")
        
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False
    
    return True


def test_prompt_template():
    """测试prompt模板功能"""
    print("\n" + "=" * 50)
    print("测试Prompt模板功能")
    print("=" * 50)
    
    try:
        from core.prompt_generation.schemas import ChildProfileSummary
        
        # 创建测试数据
        profile = ChildProfileSummary(
            id=1,
            name="张小明",
            nickname="小明",
            age=8,
            academic_level="小学二年级",
            school_name="实验小学",
            learning_style="视觉型学习者",
            attention_span_minutes=25,
            personality_traits="活泼好动，好奇心强",
            favorite_subjects="数学，科学",
            disliked_subjects="语文",
            good_at_subjects="数学，英语",
            weak_at_subjects="语文写作"
        )
        
        # 测试基本模板
        template = """
学生基本信息：
- 姓名：{child_name}（{child_nickname}）
- 年龄：{child_age}岁
- 学业等级：{academic_level}
- 学校：{school_name}
- 学习风格：{learning_style}
- 注意力持续时间：{attention_span}分钟
- 性格特点：{personality_traits}
- 喜欢的科目：{favorite_subjects}
- 不喜欢的科目：{disliked_subjects}
- 擅长的科目：{good_at_subjects}
- 薄弱的科目：{weak_at_subjects}
"""
        
        # 模拟模板变量替换
        template_vars = {
            'child_name': profile.name,
            'child_nickname': profile.nickname or '',
            'child_age': profile.age or '',
            'academic_level': profile.academic_level or '',
            'school_name': profile.school_name or '',
            'learning_style': profile.learning_style or '',
            'attention_span': profile.attention_span_minutes or '',
            'personality_traits': profile.personality_traits or '',
            'favorite_subjects': profile.favorite_subjects or '',
            'disliked_subjects': profile.disliked_subjects or '',
            'good_at_subjects': profile.good_at_subjects or '',
            'weak_at_subjects': profile.weak_at_subjects or ''
        }
        
        result = template
        for key, value in template_vars.items():
            result = result.replace(f"{{{key}}}", str(value))
        
        print("✓ 模板处理成功")
        print("\n生成的Prompt:")
        print("-" * 30)
        print(result)
        
    except Exception as e:
        print(f"✗ 模板测试失败: {e}")
        return False
    
    return True


def test_api_structure():
    """测试API结构"""
    print("\n" + "=" * 50)
    print("测试API结构")
    print("=" * 50)
    
    try:
        from core.prompt_generation.api import prompt_generation_router
        print("✓ API路由器导入成功")
        
        from core.prompt_generation.endpoints.prompt_api import router
        print("✓ API端点导入成功")
        
        # 检查路由器是否有路由
        if hasattr(prompt_generation_router, 'routes'):
            route_count = len(prompt_generation_router.routes)
            print(f"✓ 路由器包含 {route_count} 个路由")
        
    except Exception as e:
        print(f"✗ API结构测试失败: {e}")
        return False
    
    return True


def test_main_app():
    """测试主应用集成"""
    print("\n" + "=" * 50)
    print("测试主应用集成")
    print("=" * 50)
    
    try:
        from main import app
        print("✓ 主应用导入成功")
        
        # 检查路由是否包含prompt-generation
        routes = [route.path for route in app.routes if hasattr(route, 'path')]
        prompt_routes = [route for route in routes if 'prompt-generation' in route]
        
        if prompt_routes:
            print(f"✓ 找到 {len(prompt_routes)} 个prompt-generation相关路由")
            for route in prompt_routes:
                print(f"  - {route}")
        else:
            print("⚠ 未找到prompt-generation路由，可能需要检查路由配置")
        
    except Exception as e:
        print(f"✗ 主应用集成测试失败: {e}")
        return False
    
    return True


if __name__ == "__main__":
    print("开始Prompt生成功能简化测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    if test_import():
        success_count += 1
    
    if test_prompt_template():
        success_count += 1
    
    if test_api_structure():
        success_count += 1
    
    if test_main_app():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试完成: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！Prompt生成功能基础结构正常")
    else:
        print("⚠ 部分测试失败，请检查相关配置")
    
    print("=" * 60)
