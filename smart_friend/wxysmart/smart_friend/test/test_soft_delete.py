#!/usr/bin/env python3
# 软删除功能测试脚本
import sys
from pathlib import Path
from datetime import datetime
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent  # 向上一级到项目根目录
sys.path.insert(0, str(project_root))

from backend.services.child_service import ChildService
from schemas.child_schemas import (
    ParentCreate, ChildCreate, ChildParentRelationshipCreate,
    ChildAcademicRecordCreate,
    GenderEnum, RelationshipEnum, AcademicLevelEnum, 
    BehaviorLevelEnum
)


def test_soft_delete():
    """测试软删除功能"""
    print("=== 测试软删除功能 ===")
    
    service = ChildService()
    random_id = random.randint(10000, 99999)
    
    # 1. 创建测试数据
    print("\n1. 创建测试数据...")
    
    # 创建家长
    parent_data = ParentCreate(
        name="测试家长",
        gender=GenderEnum.MALE,
        age=35,
        phone=f"138{random_id % 100000000:08d}",
        email=f"test_parent_{random_id}@example.com",
        notes="软删除测试家长"
    )
    
    parent = service.create_parent(parent_data)
    print(f"✓ 创建家长: {parent.name} (ID: {parent.id})")
    
    # 创建小孩
    child_data = ChildCreate(
        name="测试小孩",
        gender=GenderEnum.FEMALE,
        age=8,
        academic_level=AcademicLevelEnum.PRIMARY_2,
        notes="软删除测试小孩"
    )
    
    child = service.create_child(child_data)
    print(f"✓ 创建小孩: {child.name} (ID: {child.id})")
    
    # 创建关系
    relationship_data = ChildParentRelationshipCreate(
        child_id=child.id,
        parent_id=parent.id,
        relationship_type=RelationshipEnum.FATHER,
        is_primary_contact=True,
        notes="软删除测试关系"
    )
    
    relationship = service.create_child_parent_relationship(relationship_data)
    print(f"✓ 创建关系: {relationship.relationship_type} (ID: {relationship.id})")
    
    # 创建学业记录
    academic_data = ChildAcademicRecordCreate(
        child_id=child.id,
        academic_year="2023-2024",
        semester="上学期",
        record_date=datetime.now(),
        record_type="期中考试",
        subject="数学",
        score=85.0,
        max_score=100.0,
        notes="软删除测试学业记录"
    )
    
    academic_record = service.create_academic_record(academic_data)
    print(f"✓ 创建学业记录: {academic_record.subject} (ID: {academic_record.id})")
    
    # 2. 验证数据存在
    print("\n2. 验证数据存在...")
    
    # 查询家长的小孩
    parent_children = service.get_parent_children(parent.id)
    print(f"✓ 家长的小孩数量: {len(parent_children)}")
    
    # 查询小孩的家长
    child_parents = service.get_child_parents(child.id)
    print(f"✓ 小孩的家长数量: {len(child_parents)}")
    
    # 查询学业记录
    academic_records = service.get_child_academic_records(child.id)
    print(f"✓ 小孩的学业记录数量: {len(academic_records)}")
    
    # 3. 测试软删除
    print("\n3. 测试软删除...")
    
    # 软删除学业记录
    delete_result = service.delete_academic_record(academic_record.id)
    print(f"✓ 软删除学业记录: {'成功' if delete_result else '失败'}")
    
    # 软删除关系
    delete_result = service.delete_child_parent_relationship(relationship.id)
    print(f"✓ 软删除关系: {'成功' if delete_result else '失败'}")
    
    # 软删除小孩
    delete_result = service.delete_child(child.id)
    print(f"✓ 软删除小孩: {'成功' if delete_result else '失败'}")
    
    # 软删除家长
    delete_result = service.delete_parent(parent.id)
    print(f"✓ 软删除家长: {'成功' if delete_result else '失败'}")
    
    # 4. 验证软删除效果
    print("\n4. 验证软删除效果...")
    
    # 查询家长的小孩（应该为空）
    parent_children_after = service.get_parent_children(parent.id)
    print(f"✓ 软删除后家长的小孩数量: {len(parent_children_after)}")
    
    # 查询小孩的家长（应该为空）
    child_parents_after = service.get_child_parents(child.id)
    print(f"✓ 软删除后小孩的家长数量: {len(child_parents_after)}")
    
    # 查询学业记录（应该为空）
    academic_records_after = service.get_child_academic_records(child.id)
    print(f"✓ 软删除后小孩的学业记录数量: {len(academic_records_after)}")
    
    # 查询激活的家长（应该不包含被软删除的）
    active_parents = service.get_parents(is_active=True)
    parent_id = parent.id  # 提前获取ID
    deleted_parent_in_active = any(p.id == parent_id for p in active_parents)
    print(f"✓ 被软删除的家长是否在激活列表中: {'是' if deleted_parent_in_active else '否'}")

    # 查询激活的小孩（应该不包含被软删除的）
    active_children = service.get_children(is_active=True)
    child_id = child.id  # 提前获取ID
    deleted_child_in_active = any(c.id == child_id for c in active_children)
    print(f"✓ 被软删除的小孩是否在激活列表中: {'是' if deleted_child_in_active else '否'}")
    
    # 5. 验证数据仍在数据库中
    print("\n5. 验证数据仍在数据库中...")

    # 直接查询被软删除的记录
    deleted_parent = service.get_parent(parent_id)
    deleted_child = service.get_child(child_id)
    
    print(f"✓ 被软删除的家长仍可查询: {'是' if deleted_parent else '否'}")
    print(f"✓ 被软删除的小孩仍可查询: {'是' if deleted_child else '否'}")
    
    if deleted_parent:
        print(f"  - 家长is_active状态: {deleted_parent.is_active}")
    if deleted_child:
        print(f"  - 小孩is_active状态: {deleted_child.is_active}")
    
    print("\n=== 软删除功能测试完成！ ===")
    
    # 验证结果
    success_conditions = [
        len(parent_children_after) == 0,  # 软删除后关系查询为空
        len(child_parents_after) == 0,    # 软删除后关系查询为空
        len(academic_records_after) == 0, # 软删除后学业记录查询为空
        not deleted_parent_in_active,     # 被软删除的家长不在激活列表中
        not deleted_child_in_active,      # 被软删除的小孩不在激活列表中
        deleted_parent is not None,       # 被软删除的家长仍可直接查询
        deleted_child is not None,        # 被软删除的小孩仍可直接查询
        deleted_parent and not deleted_parent.is_active,  # 家长is_active为False
        deleted_child and not deleted_child.is_active     # 小孩is_active为False
    ]
    
    if all(success_conditions):
        print("🎉 所有软删除功能测试通过！")
        return True
    else:
        print("❌ 部分软删除功能测试失败")
        return False


def main():
    """主函数"""
    try:
        success = test_soft_delete()
        
        if success:
            print("\n✅ 软删除功能完全正常！")
        else:
            print("\n❌ 软删除功能存在问题")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
