#!/usr/bin/env python3
"""
TTS Utils 测试脚本

测试新的模块化TTS工具的功能
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from utils.tts_utils import (
    TTSFactory, 
    create_tts_client, 
    quick_tts_play, 
    quick_tts_file,
    TextPreprocessor,
    AudioPlayerFactory
)

def test_text_preprocessor():
    """测试文本预处理器"""
    print("=== 测试文本预处理器 ===")
    
    test_texts = [
        "你好，这是一个**测试**文本。",
        "```python\nprint('hello')\n```",
        "[链接文本](http://example.com)",
        "数字测试：3.14，百分比：50%",
        "特殊字符：@用户 #标签 &符号"
    ]
    
    for text in test_texts:
        processed = TextPreprocessor.preprocess_text_for_tts(text)
        print(f"原文: {text}")
        print(f"处理后: {processed}")
        print()

def test_audio_player_factory():
    """测试音频播放器工厂"""
    print("=== 测试音频播放器工厂 ===")
    
    try:
        pygame_player = AudioPlayerFactory.create_player("pygame")
        print(f"Pygame播放器创建成功: {type(pygame_player).__name__}")
    except Exception as e:
        print(f"Pygame播放器创建失败: {e}")
    
    try:
        pydub_player = AudioPlayerFactory.create_player("pydub")
        print(f"Pydub播放器创建成功: {type(pydub_player).__name__}")
    except Exception as e:
        print(f"Pydub播放器创建失败: {e}")
    
    try:
        auto_player = AudioPlayerFactory.create_player("auto")
        print(f"自动选择播放器成功: {type(auto_player).__name__}")
    except Exception as e:
        print(f"自动选择播放器失败: {e}")

def test_tts_factory():
    """测试TTS工厂"""
    print("=== 测试TTS工厂 ===")
    
    # 使用默认凭据（如果可用）
    app_id = "5311525929"
    token = "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23"
    
    try:
        simple_client = TTSFactory.create_simple_client(app_id, token)
        print(f"简单TTS客户端创建成功: {type(simple_client).__name__}")
    except Exception as e:
        print(f"简单TTS客户端创建失败: {e}")
    
    try:
        advanced_manager = TTSFactory.create_advanced_manager(app_id, token)
        print(f"高级TTS管理器创建成功: {type(advanced_manager).__name__}")
    except Exception as e:
        print(f"高级TTS管理器创建失败: {e}")
    
    try:
        default_manager = TTSFactory.create_default_manager()
        print(f"默认TTS管理器创建成功: {type(default_manager).__name__}")
    except Exception as e:
        print(f"默认TTS管理器创建失败: {e}")

def test_convenience_functions():
    """测试便捷函数"""
    print("=== 测试便捷函数 ===")
    
    app_id = "5311525929"
    token = "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23"
    
    try:
        client = create_tts_client("simple", app_id, token)
        print(f"便捷函数创建客户端成功: {type(client).__name__}")
    except Exception as e:
        print(f"便捷函数创建客户端失败: {e}")
    
    # 注意：以下测试需要真实的TTS服务，在没有网络连接时会失败
    print("注意：以下测试需要网络连接和有效的TTS服务凭据")
    
    # 测试文件保存（不实际执行，避免网络请求）
    # success = quick_tts_file("测试文本", "test_output.mp3", app_id, token)
    # print(f"快速TTS文件保存: {'成功' if success else '失败'}")
    
    # 测试播放（不实际执行，避免网络请求）
    # success = quick_tts_play("测试文本", app_id, token)
    # print(f"快速TTS播放: {'成功' if success else '失败'}")

async def test_streaming_synthesis():
    """测试流式语音合成（演示用，不实际执行）"""
    print("=== 流式语音合成演示 ===")
    print("注意：此功能需要网络连接和有效的TTS服务凭据")
    
    # 演示代码（不实际执行）
    code_example = '''
    async def streaming_example():
        manager = TTSFactory.create_advanced_manager()
        await manager.start_streaming_synthesis()
        
        # 模拟逐字发送文本
        text = "这是一个流式语音合成的测试"
        for i in range(0, len(text), 2):
            chunk = text[i:i+2]
            await manager.add_streaming_text(chunk)
            await asyncio.sleep(0.2)
        
        await asyncio.sleep(2)
        await manager.stop_streaming_synthesis()
    '''
    
    print("流式合成示例代码:")
    print(code_example)

def main():
    """主测试函数"""
    print("开始测试TTS Utils模块...")
    print("=" * 50)
    
    # 测试各个组件
    test_text_preprocessor()
    test_audio_player_factory()
    test_tts_factory()
    test_convenience_functions()
    
    # 异步测试
    asyncio.run(test_streaming_synthesis())
    
    print("=" * 50)
    print("TTS Utils模块测试完成！")
    print("\n模块特性总结:")
    print("✓ 模块化架构设计")
    print("✓ 单一职责原则")
    print("✓ 大量使用类和继承")
    print("✓ 支持多种音频播放器")
    print("✓ 支持流式语音合成")
    print("✓ 提供便捷函数接口")
    print("✓ 完整的错误处理")
    print("✓ 可扩展的工厂模式")

if __name__ == "__main__":
    main()
