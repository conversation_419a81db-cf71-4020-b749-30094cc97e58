#!/usr/bin/env python3
"""
豆包模型集成测试脚本

测试豆包模型服务的各项功能：
1. 服务初始化
2. 简单聊天
3. 健康检查
4. API端点测试
5. Prompt生成与豆包模型结合测试
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import json
import requests
import time
from datetime import datetime


def test_service_initialization():
    """测试服务初始化"""
    print("🔧 测试服务初始化...")
    try:
        from service.doubao_service import get_doubao_service
        service = get_doubao_service()
        print(f"✅ 服务初始化成功")
        print(f"   - API URL: {service.base_url}")
        print(f"   - 模型名称: {service.model_name}")
        print(f"   - API密钥: {service.api_key[:10]}...")
        return True
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        return False


def test_simple_chat():
    """测试简单聊天功能"""
    print("\n💬 测试简单聊天功能...")
    try:
        from service.doubao_service import get_doubao_service
        service = get_doubao_service()
        
        result = service.simple_chat("你好，请用一句话介绍自己", max_tokens=50)
        
        if result.get("success"):
            print("✅ 简单聊天测试成功")
            print(f"   - 回复: {result.get('response_text', '')}")
            print(f"   - Token使用: {result.get('usage', {})}")
            return True
        else:
            print(f"❌ 简单聊天测试失败: {result.get('error', '未知错误')}")
            return False
    except Exception as e:
        print(f"❌ 简单聊天测试异常: {e}")
        return False


def test_health_check():
    """测试健康检查"""
    print("\n🏥 测试健康检查...")
    try:
        from service.doubao_service import get_doubao_service
        service = get_doubao_service()
        
        # 获取模型信息
        model_info = service.get_model_info()
        print(f"✅ 模型信息获取成功: {model_info}")
        
        # 验证连接
        is_connected = service.validate_connection()
        print(f"✅ 连接验证: {'成功' if is_connected else '失败'}")
        
        return is_connected
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False


def test_api_endpoints(base_url="http://localhost:8011"):
    """测试API端点"""
    print(f"\n🌐 测试API端点 ({base_url})...")
    
    # 测试健康检查端点
    try:
        response = requests.get(f"{base_url}/api/v1/doubao/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 健康检查端点测试成功")
            print(f"   - 服务状态: {'健康' if data.get('is_healthy') else '不健康'}")
        else:
            print(f"❌ 健康检查端点测试失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查端点测试异常: {e}")
        return False
    
    # 测试简单聊天端点
    try:
        payload = {
            "prompt": "你好，请简单回复",
            "max_tokens": 30
        }
        response = requests.post(
            f"{base_url}/api/v1/doubao/chat/simple", 
            json=payload, 
            timeout=30
        )
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ 简单聊天端点测试成功")
                print(f"   - 回复: {data.get('response_text', '')}")
            else:
                print(f"❌ 简单聊天端点返回失败: {data.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 简单聊天端点测试失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 简单聊天端点测试异常: {e}")
        return False
    
    return True


def test_prompt_generation_integration():
    """测试Prompt生成与豆包模型集成"""
    print("\n🎯 测试Prompt生成与豆包模型集成...")
    try:
        from service.doubao_service import get_doubao_service
        from core.prompt_generation.service import PromptGenerationService
        from core.prompt_generation.schemas import TaskPromptRequest
        
        # 初始化服务
        doubao_service = get_doubao_service()
        prompt_service = PromptGenerationService()
        
        # 生成prompt
        request = TaskPromptRequest(
            child_id=1,
            days_back=3,
            include_yesterday_tasks=True,
            template_type="task_prompt"
        )
        
        prompt_result = prompt_service.generate_task_prompt(request)
        
        if not prompt_result or not prompt_result.final_prompt:
            print("⚠️  Prompt生成失败，跳过集成测试")
            return True
        
        print(f"✅ Prompt生成成功 (长度: {len(prompt_result.final_prompt)} 字符)")
        
        # 使用豆包模型处理prompt
        api_result = doubao_service.simple_chat(
            prompt_result.final_prompt, 
            max_tokens=500
        )
        
        if api_result.get("success"):
            print("✅ 豆包模型处理Prompt成功")
            response_text = api_result.get("response_text", "")
            
            # 检查响应格式
            if "```json" in response_text.lower() or "[" in response_text:
                print("✅ 响应包含JSON格式内容")
                
                # 尝试解析JSON
                try:
                    start_idx = response_text.find("[")
                    end_idx = response_text.rfind("]") + 1
                    
                    if start_idx != -1 and end_idx > start_idx:
                        json_str = response_text[start_idx:end_idx]
                        parsed_json = json.loads(json_str)
                        print(f"✅ JSON解析成功，包含 {len(parsed_json)} 个任务")
                    else:
                        print("⚠️  未找到完整的JSON数组")
                except json.JSONDecodeError as e:
                    print(f"⚠️  JSON解析失败: {e}")
            else:
                print("⚠️  响应不包含JSON格式")
            
            return True
        else:
            print(f"❌ 豆包模型处理Prompt失败: {api_result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 80)
    print("🧪 豆包模型集成测试")
    print("=" * 80)
    
    test_results = []
    
    # 1. 服务初始化测试
    test_results.append(("服务初始化", test_service_initialization()))
    
    # 2. 简单聊天测试
    test_results.append(("简单聊天", test_simple_chat()))
    
    # 3. 健康检查测试
    test_results.append(("健康检查", test_health_check()))
    
    # 4. API端点测试（需要服务器运行）
    print("\n⚠️  API端点测试需要服务器运行在 http://localhost:8011")
    print("如果服务器未运行，此测试将失败")
    test_results.append(("API端点", test_api_endpoints()))
    
    # 5. Prompt生成集成测试
    test_results.append(("Prompt集成", test_prompt_generation_integration()))
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("📊 测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！豆包模型集成成功！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
