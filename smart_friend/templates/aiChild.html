<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Friend - AI学习助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        .header {
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.2em;
        }
        
        .status {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status.loading {
            background: #fff3cd;
            border-color: #ffc107;
        }
        
        .status.error {
            background: #f8d7da;
            border-color: #dc3545;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .feature h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #666;
            font-size: 0.9em;
        }
        
        .api-links {
            margin-top: 30px;
        }
        
        .api-links a {
            display: inline-block;
            background: #007bff;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            margin: 5px;
            transition: background 0.3s;
        }
        
        .api-links a:hover {
            background: #0056b3;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.9em;
        }

        .chat-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #e9ecef;
        }

        .chat-messages {
            min-height: 200px;
            margin-bottom: 15px;
        }

        .message {
            margin: 10px 0;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.ai {
            background: #e9ecef;
            color: #333;
            margin-right: auto;
        }

        .message.system {
            background: #fff3cd;
            color: #856404;
            text-align: center;
            font-style: italic;
            margin: 5px auto;
            max-width: 90%;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #007bff;
        }

        .send-button {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-button:hover {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: #e9ecef;
            border-radius: 18px;
            margin: 10px 0;
            max-width: 80%;
            color: #666;
            font-style: italic;
        }

        .typing-indicator.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Smart Friend</h1>
            <p>AI智能学习助手 - 让学习更有趣！</p>
        </div>
        
        <div id="status" class="status loading">
            <h3>🔄 正在初始化系统...</h3>
            <p>请稍等，我们正在启动AI学习助手</p>
        </div>
        
        <div class="chat-container">
            <h3>💬 与AI助手对话</h3>
            <div class="chat-messages" id="chatMessages">
                <div class="message system">
                    👋 你好！我是你的AI学习助手。我可以帮你制定学习计划、回答问题、提供学习建议。试着问我一些问题吧！
                </div>
            </div>
            <div class="typing-indicator" id="typingIndicator">
                🤖 AI助手正在思考...
            </div>
            <div class="chat-input-container">
                <input type="text" class="chat-input" id="chatInput" placeholder="输入你的问题或学习需求..." maxlength="500">
                <button class="send-button" id="sendButton" onclick="sendMessage()">
                    📤
                </button>
            </div>
        </div>

        <div class="features">
            <div class="feature">
                <h3>🧠 智能对话</h3>
                <p>与AI助手进行自然对话，获得个性化学习建议</p>
            </div>
            <div class="feature">
                <h3>📚 学习规划</h3>
                <p>AI自动生成个性化学习计划，提高学习效率</p>
            </div>
            <div class="feature">
                <h3>🎯 意图识别</h3>
                <p>智能理解学习需求，提供精准的学习支持</p>
            </div>
            <div class="feature">
                <h3>📊 进度跟踪</h3>
                <p>实时监控学习进度，生成详细学习报告</p>
            </div>
            <div class="feature">
                <h3>🎤 语音交互</h3>
                <p>支持语音输入和输出，让学习更加便捷</p>
            </div>
            <div class="feature">
                <h3>🔧 OpenManus增强</h3>
                <p>集成先进AI规划系统，提供更智能的学习体验</p>
            </div>
        </div>
        
        <div class="api-links">
            <a href="/docs" target="_blank">📖 API文档</a>
            <a href="/api/v1/openmanus-direct/status" target="_blank">🔍 系统状态</a>
            <a href="/health" target="_blank">💚 健康检查</a>
        </div>
        
        <div class="footer">
            <p>Smart Friend v2.0.0 - 基于OpenManus AI技术</p>
            <p>© 2024 Smart Friend Team. 让每个孩子都能享受智能学习的乐趣！</p>
        </div>
    </div>

    <script>
        let isSystemReady = false;

        // Check system status
        async function checkStatus() {
            try {
                const response = await fetch('/api/v1/openmanus-direct/status');
                const data = await response.json();

                const statusDiv = document.getElementById('status');

                if (data.ready) {
                    isSystemReady = true;
                    statusDiv.className = 'status';
                    statusDiv.innerHTML = `
                        <h3>✅ 系统就绪！</h3>
                        <p>OpenManus AI学习助手已启动，准备为您提供智能学习服务</p>
                        <p><strong>集成类型:</strong> ${data.integration_type || 'direct_main_py'}</p>
                    `;

                    // Enable chat input
                    document.getElementById('chatInput').disabled = false;
                    document.getElementById('sendButton').disabled = false;
                } else {
                    isSystemReady = false;
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = `
                        <h3>❌ 系统未就绪</h3>
                        <p>${data.message || '系统初始化失败'}</p>
                    `;

                    // Disable chat input
                    document.getElementById('chatInput').disabled = true;
                    document.getElementById('sendButton').disabled = true;
                }
            } catch (error) {
                isSystemReady = false;
                const statusDiv = document.getElementById('status');
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `
                    <h3>❌ 连接失败</h3>
                    <p>无法连接到服务器，请检查网络连接</p>
                `;

                // Disable chat input
                document.getElementById('chatInput').disabled = true;
                document.getElementById('sendButton').disabled = true;
            }
        }

        // Add message to chat
        function addMessage(content, type = 'ai', isHTML = false) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            if (isHTML) {
                messageDiv.innerHTML = content;
            } else {
                messageDiv.textContent = content;
            }

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Show/hide typing indicator
        function showTyping(show = true) {
            const typingIndicator = document.getElementById('typingIndicator');
            if (show) {
                typingIndicator.classList.add('show');
            } else {
                typingIndicator.classList.remove('show');
            }
        }

        // Send message to AI
        async function sendMessage() {
            if (!isSystemReady) {
                addMessage('系统未就绪，请等待系统初始化完成', 'system');
                return;
            }

            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();

            if (!message) {
                return;
            }

            // Add user message
            addMessage(message, 'user');
            chatInput.value = '';

            // Disable input while processing
            chatInput.disabled = true;
            document.getElementById('sendButton').disabled = true;
            showTyping(true);

            try {
                const response = await fetch('/api/v1/openmanus-direct/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        child_id: 1
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Add AI response
                    addMessage(data.message, 'ai');

                    // Show intent information if available
                    if (data.intent_info && data.intent_info.predicted_intention) {
                        const intentText = `🎯 识别意图: ${data.intent_info.predicted_intention} (置信度: ${(data.intent_info.confidence * 100).toFixed(1)}%)`;
                        addMessage(intentText, 'system');
                    }

                    // Show summary if available
                    if (data.summary) {
                        addMessage(`📝 学习总结: ${data.summary}`, 'system');
                    }
                } else {
                    addMessage(`抱歉，处理您的请求时出现错误: ${data.error || '未知错误'}`, 'ai');
                }

            } catch (error) {
                addMessage('抱歉，网络连接出现问题，请稍后再试', 'ai');
                console.error('Chat error:', error);
            } finally {
                // Re-enable input
                showTyping(false);
                chatInput.disabled = false;
                document.getElementById('sendButton').disabled = false;
                chatInput.focus();
            }
        }

        // Handle Enter key in chat input
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chatInput');
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        });

        // Check status on page load
        window.addEventListener('load', checkStatus);

        // Refresh status every 30 seconds
        setInterval(checkStatus, 30000);
    </script>
</body>
</html>
