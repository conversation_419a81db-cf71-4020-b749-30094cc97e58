<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Child - 语音识别界面</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

    <style>
        /* 新增浮窗消息样式 */
        /* 新增浮窗消息样式 */
        .floating-notification {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: #fff;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transform: translateY(-100%);
            transition: transform 0.3s ease-out;
        }

        .floating-notification.show {
            transform: translateY(0);
        }

        .floating-error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .floating-success {
            background-color: #d4edda;
            color: #155724;
        }





        /* 新增进度条样式 */
        .progress-container {
            width: 100%;
            background-color: #f3f3f3;
            border-radius: 5px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-bar {
            width: 0%;
            height: 30px;
            background-color: #007bff;
            text-align: center;
            line-height: 30px;
            color: white;
            border-radius: 5px;
            transition: width 0.3s ease;
        }

        /* 新增步骤指示样式 */
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .step {
            flex: 1;
            text-align: center;
        }

        .step.active {
            font-weight: bold;
            color: #007bff;
        }

        .current-step-text {
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
        }

        /* 新增图表容器样式 */
        #taskChart {
            width: 100%;
            height: 600px;
            margin-top: 20px;
        }

        .input-section textarea {
            width: 40%;
            height: 200px;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            margin: 10px;
        }

        .input-section textarea:focus {
            border-color: #007bff;
            outline: none;
        }

        .input-section #submitData {
            margin: 20px auto;
            display: block;
        }

        /* 新增任务计划表格样式 */
        .task-plan-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
        }

        .task-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .task-table th,
        .task-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }

        .task-table th {
            background-color: #007bff;
            color: white;
        }

        .task-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        .task-table ul {
            list-style-type: none;
            padding-left: 0;
        }

        .task-table li {
            margin-bottom: 8px;
        }

        /* .task-plan-section {
            margin-top: 20px;
        } */

        #taskPlanTable {
            width: 100%;
            border-collapse: collapse;
        }

        #taskPlanTable th,
        #taskPlanTable td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .tag {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 8px;
            border-radius: 16px;
            margin: 2px;
            font-size: 0.9em;
            margin-bottom: 5px;
            position: relative;
        }

        .sub-task-content {
            margin-bottom: 5px;
        }

        .sub-task-time input {
            width: auto;
            display: inline-block;
        }

        .tag-delete {
            position: absolute;
            top: 2px;
            right: 5px;
            cursor: pointer;
            color: red;
        }


        .tag-delete:hover {
            color: #cc0000;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 1200px;
            width: 120%;
            margin-left: -20%;
            /* 负值会让容器向左移动，可根据需求调整数值 */
            /* 确保没有设置 position: relative 等影响定位的属性 */
            position: static;
            z-index: auto;
            /* 确保 z-index 不会覆盖浮窗 */
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .status-card {
            position: fixed;
            top: 20%;
            right: 2%;
            transform: translateY(-50%);
            max-width: 300px;
            /* 可根据需要调整宽度 */
            z-index: 1000;
            /* 确保在其他元素之上 */
            border-radius: 15px;
            /* 设置圆角 */
            padding: 30px;
            text-align: center;
            border: 2px solid rgba(255, 255, 255, 0.3);
            /* 增加边框 */
            /* 使用渐变背景，和浅紫色搭配 */
            background: linear-gradient(135deg, #a599e9 0%, #73a6ff 100%);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            /* 添加阴影 */
        }



        .microphone-indicator {
            font-size: 4em;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .microphone-indicator.recording {
            animation: pulse 1.5s infinite;
            color: #dc3545;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }

            100% {
                transform: scale(1);
            }
        }

        .status {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .status.connected {
            color: #28a745;
        }

        .status.recording {
            color: #dc3545;
        }

        .status.inactive {
            color: #6c757d;
        }

        .controls {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 180px;
            justify-content: center;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-start {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }

        .btn-stop {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }

        .btn-connect {
            background: linear-gradient(45deg, #007bff, #6610f2);
            color: white;
        }

        .btn-submit {
            background: linear-gradient(45deg, #4685b9, #6ec79a);
            color: white;
        }

        .btn-checkTask {
            background: linear-gradient(45deg, #3fc43f, #6ec79a);
            color: white;
        }

        .results-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
        }

        .voice-results-section {
            position: fixed;
            top: 60%;
            bottom: -15%;
            /* 增加底部距离，确保有空间扩展 */
            right: 1%;
            transform: translateY(-60%);
            max-width: 350px;
            /* 适当增加宽度 */
            width: 50%;
            /* 保证在小屏幕也有合适的宽度 */
            z-index: 1000;
            border-radius: 20px;
            /* 增加圆角 */
            padding: 25px;
            /* 增加内边距 */
            background: linear-gradient(135deg, #7ca6ee 0%, #5c8ee6 100%);
            /* 使用渐变背景 */
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            /* 增加阴影 */
            transition: all 0.3s ease;
            /* 添加过渡效果 */
            color: white;
            /* 设置文本颜色 */
            display: flex;
            /* 使用 flex 布局 */
            flex-direction: column;
            /* 垂直排列子元素 */
        }

        .message-section {
            position: fixed;
            top: 12%;
            bottom: 76%;
            right: 47%;
            transform: translateY(-60%);
            max-width: 350px;
            width: 33%;
            z-index: 1000;
            border-radius: 20px;
            padding: 25px;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            /* 新增样式 */
            text-align: center; /* 文本居中 */
            justify-content: center; /* 垂直居中 */
            align-items: center; /* 水平居中 */
            line-height: 1.5; /* 合适的行高 */
        }

        .message-section.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message-section.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .results-header {
            flex-shrink: 0;
            /* 头部不收缩 */
            margin-bottom: 20px;
            /* 与列表之间的间距 */
        }

        .results-header h3 {
            color: #333;
            margin: 0;
        }

        .clear-btn {
            position: fixed;
            right: 10%;
            top: 3%;
            background: linear-gradient(45deg, #ff4757, #ff6b81);
            /* 好看的渐变背景色 */
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            /* 添加过渡效果 */
        }

        .clear-btn:hover {
            background: linear-gradient(45deg, #ff6b81, #ff4757);
            /* 鼠标悬停时的渐变背景色 */
            transform: translateY(-2px);
            /* 鼠标悬停时向上移动 2px */
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            /* 鼠标悬停时添加阴影 */
        }

        .results-list {
            flex-grow: 1;
            /* 列表自动填充剩余空间 */
            overflow-y: auto;
            /* 内容超出时显示垂直滚动条 */
            border: 1px solid #dee2e6;
            border-radius: 10px;
            background: white;
            padding: 20px;
            color: #333;
        }

        .result-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
            animation: slideIn 0.3s ease;
        }

        .result-item.final {
            border-left-color: #28a745;
            background: #d4edda;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .result-text {
            font-size: 1.1em;
            color: #333;
            margin-bottom: 5px;
        }

        .result-meta {
            font-size: 0.9em;
            color: #666;
            display: flex;
            justify-content: space-between;
        }

        .empty-state {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 40px;
        }





        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
        }

        .connection-status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .connection-status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* 新增摄像头相关样式 */
        #video,
        #canvas {
            max-width: 100%;
            margin: 20px 0;
        }

        #camera-controls {
            margin: 20px 0;
        }

        #notification {
            position: fixed;
            top: 20px;
            left: 0%;
            transform: translateX(-50%);
            background-color: #4CAF50;
            color: white;
            padding: 15px;
            display: none;
        }

        .image-container {
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .container {
                background: rgb(168, 75, 75);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                padding: 40px;
                max-width: 1200px;
                /* 增加最大宽度 */
                width: 70%;
                /* 增加宽度比例 */
                margin-left: 5%;
                /* 让主体稍微偏左 */
                margin-right: auto;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }

        .input-section {
            text-align: center;
        }

        .subject-detection-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;

        }

        .subject-detection-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .subject-detection-header h3 {
            color: #333;
            margin: 0;
        }

        .subject-status-indicator {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeeba;
            color: #856404;
        }

        .subject-webcam-container {
            text-align: center;
            margin-bottom: 20px;
        }

        .task-webcam-container {
            text-align: center;
            margin-bottom: 20px;
        }

        #subject-webcam {
            max-width: 100%;
            height: 200px;
            border: 2px solid #ddd;
            border-radius: 10px;
        }

        #stask-webcam {
            max-width: 100%;
            height: 200px;
            border: 2px solid #ddd;
            border-radius: 10px;
        }

        .subject-control-buttons {
            display: flex;
            /* 使用 Flexbox 布局 */
            text-align: center;
            margin-bottom: 20px;
        }


        .subject-result-container {
            min-height: 150px;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            text-align: center;
        }

        .subject-stats-container {
            margin-top: 20px;
        }

        .input-section {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            /* 文本框之间的间距 */
            margin: 20px 0;
        }

        /* 优化文本框样式 */
        .input-section textarea {
            width: 40%;
            height: 200px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 16px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .input-section textarea:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
            outline: none;
        }


        .input-section label {
            margin-bottom: 10px;
            /* 为 label 和 textarea 之间添加间距 */
            font-weight: bold;
            /* 让 label 文字加粗 */
        }

        .finish-section {
            position: fixed;
            top: 90%;
            left: 90%;
            transform: translate(-50%, -50%);
            background: linear-gradient(45deg, #7ca6ee, #5c8ee6);
            /* 鼠标悬停时的渐变背景色 */
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 20px;
            z-index: 1000;
            /* 确保显示在其他元素之上 */
        }

        /* 可以添加遮罩层，让背景变暗 */
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            /* 遮罩层在 finish-section 之下 */
            display: none;
            /* 默认隐藏 */
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        .loading-circle {
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-top: 3px solid #007bff;
            border-radius: 50%;
            width: 12px;
            height: 12px;
            animation: spin 1s linear infinite;
            display: none;
            margin-left: 5px;
        }

        /* 新增或修改消息框样式 */
        #envCheckingModal {
            display: none;
            position: fixed;
            top: 10%;
            left: 40%;
            width: 20%;
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #ffeeba;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 1001;
            font-weight: bold;
            font-size: 1.1em;
        }

        /* 科目检测弹窗样式 */
        #subjectCheckModal {
            display: none;
            position: fixed;
            top: 85%;
            left: 88.5%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #a599e9 0%, #73a6ff 100%);
            /* 紫色渐变背景 */
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            z-index: 1001;
            width: 350px;
            /* 设置固定宽度 */
            max-width: 90%;
            /* 最大宽度，防止在小屏幕下过大 */
            color: white;
            /* 文字颜色 */
        }

        #subjectCheckModal h3 {
            margin-top: 0;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.8em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            /* 文字阴影 */
        }

        #subjectCheckModal p {
            font-size: 1.2em;
            margin-bottom: 15px;
        }

        #subjectCheckModal button {
            display: block;
            margin: 20px auto 0;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            /* 按钮渐变背景 */
            color: white;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #subjectCheckModal button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .deleted-subtasks-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
        }

        #deletedSubtasksTable {
            width: 100%;
            border-collapse: collapse;
        }

        #deletedSubtasksTable th,
        #deletedSubtasksTable td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        #deletedSubtasksTable th {
            background-color: #007bff;
            color: white;
        }

        #deletedSubtasksTable tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        /* 定制撤销按钮样式 */
        #deletedSubtasksTable td button {
            padding: 8px 16px;
            /* 增大内边距 */
            border: none;
            border-radius: 5px;
            /* 圆角边框 */
            background: linear-gradient(45deg, #28a745, #20c997);
            /* 绿色渐变背景 */
            color: white;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #deletedSubtasksTable td button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

    .tag {
        display: inline-block;
        background-color: #007bff;
        color: white;
        padding: 4px 8px;
        border-radius: 16px;
        margin: 2px;
        font-size: 0.9em;
    }

        .voice-input-section {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
        }

        .task-select {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            flex: 1;
        }

        .subtask-input {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            flex: 2;
        }

        /* 时间输入框样式 */
        input[type="time"] {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: border-color 0.3s ease;
            width: 120px;
            /* 固定宽度 */
            margin: 0 5px;
            /* 添加左右边距 */
        }

        input[type="time"]:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
        }

        /* 时间输入框中间的分隔符样式 */
        .task-plan-section td>span {
            margin: 0 5px;
            font-weight: bold;
            color: #333;
        }

        /* 美化检测结果界面样式 */
        .detection-results-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 20px;
            padding: 30px;
            margin-top: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .detection-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-left: 5px solid transparent;
        }

        .detection-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .detection-card.desktop-card {
            border-left-color: #4CAF50;
        }

        .detection-card.posture-card {
            border-left-color: #2196F3;
        }

        .detection-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .detection-icon {
            font-size: 2.5em;
            margin-right: 15px;
            padding: 15px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .detection-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .score-display {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 12px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .score-number {
            font-size: 3em;
            font-weight: bold;
            color: #333;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .score-label {
            font-size: 1.2em;
            color: #666;
            margin-left: 10px;
        }

        .progress-ring {
            width: 120px;
            height: 120px;
            position: relative;
        }

        .progress-ring svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            fill: none;
            stroke-width: 8;
            stroke-linecap: round;
        }

        .progress-ring-bg {
            stroke: #e6e6e6;
        }

        .progress-ring-progress {
            stroke: #4CAF50;
            stroke-dasharray: 283;
            stroke-dashoffset: 283;
            transition: stroke-dashoffset 1s ease-in-out;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            background: rgba(255, 255, 255, 0.7);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateX(5px);
        }

        .info-label {
            font-weight: bold;
            color: #555;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 1.1em;
            color: #333;
        }

        .suggestions-container {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 12px;
            padding: 20px;
            margin-top: 15px;
        }

        .suggestions-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .suggestions-title::before {
            content: "💡";
            font-size: 1.5em;
            margin-right: 10px;
        }

        .suggestion-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 12px 15px;
            margin-bottom: 8px;
            border-radius: 8px;
            border-left: 3px solid #28a745;
            transition: all 0.3s ease;
            animation: slideInLeft 0.5s ease-out;
        }

        .suggestion-item:hover {
            background: rgba(255, 255, 255, 0.95);
            transform: translateX(5px);
        }

        .alerts-container {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 12px;
            padding: 20px;
            margin-top: 15px;
        }

        .alerts-title {
            font-weight: bold;
            color: #d63031;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .alerts-title::before {
            content: "⚠️";
            font-size: 1.5em;
            margin-right: 10px;
        }

        .alert-item {
            background: rgba(255, 255, 255, 0.9);
            padding: 12px 15px;
            margin-bottom: 8px;
            border-radius: 8px;
            border-left: 3px solid #e74c3c;
            color: #d63031;
            font-weight: 500;
            animation: pulse 2s infinite;
        }

        .loading-detection {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
            text-align: center;
        }

        .loading-spinner-large {
            width: 60px;
            height: 60px;
            border: 6px solid #f3f3f3;
            border-top: 6px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .loading-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }

        .loading-subtext {
            font-size: 0.9em;
            color: #999;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
            }
        }

        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-excellent {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .status-good {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
        }

        .status-fair {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
        }

        .status-poor {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        /* 检测按钮悬停效果 */
        #desktop-detect-btn:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50) !important;
            transform: translateY(-3px) !important;
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4) !important;
        }

        #posture-detect-btn:hover {
            background: linear-gradient(45deg, #1976D2, #2196F3) !important;
            transform: translateY(-3px) !important;
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4) !important;
        }

        /* 结果卡片进入动画 */
        .detection-card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 进度环动画 */
        .progress-ring-progress {
            animation: progressRing 2s ease-in-out;
        }

        @keyframes progressRing {
            from {
                stroke-dashoffset: 283;
            }
        }

        /* 信息项悬停效果 */
        .info-item:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* 加载动画优化 */
        .loading-spinner-large {
            animation: spin 1.5s linear infinite, pulse 2s ease-in-out infinite alternate;
        }

        /* 坐姿检测专用样式 */
        .posture-detection-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }

        .posture-result-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .posture-info-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .posture-info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .posture-info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(102, 126, 234, 0.2);
            background: rgba(255, 255, 255, 1);
        }

        .posture-info-label {
            font-weight: 600;
            color: #4a5568;
            font-size: 0.9em;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
        }

        .posture-info-label::before {
            content: '📊';
            margin-right: 8px;
            font-size: 1.1em;
        }

        .posture-info-value {
            font-size: 1.2em;
            color: #2d3748;
            font-weight: 500;
            line-height: 1.4;
            word-break: break-word;
        }

        .posture-info-value.boolean-true {
            color: #38a169;
            font-weight: 600;
        }

        .posture-info-value.boolean-false {
            color: #e53e3e;
            font-weight: 600;
        }

        .posture-info-value.number {
            color: #3182ce;
            font-family: 'Courier New', monospace;
        }

        .posture-section-title {
            font-size: 1.3em;
            font-weight: 700;
            color: white;
            margin-bottom: 15px;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .posture-section-title::before {
            content: '🧘';
            margin-right: 10px;
            font-size: 1.2em;
        }

        .posture-stats-summary {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .posture-stats-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            color: white;
        }

        .posture-stats-row:last-child {
            margin-bottom: 0;
        }

        .posture-stats-label {
            font-weight: 500;
            opacity: 0.9;
        }

        .posture-stats-value {
            font-weight: 600;
            font-size: 1.1em;
        }

        .posture-visualization {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }

        .posture-visualization h6 {
            color: #4a5568;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .posture-visualization img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .posture-visualization img:hover {
            transform: scale(1.02);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .posture-result-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .posture-info-card {
                padding: 15px;
            }

            .posture-section-title {
                font-size: 1.1em;
            }
        }

        @keyframes pulse {
            from {
                transform: scale(1);
            }
            to {
                transform: scale(1.05);
            }
        }

        /* 状态徽章动画 */
        .status-badge {
            animation: bounceIn 0.6s ease-out;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* 摄像头预览容器样式 */
        .camera-preview-container {
            background: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .camera-controls {
            margin-top: 15px;
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .camera-controls .btn {
            font-size: 1em;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .camera-controls .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .camera-controls .btn:not(:disabled):hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* 摄像头视频样式 */
        #desktop-webcam, #posture-webcam {
            background: #f0f0f0;
            display: block;
            margin: 0 auto;
        }

        #desktop-webcam:not([src]) {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
        }

        #posture-webcam:not([src]) {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }

        /* 检测功能区域间距 */
        .detection-results-container + .detection-results-container {
            margin-top: 30px;
        }

        /* 精美的坐姿检测结果样式 */
        .posture-result-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .posture-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            color: white;
        }

        .posture-icon {
            font-size: 3em;
            margin-right: 15px;
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 50%;
            backdrop-filter: blur(10px);
        }

        .posture-title {
            font-size: 1.8em;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .posture-cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .posture-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-left: 5px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .posture-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .posture-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 1);
        }

        .status-card {
            border-left-color: #4CAF50;
        }

        .message-card {
            border-left-color: #2196F3;
        }

        .posture-status-card.good-posture {
            border-left-color: #4CAF50;
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(255, 255, 255, 0.95));
        }

        .posture-status-card.bad-posture {
            border-left-color: #f44336;
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(255, 255, 255, 0.95));
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        .card-icon {
            font-size: 1.5em;
            margin-right: 10px;
            padding: 8px;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .card-header h5 {
            margin: 0;
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
        }

        .card-content {
            text-align: center;
            padding: 10px 0;
        }

        .status-text, .message-text {
            font-size: 1.1em;
            color: #333;
            font-weight: 500;
            line-height: 1.4;
        }

        .posture-status-icon {
            font-size: 3em;
            margin-bottom: 10px;
            display: block;
        }

        .posture-status-text {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
        }

        .good-posture .posture-status-text {
            color: #4CAF50;
        }

        .bad-posture .posture-status-text {
            color: #f44336;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .posture-cards-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .posture-card {
                padding: 20px;
            }

            .posture-title {
                font-size: 1.5em;
            }

            .posture-icon {
                font-size: 2.5em;
                padding: 12px;
            }
        }
    </style>
</head>

<body>
    <!-- <div id="taskChart"></div> -->
    <div class="connection-status" id="connectionStatus">连接中...</div>
    <div id="envCheckingModal"
        style="display: none; transform: translate(-50%, -50%); background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.3); z-index: 1001;">
        桌面环境检测中，请稍候...
    </div>
    <div id="subjectCheckModal"
        style=" display:none; background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.3); z-index: 1001;">


        <!-- <button onclick="closeSubjectCheckModal()">关闭</button> -->
    </div>

    <!-- 正在分析中弹窗 -->
    <div id="analyzingModal"
        style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: rgba(0, 0, 0, 0.7); color: white; padding: 20px; border-radius: 10px; z-index: 1002;">
        正在分析中，请稍候...
    </div>

    <!-- 分析结果弹窗 -->
    <div id="analysisResultModal"
        style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.3); z-index: 1002; max-height: 80vh; overflow-y: auto;">
        <h3>分析结果</h3>
        <p id="resultContent" style="display: none"></p>
        <div id="markdown-container"></div>
        <button class="clear-btn" onclick="closeAnalysisResultModal()">关闭</button>
    </div>


    <div class="container">
        <div class="header">
            <h1>🎤 AI Child 语音识别</h1>
            <p>基于豆包(火山引擎)API的实时语音转文字服务</p>
        </div>

        <div class="status-card" style="display: none">
            <div class="microphone-indicator" id="micIndicator">🎤</div>
            <div id="status" class="status inactive">等待连接...</div>
            <div id="statusMessage" style="margin-top: 10px; color: #666;">
                请点击"连接服务"按钮启动自动语音识别
            </div>
            <div id="silenceStatus" style="margin-top: 10px; color: #ff6b35; font-weight: bold; display: none;">
                静音检测: <span id="silenceCountdown">2.0</span>秒后自动停止
            </div>
        </div>

        <div class="controls">
            <button id="connectBtn" class="btn btn-connect" onclick="connectService()">
                <span>🔗</span>
                连接服务
            </button>
            <button id="startBtn" class="btn btn-start" onclick="startRecognition()" disabled style="display: none;">
                <span>🎙️</span>
                开始语音识别
            </button>
            <button id="stopBtn" class="btn btn-stop" onclick="stopRecognition()" disabled style="display: none;">
                <span>⏹️</span>
                停止语音识别
            </button>
            <div id="autoModeIndicator" class="btn"
                style="background: linear-gradient(45deg, #28a745, #20c997); color: white; cursor: default;">
                <span>🔄</span>
                自动语音识别模式
            </div>
            <button id="voiceInteractBtn" class="btn btn-submit" onclick="startVoiceInteraction()"
                style="display: none;">
                <span>🎤</span>
                语音对话
            </button>
        </div>
        <!-- <div class="input-section">
            <textarea id="teacherDailyTask" 
            placeholder="输入教师每日任务" 
            style="width: 40%; height: 200px;"></textarea>
            <textarea id="yesterdayFeedback" placeholder="输入昨日任务反馈" style="width: 40%; height: 200px;"></textarea>
    
        </div>
        <div class="input-section">
            <textarea id="studentPortait" 
            placeholder="学生肖像" 
            style="width: 40%; height: 200px;"></textarea>
            <textarea id="learnRecord" placeholder="学生学习记录" style="width: 40%; height: 200px;"></textarea>
    
        </div> -->

        <!-- 小孩ID输入区域 -->
        <div class="input-section">
            <label for="childIdInput">小孩ID:</label>
            <input type="number" id="childIdInput" placeholder="请输入小孩ID" value="4"
                style="width: 200px; padding: 10px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; margin: 10px;">
        </div>

        <!-- 文本任务输入区域 -->
        <div class="task-input-section"
            style="background: #f8f9fa; border-radius: 15px; padding: 30px; margin-top: 20px;">
            <h3>📝 文本任务输入</h3>
            <div class="input-section">
                <label for="dailyTaskText">当日任务:</label>
                <textarea id="dailyTaskText" placeholder="请输入当日任务内容"
                    style="width: 80%; height: 150px; padding: 15px; border: 2px solid #e0e0e0; border-radius: 12px; font-size: 16px; margin: 10px;"></textarea>
            </div>
            <div class="input-section">
                <button id="submitTextTask" class="btn btn-submit">提交文本任务</button>
            </div>
        </div>

        <!-- 语音任务输入区域 -->
        <div class="voice-task-input-section"
            style="background: #ffffff; border-radius: 15px; padding: 30px; margin-top: 20px;">
            <h3>🎤 语音任务输入</h3>
            <div class="input-section">
                <button id="startVoiceTaskInput" class="btn btn-start">
                    <span>🎙️</span>
                    开始语音输入任务
                </button>
                <button id="stopVoiceTaskInput" class="btn btn-stop" disabled>
                    <span>⏹️</span>
                    停止语音输入
                </button>
            </div>
            <div class="input-section">
                <textarea id="voiceTaskResult" placeholder="语音识别结果将显示在这里"
                    style="width: 80%; height: 100px; padding: 15px; border: 2px solid #e0e0e0; border-radius: 12px; font-size: 16px; margin: 10px; background-color: #f9f9f9;"></textarea>
            </div>
            <div class="input-section"
                style="margin: 10px 0; padding: 10px; border: 1px dashed #ccc; border-radius: 5px; background-color: #f0f8ff;">
                <label for="manualVoiceText" style="display: block; margin-bottom: 5px; font-weight: bold;">🖊️
                    手动输入语音文本（用于测试）:</label>
                <input type="text" id="manualVoiceText" placeholder="例如：今天数学作业完成练习册第30到35页"
                    style="width: 70%; margin: 5px; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                <button id="useManualVoiceText" class="btn btn-start"
                    style="background-color: #28a745; margin-left: 10px;">使用此文本</button>
            </div>
            <div class="input-section">
                <button id="submitVoiceTask" class="btn btn-submit" disabled>提交语音任务</button>
            </div>
        </div>

        <!-- 图像任务输入区域 -->
        <div class="image-task-input-section"
            style="background: #fff8f0; border-radius: 15px; padding: 30px; margin-top: 20px;">
            <h3>📷 图像任务输入</h3>
            <div class="input-section">
                <button id="startImageCapture" class="btn btn-connect">启动摄像头</button>
                <button id="captureImageTask" class="btn btn-start" disabled>拍照捕获任务</button>
            </div>
            <div class="input-section">
                <video id="imageTaskVideo" autoplay playsinline
                    style="max-width: 400px; height: 300px; border: 2px solid #ddd; border-radius: 10px; margin: 10px;"></video>
                <canvas id="imageTaskCanvas" style="display: none;"></canvas>
            </div>
            <div class="input-section">
                <img id="capturedTaskImage"
                    style="display: none; max-width: 400px; height: 300px; border: 2px solid #ddd; border-radius: 10px; margin: 10px;">
            </div>
            <div class="input-section">
                <button id="submitImageTask" class="btn btn-submit" disabled>提交图像任务</button>
            </div>
        </div>

        <div class="input-section">
            <button id="getPortrait" class="btn btn-submit"
                style="display: none;left: 25%; text-align: center;">获取肖像</button>
            <button id="generateTask" class="btn btn-submit" style="left: 65%; text-align: center;">生成任务</button>
            <!-- <button id="nowgetTask" class="btn btn-submit" style="left: 65%; text-align: center;">直接获取任务</button> -->
        </div>
        <div id="taskPlanLoading" style="display: none; text-align: center; color: #007bff; margin-bottom: 20px;">
            <span class="loading-spinner"></span>任务计划正在生成中，请稍候...
        </div>
        <!-- <div class="task-plan-section">
            <h3>个性化任务计划</h3>
            <table id="taskPlanTable">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>时段</th>
                        <th>子任务</th>
                        <th>定制</th>
                        <th>难点</th>
                        <th>方案</th>
                        <th>执行信心指数</th>
                    </tr>
                </thead>
                <tbody id="taskPlanBody">
                </tbody>
            </table>
        </div> -->
        <div class="step-indicator">
            <div class="step">步骤 1: 获取小孩信息 <span class="loading-circle" id="step1Loading"></span></div>
            <div class="step">步骤 2: 请求生成任务 <span class="loading-circle" id="step2Loading"></span></div>
            <div class="step">步骤 3: 显示任务计划 <span class="loading-circle" id="step3Loading"></span></div>
        </div>
        <div class="progress-container">
            <div class="progress-bar" id="taskGenerationProgress"></div>
        </div>
        <div class="task-plan-section">
            <h3>个性化任务计划</h3>

            <!-- 新增语音输入相关元素 -->
            <div class="voice-input-section">
                <select id="taskNameSelect" class="task-select">
                    <option value="">请选择任务</option>
                </select>
                <button id="startVoiceInput" class="btn btn-start">

                    语音输入任务
                </button>
                <button id="stopVoiceTaskInput1" class="btn btn-stop">

                    停止语音输入
                </button>

                <button id="task-generate-btn" class="btn btn-checkTask" style=" margin: 20px auto;
                            display: block;">拍照生成任务</button>

            </div>

            <div class="voice-input-section">


                <input type="text" id="newSubTaskInput" placeholder="新增子任务内容" class="subtask-input">
                <button id="addSubTaskBtn" class="btn btn-submit">添加任务</button>
            </div>
            <table id="taskPlanTable">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>时间段</th>

                        <th>子任务</th>

                        <th>定制方案</th>
                        <th>难点</th>
                        <th>解决方案</th>
                        <th>信心指数</th>
                        <!-- <th>奖励</th> -->
                    </tr>
                </thead>
                <tbody id="taskPlanBody">
                </tbody>
            </table>
        </div>
        <!-- 新增已删除子任务表格 -->
        <div class="deleted-subtasks-section">
            <h3>已删除的子任务</h3>
            <table id="deletedSubtasksTable">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>内容</th>
                        <th>时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="deletedSubtasksBody">
                </tbody>
            </table>
        </div>

        <div class="input-section">
            <button id="checkTask" class="btn btn-checkTask" style=" margin: 20px auto;
        display: block;">确认任务</button>
        </div>
        <!-- 新增摄像头相关 HTML 元素 -->
        <div id="camera-controls">
            <button id="start-camera" class="btn btn-connect" style="display: none">启动摄像头</button>
            <button id="capture" class="btn btn-start" disabled style="display: none">拍照</button>
            <button id="analyze" class="btn btn-stop" disabled style="display: none">分析</button>
        </div>
        <video id="video" autoplay playsinline></video>
        <canvas id="canvas" style="display: none;"></canvas>
        <div class="image-container" id="original-image-container">
            <p class="placeholder-text">检测图像:</p>
            <img id="original-image" style="display: none;">
        </div>
        <!-- <div class="image-container" id="annotated-image-container">
            <p class="placeholder-text">标注图像将显示在这里</p>
            <img id="annotated-image" style="display: none;">
        </div> -->
        <!-- <pre id="items-text"></pre>
        <pre id="tidiness-text"></pre>
        <div id="notification"></div> -->

        <!-- 新增桌面整洁度和清理建议文本框 -->
        <div class="input-section">
            <label for="deskTidiness">桌面整洁度:</label>
            <textarea id="deskTidiness" style="width: 80%; height: 100px;"></textarea>
        </div>
        <div class="input-section">
            <label for="deskSuggestion">桌面清理建议:</label>
            <textarea id="deskSuggestion" style="width: 80%; height: 100px;"></textarea>
        </div>
        <div class="input-section">
            <button id="checkEnv" class="btn btn-checkTask" style=" margin: 20px auto;
        display: block;">清理完成</button>
        </div>


        <div class="results-section subject-detection-section">
            <div class="results-header subject-detection-header">
                <h3>📚 学科检测功能</h3>
            </div>

            <!-- 学科检测状态
            <div id="subject-status-indicator" class="subject-status-indicator alert alert-warning">
                <span id="subject-status-text">学科检测状态: 未初始化</span>
            </div> -->

            <!-- 摄像头视频流 -->
            <!-- <div class="subject-webcam-container">
                <video id="subject-webcam" autoplay playsinline></video>
            </div> -->

            <!-- 摄像头视频流 -->
            <!-- <div class="task-webcam-container">
                <video id="task-webcam" autoplay playsinline></video>
            </div> -->


            <!-- 控制按钮 -->


            <div class="input-section">
                <button id="subject-capture-btn" class="btn btn-checkTask" style=" margin: 20px auto;
            display: block;">拍照检测学科</button>

                <button id="task-generate-btn" class="btn btn-checkTask" style=" margin: 20px auto;
                            display: block;">拍照生成任务</button>
            </div>

            <div class="input-section">
                <button id="summary-btn" class="btn btn-checkTask" style=" margin: 20px auto;
            display: block;">总结汇报</button>
                <button id="finish-task-btn" class="btn btn-checkTask" style=" margin: 20px auto;
            display: block; background: linear-gradient(45deg, #3fc43f, #6ec79a);">提交检测</button>
            </div>

            <!-- 检测结果显示区域 -->
            <div id="subject-result-container" class="subject-result-container">
                <p class="text-muted">请拍照或上传图片进行学科检测</p>
            </div>

            <!-- 桌面检测功能区域 -->
            <div class="detection-results-container" style="margin-top: 20px;">
                <div id="desktop-detection-section" class="detection-card desktop-card">
                    <div class="detection-header">
                        <div class="detection-icon">📚</div>
                        <h5 class="detection-title">桌面环境检测</h5>
                    </div>

                    <!-- 桌面检测摄像头预览 -->
                    <div class="camera-preview-container">
                        <video id="desktop-webcam" autoplay playsinline
                               style="max-width: 100%; height: 200px; border: 2px solid #4CAF50; border-radius: 10px; margin: 10px 0;"></video>
                        <div class="camera-controls">
                            <button id="start-desktop-camera" class="btn btn-connect"
                                    style="background: linear-gradient(45deg, #4CAF50, #45a049); margin: 5px;">
                                <span>📹</span> 启动桌面摄像头
                            </button>
                            <button id="desktop-detect-btn" class="btn btn-checkTask"
                                    style="background: linear-gradient(45deg, #4CAF50, #45a049); margin: 5px;" disabled>
                                <span style="margin-right: 10px;">📚</span>
                                开始桌面检测
                            </button>
                        </div>
                    </div>

                    <!-- 桌面检测结果显示区域 -->
                    <div id="desktop-result">
                        <div class="loading-detection">
                            <div class="loading-text">准备检测桌面环境</div>
                            <div class="loading-subtext">请先启动摄像头，然后点击"开始桌面检测"</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 坐姿检测功能区域 -->
            <div class="detection-results-container" style="margin-top: 20px;">
                <div id="posture-detection-section" class="detection-card posture-card">
                    <div class="detection-header">
                        <div class="detection-icon">🧘</div>
                        <h5 class="detection-title">坐姿与专注度检测</h5>
                    </div>

                    <!-- 坐姿检测摄像头预览 -->
                    <div class="camera-preview-container">
                        <video id="posture-webcam" autoplay playsinline
                               style="max-width: 100%; height: 200px; border: 2px solid #2196F3; border-radius: 10px; margin: 10px 0;"></video>
                        <div class="camera-controls">
                            <button id="start-posture-camera" class="btn btn-connect"
                                    style="background: linear-gradient(45deg, #2196F3, #1976D2); margin: 5px;">
                                <span>📹</span> 启动坐姿摄像头
                            </button>
                            <button id="posture-detect-btn" class="btn btn-checkTask"
                                    style="background: linear-gradient(45deg, #2196F3, #1976D2); margin: 5px;" disabled>
                                <span style="margin-right: 10px;">🧘</span>
                                开始坐姿检测
                            </button>
                        </div>
                    </div>

                    <!-- 坐姿检测结果显示区域 -->
                    <div id="posture-result">
                        <div class="loading-detection">
                            <div class="loading-text">准备检测坐姿状态</div>
                            <div class="loading-subtext">请先启动摄像头，然后点击"开始坐姿检测"</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div id="subject-stats-container" class="subject-stats-container" style="margin-top: 20px;">
                <!-- 统计信息将在这里显示 -->
            </div>

        </div>

        <div class="voice-results-section">
            <div class="results-header">
                <h3>📝 识别结果</h3>
                <div id="recordingStatus" style="display: none; color: red;">录音中...</div>
                <button class="clear-btn" onclick="clearResults()">清空结果</button>
            </div>
            <div class="results-list" id="resultsList">
                <div class="empty-state">
                    暂无识别结果，请开始语音识别...
                </div>
            </div>

            <p id="currentSubjectText">检测的科目: </p>
            <p id="targetTaskNameText">计划的科目: </p>
            <div class="subject-webcam-container">
                <video id="subject-webcam" autoplay playsinline></video>
            </div>
        </div>

        <div class="message-section">

        </div>

        <!-- 音频播放器 -->
        <div class="audio-player-section" id="audioPlayer" style="display: none;">
            <div class="audio-player-header">
                <span>🔊</span>
                <h4>语音播放</h4>
            </div>
            <div class="audio-text" id="audioText">
                等待语音播放...
            </div>
        </div>



    </div>

    <script>
        // 全局变量
        const socket = io({
            reconnection: true,
            reconnectionDelay: 1000,
            reconnectionAttempts: 10,
            timeout: 20000,
            forceNew: true
        }); // 连接到主服务（包含ASR和TTS）
        let isRecording = false;
        let isConnected = false;
        let audioContext;
        let processor;
        let microphone;
        let currentSubject = '';
        let targetTaskName = '';
        let currentAudio = null;
        let port = 8009;
        let tableId=null;
        // 新增全局变量，用于标记是否处于子任务语音输入状态
        let isSubTaskVoiceInput = false;
        // 存储冲突状态，用于判断是否显示消息
        let hasConflict = false;
        // 新增摄像头相关全局变量
        let video = document.getElementById('video');
        let canvas = document.getElementById('canvas');
        let captureButton = document.getElementById('capture');
        let analyzeButton = document.getElementById('analyze');
        let checkTaskButton = document.getElementById('checkTask');
        let checkEnvButton = document.getElementById('checkEnv');
        let startCameraButton = document.getElementById('start-camera');
        let subjectResultContainer = document.getElementById('subject-result-container');
        let subjectWebcam = document.getElementById('subject-webcam');
        let taskWebcam = document.getElementById('task-webcam');
        let subjectCaptureBtn = document.getElementById('subject-capture-btn');
        let taskGenerateBtn = document.getElementById('task-generate-btn');
        let summaryBtn = document.getElementById('summary-btn');
        let subjectStatsContainer = document.getElementById('subject-stats-container');

        // 桌面检测相关元素
        let desktopWebcam = document.getElementById('desktop-webcam');
        let startDesktopCameraBtn = document.getElementById('start-desktop-camera');
        let desktopDetectBtn = document.getElementById('desktop-detect-btn');

        // 坐姿检测相关元素
        let postureWebcam = document.getElementById('posture-webcam');
        let startPostureCameraBtn = document.getElementById('start-posture-camera');
        let postureDetectBtn = document.getElementById('posture-detect-btn');

        let finishTaskBtn = document.getElementById('finish-task-btn');
        let getPortraitBtn = document.getElementById('getPortrait');
        let generateTaskBtn = document.getElementById('generateTask');
        let getTaskBtn = document.getElementById('nowgetTask'); // 注意：此按钮已被注释

        // 新增任务输入相关元素
        let childIdInput = document.getElementById('childIdInput');
        let submitTextTaskBtn = document.getElementById('submitTextTask');
        let dailyTaskTextArea = document.getElementById('dailyTaskText');
        let startVoiceTaskInputBtn = document.getElementById('startVoiceTaskInput');
        let stopVoiceTaskInputBtn = document.getElementById('stopVoiceTaskInput');
        let stopVoiceTaskInput1Btn = document.getElementById('stopVoiceTaskInput1');
        let submitVoiceTaskBtn = document.getElementById('submitVoiceTask');
        let voiceTaskResultArea = document.getElementById('voiceTaskResult');
        let startImageCaptureBtn = document.getElementById('startImageCapture');
        let captureImageTaskBtn = document.getElementById('captureImageTask');
        let submitImageTaskBtn = document.getElementById('submitImageTask');
        let imageTaskVideo = document.getElementById('imageTaskVideo');
        let imageTaskCanvas = document.getElementById('imageTaskCanvas');
        let capturedTaskImage = document.getElementById('capturedTaskImage');
        let manualVoiceTextInput = document.getElementById('manualVoiceText');
        let useManualVoiceTextBtn = document.getElementById('useManualVoiceText');



        let capturedImage = null;
        let analysisResult = null;
        let imageTaskStream = null;
        let isVoiceTaskRecording = false;

        // 桌面检测和坐姿检测相关变量
        let desktopCameraStream = null;
        let postureCameraStream = null;


        let tempTaskPlan=null;
        // 显示消息
        function showtmpMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `floating-notification ${type === 'error' ? 'floating-error' : 'floating-success'}`;
            messageDiv.textContent = message;

            // 严格选择 body 元素并添加消息元素
            const bodyElement = document.querySelector('body');
            if (bodyElement) {
                bodyElement.appendChild(messageDiv);
                console.log('消息元素已添加到 body 中');
            } else {
                console.error('未找到 body 元素，无法添加消息元素');
            }

            // 添加 show 类以触发过渡效果
            setTimeout(() => {
                messageDiv.classList.add('show');
            }, 50);

            // 3秒后自动移除
            setTimeout(() => {
                messageDiv.classList.remove('show');
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                        console.log('消息元素已移除');
                    }
                }, 300);
            }, 3000);
        }


        async function commitChangeAction(changeMessage) {
            const childId = parseInt(childIdInput.value);
            const response = await fetch(`http://localhost:${port}/api/v1/user-plan-actions/actions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "user_id": childId,
                    "table_id": `${tableId}`,
                    "user_operation": changeMessage,
                })
            });

            const responsedata = await response.json();
            console.log("保存操作结果：",responsedata)

            if (!responsedata.success) {
                console.log("保存记录失败：",responsedata.message)
            }else{
                console.log(`成功保存用户:${childId}操作 表：${tableId} 记录 ${changeMessage}`)
                changeType=null
            }
        }


        // 显示消息的函数
        function showMessageInSection(message, type = 'error', shouldShow) {
            const messageSection = document.querySelector('.message-section');
            // 设置消息内容
            messageSection.textContent = message;
            // 移除之前的类型类
            messageSection.classList.remove('success', 'error');
            // 根据类型添加对应的类
            messageSection.classList.add(type);

            if (shouldShow) {
                // 显示消息容器
                messageSection.style.display = 'block';
            } else {
                // 隐藏消息容器
                messageSection.style.display = 'none';
            }
        }




        finishTaskBtn.addEventListener('click', async () => {
            try {


                const response = await fetch('/api/sumbitFinishTask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    // body: JSON.stringify({
                    //     image: capturedImage
                    // })
                });

                // 读取响应体为文本
                const responseText = await response.text();

                console.log('提交任务接口 /api/sumbitFinishTask 响应内容:', responseText);


            } catch (error) {
                console.error('任务确认执行过程中出错:', error);
                showNotification(`任务确认执行过程中出错: ${error.message}`, 5000);
            }
        });
        getPortraitBtn.addEventListener('click', async () => {

            const id = 4; // 这里可以替换成实际获取 id 的逻辑，例如从输入框获取


            const baseUrl_portrait = `http://localhost:${port}/api/v1/user-management/children`;
            const apiUrl_portrait = `${baseUrl_portrait}/${id}`; // 拼接 URL
            try {
                const response = await fetch(apiUrl_portrait, {
                    method: 'GET',

                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('获取小孩肖像信息:', data);

                // 将获取到的数据显示在 studentPortait 文本区域中
                const studentPortait = document.getElementById('studentPortait');
                studentPortait.value = JSON.stringify(data, null, 2); // 使用 JSON.stringify 格式化数据

            } catch (error) {
                console.error('获取小孩肖像信息失败:', error);
                showtmpMessage('获取小孩肖像信息失败: ' + error.message, 'error');
                return null;
            }


            // 假设这里有一个函数或者变量能获取到 id 值

            const baseUrl_learnRcord = `http://localhost:${port}/api/v1/daily-learning/learning-statistics`;
            const apiUrl_learnRcord = `${baseUrl_learnRcord}/${id}`; // 拼接 URL
            try {
                const response = await fetch(apiUrl_learnRcord, {
                    method: 'GET',

                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('获取小孩学习统计信息:', data);

                // 将获取到的数据显示在 studentPortait 文本区域中
                const learnRecord = document.getElementById('learnRecord');
                learnRecord.value = JSON.stringify(data, null, 2); // 使用 JSON.stringify 格式化数据

            } catch (error) {
                console.error('获取小孩学习统计信息失败:', error);
                showtmpMessage('获取小孩学习统计信息失败: ' + error.message, 'error');
                return null;
            }
        });

        // 手动输入语音文本功能
        useManualVoiceTextBtn.addEventListener('click', () => {
            const manualText = manualVoiceTextInput.value.trim();

            if (!manualText) {
                showtmpMessage('请输入语音文本内容', 'error');
                return;
            }

            console.log('🖊️ 使用手动输入的语音文本:', manualText);

            // 将手动输入的文本设置到语音任务结果框
            voiceTaskResultArea.value = manualText;

            // 启用提交按钮
            submitVoiceTaskBtn.disabled = false;

            // 清空手动输入框
            manualVoiceTextInput.value = '';

            showtmpMessage('语音文本已设置，可以提交了', 'success');
        });

        // 文本任务提交功能
        submitTextTaskBtn.addEventListener('click', async () => {
            const childId = parseInt(childIdInput.value);
            const taskText = dailyTaskTextArea.value.trim();

            if (!childId || childId <= 0) {
                showtmpMessage('请输入有效的小孩ID', 'error');
                return;
            }

            if (!taskText) {
                showtmpMessage('请输入任务内容', 'error');
                return;
            }

            try {
                submitTextTaskBtn.disabled = true;
                submitTextTaskBtn.innerHTML = '<span class="loading-spinner"></span>提交中...';

                const response = await fetch(`http://localhost:${port}/api/v1/multimodal-task-input/text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        child_id: childId,
                        text_content: taskText
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('文本任务提交结果:', result);

                if (result.success) {
                    showtmpMessage('文本任务提交成功！', 'success');
                    dailyTaskTextArea.value = ''; // 清空输入框
                } else {
                    showtmpMessage(`文本任务提交失败: ${result.message}`, 'error');
                }

            } catch (error) {
                console.error('文本任务提交失败:', error);
                showtmpMessage(`文本任务提交失败: ${error.message}`, 'error');
            } finally {
                submitTextTaskBtn.disabled = false;
                submitTextTaskBtn.innerHTML = '提交文本任务';
            }
        });

        // 显示加载圆圈
        function showLoadingCircle(stepNumber) {
            const loadingCircle = document.getElementById(`step${stepNumber}Loading`);
            if (loadingCircle) {
                loadingCircle.style.display = 'inline-block';
            }
        }

        // 隐藏加载圆圈
        function hideLoadingCircle(stepNumber) {
            const loadingCircle = document.getElementById(`step${stepNumber}Loading`);
            if (loadingCircle) {
                loadingCircle.style.display = 'none';
            }
        }

        // 更新进度条
        function updateProgressBar(percentage) {
            const progressBar = document.getElementById('taskGenerationProgress');
            if (progressBar) {
                progressBar.style.width = `${percentage}%`;
            }
        }


        generateTaskBtn.addEventListener('click', async () => {
            const progressBar = document.getElementById('taskGenerationProgress');

            const steps = document.querySelectorAll('.step');
            // 初始化进度条和步骤状态
            progressBar.style.width = '0%';

            steps.forEach(step => step.classList.remove('active'));

            // 隐藏确认任务按钮（开始新的任务生成时）
            hideConfirmTaskButton();

            // 清除之前的任务计划数据
            window.currentTaskPlan = null;

            final_taskprompt=null;
            const id = parseInt(childIdInput.value) || 4; // 从输入框获取ID，默认为4
            const baseUrl = `http://localhost:${port}/api/v1/prompt-generation/task-prompt/${id}`;

            // 步骤一：生成 prompt

            steps[0].classList.add('active');
            console.log("给小孩", id, "生成prompt");
            showLoadingCircle(1);
            try {
                const response = await fetch(baseUrl, {
                    method: 'GET',
                    // headers: {
                    //     'Content-Type': 'application/json'
                    // },
                    // body: JSON.stringify({
                    //     "child_id":id,
                    //     "days_back": 7,
                    //     "include_yesterday_tasks": true,
                    //     "template_type": "task_prompt",
                    //     "subject_filter": "string"
                    // })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();


                final_taskprompt=data.final_prompt
                // 更新进度条到 33%
                hideLoadingCircle(1);
                updateProgressBar(33);
                // progressBar.style.width = '33%';

                steps[0].classList.remove('active');    
            } catch (error) {
                console.error('获取生成的prompt失败:', error);
                showtmpMessage('获取生成的prompt失败: ' + error.message, 'error');
                return null;
            }

            console.log('获取生成的prompt成功');
            const url_generateTask = `http://localhost:${port}/api/v1/chat/completions`;

            // 步骤二：请求生成任务
            showLoadingCircle(2);
            steps[1].classList.add('active');
            try {
                const response = await fetch(url_generateTask, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({

                        // "prompt": final_taskprompt,
                        // "temperature": 2,
                        // "top_p": 1,
                        // "max_tokens": 10000

                        "messages": [
                            {
                            "role": "user",
                            "content": final_taskprompt
                            }
                        ],
                        "temperature": 1,
                        "top_p": 1,
                        "max_tokens":4000,
                        "stream": false
                        })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('生成计划:');
                console.log('=======================================');
                console.log('生成计划成功',data);
                // 更新进度条到 66%
                hideLoadingCircle(2);
                updateProgressBar(66);

                steps[1].classList.remove('active');

                showTaskPlanAndSaveToDatabase(data.response_text)


                // 步骤三：显示任务计划

                steps[2].classList.add('active');

                showLoadingCircle(3);
               
                // 更新进度条到 100%
                hideLoadingCircle(3);
                updateProgressBar(100);

                steps[2].classList.remove('active');

            } catch (error) {
                console.error('生成计划失败:', error);
                showtmpMessage('生成计划失败: ' + error.message, 'error');
                return null;
            }
        });

         // showTaskPlan(data.response_text);

            // //++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            // try {

            //     taskTemplate = [
            //         {
            //             "task_name": "数学作业",
            //             "time_slot": "18:00 - 18:45",
            //             "subject": "数学",
            //             "sub_tasks": [
            //                 {
            //                     "sub_task_name": "练习册第9页",
            //                     "time_slot": "18:00 - 18:15"
            //                 },
            //                 {
            //                     "sub_task_name": "口算题卡20题",
            //                     "time_slot": "18:15 - 18:30"
            //                 },
            //                 {
            //                     "sub_task_name": "复习乘法表",
            //                     "time_slot": "18:30 - 18:45"
            //                 }
            //             ],
            //             "customization": "针对计算速度慢问题，进行计时口算练习；乘法表采用趣味口诀记忆",
            //             "difficulty": "乘法表记忆，计算速度",
            //             "solution": "准备一个小闹钟进行口算限时训练，编一些有趣的乘法口诀帮助记忆，每正确完成一组口算题或背熟一段乘法口诀奖励1积分",
            //             "confidence_index": 3
            //         },
            //         {
            //             "task_name": "休息时间",
            //             "time_slot": "18:45 - 19:00",
            //             "subject": "休息",
            //             "sub_tasks": [
            //                 {
            //                     "sub_task_name": "活动身体，放松一下，准备语文学习",
            //                     "time_slot": "18:45 - 19:00"
            //                 }
            //             ],
            //             "customization": "简单做几下伸展运动，喝口水，收拾数学用品，拿出语文学习材料",
            //             "difficulty": "无",
            //             "solution": "适当活动，调整状态",
            //             "confidence_index": 5
            //         },
            //         {
            //             "task_name": "语文作业",
            //             "time_slot": "19:00 - 20:00",
            //             "subject": "语文",
            //             "sub_tasks": [
            //                 {
            //                     "sub_task_name": "阅读《小红帽》故事",
            //                     "time_slot": "19:00 - 19:15"
            //                 },
            //                 {
            //                     "sub_task_name": "完成阅读理解题",
            //                     "time_slot": "19:15 - 19:30"
            //                 },
            //                 {
            //                     "sub_task_name": "练字30个",
            //                     "time_slot": "19:30 - 19:45"
            //                 },
            //                 {
            //                     "sub_task_name": "撰写一篇关于小狗的作文（初稿）",
            //                     "time_slot": "19:45 - 20:00"
            //                 }
            //             ],
            //             "customization": "针对阅读理解和背诵困难，阅读后用自己的话复述故事，阅读理解采用图文结合方式辅助理解；对于作文先搭框架再写作；针对背诵困难，采用分段背诵并奖励积分",
            //             "difficulty": "阅读理解，作文写作，背诵",
            //             "solution": "阅读时画出重要情节和角色，做完阅读理解题每答对一题得1积分，练字写得工整好看奖励1积分；写作文先列出小狗的外形、生活习性、和自己的故事等框架，完成初步框架搭建奖励1积分；分段背诵，每背熟一段奖励1积分",
            //             "confidence_index": 3
            //         },
            //         {
            //             "task_name": "休息时间",
            //             "time_slot": "20:00 - 20:15",
            //             "subject": "休息",
            //             "sub_tasks": [
            //                 {
            //                     "sub_task_name": "活动一下身体，听听音乐，准备英语学习",
            //                     "time_slot": "20:00 - 20:15"
            //                 }
            //             ],
            //             "customization": "跳绳几分钟或者做几个简单的健身动作，放松身心，更换学习材料",
            //             "difficulty": "无",
            //             "solution": "进行体力活动，转换思维",
            //             "confidence_index": 5
            //         },
            //         {
            //             "task_name": "英语作业",
            //             "time_slot": "20:15 - 20:45",
            //             "subject": "英语",
            //             "sub_tasks": [
            //                 {
            //                     "sub_task_name": "学习Unit 3新单词",
            //                     "time_slot": "20:15 - 20:25"
            //                 },
            //                 {
            //                     "sub_task_name": "听读课文",
            //                     "time_slot": "20:25 - 20:35"
            //                 },
            //                 {
            //                     "sub_task_name": "完成练习册第6页",
            //                     "time_slot": "20:35 - 20:45"
            //                 }
            //             ],
            //             "customization": "针对新单词记忆，采用游戏记忆法；利用孩子英语学习兴趣高的特点，完成任务后可观看一小段英语动画",
            //             "difficulty": "新单词记忆",
            //             "solution": "制作单词小卡片，玩单词接龙游戏，每成功说出一个单词奖励1积分；完成所有英语任务后，奖励观看5分钟英语动画",
            //             "confidence_index": 4
            //         },
            //         {
            //             "task_name": "休息时间",
            //             "time_slot": "20:45 - 21:00",
            //             "subject": "休息",
            //             "sub_tasks": [
            //                 {
            //                     "sub_task_name": "活动身体，放松眼睛，准备额外语文任务",
            //                     "time_slot": "20:45 - 21:00"
            //                 }
            //             ],
            //             "customization": "做眼保健操，在室内走动几步，准备接下来的语文任务材料",
            //             "difficulty": "无",
            //             "solution": "放松身体和眼睛，为后续学习做准备",
            //             "confidence_index": 5
            //         },
            //         {
            //             "task_name": "语文作业",
            //             "time_slot": "21:00 - 21:30",
            //             "subject": "语文",
            //             "sub_tasks": [
            //                 {
            //                     "sub_task_name": "对关于小狗的作文进行修改完善（终稿）",
            //                     "time_slot": "21:00 - 21:15"
            //                 },
            //                 {
            //                     "sub_task_name": "背诵描写小狗的精彩段落（从优秀范文中选取）",
            //                     "time_slot": "21:15 - 21:30"
            //                 }
            //             ],
            //             "customization": "针对语文基础薄弱和背诵困难，提供优秀范文段落模仿修改，采用分层次背诵奖励",
            //             "difficulty": "作文修改，段落背诵",
            //             "solution": "给出修改提示，比如语句是否通顺、描写是否生动等，修改得好奖励2积分；背诵段落按层次划分，每背下一个层次奖励1积分",
            //             "confidence_index": 3
            //         }
            //     ]

            //     showTaskPlan(JSON.stringify(taskTemplate));


                // //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++


        // 语音任务输入功能
        startVoiceTaskInputBtn.addEventListener('click', async () => {
            const childId = parseInt(childIdInput.value);

            if (!childId || childId <= 0) {
                showtmpMessage('请输入有效的小孩ID', 'error');
                return;
            }

            try {
                console.log('🎤 开始语音任务录制...');

                // 清空之前的结果
                voiceTaskResultArea.value = '';

                // 设置语音任务录制状态
                isVoiceTaskRecording = true;
                console.log('📊 设置语音任务录制状态:', isVoiceTaskRecording);

                startVoiceTaskInputBtn.disabled = true;
                stopVoiceTaskInputBtn.disabled = false;
                submitVoiceTaskBtn.disabled = true;

                // 连接ASR服务（如果未连接）
                if (!isConnected) {
                    console.log('🔗 ASR服务未连接，正在连接...');
                    await connectService();
                }

                // 开始录音
                console.log('🎙️ 开始语音识别...');
                await startRecognition();

                console.log('✅ 语音任务录制已启动');
                showtmpMessage('开始语音录制，请说话...', 'success');

            } catch (error) {
                console.error('❌ 启动语音录制失败:', error);
                showtmpMessage(`启动语音录制失败: ${error.message}`, 'error');
                isVoiceTaskRecording = false;
                startVoiceTaskInputBtn.disabled = false;
                stopVoiceTaskInputBtn.disabled = true;
            }
        });



        stopVoiceTaskInputBtn.addEventListener('click', async () => {
            try {
                console.log('🛑 停止语音任务录制...');

                // 停止录音
                await stopRecognition();

                // 等待一小段时间确保最后的识别结果被处理
                setTimeout(() => {
                    console.log('⏰ 处理停止录制后的清理工作...');

                    // 清理临时结果标记
                    let voiceText = voiceTaskResultArea.value;
                    if (voiceText.includes('[临时]')) {
                        // 移除临时标记，保留最后的识别结果
                        voiceText = voiceText.replace(/\[临时\]\s*/g, '').trim();
                        voiceTaskResultArea.value = voiceText;
                        console.log('🧹 清理临时标记后的结果:', voiceText);
                    }

                    // 重置状态
                    isVoiceTaskRecording = false;
                    console.log('📊 重置语音任务录制状态:', isVoiceTaskRecording);

                    startVoiceTaskInputBtn.disabled = false;
                    stopVoiceTaskInputBtn.disabled = true;

                    // 检查是否有识别结果
                    console.log('🔍 检查最终语音结果:', voiceText);

                    if (voiceText && voiceText.trim()) {
                        submitVoiceTaskBtn.disabled = false;
                        showtmpMessage('语音录制已停止，识别结果已准备好提交', 'success');
                        console.log('✅ 语音任务提交按钮已启用');
                    } else {
                        submitVoiceTaskBtn.disabled = true;
                        showtmpMessage('语音录制已停止，但未识别到有效内容', 'warning');
                        console.log('⚠️ 未识别到有效内容');
                    }
                }, 1500); // 等待1.5秒确保所有识别结果都被处理

            } catch (error) {
                console.error('❌ 停止语音录制失败:', error);
                showtmpMessage(`停止语音录制失败: ${error.message}`, 'error');
                isVoiceTaskRecording = false;
                startVoiceTaskInputBtn.disabled = false;
                stopVoiceTaskInputBtn.disabled = true;
                submitVoiceTaskBtn.disabled = true;
            }
        });


        // 封装启动语音任务录制的函数
        async function startVoiceTaskRecording() {
            const childId = parseInt(childIdInput.value);

            if (!childId || childId <= 0) {
                showtmpMessage('请输入有效的小孩ID', 'error');
                return;
            }

            try {
                console.log('🎤 开始语音任务录制...');

                // 清空之前的结果
                voiceTaskResultArea.value = '';

                // 设置语音任务录制状态
                isVoiceTaskRecording = true;
                console.log('📊 设置语音任务录制状态:', isVoiceTaskRecording);

                startVoiceTaskInputBtn.disabled = true;
                stopVoiceTaskInputBtn.disabled = false;
                submitVoiceTaskBtn.disabled = true;

                // 连接ASR服务（如果未连接）
                if (!isConnected) {
                    console.log('🔗 ASR服务未连接，正在连接...');
                    await connectService();
                }

                // 开始录音
                console.log('🎙️ 开始语音识别...');
                await startRecognition();

                console.log('✅ 语音任务录制已启动');
                showtmpMessage('开始语音录制，请说话...', 'success');

            } catch (error) {
                console.error('❌ 启动语音录制失败:', error);
                showtmpMessage(`启动语音录制失败: ${error.message}`, 'error');
                isVoiceTaskRecording = false;
                startVoiceTaskInputBtn.disabled = false;
                stopVoiceTaskInputBtn.disabled = true;
            }
        }


        stopVoiceTaskInput1Btn.addEventListener('click', async () => {
            stopVoiceTaskInput1Btn=false;

            // try {
            //     console.log('🛑 停止语音任务录制...');

            //     // 停止录音
            //     await stopRecognition();

            //     // 获取输入框元素
            //     const newSubTaskInput = document.getElementById('newSubTaskInput');
            //     // 等待一小段时间确保最后的识别结果被处理
            //     setTimeout(() => {
            //         console.log('⏰ 处理停止录制后的清理工作...');

            //         // 清理临时结果标记
            //         let voiceText = voiceTaskResultArea.value;
            //         if (voiceText.includes('[临时]')) {
            //             // 移除临时标记，保留最后的识别结果
            //             voiceText = voiceText.replace(/\[临时\]\s*/g, '').trim();
            //             voiceTaskResultArea.value = voiceText;
            //             newSubTaskInput.value = voiceText;
            //             console.log('🧹 清理临时标记后的结果:', voiceText);
            //         }

            //         // 重置状态
            //         isVoiceTaskRecording = false;
            //         console.log('📊 重置语音任务录制状态:', isVoiceTaskRecording);

            //         startVoiceTaskInputBtn.disabled = false;
            //         stopVoiceTaskInputBtn.disabled = true;

            //         // 检查是否有识别结果
            //         console.log('🔍 检查最终语音结果:', voiceText);

            //         if (voiceText && voiceText.trim()) {
            //             submitVoiceTaskBtn.disabled = false;
            //             showtmpMessage('语音录制已停止，识别结果已准备好提交', 'success');
            //             console.log('✅ 语音任务提交按钮已启用');
            //         } else {
            //             submitVoiceTaskBtn.disabled = true;
            //             showtmpMessage('语音录制已停止，但未识别到有效内容', 'warning');
            //             console.log('⚠️ 未识别到有效内容');
            //         }
            //     }, 1500); // 等待1.5秒确保所有识别结果都被处理

            // } catch (error) {
            //     console.error('❌ 停止语音录制失败:', error);
            //     showtmpMessage(`停止语音录制失败: ${error.message}`, 'error');
            //     isVoiceTaskRecording = false;
            //     startVoiceTaskInputBtn.disabled = false;
            //     stopVoiceTaskInputBtn.disabled = true;
            //     submitVoiceTaskBtn.disabled = true;
            // }
        });

        submitVoiceTaskBtn.addEventListener('click', async () => {
            const childId = parseInt(childIdInput.value);
            const voiceText = voiceTaskResultArea.value.trim();

            console.log('🎤 提交语音任务 - 小孩ID:', childId, '语音文本:', voiceText);

            if (!childId || childId <= 0) {
                showtmpMessage('请输入有效的小孩ID', 'error');
                return;
            }

            if (!voiceText) {
                showtmpMessage('没有语音识别结果，请重新录制', 'error');
                return;
            }

            try {
                submitVoiceTaskBtn.disabled = true;
                submitVoiceTaskBtn.innerHTML = '<span class="loading-spinner"></span>提交中...';

                console.log('🌐 发送语音任务到API:', `http://localhost:${port}/api/v1/multimodal-task-input/voice`);

                const response = await fetch(`http://localhost:${port}/api/v1/multimodal-task-input/voice`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        child_id: childId,
                        voice_text: voiceText
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('✅ 语音任务提交结果:', result);

                if (result.success) {
                    const taskCount = result.total_tasks || 0;
                    const storedCount = result.stored_tasks || 0;
                    const taskIds = result.stored_task_ids || [];

                    let successMessage = `🎉 语音任务提交成功！\n`;
                    successMessage += `📝 解析出${taskCount}个任务\n`;
                    successMessage += `💾 成功存储${storedCount}个任务到数据库\n`;
                    if (taskIds.length > 0) {
                        successMessage += `🆔 任务ID: ${taskIds.join(', ')}`;
                    }

                    showtmpMessage(successMessage, 'success');
                    voiceTaskResultArea.value = ''; // 清空结果
                    submitVoiceTaskBtn.disabled = true; // 禁用提交按钮直到下次录制

                    // 显示任务详情
                    if (result.tasks && result.tasks.length > 0) {
                        console.log('📋 解析的任务详情:');
                        result.tasks.forEach((task, index) => {
                            console.log(`任务${index + 1}: ${task.task_name} (${task.subject})`);
                            console.log(`描述: ${task.description}`);
                            console.log(`预计时长: ${task.estimated_duration}分钟`);
                        });
                    }
                } else {
                    showtmpMessage(`语音任务提交失败: ${result.message}`, 'error');
                }

            } catch (error) {
                console.error('❌ 语音任务提交失败:', error);
                showtmpMessage(`语音任务提交失败: ${error.message}`, 'error');
            } finally {
                // 只有在有内容时才启用提交按钮
                if (voiceTaskResultArea.value.trim()) {
                    submitVoiceTaskBtn.disabled = false;
                }
                submitVoiceTaskBtn.innerHTML = '提交语音任务';
            }
        });

        // 图像任务输入功能
        startImageCaptureBtn.addEventListener('click', async () => {
            try {
                imageTaskStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });

                imageTaskVideo.srcObject = imageTaskStream;
                startImageCaptureBtn.disabled = true;
                captureImageTaskBtn.disabled = false;

                showtmpMessage('摄像头已启动，可以拍照了', 'success');

            } catch (error) {
                console.error('启动摄像头失败:', error);
                showtmpMessage(`启动摄像头失败: ${error.message}`, 'error');
            }
        });

        captureImageTaskBtn.addEventListener('click', async () => {
            const childId = parseInt(childIdInput.value);

            if (!childId || childId <= 0) {
                showtmpMessage('请输入有效的小孩ID', 'error');
                return;
            }

            try {
                // 创建canvas并绘制当前视频帧
                imageTaskCanvas.width = imageTaskVideo.videoWidth;
                imageTaskCanvas.height = imageTaskVideo.videoHeight;
                const ctx = imageTaskCanvas.getContext('2d');
                ctx.drawImage(imageTaskVideo, 0, 0);

                // 显示捕获的图像
                const imageDataUrl = imageTaskCanvas.toDataURL('image/jpeg', 0.9);
                capturedTaskImage.src = imageDataUrl;
                capturedTaskImage.style.display = 'block';

                captureImageTaskBtn.disabled = true;
                submitImageTaskBtn.disabled = false;

                showtmpMessage('图像已捕获，可以提交了', 'success');

            } catch (error) {
                console.error('拍照失败:', error);
                showtmpMessage(`拍照失败: ${error.message}`, 'error');
            }
        });

        submitImageTaskBtn.addEventListener('click', async () => {
            const childId = parseInt(childIdInput.value);

            if (!childId || childId <= 0) {
                showtmpMessage('请输入有效的小孩ID', 'error');
                return;
            }

            if (!capturedTaskImage.src) {
                showtmpMessage('请先拍照', 'error');
                return;
            }

            try {
                submitImageTaskBtn.disabled = true;
                submitImageTaskBtn.innerHTML = '<span class="loading-spinner"></span>提交中...';

                // 将图像转换为blob
                imageTaskCanvas.toBlob(async (blob) => {
                    const formData = new FormData();
                    formData.append('child_id', childId.toString());
                    formData.append('image_file', blob, `task_image_${Date.now()}.jpg`);

                    try {
                        // 使用多模态服务处理图像
                        const response = await fetch(`http://localhost:${port}/api/v1/multimodal-task-input/image`, {
                            method: 'POST',
                            body: formData
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const result = await response.json();
                        console.log('图像任务提交结果:', result);

                        if (result.success) {
                            showtmpMessage('图像任务提交成功！', 'success');
                            // 重置界面
                            capturedTaskImage.style.display = 'none';
                            capturedTaskImage.src = '';
                            captureImageTaskBtn.disabled = false;
                        } else {
                            showtmpMessage(`图像任务提交失败: ${result.message}`, 'error');
                        }

                    } catch (error) {
                        console.error('图像任务提交失败:', error);
                        showtmpMessage(`图像任务提交失败: ${error.message}`, 'error');
                    } finally {
                        submitImageTaskBtn.disabled = false;
                        submitImageTaskBtn.innerHTML = '提交图像任务';
                    }
                }, 'image/jpeg', 0.9);

            } catch (error) {
                console.error('图像任务提交失败:', error);
                showtmpMessage(`图像任务提交失败: ${error.message}`, 'error');
                submitImageTaskBtn.disabled = false;
                submitImageTaskBtn.innerHTML = '提交图像任务';
            }
        });

        // 初始化学科检测功能
        async function initSubjectDetection() {
            try {
                // 获取摄像头权限
                subjectStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });
                subjectWebcam.srcObject = subjectStream;

                console.log('学科检测功能初始化完成');
            } catch (err) {
                console.error('初始化学科检测功能失败:', err);
                subjectStatusText.textContent = '无法访问摄像头，请检查权限设置';
                subjectStatusIndicator.className = 'alert alert-danger';
            }
        }

        // 显示学科检测结果
        function displaySubjectResult(result) {
            if (result.success) {
                const subject = result.subject;
                const confidence = result.confidence;
                const confidencePercent = (confidence * 100).toFixed(1);

                // 根据置信度设置颜色
                let confidenceColor = 'text-danger';
                if (confidence >= 0.7) confidenceColor = 'text-success';
                else if (confidence >= 0.4) confidenceColor = 'text-warning';

                subjectResultContainer.innerHTML = `
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="fas fa-book fa-3x text-primary mb-3"></i>
                            <h4 class="font-weight-bold">${subject}</h4>
                        </div>
                        <div class="progress mb-2" style="height: 25px;">
                            <div class="progress-bar ${confidence >= 0.7 ? 'bg-success' : confidence >= 0.4 ? 'bg-warning' : 'bg-danger'}"
                                 role="progressbar"
                                 style="width: ${confidencePercent}%"
                                 aria-valuenow="${confidencePercent}"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                                ${confidencePercent}%
                            </div>
                        </div>
                        <p class="${confidenceColor}">置信度: ${confidencePercent}%</p>
                    </div>
                `;

                // 更新 currentSubject 变量
                currentSubject = subject;
                showSubjectCheckModal();

                // 如果有图像路径，显示图像
                if (result.image_path) {
                    subjectResultContainer.innerHTML += `
                        <div class="mt-3 text-center">
                            <img src="/${result.image_path}" class="img-fluid rounded" style="max-height: 200px;">
                        </div>
                    `;
                }
            } else {
                subjectResultContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> 检测失败: ${result.message || '未知错误'}
                    </div>
                `;
            }
        }
        // 更新学科检测统计
        async function updateSubjectStats() {
            try {
                const response = await fetch('/api/subject/status');
                const data = await response.json();

                if (data.success) {
                    updateSubjectStatsDisplay(data);
                } else {
                    console.error('获取学科检测统计失败:', data.message);
                }
            } catch (err) {
                console.error('获取学科检测统计时出错:', err);
            }
        }

        // 显示学科检测统计
        function updateSubjectStatsDisplay(stats) {
            if (stats.success) {
                const historyCount = stats.history ? stats.history.length : 0;
                const durationsCount = stats.durations ? Object.keys(stats.durations).length : 0;

                // subjectStatsContainer.innerHTML = `
                //     <div class="card">
                //         <div class="card-body">
                //             <h5 class="card-title">学科检测统计</h5>
                //             <p class="card-text">检测状态: ${stats.active ? '活跃' : '未活跃'}</p>
                //             <p class="card-text">功能状态: ${stats.initialized ? '已初始化' : '未初始化'}</p>
                //             <p class="card-text">历史记录数: ${historyCount}</p>
                //             <p class="card-text">学科时长记录: ${durationsCount} 个学科</p>
                //             ${stats.last_detected ? `
                //                 <div class="mt-2">
                //                     <strong>最后检测:</strong><br>
                //                     学科: ${stats.last_detected.subject}<br>
                //                     置信度: ${(stats.last_detected.confidence * 100).toFixed(1)}%<br>
                //                     时间: ${new Date(stats.last_detected.timestamp).toLocaleString()}
                //                 </div>
                //             ` : ''}
                //         </div>
                //     </div>
                // `;


                subjectStatsContainer.innerHTML = `
                    <div class="card">
                        <div class="card-body">
                            ${stats.last_detected ? `
                                <div class="mt-2">
                                    <strong>最后检测:</strong><br>
                                    学科: ${stats.last_detected.subject}<br>
                                    置信度: ${(stats.last_detected.confidence * 100).toFixed(1)}%<br>
                                    时间: ${new Date(stats.last_detected.timestamp).toLocaleString()}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            } else {
                subjectStatsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> 获取统计失败: ${stats.message || '未知错误'}
                    </div>
                `;
            }
        }

        subjectCaptureBtn.addEventListener('click', async () => {
            try {
                console.log("开始检测学科拍照...")
                // 创建canvas并绘制当前视频帧
                const canvas = document.createElement('canvas');
                canvas.width = subjectWebcam.videoWidth;
                canvas.height = subjectWebcam.videoHeight;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(subjectWebcam, 0, 0);

                // 转换为blob
                canvas.toBlob(async (blob) => {
                    const formData = new FormData();
                    formData.append('image', blob, `capture_${Date.now()}.jpg`);

                    // 显示加载状态
                    subjectResultContainer.innerHTML = `
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">正在检测...</span>
                                
                            </div>
                            <p class="mt-2">正在检测学科...</p>
                        </div>
                    `;

                    try {
                        const response = await fetch('/api/subject/detect', {
                            method: 'POST',
                            body: formData
                        });

                        const result = await response.json();
                        displaySubjectResult(result);

                        // 更新统计
                        updateSubjectStats();
                    } catch (err) {
                        console.error('检测学科失败:', err);
                        subjectResultContainer.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i> 检测失败: ${err.message}
                            </div>
                        `;
                    }
                }, 'image/jpeg', 0.9);
            } catch (err) {
                console.error('拍照失败:', err);
                subjectResultContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> 拍照失败: ${err.message}
                    </div>
                `;
            }
        });


        //利用拍照生成任务
        taskGenerateBtn.addEventListener('click', async () => {

            const childId = parseInt(childIdInput.value);

            if (!childId || childId <= 0) {
                showtmpMessage('请输入有效的小孩ID', 'error');
                return;
            }

            if (!capturedTaskImage.src) {
                showtmpMessage('请先拍照', 'error');
                return;
            }

            try {
                submitImageTaskBtn.disabled = true;
                submitImageTaskBtn.innerHTML = '<span class="loading-spinner"></span>提交中...';

                // 将图像转换为blob
                imageTaskCanvas.toBlob(async (blob) => {
                    const formData = new FormData();
                    formData.append('child_id', childId.toString());
                    formData.append('image_file', blob, `task_image_${Date.now()}.jpg`);

                    try {
                        // 使用多模态服务处理图像
                        const response = await fetch(`http://localhost:${port}/api/v1/multimodal-task-input/imageforChange`, {
                            method: 'POST',
                            body: formData
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const result = await response.json();
                        console.log('图像添加任务提交结果:', result);

                        console.log("子任务：", result.tasks[0].description)

                        // 获取输入框元素
                        const newSubTaskInput = document.getElementById('newSubTaskInput');

                        // 将子任务内容赋值给输入框
                        if (newSubTaskInput) {
                            newSubTaskInput.value = result.tasks[0].description;
                        }

                        if (result.success) {
                            showtmpMessage('图像添加任务提交成功！', 'success');
                            // 重置界面
                            capturedTaskImage.style.display = 'none';
                            capturedTaskImage.src = '';
                            captureImageTaskBtn.disabled = false;
                        } else {
                            showtmpMessage(`图像添加任务提交失败: ${result.message}`, 'error');
                        }

                    } catch (error) {
                        console.error('图像添加任务提交失败:', error);
                        showtmpMessage(`图像添加任务提交失败: ${error.message}`, 'error');
                    } finally {
                        submitImageTaskBtn.disabled = false;
                        submitImageTaskBtn.innerHTML = '提交图像添加任务';
                    }
                }, 'image/jpeg', 0.9);

            } catch (error) {
                console.error('图像任务添加提交失败:', error);
                showtmpMessage(`图像任务添加提交失败: ${error.message}`, 'error');
                submitImageTaskBtn.disabled = false;
                submitImageTaskBtn.innerHTML = '提交图像添加任务';
            }

        });
        //每 30 秒触发一次按钮点击事件
        // setInterval(() => {
        //     subjectCaptureBtn.click();
        // }, 3000000);

        // 桌面摄像头启动功能
        startDesktopCameraBtn.addEventListener('click', async () => {
            try {
                desktopCameraStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });
                desktopWebcam.srcObject = desktopCameraStream;
                startDesktopCameraBtn.disabled = true;
                desktopDetectBtn.disabled = false;
                startDesktopCameraBtn.innerHTML = '<span>✅</span> 桌面摄像头已启动';
                showtmpMessage('桌面摄像头启动成功！', 'success');
            } catch (error) {
                console.error('启动桌面摄像头失败:', error);
                showtmpMessage(`启动桌面摄像头失败: ${error.message}`, 'error');
            }
        });

        // 坐姿摄像头启动功能
        startPostureCameraBtn.addEventListener('click', async () => {
            try {
                postureCameraStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });
                postureWebcam.srcObject = postureCameraStream;
                startPostureCameraBtn.disabled = true;
                postureDetectBtn.disabled = false;
                startPostureCameraBtn.innerHTML = '<span>✅</span> 坐姿摄像头已启动';
                showtmpMessage('坐姿摄像头启动成功！', 'success');
            } catch (error) {
                console.error('启动坐姿摄像头失败:', error);
                showtmpMessage(`启动坐姿摄像头失败: ${error.message}`, 'error');
            }
        });

        // 学习状态映射函数
        function getLearningStateStatus(state) {
            const stateMap = {
                '专注学习': { class: 'status-excellent' },
                '一般专注': { class: 'status-good' },
                '轻微分心': { class: 'status-fair' },
                '严重分心': { class: 'status-poor' },
                '疲劳状态': { class: 'status-poor' },
                '认知过载': { class: 'status-poor' }
            };
            return stateMap[state] || { class: 'status-fair' };
        }

        // 桌面检测功能
        desktopDetectBtn.addEventListener('click', async () => {
            try {
                console.log("开始桌面环境检测...")

                // 检查是否有摄像头流
                if (!desktopWebcam.srcObject) {
                    showtmpMessage('请先启动桌面摄像头', 'error');
                    return;
                }

                // 创建canvas并绘制当前视频帧
                const canvas = document.createElement('canvas');
                canvas.width = desktopWebcam.videoWidth;
                canvas.height = desktopWebcam.videoHeight;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(desktopWebcam, 0, 0);

                // 获取图像数据
                const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9);
                const childAge = parseInt(childIdInput.value) || 8;

                // 显示桌面检测加载状态
                const desktopResultDiv = document.getElementById('desktop-result');

                desktopResultDiv.innerHTML = `
                    <div class="detection-header">
                        <div class="detection-icon">📚</div>
                        <h5 class="detection-title">桌面环境检测</h5>
                    </div>
                    <div class="loading-detection">
                        <div class="loading-spinner-large"></div>
                        <div class="loading-text">正在分析桌面环境</div>
                        <div class="loading-subtext">请保持桌面清晰可见...</div>
                    </div>
                `;

                // 调用桌面检测API
                const response = await fetch('/api/v1/detection/desktop-detection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image: imageDataUrl
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('桌面检测结果:', result);

                if (result.success) {
                    const tidinessScore = result.overall_score || 0;
                    const tidinessStatus = tidinessScore >= 80 ? 'status-excellent' :
                                         tidinessScore >= 60 ? 'status-good' :
                                         tidinessScore >= 40 ? 'status-fair' : 'status-poor';

                    desktopResultDiv.innerHTML = `
                        <div class="detection-header">
                            <div class="detection-icon">📚</div>
                            <h5 class="detection-title">桌面环境检测结果</h5>
                        </div>
                        <div class="score-display">
                            <div>
                                <div class="score-number">${tidinessScore.toFixed(1)}</div>
                                <div class="score-label">整洁度评分</div>
                            </div>
                            <div class="progress-ring">
                                <svg width="120" height="120">
                                    <circle class="progress-ring-circle progress-ring-bg" cx="60" cy="60" r="45"></circle>
                                    <circle class="progress-ring-circle progress-ring-progress" cx="60" cy="60" r="45"
                                            style="stroke-dashoffset: ${283 - (283 * tidinessScore / 100)};"></circle>
                                </svg>
                            </div>
                        </div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">整洁等级</div>
                                <div class="info-value">
                                    <span class="status-badge ${tidinessStatus}">${result.quality_level || '未知'}</span>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">检测物品</div>
                                <div class="info-value">${result.objects_count || 0} 个物品</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">学习用品</div>
                                <div class="info-value">${result.learning_objects || 0}/${result.total_objects || 0}</div>
                            </div>
                        </div>
                        ${result.suggestions && result.suggestions.length > 0 ? `
                            <div class="suggestions-container">
                                <div class="suggestions-title">整理建议</div>
                                ${result.suggestions.map(suggestion => `
                                    <div class="suggestion-item">${suggestion}</div>
                                `).join('')}
                            </div>
                        ` : ''}
                        ${result.annotated_image ? `
                            <div style="margin-top: 20px; text-align: center;">
                                <h6>检测结果可视化</h6>
                                <img src="${result.annotated_image}" style="max-width: 100%; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" alt="桌面检测结果">
                            </div>
                        ` : ''}
                    `;

                    showtmpMessage('📚 桌面环境检测完成！', 'success');

                } else {
                    throw new Error(result.message || '桌面检测失败');
                }

            } catch (error) {
                console.error('桌面检测失败:', error);
                document.getElementById('desktop-result').innerHTML = `
                    <div class="detection-header">
                        <div class="detection-icon">📚</div>
                        <h5 class="detection-title">桌面环境检测</h5>
                    </div>
                    <div class="alerts-container">
                        <div class="alert-item">检测失败: ${error.message}</div>
                        <div class="suggestion-item" style="margin-top: 10px;">
                            💡 请检查摄像头是否正常工作，然后重试
                        </div>
                    </div>
                `;
                showtmpMessage(`桌面检测失败: ${error.message}`, 'error');
            }
        });

        // 坐姿专注检测功能
        postureDetectBtn.addEventListener('click', async () => {
            try {
                console.log("开始坐姿专注检测...")

                // 检查是否有摄像头流
                if (!postureWebcam.srcObject) {
                    showtmpMessage('请先启动坐姿摄像头', 'error');
                    return;
                }

                // 创建canvas并绘制当前视频帧
                const canvas = document.createElement('canvas');
                canvas.width = postureWebcam.videoWidth;
                canvas.height = postureWebcam.videoHeight;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(postureWebcam, 0, 0);

                // 获取图像数据
                const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9);
                const childAge = parseInt(childIdInput.value) || 8;

                // 显示坐姿检测加载状态
                const postureResultDiv = document.getElementById('posture-result');

                postureResultDiv.innerHTML = `
                    <div class="detection-header">
                        <div class="detection-icon">🧘</div>
                        <h5 class="detection-title">坐姿与专注度检测</h5>
                    </div>
                    <div class="loading-detection">
                        <div class="loading-spinner-large"></div>
                        <div class="loading-text">正在分析坐姿状态</div>
                        <div class="loading-subtext">请保持正常坐姿...</div>
                    </div>
                `;

                // 调用坐姿检测API
                const response = await fetch('/api/v1/detection/posture-detection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image: imageDataUrl
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('坐姿检测结果:', result);

                if (result.success) {
                    // 从result中获取数据
                    const resultData = result.result || {};
                    const analysisData = result.analysis || {};

                    // 创建精美的坐姿检测结果显示函数（只显示三个框：Status、Message、Is Good Posture）
                    function createPostureDisplay(data, title) {
                        if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
                            return '';
                        }

                        // 从数据中提取关键信息
                        let status = '检测完成';
                        let message = '请保持良好坐姿';
                        let isGoodPosture = false;

                        // 分析数据并提取状态信息
                        Object.entries(data).forEach(([key, value]) => {
                            if (key.toLowerCase().includes('status') || key.toLowerCase().includes('state')) {
                                status = value || '检测完成';
                            } else if (key.toLowerCase().includes('message') || key.toLowerCase().includes('msg')) {
                                message = value || '请保持良好坐姿';
                            } else if (key.toLowerCase().includes('good') || key.toLowerCase().includes('posture')) {
                                isGoodPosture = Boolean(value);
                            } else if (typeof value === 'object' && value !== null) {
                                // 从详细数据中分析整体状态
                                const analyses = [];
                                for (const [objKey, objValue] of Object.entries(value)) {
                                    if (objKey === 'head_analysis' && objValue === true) {
                                        analyses.push('头部');
                                    }
                                    if (objKey === 'shoulder_analysis' && objValue === true) {
                                        analyses.push('肩膀');
                                    }
                                    if (objKey === 'body_analysis' && objValue === true) {
                                        analyses.push('身体');
                                    }
                                    if (objKey === 'issues' && Array.isArray(objValue)) {
                                        if (objValue.length === 0) {
                                            isGoodPosture = true;
                                            message = '坐姿良好，继续保持';
                                        } else {
                                            isGoodPosture = false;
                                            const issueMap = {
                                                'shoulders_uneven': '肩膀不平衡',
                                                'head_tilt': '头部倾斜',
                                                'body_lean': '身体倾斜'
                                            };
                                            const issues = objValue.map(issue => issueMap[issue] || issue);
                                            message = `需要调整：${issues.join('、')}`;
                                        }
                                    }
                                }
                                if (analyses.length >= 2) {
                                    status = `${analyses.join('、')}姿态良好`;
                                }
                            }
                        });

                        return `
                            <div class="posture-result-container">
                                <div class="posture-header">
                                    <div class="posture-icon">🧘</div>
                                    <h4 class="posture-title">坐姿检测结果</h4>
                                </div>

                                <div class="posture-cards-grid">
                                    <!-- Status 框 -->
                                    <div class="posture-card status-card">
                                        <div class="card-header">
                                            <div class="card-icon">📊</div>
                                            <h5>Status</h5>
                                        </div>
                                        <div class="card-content">
                                            <div class="status-text">${status}</div>
                                        </div>
                                    </div>

                                    <!-- Message 框 -->
                                    <div class="posture-card message-card">
                                        <div class="card-header">
                                            <div class="card-icon">💬</div>
                                            <h5>Message</h5>
                                        </div>
                                        <div class="card-content">
                                            <div class="message-text">${message}</div>
                                        </div>
                                    </div>

                                    <!-- Is Good Posture 框 -->
                                    <div class="posture-card posture-status-card ${isGoodPosture ? 'good-posture' : 'bad-posture'}">
                                        <div class="card-header">
                                            <div class="card-icon">${isGoodPosture ? '✅' : '❌'}</div>
                                            <h5>Is Good Posture</h5>
                                        </div>
                                        <div class="card-content">
                                            <div class="posture-status-icon">${isGoodPosture ? '✅' : '❌'}</div>
                                            <div class="posture-status-text">${isGoodPosture ? '良好' : '需要改善'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    // 构建完整的结果显示
                    let resultHTML = '';

                    // 主要检测结果
                    if (Object.keys(resultData).length > 0) {
                        resultHTML += createPostureDisplay(resultData, "坐姿分析结果");
                    }

                    // 可视化图像
                    if (result.visualization_image) {
                        resultHTML += `
                            <div class="posture-visualization">
                                <h6>🎯 坐姿检测可视化</h6>
                                <img src="${result.visualization_image}" alt="坐姿检测结果">
                            </div>
                        `;
                    }

                    // 如果没有任何数据，显示提示
                    if (!resultHTML) {
                        resultHTML = `
                            <div class="posture-detection-container">
                                <div class="posture-section-title">坐姿检测结果</div>
                                <div class="posture-stats-summary">
                                    <div style="text-align: center; color: white; padding: 20px;">
                                        <div style="font-size: 3em; margin-bottom: 10px;">🤔</div>
                                        <div>检测完成，但未获取到详细数据</div>
                                        <div style="opacity: 0.8; margin-top: 5px;">请确保姿态清晰可见</div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    postureResultDiv.innerHTML = resultHTML;
                    showtmpMessage('🧘 坐姿检测完成！', 'success');

                } else {
                    throw new Error(result.message || '坐姿检测失败');
                }

            } catch (error) {
                console.error('坐姿检测失败:', error);
                document.getElementById('posture-result').innerHTML = `
                    <div class="detection-header">
                        <div class="detection-icon">🧘</div>
                        <h5 class="detection-title">坐姿与专注度检测</h5>
                    </div>
                    <div class="alerts-container">
                        <div class="alert-item">检测失败: ${error.message}</div>
                        <div class="suggestion-item" style="margin-top: 10px;">
                            💡 请确保您在摄像头视野范围内，然后重试
                        </div>
                    </div>
                `;
                showtmpMessage(`坐姿检测失败: ${error.message}`, 'error');
            }
        });

        // 添加子任务

        checkTaskButton.addEventListener('click', async () => {
            try {
                // 显示消息框
                envCheckingModal.style.display = 'block';
                // 模拟点击开启摄像头按钮
                await simulateStartCamera();
                // 模拟点击拍照按钮
                await simulateCapture();
                // 模拟点击分析按钮
                await simulateAnalyze();

                const response = await fetch('/api/check_task_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    // body: JSON.stringify({
                    //     image: capturedImage
                    // })
                });

                // 读取响应体为文本
                const responseText = await response.text();

                // console.log('服务器桌面检测接口 /api/check_task_data 响应内容:', responseText);


            } catch (error) {
                console.error('任务确认执行过程中出错:', error);
                showNotification(`任务确认执行过程中出错: ${error.message}`, 5000);
            }
        });

        checkEnvButton.addEventListener('click', async () => {
            try {

                const response = await fetch('/api/check_env_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    // body: JSON.stringify({
                    //     image: capturedImage
                    // })
                });

                // 读取响应体为文本
                const responseText = await response.text();

                console.log('服务器桌面检测接口 /api/check_env_data 响应内容:', responseText);


            } catch (error) {
                console.error('任务确认执行过程中出错:', error);
                showNotification(`任务确认执行过程中出错: ${error.message}`, 5000);
            }
        });

        // setInterval(() => {
        //     getTargetSubjet();
        // }, 3000);
        async function getTargetSubjet() {
            try {

                const response = await fetch('/api/getTargetSubjet', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },

                });

                const result = await response.json();

                targetTaskName = result.targetSubjetName

                console.log('服务器桌面检测接口 /api/getTargetSubjet 响应内容:', result);
                showSubjectCheckModal();

            } catch (error) {
                console.error('任务确认执行过程中出错:', error);
                showNotification(`任务确认执行过程中出错: ${error.message}`, 5000);
            }
        }


        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function () {
            initSubjectDetection();
            connectService();
            populateTaskNameSelect();
            setTimeout(function () {
                console.log('🎵 页面加载完成，播放欢迎语音');
                showtmpMessage('🎵 页面加载完成，播放欢迎语音', 'success');

                // 发送TTS请求到后端
                fetch(`http://localhost:${port}/api/v1/tts/play`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: '我们一起开始今日作业吧',
                        return_audio: true,
                        async_mode: false
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        console.log('页面加载TTS请求响应:', data);
                        if (data.success) {
                            showtmpMessage('🎵 欢迎语音播放请求已发送', 'success');

                            // 如果返回了音频数据，直接播放
                            if (data.audio_data) {
                                console.log('🎵 收到音频数据，开始播放');
                                playAudioFromBase64(data.audio_data, data.message || '我们一起开始今日作业吧', 'mp3');
                            }

                            // 如果有音频URL，也可以播放
                            if (data.audio_url) {
                                console.log('🎵 收到音频URL:', data.audio_url);
                                const audio = new Audio(`http://localhost:${port}${data.audio_url}`);
                                audio.play().catch(error => {
                                    console.error('播放音频URL失败:', error);
                                });
                            }
                        } else {
                            console.error('页面加载TTS请求失败:', data.message);
                        }
                    })
                    .catch(error => {
                        console.error('页面加载TTS请求错误:', error);
                    });
            }, 2000); // 延迟2秒播放，确保页面完全加载
            // 调用填充任务名称下拉框的函数

        });


        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function () {
            if (isRecording) {
                stopRecognition();
            }
            if (socket) {
                socket.disconnect();
            }
        });


        async function simulateStartCamera() {
            if (startCameraButton.disabled) {
                return;
            }
            await startCameraButton.click();
            // 等待摄像头启动，可根据实际情况调整等待时间
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        async function simulateCapture() {
            if (captureButton.disabled) {
                throw new Error('拍照按钮不可用，请先启动摄像头');
            }
            await captureButton.click();
            // 等待拍照完成，可根据实际情况调整等待时间
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        async function simulateAnalyze() {
            if (analyzeButton.disabled) {
                throw new Error('分析按钮不可用，请先拍照');
            }
            await analyzeButton.click();
        }

        // 摄像头相关函数
        startCameraButton.addEventListener('click', async () => {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                video.srcObject = stream;
                captureButton.disabled = false;
                startCameraButton.disabled = true;
            } catch (error) {
                console.error('无法访问摄像头:', error);
                showNotification(`无法访问摄像头: ${error.message}`, 5000);
            }
        });

        captureButton.addEventListener('click', () => {
            const context = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);
            capturedImage = canvas.toDataURL('image/jpeg', 0.8);
            document.getElementById('original-image').src = capturedImage;
            document.getElementById('original-image').style.display = 'block';
            document.querySelector('#original-image-container .placeholder-text').style.display = 'none';
            analyzeButton.disabled = false;
            showNotification('拍照成功！', 3000);
        });

        analyzeButton.addEventListener('click', async () => {
            if (!capturedImage) {
                showNotification('请先拍照', 3000);
                return;
            }

            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image: capturedImage
                    })
                });

                // 直接解析响应为 JSON 对象
                const analysisResult = await response.json();

                console.log('服务器桌面检测接口 /api/analyze 响应内容:', analysisResult);
                console.log('整洁度评分: analysisResult.tidiness_score')
                console.log(analysisResult.tidiness_score)

                console.log('整洁等级: ${analysisResult.tidiness_level}')
                console.log(analysisResult.tidiness_level)

                console.log('整洁建议: ${analysisResult.tidiness_suggestion}')
                console.log(analysisResult.tidiness_suggestion)


                // 将整洁度评分和等级赋值到 deskTidiness 文本框
                const deskTidinessTextarea = document.getElementById('deskTidiness');
                deskTidinessTextarea.value = `整洁度评分: ${analysisResult.tidiness_score}   整洁等级: ${analysisResult.tidiness_level}`;

                // 将建议赋值到 deskSuggestion 文本框
                const deskSuggestionTextarea = document.getElementById('deskSuggestion');
                deskSuggestionTextarea.value = analysisResult.tidiness_suggestion;

                console.log('分析完成');
                // 隐藏消息框
                envCheckingModal.style.display = 'none';
            } catch (error) {
                console.error('分析图像时出错:', error);
                console.log(`分析失败`);
            }
        });

        function showNotification(message, duration) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.style.display = 'block';
            setTimeout(() => {
                notification.style.display = 'none';
            }, duration);
        }

        // 原有语音识别相关代码保持不变
        // DOM元素
        const connectBtn = document.getElementById('connectBtn');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('status');
        const statusMessage = document.getElementById('statusMessage');
        const micIndicator = document.getElementById('micIndicator');
        const resultsList = document.getElementById('resultsList');
        const connectionStatus = document.getElementById('connectionStatus');
        const silenceStatus = document.getElementById('silenceStatus');
        const silenceCountdown = document.getElementById('silenceCountdown');
        const analyzingModal = document.getElementById('analyzingModal');
        const analysisResultModal = document.getElementById('analysisResultModal');
        const resultContent = document.getElementById('resultContent');

        // 显示正在分析中弹窗
        function showAnalyzingModal() {
            analyzingModal.style.display = 'block';
        }

        // 隐藏正在分析中弹窗
        function hideAnalyzingModal() {
            analyzingModal.style.display = 'none';
        }

        // 显示分析结果弹窗
        function showAnalysisResultModal(result) {
            resultContent.textContent = result;
            analysisResultModal.style.display = 'block';
        }

        // 关闭分析结果弹窗
        function closeAnalysisResultModal() {
            analysisResultModal.style.display = 'none';
        }
        // Socket事件监听
        // 监听 current_subject 事件
        socket.on('current_subject', function (data) {
            currentSubject = data;
        });

        socket.on('processNum', function (data) {
            console.log("获取的processNum")
            processNum = data;
            // 根据 processNum 的值调用对应的函数
            console.log("获取的processNum", processNum)
            switch (processNum) {
                case 0:
                    console.log("传输0，")
                    break;
                case 1:

                    break;
                case 2:
                    console.log("传输2，点击确认任务")
                    checkTaskButton.click();
                    break;
                case 3:
                    console.log("传输3，点击桌面清理")
                    checkEnvButton.click();
                    break;
                case 4:
                    // handleProcessNum4();
                    break;
                case 5:
                    finishTaskBtn.click();
                    break;
                case 6:
                    summaryBtn().click();
                    break;
                default:
                    console.warn('收到的 processNum 不在 0 到 6 的范围内:', processNum);
            }
        });

        // 监听 target_task_name 事件
        // socket.on('target_task_name', function(data) {
        //     targetTaskName = data;
        // });
        socket.on('connect', function () {
            console.log('✅ 已连接到服务器');
            updateConnectionStatus(true);
            updateStatus('已连接到服务器', 'connected');
            updateStatusMessage('请点击"连接服务"按钮连接到语音识别服务');

            // 重连后重新连接ASR服务
            if (isConnected) {
                console.log('🔄 检测到重连，重新连接ASR服务...');
                setTimeout(() => {
                    connectService();
                }, 1000);
            }
        });

        socket.on('disconnect', function (reason) {
            console.log('❌ 与服务器断开连接，原因:', reason);
            updateConnectionStatus(false);
            updateStatus('与服务器断开连接', 'inactive');
            updateStatusMessage('正在尝试重新连接...');
            resetButtons();
        });

        socket.on('reconnect', function (attemptNumber) {
            console.log('🔄 Socket.IO重连成功，尝试次数:', attemptNumber);
            updateStatus('重连成功', 'connected');
            updateStatusMessage('连接已恢复，请重新连接语音服务');
        });

        socket.on('reconnect_attempt', function (attemptNumber) {
            console.log('🔄 Socket.IO重连尝试:', attemptNumber);
            updateStatus('正在重连...', 'inactive');
            updateStatusMessage(`重连尝试 ${attemptNumber}/10...`);
        });

        socket.on('reconnect_error', function (error) {
            console.error('❌ Socket.IO重连失败:', error);
            updateStatus('重连失败', 'inactive');
            updateStatusMessage('重连失败，请刷新页面');
        });

        socket.on('reconnect_failed', function () {
            console.error('❌ Socket.IO重连彻底失败');
            updateStatus('连接失败', 'inactive');
            updateStatusMessage('无法连接到服务器，请刷新页面');
        });

        socket.on('status', function (data) {
            console.log('状态更新:', data);
            updateStatusMessage(data.message);
        });

        socket.on('error', function (data) {
            console.error('语音识别错误:', data.message);
            showtmpMessage(data.message, 'error');

            // 重置所有状态
            isRecording = false;
            isVoiceTaskRecording = false;

            // 停止音频处理
            stopAudioProcessing();

            // 重置按钮状态
            resetButtons();

            // 重置语音任务按钮
            if (startVoiceTaskInputBtn) startVoiceTaskInputBtn.disabled = false;
            if (stopVoiceTaskInputBtn) stopVoiceTaskInputBtn.disabled = true;

            console.log('错误处理完成，状态已重置');
        });


        socket.on('targetSubjetName', function (data) {
            targetTaskName = data;
        });
        socket.on('recognition_started', function (data) {
            console.log('语音识别已开始:', data.message);
            isRecording = true;
            updateStatus('正在录音...', 'recording');
            updateStatusMessage('请说话，系统正在识别您的语音');
            micIndicator.classList.add('recording');

            startBtn.disabled = true;
            stopBtn.disabled = false;
            connectBtn.disabled = true;

            // 显示静音检测状态
            if (data.silence_timeout) {
                silenceStatus.style.display = 'block';
                startSilenceCountdown(data.silence_timeout);
            }

             // 显示录音状态标志
            const recordingStatus = document.getElementById('recordingStatus');
            if (recordingStatus) {
                recordingStatus.style.display = 'block';
            }

            // showtmpMessage('语音识别已开始，将在4秒静音后自动停止', 'success');
        });

        socket.on('recognition_stopped', function (data) {
            // console.log('✅ 语音识别已停止:', data.message);

            // 重置所有录音相关状态
            isRecording = false;
            isVoiceTaskRecording = false;

            updateStatus('识别已停止', 'connected');
            updateStatusMessage('语音识别已停止，准备自动重新开始...');
            micIndicator.classList.remove('recording');

            // 重置按钮状态，确保可以重新开始
            startBtn.disabled = false;
            stopBtn.disabled = true;
            connectBtn.disabled = false;

            // 重置语音任务相关按钮
            if (startVoiceTaskInputBtn) startVoiceTaskInputBtn.disabled = false;
            if (stopVoiceTaskInputBtn) stopVoiceTaskInputBtn.disabled = true;

            // 停止音频处理
            stopAudioProcessing();

            // 隐藏录音状态标志
            const recordingStatus = document.getElementById('recordingStatus');
            if (recordingStatus) {
                recordingStatus.style.display = 'none';
            }

            // console.log('🔄 按钮状态已重置，准备自动重新开始识别');
            // showtmpMessage('语音识别已停止，3秒后自动重新开始', 'info');

            // 自动重新开始识别（等待3秒让ASR服务完全重连）
            setTimeout(() => {
                console.log('🔄 自动重新开始语音识别...');
                startRecognition().then(() => {
                    // console.log('✅ 自动重新开始识别成功');
                    // showtmpMessage('第二次语音识别已自动开始', 'success');
                }).catch(error => {
                    console.error('❌ 自动重新开始识别失败:', error);
                    // showtmpMessage('自动重新开始失败，请手动点击开始', 'error');
                });
            }, 3000); // 等待3秒
        });

        socket.on('recognition_result', function (data) {
            console.log('🎤 识别结果:', data);
            console.log('📊 当前语音任务录制状态:', isVoiceTaskRecording);
            console.log('📊 当前普通录音状态:', isRecording);
            // 获取子任务输入框元素
            const newSubTaskInput = document.getElementById('newSubTaskInput');


            // 如果是语音任务输入模式，更新到语音任务结果框
            if (isVoiceTaskRecording) {
                console.log('🎯 语音任务模式 - 处理识别结果:', data.text, 'is_final:', data.is_final);

                // 无论是临时结果还是最终结果，都显示在语音任务框中
                if (data.text && data.text.trim()) {
                    if (data.is_final) {
                        // 最终结果，追加到文本框
                        const currentText = voiceTaskResultArea.value;
                        const newText = currentText + (currentText ? ' ' : '') + data.text;
                        voiceTaskResultArea.value = newText;
                        newSubTaskInput.value = newText;
                        console.log('✅ 语音任务最终结果已更新:', newText);

                        // 启用提交按钮
                        if (newText.trim()) {
                            submitVoiceTaskBtn.disabled = false;
                            console.log('✅ 语音任务提交按钮已启用');
                        }
                    } else {
                        // 临时结果，也实时显示在文本框中
                        const currentText = voiceTaskResultArea.value;
                        // 移除之前的临时结果（如果有的话）
                        const finalText = currentText.split('[临时]')[0];
                        const newText = finalText + (finalText ? ' ' : '') + '[临时] ' + data.text;
                        voiceTaskResultArea.value = newText;
                        newSubTaskInput.value = newText;
                        console.log('🔄 语音任务临时结果已更新:', newText);
                    }
                }
            } else {
                // 正常的语音识别结果显示
                // console.log('📝 普通语音识别模式 - 显示结果');
                addResult(data.text, data.is_final, data.timestamp);
            }

            if (data.is_final) {
                // 处理最终结果
                addResult(data.text, data.is_final, data.timestamp);
                
                // 检查是否需要重置状态
                // 例如，检查录音状态变量
                if (isRecording) {
                    // 这里可以根据实际情况决定是否需要停止并重新开始识别
                    // 示例：停止当前识别并重新开始
                    stopRecognition().then(() => {
                        startRecognition();
                    }).catch(error => {
                        console.error('重新启动识别失败:', error);
                    });
                }
            }
        });

        // NEW: Handle OpenManus responses (wxysmart-compatible)
        socket.on('openmanus_response', function (data) {
            console.log('🧠 OpenManus响应:', data);

            // Display OpenManus response in the chat area
            if (data.success && data.message) {
                // Create a new message element for OpenManus response
                const messageDiv = document.createElement('div');
                messageDiv.className = 'openmanus-response';
                messageDiv.style.cssText = `
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px;
                    margin: 10px 0;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                `;

                // Add intent information
                const intentInfo = data.intent_info || {};
                const intentBadge = `
                    <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">
                        🎯 ${intentInfo.intent || 'unknown'} (${(intentInfo.confidence || 0).toFixed(2)})
                    </span>
                `;

                // Add performance info
                const perfInfo = data.performance_info || {};
                const perfBadge = `
                    <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                        ⚡ ${(perfInfo.processing_duration || 0).toFixed(2)}s
                    </span>
                `;

                messageDiv.innerHTML = `
                    <div style="margin-bottom: 8px;">
                        ${intentBadge}
                        ${perfBadge}
                    </div>
                    <div style="font-size: 14px; line-height: 1.4;">
                        ${data.message.replace(/\n/g, '<br>')}
                    </div>
                    <div style="font-size: 11px; opacity: 0.8; margin-top: 8px;">
                        OpenManus Enhanced • ${new Date(data.timestamp).toLocaleTimeString()}
                    </div>
                `;

                // Add to results area
                const resultsDiv = document.getElementById('results');
                if (resultsDiv) {
                    resultsDiv.appendChild(messageDiv);
                    resultsDiv.scrollTop = resultsDiv.scrollHeight;
                }

                // Show success notification
                showtmpMessage(`OpenManus处理完成 - ${intentInfo.intent || '未知意图'}`, 'success');

                // Optional: Trigger TTS for the response
                if (data.message && data.message.length < 500) { // Only for shorter responses
                    // You can add TTS call here if needed
                    // playTTS(data.message);
                }
            } else {
                // Handle error response
                console.error('OpenManus处理失败:', data.error || 'Unknown error');
                showtmpMessage('OpenManus处理失败，请重试', 'error');
            }
        });

        socket.on('task_created', function (data) {
            console.log('任务创建结果:', data);
            showtmpMessage('语音大模型已处理，任务已创建！', 'success');

            // 添加任务创建结果到显示区域
            addTaskResult(data.voice_result, data.daily_task, data.timestamp);
        });

        socket.on('recognition_restarted', function (data) {
            console.log('语音识别已重启:', data.message);
            isRecording = true;
            updateStatus('正在录音...', 'recording');
            updateStatusMessage('语音识别已自动重启，请说话');
            micIndicator.classList.add('recording');

            // 在自动模式下不需要手动控制按钮
            startBtn.disabled = true;
            stopBtn.disabled = true;
            connectBtn.disabled = true;

            // 显示静音检测状态
            if (data.silence_timeout) {
                silenceStatus.style.display = 'block';
                startSilenceCountdown(data.silence_timeout);
            }

            // showtmpMessage('语音识别已自动重启', 'success');
        });

        socket.on('connection_confirmed', function (data) {
            console.log('✅ 后端连接确认:', data.message);
            showtmpMessage('音频传输通道已建立', 'success');
        });
        // 显示科目检测弹窗
        function showSubjectCheckModal() {
            const currentSubjectText = document.getElementById('currentSubjectText');
            const targetTaskNameText = document.getElementById('targetTaskNameText');
            currentSubjectText.textContent = `检测的科目: ${currentSubject}`;
            targetTaskNameText.textContent = `计划的科目: ${targetTaskName}`;
            document.getElementById('subjectCheckModal').style.display = 'none';
        }

        // 关闭科目检测弹窗
        function closeSubjectCheckModal() {
            document.getElementById('subjectCheckModal').style.display = 'none';
        }

        // 连接到语音识别服务
        function connectService() {
            console.log('连接到语音识别服务...');
            updateStatus('正在连接...', 'inactive');
            updateStatusMessage('正在快速连接到语音识别服务...');

            // 记录连接开始时间
            const connectStartTime = Date.now();

            return fetch('/api/v1/asr/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 计算连接时间
                        const connectTime = Date.now() - connectStartTime;
                        const connectionType = data.connection_type || '标准连接';

                        updateStatus('服务已连接', 'connected');
                        updateStatusMessage(`语音识别服务已连接 (${connectionType}, ${connectTime}ms)`);

                        // 在自动模式下隐藏手动控制按钮
                        if (startBtn) startBtn.style.display = 'none';
                        if (stopBtn) stopBtn.style.display = 'none';
                        if (connectBtn) {
                            connectBtn.textContent = '🔗 断开服务';
                            connectBtn.onclick = disconnectService;
                        }
                        // 显示语音交互按钮
                        const voiceInteractBtn = document.getElementById('voiceInteractBtn');
                        if (voiceInteractBtn) voiceInteractBtn.style.display = 'block';

                        // 显示连接统计信息
                        let message = `成功连接到语音识别服务 (${connectionType}, ${connectTime}ms)`;
                        if (data.connection_stats && data.connection_stats.success_rate) {
                            message += ` - 成功率: ${data.connection_stats.success_rate.toFixed(1)}%`;
                        }
                        showtmpMessage(message, 'success');

                        console.log('服务连接成功:', data);
                        return true;
                    } else {
                        updateStatus('连接失败', 'inactive');
                        updateStatusMessage(data.message);
                        showtmpMessage('连接失败: ' + data.message, 'error');
                        throw new Error(data.message);
                    }
                })
                .catch(error => {
                    console.error('连接错误:', error);
                    updateStatus('连接错误', 'inactive');
                    updateStatusMessage('连接时发生错误');
                    showtmpMessage('连接时发生错误: ' + error.message, 'error');
                    throw error;
                });
        }

        // 自动启动语音识别（用于自动模式）
        function autoStartRecognition() {
            console.log('🎤 开始自动启动语音识别...');

            // 检查浏览器支持
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                console.error('❌ 浏览器不支持音频API');
                showtmpMessage('浏览器不支持音频API，请使用现代浏览器', 'error');
                return;
            }

            // 请求麦克风权限并开始录音
            navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            })
                .then(stream => {
                    console.log('✅ 获得麦克风权限，开始设置音频处理...');

                    // 先设置音频处理
                    setupAudioProcessing(stream);

                    // 等待音频处理设置完成后再启用录音
                    setTimeout(() => {
                        // 重要：设置录音状态为true，确保音频数据会被发送
                        isRecording = true;

                        console.log('✅ 音频处理已设置完成，录音已启动');
                        console.log('📊 当前状态 - 录音:', isRecording, 'Socket连接:', socket.connected);

                        // 更新状态
                        updateStatus('自动语音识别已启动', 'recording');
                        updateStatusMessage('系统正在持续监听语音输入，基于内容变化检测2秒静音');
                        micIndicator.classList.add('recording');

                        // 发送测试消息确认连接
                        if (socket && socket.connected) {
                            socket.emit('test_connection', { message: '前端音频系统已就绪' });
                            console.log('� 已发送测试连接消息');

                            // 重要：通知服务器开始语音识别
                            socket.emit('start_recognition');
                            console.log('🎤 已发送开始语音识别事件');
                        } else {
                            console.warn('⚠️ Socket未连接，无法发送测试消息');
                        }

                        // 显示成功消息
                        showtmpMessage('自动语音识别已启动，开始监听语音输入', 'success');
                    }, 500);  // 等待500ms确保音频处理器已就绪

                })
                .catch(error => {
                    console.error('❌ 无法访问麦克风:', error);
                    if (error.name === 'NotAllowedError') {
                        showtmpMessage('麦克风权限被拒绝，请在浏览器设置中允许麦克风访问', 'error');
                    } else if (error.name === 'NotFoundError') {
                        showtmpMessage('未找到麦克风设备，请检查硬件连接', 'error');
                    } else {
                        showtmpMessage('无法访问麦克风: ' + error.message, 'error');
                    }
                });
        }
        // 断开语音识别服务
        function disconnectService() {
            console.log('断开语音识别服务...');

            // 如果正在录音，先停止
            if (isRecording) {
                stopRecognition();
            }

            fetch('/api/v1/asr/disconnect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
                .then(response => response.json())
                .then(data => {
                    updateStatus('服务已断开', 'inactive');
                    updateStatusMessage('语音识别服务已断开');
                    resetButtons();
                    showtmpMessage('已断开语音识别服务', 'success');
                })
                .catch(error => {
                    console.error('断开连接错误:', error);
                    showtmpMessage('断开连接时发生错误: ' + error.message, 'error');
                });
        }

        // 开始语音识别
        function startRecognition() {
            console.log('开始语音识别...');

            // 如果已经在录音，先停止
            if (isRecording) {
                console.log('检测到已在录音，先停止当前录音...');
                stopAudioProcessing();
                isRecording = false;
            }

            // 请求麦克风权限并开始录音
            return navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            })
                .then(stream => {
                    // console.log('获得麦克风权限，准备开始识别...');

                    // 通过Socket通知服务器开始识别
                    socket.emit('start_recognition');
                    // console.log('已发送开始识别信号');

                    // 设置音频处理
                    setupAudioProcessing(stream);

                    // 设置录音状态
                    isRecording = true;
                    // console.log('录音状态已设置为true');

                    return true;
                })
                .catch(error => {
                    console.error('无法访问麦克风:', error);
                    showtmpMessage('无法访问麦克风，请检查权限设置', 'error');
                    // 确保状态重置
                    isRecording = false;
                    isVoiceTaskRecording = false;
                    throw error;
                });
        }

        // 停止语音识别
        function stopRecognition() {
            console.log('停止语音识别...');

            // 重置所有录音相关状态
            isRecording = false;
            isVoiceTaskRecording = false;

            // 停止音频处理
            stopAudioProcessing();

            // 通过Socket通知服务器停止识别
            socket.emit('stop_recognition');

            // 重置UI状态
            if (micIndicator) micIndicator.classList.remove('recording');

            // 重置按钮状态
            if (startVoiceTaskInputBtn) startVoiceTaskInputBtn.disabled = false;
            if (stopVoiceTaskInputBtn) stopVoiceTaskInputBtn.disabled = true;

            console.log('语音识别停止完成，状态已重置');
            return Promise.resolve();
        }

        // 新增：语音交互功能
        let isVoiceInteracting = false;
        let voiceInteractionAudio = null;

        // 开始语音交互
        async function startVoiceInteraction() {
            if (isVoiceInteracting) {
                console.log('语音交互正在进行中...');
                return;
            }

            try {
                isVoiceInteracting = true;
                const voiceInteractBtn = document.getElementById('voiceInteractBtn');
                voiceInteractBtn.disabled = true;
                voiceInteractBtn.innerHTML = '<span>🎤</span> 正在录音...';

                // 获取麦克风权限
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

                // 创建音频录制器
                const mediaRecorder = new MediaRecorder(stream);
                const audioChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = async () => {
                    // 停止所有音频轨道
                    stream.getTracks().forEach(track => track.stop());

                    // 创建音频blob
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });

                    // 转换为base64
                    const reader = new FileReader();
                    reader.onloadend = async () => {
                        const base64Audio = reader.result.split(',')[1];
                        await sendVoiceInteraction(base64Audio);
                    };
                    reader.readAsDataURL(audioBlob);
                };

                // 开始录音
                mediaRecorder.start();

                // 3秒后自动停止录音
                setTimeout(() => {
                    if (mediaRecorder.state === 'recording') {
                        mediaRecorder.stop();
                    }
                }, 3000);

            } catch (error) {
                console.error('语音交互启动失败:', error);
                showtmpMessage('语音交互启动失败: ' + error.message, 'error');
                resetVoiceInteractionButton();
            }
        }

        // 发送语音交互请求
        async function sendVoiceInteraction(audioData) {
            try {
                const voiceInteractBtn = document.getElementById('voiceInteractBtn');
                voiceInteractBtn.innerHTML = '<span>🤖</span> AI处理中...';

                const response = await fetch('/api/v1/voice/interact', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        audio_data: audioData,
                        return_audio: true,
                        speaker: 'zh_female_shuangkuaisisi_moon_bigtts'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // 显示识别的文本
                    if (result.recognized_text) {
                        addResult(result.recognized_text, true, Date.now());
                        showtmpMessage(`识别: ${result.recognized_text}`, 'success');
                    }

                    // 显示AI回复
                    if (result.response_text) {
                        showtmpMessage(`AI回复: ${result.response_text}`, 'success');
                    }

                    // 播放AI回复音频
                    if (result.audio_data) {
                        await playAudioResponse(result.audio_data);
                    }

                    console.log('语音交互成功:', result);
                } else {
                    showtmpMessage('语音交互失败: ' + (result.error || result.message), 'error');
                }

            } catch (error) {
                console.error('语音交互请求失败:', error);
                showtmpMessage('语音交互请求失败: ' + error.message, 'error');
            } finally {
                resetVoiceInteractionButton();
            }
        }

        // 播放AI回复音频
        async function playAudioResponse(base64Audio) {
            try {
                // 将base64转换为blob
                const audioData = atob(base64Audio);
                const arrayBuffer = new ArrayBuffer(audioData.length);
                const uint8Array = new Uint8Array(arrayBuffer);

                for (let i = 0; i < audioData.length; i++) {
                    uint8Array[i] = audioData.charCodeAt(i);
                }

                const audioBlob = new Blob([arrayBuffer], { type: 'audio/mp3' });
                const audioUrl = URL.createObjectURL(audioBlob);

                // 创建音频元素并播放
                voiceInteractionAudio = new Audio(audioUrl);
                voiceInteractionAudio.onended = () => {
                    URL.revokeObjectURL(audioUrl);
                    voiceInteractionAudio = null;
                };

                await voiceInteractionAudio.play();
                console.log('AI回复音频播放完成');

            } catch (error) {
                console.error('播放AI回复音频失败:', error);
                showtmpMessage('播放AI回复音频失败', 'error');
            }
        }

        // 重置语音交互按钮
        function resetVoiceInteractionButton() {
            isVoiceInteracting = false;
            const voiceInteractBtn = document.getElementById('voiceInteractBtn');
            voiceInteractBtn.disabled = false;
            voiceInteractBtn.innerHTML = '<span>🎤</span> 语音对话';
        }

        // 设置音频处理
        function setupAudioProcessing(stream) {
            try {
                // 创建音频上下文
                audioContext = new (window.AudioContext || window.webkitAudioContext)({
                    sampleRate: 16000
                });

                // 创建音频源
                microphone = audioContext.createMediaStreamSource(stream);

                // 创建脚本处理器 - 使用更小的缓冲区进行VAD检测
                processor = audioContext.createScriptProcessor(512, 1, 1); // 32ms延迟，适合20ms VAD检测

                processor.onaudioprocess = function (event) {
                    if (isRecording) {
                        const inputBuffer = event.inputBuffer;
                        const inputData = inputBuffer.getChannelData(0);

                        // 转换为16位PCM
                        const pcmData = convertFloat32ToInt16(inputData);

                        // 转换为base64并发送，启用VAD检测
                        const base64Data = arrayBufferToBase64(pcmData.buffer);
                        socket.emit('audio_data', {
                            audio: base64Data,
                            timestamp: Date.now(),
                            chunk_size: inputData.length,
                            use_vad: true  // 启用VAD检测
                        });
                    }
                };

                // 连接音频节点
                microphone.connect(processor);
                processor.connect(audioContext.destination);

                // console.log('音频处理设置完成');

            } catch (error) {
                console.error('设置音频处理时出错:', error);
                showtmpMessage('设置音频处理时出错: ' + error.message, 'error');
            }
        }

        // 停止音频处理
        function stopAudioProcessing() {
            try {
                if (processor) {
                    processor.disconnect();
                    processor = null;
                }

                if (microphone) {
                    microphone.disconnect();
                    microphone = null;
                }

                if (audioContext) {
                    audioContext.close();
                    audioContext = null;
                }

                // console.log('音频处理已停止');

            } catch (error) {
                console.error('停止音频处理时出错:', error);
            }
        }

        // 转换Float32数组为Int16数组
        function convertFloat32ToInt16(float32Array) {
            const int16Array = new Int16Array(float32Array.length);
            for (let i = 0; i < float32Array.length; i++) {
                const sample = Math.max(-1, Math.min(1, float32Array[i]));
                int16Array[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
            }
            return int16Array;
        }

        // 将ArrayBuffer转换为base64
        function arrayBufferToBase64(buffer) {
            let binary = '';
            const bytes = new Uint8Array(buffer);
            const len = bytes.byteLength;
            for (let i = 0; i < len; i++) {
                binary += String.fromCharCode(bytes[i]);
            }
            return window.btoa(binary);
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            if (connected) {
                connectionStatus.textContent = '🟢 已连接';
                connectionStatus.className = 'connection-status connected';
            } else {
                connectionStatus.textContent = '🔴 未连接';
                connectionStatus.className = 'connection-status disconnected';
            }
        }

        // 更新状态
        function updateStatus(message, type) {
            status.textContent = message;
            status.className = 'status ' + type;
        }

        // 更新状态消息
        function updateStatusMessage(message) {
            statusMessage.textContent = message;
        }

        // 静音倒计时函数
        function startSilenceCountdown(timeout) {
            console.log(`开始静音倒计时: ${timeout}秒`);
            // 这里可以添加倒计时显示逻辑
            // 例如更新UI显示剩余时间
            const silenceStatus = document.getElementById('silenceStatus');
            if (silenceStatus) {
                silenceStatus.textContent = `静音检测中... (${timeout}秒后自动停止)`;
            }
        }

        // 检查submitData元素是否存在，如果存在才添加事件监听器
        const submitDataBtn = document.getElementById('submitData');
        if (submitDataBtn) {
            submitDataBtn.addEventListener('click', async () => {
                const teacherDailyTask = document.getElementById('teacherDailyTask').value;
                const yesterdayFeedback = document.getElementById('yesterdayFeedback').value;
                console.log('teacherDailyTask:', teacherDailyTask);
                console.log('yesterdayFeedback:', yesterdayFeedback);
                // 显示加载提示
                const taskPlanLoading = document.getElementById('taskPlanLoading');
                taskPlanLoading.style.display = 'block';

                try {
                    const response = await fetch('/api/submit_task_data', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            teacher_daily_task: teacherDailyTask,
                            yesterday_feedback: yesterdayFeedback
                        })
                    });

                    if (!response.ok) {
                        console.log('response generate fail:');
                        throw new Error('提交数据失败');
                    }

                    const result = await response.json();
                    console.log('提交结果:', result);
                } catch (error) {
                    console.error('提交数据出错:', error);
                }
            });
        }




        summaryBtn.addEventListener('click', async () => {
            // 显示正在分析中弹窗
            showAnalyzingModal();
            try {
                const response = await fetch('/api/getSummary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },

                });


                const result = await response.json();

                // 适配学习统计API的响应格式
                const analysisText = `学习统计分析报告：

总学习时间：${result.total_study_time} 分钟
平均完成率：${result.average_completion_rate}%
平均正确率：${result.average_accuracy_rate}%
平均享受程度：${result.average_enjoyment}/5
学习学科：${result.subjects_studied.join(', ')}
统计周期：${result.period_days} 天
总记录数：${result.total_records} 条`;

                const html = marked.parse(analysisText);
                const container = document.getElementById('markdown-container');
                container.innerHTML = html;
                console.log('学习统计结果:', result);

                // 隐藏正在分析中弹窗
                hideAnalyzingModal();

                // 显示分析结果弹窗
                if (result.total_records > 0) {
                    showAnalysisResultModal(
                    `学习统计分析：

                    总学习时间：${result.total_study_time} 分钟
                    平均完成率：${result.average_completion_rate}%
                    平均正确率：${result.average_accuracy_rate}%
                    平均享受程度：${result.average_enjoyment}/5
                    学习学科：${result.subjects_studied.join(', ')}
                    统计周期：${result.period_days} 天
                    `);
                } else {
                    showAnalysisResultModal(`暂无学习数据`);
                }

            } catch (error) {
                console.error('提交数据出错:', error);
            }
        });





        // 重置按钮状态
        function resetButtons() {
            startBtn.disabled = true;
            stopBtn.disabled = true;
            connectBtn.disabled = false;
            connectBtn.textContent = '🔗 连接服务';
            connectBtn.onclick = connectService;
            // 隐藏语音交互按钮
            document.getElementById('voiceInteractBtn').style.display = 'none';
            isRecording = false;
            micIndicator.classList.remove('recording');
        }

        // 显示消息
        function showtmpMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = type === 'error' ? 'floating-error' : 'floating-success';
            messageDiv.textContent = message;

            // 插入到状态卡片后面
            const statusCard = document.querySelector('.status-card');
            statusCard.parentNode.insertBefore(messageDiv, statusCard.nextSibling);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }

   // 添加识别结果
   function addResult(text, isFinal, timestamp) {
            requestAnimationFrame(() => {
                // 查找现有的结果项
                let resultItem = resultsList.querySelector('.result-item');

                if (resultItem) {
                    const existingText = resultItem.querySelector('.result-text').textContent;
                    // 如果新结果和之前相同，不做任何操作
                    if (existingText === text) {
                        return;
                    }
                } else {
                    // 若不存在结果项，创建新的结果项
                    resultItem = document.createElement('div');
                    resultItem.className = 'result-item';
                }

                // 更新结果文本
                const resultText = resultItem.querySelector('.result-text') || document.createElement('div');
                resultText.className = 'result-text';
                resultText.textContent = text;
                if (!resultItem.contains(resultText)) {
                    resultItem.appendChild(resultText);
                }

                // 更新元信息
                const resultMeta = resultItem.querySelector('.result-meta') || document.createElement('div');
                resultMeta.className = 'result-meta';

                const timeSpan = resultMeta.querySelector('span:nth-child(1)') || document.createElement('span');
                const date = new Date(timestamp);
                timeSpan.textContent = date.toLocaleTimeString();
                if (!resultMeta.contains(timeSpan)) {
                    resultMeta.appendChild(timeSpan);
                }

                const typeSpan = resultMeta.querySelector('span:nth-child(2)') || document.createElement('span');
                typeSpan.textContent = isFinal ? '最终结果' : '临时结果';
                typeSpan.style.fontWeight = isFinal ? 'bold' : 'normal';
                typeSpan.style.color = isFinal ? '#ffffff' : '#007bff';
                if (!resultMeta.contains(typeSpan)) {
                    resultMeta.appendChild(typeSpan);
                }

                if (!resultItem.contains(resultMeta)) {
                    resultItem.appendChild(resultMeta);
                }

                // 确保结果项有正确的类名
                resultItem.className = 'result-item' + (isFinal ? ' final' : '');

                // 清空列表并添加新的结果项
                resultsList.innerHTML = '';
                resultsList.appendChild(resultItem);

                if (isFinal) {
                    // 将最终文本交给后端进行二分判断
                    console.log("最终传给后端判断的文本:", text);
                }

                // 如果处于子任务语音输入状态，将结果输入到新增子任务的文本框中
                if (isSubTaskVoiceInput ) {
                    const newSubTaskInput = document.getElementById('newSubTaskInput');
                     // 更新式输入到 newSubTaskInput 文本框
                    newSubTaskInput.value = text;
                    console.log("语音有结果了，更新到子任务输入框:", text);
                    // 重置状态
 
                }
            });
        }

        // 添加任务创建结果
        function addTaskResult(voiceResult, dailyTask, timestamp) {
            // 移除空状态
            const emptyState = resultsList.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }

            // 创建任务结果项
            const taskItem = document.createElement('div');
            taskItem.className = 'result-item task-result';
            taskItem.style.borderLeftColor = '#ffc107';
            taskItem.style.background = '#fff3cd';

            const taskHeader = document.createElement('div');
            taskHeader.className = 'result-text';
            taskHeader.style.fontWeight = 'bold';
            taskHeader.style.color = '#856404';
            taskHeader.textContent = '🎯 任务已创建';

            const voiceResultDiv = document.createElement('div');
            voiceResultDiv.style.marginTop = '10px';
            voiceResultDiv.style.fontSize = '0.95em';
            voiceResultDiv.innerHTML = `<strong>语音大模型结果:</strong> ${voiceResult}`;

            const taskDiv = document.createElement('div');
            taskDiv.style.marginTop = '8px';
            taskDiv.style.fontSize = '0.9em';
            taskDiv.style.color = '#666';
            taskDiv.innerHTML = `<strong>当日任务:</strong> ${dailyTask.substring(0, 100)}${dailyTask.length > 100 ? '...' : ''}`;

            const resultMeta = document.createElement('div');
            resultMeta.className = 'result-meta';

            const timeSpan = document.createElement('span');
            const date = new Date(timestamp);
            timeSpan.textContent = date.toLocaleTimeString();

            const typeSpan = document.createElement('span');
            typeSpan.textContent = '任务创建';
            typeSpan.style.fontWeight = 'bold';
            typeSpan.style.color = '#ffc107';

            resultMeta.appendChild(timeSpan);
            resultMeta.appendChild(typeSpan);

            taskItem.appendChild(taskHeader);
            taskItem.appendChild(voiceResultDiv);
            taskItem.appendChild(taskDiv);
            taskItem.appendChild(resultMeta);

            // 插入到列表顶部
            resultsList.insertBefore(taskItem, resultsList.firstChild);

            // 滚动到顶部显示最新结果
            resultsList.scrollTop = 0;
        }

        // 清空结果
        function clearResults() {
            resultsList.innerHTML = '<div class="empty-state">暂无识别结果，请开始语音识别...</div>';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function () {
            console.log('页面加载完成，初始化AI Child语音识别界面');
            updateConnectionStatus(false);
            resetButtons();
            // 初始化学科检测功能
            initSubjectDetection();
            // 显示科目检测弹窗
            showSubjectCheckModal();
            // 自动启动语音识别
            autoStartRecognition();

        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function () {
            if (isRecording) {
                stopRecognition();
            }
            if (socket) {
                socket.disconnect();
            }
        });



        // 存储已删除的子任务
        let deletedSubtasks = [];
        let fixedTasks=[];
        let speechRecognition;
        const taskNameSelect = document.getElementById('taskNameSelect');
        const startVoiceInput = document.getElementById('startVoiceInput');
        const newSubTaskInput = document.getElementById('newSubTaskInput');
        const addSubTaskBtn = document.getElementById('addSubTaskBtn');

        // 填充任务名称下拉框
        function populateTaskNameSelect() {
            taskNameSelect.innerHTML = '<option value="">请选择任务</option>';
                fixedTasks.forEach(({ taskName, taskId }) => {
                    const option = document.createElement('option');
                    option.value = taskId; // 将 option 的 value 设置为 taskId
                    option.textContent = taskName;
                    taskNameSelect.appendChild(option);
                });
        }

        // 添加子任务
        async function addSubTask() {
           

            const taskNameSelect = document.getElementById('taskNameSelect');
            const selectedOption = taskNameSelect.options[taskNameSelect.selectedIndex];
            const selectedTaskName = selectedOption ? selectedOption.textContent : '';
            console.log('selectedTaskName:',selectedTaskName);

            const subTaskContent = newSubTaskInput.value;
            // 获取 dailyTaskId 下拉框元素
            const dailyTaskIdSelect = document.getElementById('taskNameSelect');
            // 获取 dailyTaskId 的值
            const dailyTaskId = dailyTaskIdSelect ? dailyTaskIdSelect.value : null;

            if (!selectedTaskName || !subTaskContent) {
                showMessage('请选择任务并输入子任务内容', 'error');
                return;
            }

            try {
                const childId = parseInt(childIdInput.value) || 4;

                // 生成默认时间段
                const currentTime = new Date();
                const startTime = currentTime.toTimeString().slice(0, 5);
                const endTime = new Date(currentTime.getTime() + 30 * 60000).toTimeString().slice(0, 5); // 30分钟后
                const timeSlot = `${startTime} - ${endTime}`;

                tempdata=JSON.stringify({
                        child_id: childId,
                        task_name: selectedTaskName,
                        sub_task_content: subTaskContent,
                        sub_task_source: "手动添加",
                        time_slot: timeSlot,
                        plan_id:tempTaskPlan.id,
                        daily_task_id: parseInt(dailyTaskId)
                    
                    })

                console.log("新增任务参数：")
                console.log(tempdata)

                // 调用后端API添加子任务
                const response = await fetch(`http://localhost:${port}/api/add_subtask`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        child_id: childId,
                        task_name: selectedTaskName,
                        sub_task_content: subTaskContent,
                        sub_task_source: "手动添加",
                        time_slot: timeSlot,
                        plan_id:tempTaskPlan.id,
                        daily_task_id: parseInt(dailyTaskId)
                    
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {

                        const subTaskId = result.data && result.data.sub_task_id ? result.data.sub_task_id : null;

                        console.log("增加子任务返回的结果：")
                        console.log(result)


                        // 在前端添加子任务显示
                        const taskPlanTable = document.getElementById('taskPlanTable');
                        const rows = taskPlanTable.getElementsByTagName('tr');

                        for (let i = 0; i < rows.length; i++) {
                            const taskNameCell = rows[i].cells[0];
                            if (taskNameCell.textContent === selectedTaskName) {
                                const subTaskCell = rows[i].cells[2];
                                const newTag = document.createElement('div');
                                newTag.className = 'tag';
                                newTag.innerHTML = `
                                    <div class="sub-task-content">${subTaskContent}</div>
                                    <div class="sub-task-time">
                                        <input type="time" value="${startTime}" step="60" onchange="updateSubTaskTime(this, '${selectedTaskName}', '${subTaskContent}', 'start', '${result.data.sub_task_id || 'null'}', '${parseInt(dailyTaskId) || 'null'}', '${tempTaskPlan.id || 'null'}')"> - 
                                        <input type="time" value="${endTime}" step="60" onchange="updateSubTaskTime(this, '${selectedTaskName}', '${subTaskContent}', 'end', '${result.data.sub_task_id || 'null'}', '${parseInt(dailyTaskId) || 'null'}', '${tempTaskPlan.id || 'null'}')">
                                    </div>
                                    <span class="tag-delete" onclick="removeSubTask(this, '${selectedTaskName}', '${subTaskContent}', '${startTime}', '${endTime}','${result.data.sub_task_id || 'null'}', '${parseInt(dailyTaskId) || 'null'}', '${tempTaskPlan.id || 'null'}')">×</span>
                                `;
                                if (subTaskId) {
                                    newTag.dataset.subTaskId = subTaskId;
                                }
                                subTaskCell.appendChild(newTag);
                                break;
                            }
                        }

                         // 进行时间冲突检查
                        const allSubTasks = collectAllSubTasks();
                        const conflictResult = hasTimeConflict(allSubTasks);
                        if (conflictResult.conflict) {
                            const prevTask = conflictResult.prevTask;
                            const currentTask = conflictResult.currentTask;
                            const conflictMessage = `子任务时间存在冲突！冲突子任务时间为 ${prevTask.startTime} - ${prevTask.endTime}，当前子任务时间为 ${currentTask.startTime} - ${currentTask.endTime}，请重新调整！`;
                            showMessageInSection(conflictMessage, 'error', true);
                            hasConflict = true;
                               // 找到冲突的子任务 tag 并添加 conflict 类
                            const taskPlanTable = document.getElementById('taskPlanTable');
                            const tags = taskPlanTable.querySelectorAll('.tag');
                            tags.forEach(tag => {
                                const startInput = tag.querySelector('input:nth-of-type(1)');
                                const endInput = tag.querySelector('input:nth-of-type(2)');
                                if (
                                    (startInput.value === prevTask.startTime && endInput.value === prevTask.endTime) ||
                                    (startInput.value === currentTask.startTime && endInput.value === currentTask.endTime)
                                ) {
                                    tag.classList.add('conflict');
                                }
                            });
                        } else {
                            // 没有冲突，隐藏消息
                            showMessageInSection('', 'error', false);
                            const tags = taskPlanTable.querySelectorAll('.tag');
                            tags.forEach(tag => {
                                tag.classList.remove('conflict');
                            });
                            hasConflict = false;
                        }
                    
                        // 清空输入框
                        newSubTaskInput.value = '';
                        showMessage(`✅ 子任务"${subTaskContent}"已添加到"${selectedTaskName}"`, 'success');

                        updateTaskTimeRange(selectedTaskName)  

      
                    } else {
                        showMessage(`❌ 添加子任务失败: ${result.message}`, 'error');
                    }
                } else {
                    showMessage(`❌ 添加子任务失败: HTTP ${response.status}`, 'error');
                }

                newSubTaskInput.value = '';

         
            } catch (error) {
                console.error('添加子任务时出错:', error);
                showMessage('❌ 添加子任务时发生错误', 'error');
                taskNameSelect.value = '';

                updateTaskTimeRange(selectedTaskName)
            } 
        }

         // 收集所有子任务的时间信息
        function collectAllSubTasks() {
            const taskPlanTable = document.getElementById('taskPlanTable');
            const rows = taskPlanTable.getElementsByTagName('tr');
            let allSubTasks = [];

            for (let i = 1; i < rows.length; i++) { // 从 1 开始，跳过表头
                const subTaskCells = rows[i].cells[2].querySelectorAll('.tag');
                subTaskCells.forEach(subTaskCell => {
                    const startInput = subTaskCell.querySelector('input:nth-of-type(1)');
                    const endInput = subTaskCell.querySelector('input:nth-of-type(2)');
                    allSubTasks.push({
                        startTime: startInput.value,
                        endTime: endInput.value
                    });
                });
            }
            return allSubTasks;
        }

        // 初始化语音识别
        function initSpeechRecognition() {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            if (SpeechRecognition) {
                speechRecognition = new SpeechRecognition();
                speechRecognition.lang = 'zh-CN';
                speechRecognition.interimResults = false; // 只获取最终结果

                speechRecognition.onresult = function (event) {
                    const transcript = event.results[0][0].transcript;
                    newSubTaskInput.value = transcript;
                    console.log('语音识别结果:newSubTaskInput.value=', transcript);
                };

                speechRecognition.onerror = function (event) {
                    console.error('语音识别出错:', event.error);
                };
            } else {
                console.error('浏览器不支持语音识别功能');
                showtmpMessage('浏览器不支持语音识别功能，请使用现代浏览器', 'error');
            }
        }
        initSpeechRecognition();

        // 监听语音输入按钮点击事件
        startVoiceInput.addEventListener('click', function () {

            isSubTaskVoiceInput = true;


            const childId = parseInt(childIdInput.value);

            if (!childId || childId <= 0) {
                showtmpMessage('请输入有效的小孩ID', 'error');
                return;
            }

            try {
                console.log('🎤 开始语音子任务录制...');

                // 获取输入框元素
                const newSubTaskInput = document.getElementById('newSubTaskInput');

                // 将子任务内容赋值给输入框
                if (newSubTaskInput) {
                    newSubTaskInput.value = "";
                }


                // 设置语音任务录制状态
                isVoiceTaskRecording = true;
                console.log('📊 设置语音任务录制状态:', isVoiceTaskRecording);


                // 连接ASR服务（如果未连接）
                if (!isConnected) {
                    console.log('🔗 ASR服务未连接，正在连接...');
                    connectService();
                }

                // 开始录音
                console.log('🎙️ 开始语音识别...');
                startRecognition();

                console.log('✅ 语音任务录制已启动');
                showtmpMessage('开始语音录制，请说话...', 'success');

            } catch (error) {
                console.error('❌ 启动语音录制失败:', error);
                showtmpMessage(`启动语音录制失败: ${error.message}`, 'error');

            }
        });




        // 监听添加子任务按钮点击事件
        addSubTaskBtn.addEventListener('click', addSubTask);


        // 新增时间格式函数，将时间字符串转换为 HH:mm 格式
        function formatTime(timeStr) {
            const date = new Date(`1970-01-01T${timeStr}`);
            return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
        }

        // 保存任务计划到task_plans表的函数
        async function saveTaskPlanToTaskPlansTable(taskPlan) {
            try {
                console.log('=== 开始保存任务计划到task_plans表 ===');
                console.log('原始taskPlan:',taskPlan);
                console.log('taskPlan类型:', typeof taskPlan);

                // 获取child_id
                const childId = parseInt(childIdInput.value) || 4; // 从输入框获取ID，默认为4
                console.log('使用的child_id:', childId);

                // 确保taskPlan是字符串格式
                let taskPlanContent;
                if (typeof taskPlan === 'string') {
                    taskPlanContent = taskPlan;
                } else {
                    taskPlanContent = JSON.stringify(taskPlan);
                }

                console.log('准备发送的数据:');
                console.log('- child_id:', childId);
                console.log('- content长度:', taskPlanContent.length);
                console.log('- content前100字符:', taskPlanContent.substring(0, 100));

                const requestData = {
                    child_id: childId,
                    content: taskPlanContent
                };

                console.log('发送POST请求到:', `http://localhost:${port}/api/task-plans/`);
                const response = await fetch(`http://localhost:${port}/api/task-plans/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('API响应状态:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API错误响应:', errorText);
                    throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
                }

                const result = await response.json();
                console.log('任务计划保存到task_plans表成功:', result);
                console.log('返回的计划ID:', result.id);
                console.log('返回的child_id:', result.child_id);

                // 显示成功消息
                showtmpMessage(`✅ 任务计划已保存到计划表 (计划ID: ${result.id}, 学生ID: ${result.child_id})`, 'success');

                return result;

            } catch (error) {
                console.error('保存任务计划到task_plans表失败:', error);
                showtmpMessage(`⚠️ 保存任务计划到计划表失败: ${error.message}`, 'error');
                throw error;
            }
        }

        async function getLatestPlan(childId) {
            try {
                console.log('开始从数据库重新加载任务数据...');

                const response = await fetch(`http://localhost:${port}/api/task-plans/getplanbychildid/${childId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                console.log('getLatestPlan 函数从数据库获取的存储的任务数据:', result);
                tableId=result.id

                if (result) {
                    // 使用数据库中的实际数据重新渲染任务表格
                    tempTaskPlan=result
                    showTaskPlan(result.content);

                    console.log('任务数据加载完成');
                } else {
                    console.error('获取任务数据失败');
                }

            } catch (error) {
                console.error('重新加载任务数据失败:', error);
                showMessage(`❌ 重新加载任务数据失败: ${error.message}`, 'error');
            }

        }

        // 检查按钮是否存在再添加事件监听器
        if (getTaskBtn) {
            getTaskBtn.addEventListener('click', getLatestPlan(childIdInput.value));
        }

        // 显示任务计划并保存到task_plans表的函数
        async function showTaskPlanAndSaveToDatabase(taskPlan) {
            console.log('开始显示任务计划并保存到task_plans表');

            try {


                // 1. 保存任务计划到task_plans表
                console.log('保存任务计划到task_plans表:');
                await saveTaskPlanToTaskPlansTable(taskPlan);


                // 2. 获取小孩最新任务表数据并且显示
                const childId = parseInt(childIdInput.value) || 4;

                getLatestPlan(childId);
             

                // 3. 存储任务计划数据，供确认按钮使用
                window.currentTaskPlan = taskPlan;

                console.log('任务计划显示和保存完成');

            } catch (error) {
                console.error('显示任务计划或保存到数据库失败:', error);
                // 即使保存失败，也要显示任务计划
                showTaskPlan(taskPlan);
                window.currentTaskPlan = taskPlan;
                showtmpMessage('⚠️ 任务计划显示成功，但保存到计划表失败', 'warning');
            }
        }

        function showTaskPlan(taskPlan) {
            // console.log('传入的 taskPlan 类型:', typeof taskPlan);
            // console.log('传入的 taskPlan 内容:',taskPlan);

            // 检查 taskPlan 是否为数组，若不是则尝试转换或给出提示
            if (!Array.isArray(taskPlan)) {
                if (typeof taskPlan === 'string') {
                    try {
                        taskPlan = JSON.parse(taskPlan);
                    } catch (error) {
                        console.error('解析 taskPlan 字符串失败:', error);
                        showtmpMessage('任务计划数据格式错误，请联系管理员', 'error');
                        return;
                    }
                } else {
                    console.error('taskPlan 不是数组类型:', taskPlan);
                    showtmpMessage('任务计划数据格式错误，请联系管理员', 'error');
                    return;
                }
            }

            // 合并相同 task_name 的任务
            const mergedTasks = {};
            taskPlan.forEach(task => {
                if (mergedTasks[task.task_name]) {
                    // 如果 task_name 已存在，合并 sub_tasks
                    mergedTasks[task.task_name].sub_tasks = [
                        ...mergedTasks[task.task_name].sub_tasks,
                        ...task.sub_tasks
                    ];
                } else {
                    // 如果 task_name 不存在，复制整个任务对象
                    mergedTasks[task.task_name] = { ...task };
                }
            });

            // 将合并后的任务转换为数组
            const combinedTaskPlan = Object.values(mergedTasks);

            const taskPlanTable = document.getElementById('taskPlanTable');
            // 找到 tbody 元素，如果不存在则创建
            let tbody = taskPlanTable.querySelector('tbody');
            if (!tbody) {
                tbody = document.createElement('tbody');
                taskPlanTable.appendChild(tbody);
            }
            let tableHtml = '';
            fixedTasks=[]

            // 使用合并后的任务生成表格
            combinedTaskPlan.forEach(task => {
                fixedTasks.push({
                    taskName: task.task_name,
                    taskId: task.id 
                });
                // 初始化最早和最晚时间
                let earliestStartTime = null;
                let latestEndTime = null;

                // 遍历子任务，找出最早开始时间和最晚结束时间
                task.sub_tasks.forEach(subTask => {
                    const [subStartTime, subEndTime] = subTask.time_slot.split(' - ');
                    const subStartDate = new Date(`1970-01-01T${subStartTime}`);
                    const subEndDate = new Date(`1970-01-01T${subEndTime}`);

                    if (!earliestStartTime || subStartDate < earliestStartTime) {
                        earliestStartTime = subStartDate;
                    }
                    if (!latestEndTime || subEndDate > latestEndTime) {
                        latestEndTime = subEndDate;
                    }
                });

                // 格式化最早和最晚时间
                const formattedStartTime = formatTime(earliestStartTime.toLocaleTimeString('en-US', { hour12: false }));
                const formattedEndTime = formatTime(latestEndTime.toLocaleTimeString('en-US', { hour12: false }));

                tableHtml += `<tr data-plan-id="${task.plan_id || 'null'}" data-task-id="${task.id || 'null'}">`;
                tableHtml += `<td>${task.task_name}</td>`;
                // 使用子任务的最早和最晚时间
                tableHtml += `<td>
                    <input type="time" value="${formattedStartTime}" step="60" onchange="updateTaskTime(this, '${task.task_name}', 'start', '${task.id || 'null'}', '${task.plan_id || 'null'}')"> - 
                    <input type="time" value="${formattedEndTime}" step="60" onchange="updateTaskTime(this, '${task.task_name}', 'end', '${task.id || 'null'}', '${task.plan_id || 'null'}')">
                </td>`;

                // 渲染子任务和时间列
                tableHtml += '<td>';
                task.sub_tasks.forEach(subTask => {
                    const [subStartTime, subEndTime] = subTask.time_slot.split(' - ');
                    const formattedSubStartTime = formatTime(subStartTime);
                    const formattedSubEndTime = formatTime(subEndTime);
                    tableHtml += `<div class="tag">
                        <div class="sub-task-content">${subTask.sub_task_name}</div>
                        <div class="sub-task-time">
                            <input type="time" value="${formattedSubStartTime}" step="60" onchange="updateSubTaskTime(this, '${task.task_name}', '${subTask.sub_task_name}', 'start', '${subTask.id || 'null'}', '${task.id || 'null'}', '${task.plan_id || 'null'}')"> - 
                            <input type="time" value="${formattedSubEndTime}" step="60" onchange="updateSubTaskTime(this, '${task.task_name}', '${subTask.sub_task_name}', 'end', '${subTask.id || 'null'}', '${task.id || 'null'}', '${task.plan_id || 'null'}')">
                        </div>
                        <span class="tag-delete" onclick="removeSubTask(this, '${task.task_name}', '${subTask.sub_task_name}', '${formattedSubStartTime}', '${formattedSubEndTime}', '${subTask.id || 'null'}', '${task.id || 'null'}', '${task.plan_id || 'null'}')">×</span>
                    </div>`;
                });
                tableHtml += '</td>';

                // 渲染定制方案列
                tableHtml += `<td>${task.customization}</td>`;
                // 渲染难点列
                tableHtml += `<td>${task.difficulty}</td>`;
                // 渲染解决方案列
                tableHtml += `<td>${task.solution}</td>`;
                // 渲染信心指数列
                tableHtml += `<td>${task.confidence_index}</td>`;
                tableHtml += '</tr>';
            });

            // 只更新 tbody 的内容
            tbody.innerHTML = tableHtml;

            // 填充任务名称下拉框
            populateTaskNameSelect();

            // 自动保存任务计划到数据库
            // saveTaskPlanToDatabase(combinedTaskPlan,tempTaskPlan.id);
        }


        // 格式化时间函数
        function formatTime(time) {
            console.log("格式化时间4337 line：",time)
            return time.replace(/(\d{2}):(\d{2})/, '$1:$2');
        }



        // 修改 hasTimeConflict 函数，返回冲突的时间信息和对应子任务索引
        function hasTimeConflict(subTasks) {
            // 先将子任务按开始时间排序
            const sortedSubTasks = [...subTasks].map((subTask, index) => ({ ...subTask, index })).sort((a, b) => {
                const aStart = new Date(`1970-01-01T${a.startTime}`);
                const bStart = new Date(`1970-01-01T${b.startTime}`);
                return aStart - bStart;
            });

            for (let i = 1; i < sortedSubTasks.length; i++) {
                const prevEnd = new Date(`1970-01-01T${sortedSubTasks[i - 1].endTime}`);
                const currentStart = new Date(`1970-01-01T${sortedSubTasks[i].startTime}`);
                if (currentStart < prevEnd) {
                    // 返回冲突的时间信息和对应子任务索引
                    return {
                        conflict: true,
                        prevTask: sortedSubTasks[i - 1],
                        currentTask: sortedSubTasks[i]
                    };
                }
            }
            return { conflict: false };
        }



        // 更新子任务时间函数
        function updateSubTaskTime(input, taskName, subTaskName, type,subtaskid,taskid,plan_id) {
            // 每次更新前重置冲突状态
            hasConflict = false;

            const originalValue = input.value;
            const newTime = input.value;
            console.log(`子任务 ${subTaskName} （所属任务 ${taskName}）的 ${type === 'start' ? '开始' : '结束'} 时间更新为 ${newTime}`);

            const taskPlanTable = document.getElementById('taskPlanTable');
            const rows = taskPlanTable.getElementsByTagName('tr');
            let allSubTasks = [];
            let allTagElements = [];

            // 遍历表格行，收集所有任务的子任务时间信息和对应的 tag 元素
            for (let i = 1; i < rows.length; i++) { // 从 1 开始，跳过表头
                const subTaskCells = rows[i].cells[2].querySelectorAll('.tag');
                for (const [index, subTaskCell] of subTaskCells.entries()) {
                    const startInput = subTaskCell.querySelector('input:nth-of-type(1)');
                    const endInput = subTaskCell.querySelector('input:nth-of-type(2)');
                    const startTime = startInput.value;
                    const endTime = endInput.value;

                    // 检查单个子任务时间是否合理
                    if (new Date(`1970-01-01T${endTime}`) < new Date(`1970-01-01T${startTime}`)) {
                        const errorMessage = `子任务 ${subTaskName} 的结束时间不能早于开始时间，请重新调整！`;
                        showMessageInSection(errorMessage, 'error', true);
                        hasConflict = true;
                        subTaskCell.style.backgroundColor = 'red';
                        break;
                    }

                    allSubTasks.push({
                        startTime: startTime,
                        endTime: endTime,
                        index: index
                    });
                    allTagElements.push(subTaskCell);
                }

                if (hasConflict) break;
            }

            if (hasConflict) return;

            // 检查时间是否冲突
            const conflictResult = hasTimeConflict(allSubTasks);
            if (conflictResult.conflict) {
                const prevTask = conflictResult.prevTask;
                const currentTask = conflictResult.currentTask;
                const conflictMessage = `子任务时间存在冲突！冲突子任务时间为 ${prevTask.startTime} - ${prevTask.endTime}，当前子任务时间为 ${currentTask.startTime} - ${currentTask.endTime}，请重新调整！`;
                showMessageInSection(conflictMessage, 'error', true);
                hasConflict = true;

                // 将冲突的 tag 背景设为红色
                if (prevTask.index >= 0 && prevTask.index < allTagElements.length) {
                    allTagElements[prevTask.index].style.backgroundColor = 'red';
                }
                if (currentTask.index >= 0 && currentTask.index < allTagElements.length) {
                    allTagElements[currentTask.index].style.backgroundColor = 'red';
                }

                return;
            } else {
                // 没有冲突，隐藏消息，将所有 tag 背景设为蓝色

                submitupdateTaskTimeRange(subtaskid,newTime,type)
                commitChangeAction(`修改子任务 ${subTaskName} （所属任务 ${taskName}）的 ${type === 'start' ? '开始' : '结束'} 时间为 ${newTime}`);

                showMessageInSection('', 'error', false);
                hasConflict = false;

                allTagElements.forEach(tag => {
                    tag.style.backgroundColor = '#007bff'; // 蓝色
                });
            }

            updateTaskTimeRange(taskName);
        }


        // 更新任务时间范围函数
        async function submitupdateTaskTimeRange(subTaskId,newTime,typeInput) {
            try {
                const childId = parseInt(childIdInput.value) || 4;
              
                
                // 调用后端 updateSubtaskTime 接口
                const response = await fetch(`http://localhost:${port}/api/task-plans/updateSubtaskTime`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sub_task_id: parseInt(subTaskId),
                        new_time:newTime,
                        time_type: typeInput
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('更新子任务时间失败:', errorText);
                    showMessageInSection(`更新子任务时间失败: ${errorText}`, 'error', true);
                    return;
                }

                const result = await response.json();
                if (result.success) {
                    console.log('子任务时间更新成功:', result);
                } else {
                    console.error('更新子任务时间失败:', result.message);
                    showMessageInSection(`更新子任务时间失败: ${result.message}`, 'error', true);
                }
            } catch (error) {
                console.error('更新子任务时间时出错:', error);
                showMessageInSection(`更新子任务时间时出错: ${error.message}`, 'error', true);
            }

        }


     
       
       
        async function removeSubTask(span, taskName, subTaskName, formattedSubStartTime, formattedSubEndTime, subTaskId = null, taskid = null, plan_id = null) {
        try {
            const childId = parseInt(childIdInput.value) || 4;

            // 构建请求体，优先使用子任务ID
            const requestBody = {
                child_id: childId,
                task_name: taskName,
                sub_task_content: subTaskName,
                sub_task_id: subTaskId
            };

            // 如果有子任务ID，则添加到请求体中
            if (subTaskId && subTaskId !== 'null') {
                requestBody.sub_task_id = parseInt(subTaskId);
            }

            // 调用后端API删除子任务
            const response = await fetch(`http://localhost:${port}/api/remove_subtask`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    console.log(`删除子任务成功`, result.data);
                    // 如果后端返回了子任务ID，更新subTaskId
                    if (result.data && result.data.sub_task_id) {
                        subTaskId = result.data.sub_task_id;
                        console.log(`获取到删除的子任务ID: ${subTaskId}`);
                    }
                } else {
                    console.log(`删除子任务失败: ${result.message}`);
                    showMessage(`❌ 删除子任务失败: ${result.message}`, 'error');
                    return; // 删除失败，不继续执行
                }
            } else {
                console.log(`删除子任务失败: HTTP ${response.status}`);
                showMessage(`❌ 删除子任务失败: HTTP ${response.status}`, 'error');
                return; // 删除失败，不继续执行
            }

            const tag = span.closest('.tag');
            
            // 确认删除
            if (!confirm(`确定要删除子任务"${subTaskName}"吗？`)) {
                return;
            }

            // 从 DOM 中获取当前的开始时间和结束时间
            const startInput = tag.querySelector('input:nth-of-type(1)');
            const endInput = tag.querySelector('input:nth-of-type(2)');
            const startTime = startInput ? startInput.value : '';
            const endTime = endInput ? endInput.value : '';
            const timeSlot = `${startTime} - ${endTime}`;

            console.log(`已删除子任务 ${subTaskName} （所属任务 ${taskName}）`);
            tag.remove();

            // 保存已删除的子任务
            const deletedSubtask = {
                taskName: taskName,
                subTaskName: subTaskName,
                subTaskContent: subTaskName, // 保持兼容性
                timeSlot: timeSlot,
                subTaskId: subTaskId && subTaskId !== 'null' ? parseInt(subTaskId) : null,
                taskid: taskid,
                plan_id: plan_id
            };
            deletedSubtasks.push(deletedSubtask);
            commitChangeAction(`删除子任务 ${subTaskName} （所属任务 ${taskName}）`);
            // 渲染已删除子任务表格
            renderDeletedSubtasks();
            updateTaskTimeRange(taskName);

        } catch (error) {
            console.error('删除子任务失败:', error);
        }
    }
        // 更新任务时间范围函数
        function updateTaskTimeRange(taskName) {
            const taskPlanTable = document.getElementById('taskPlanTable');
            const rows = taskPlanTable.getElementsByTagName('tr');
            let earliestStartTime = null;
            let latestEndTime = null;

            // 遍历表格行找到对应的任务
            for (let i = 0; i < rows.length; i++) {
                const taskNameCell = rows[i].cells[0];
                if (taskNameCell.textContent === taskName) {
                    const subTaskCells = rows[i].cells[2].querySelectorAll('.tag');
                    // 遍历子任务，找出最早开始时间和最晚结束时间
                    subTaskCells.forEach(subTaskCell => {
                        const startInput = subTaskCell.querySelector('input:nth-of-type(1)');
                        const endInput = subTaskCell.querySelector('input:nth-of-type(2)');
                        const subStartDate = new Date(`1970-01-01T${startInput.value}`);
                        const subEndDate = new Date(`1970-01-01T${endInput.value}`);

                        if (!earliestStartTime || subStartDate < earliestStartTime) {
                            earliestStartTime = subStartDate;
                        }
                        if (!latestEndTime || subEndDate > latestEndTime) {
                            latestEndTime = subEndDate;
                        }
                    });

                    // 格式化最早和最晚时间
                    const formattedStartTime = formatTime(earliestStartTime.toLocaleTimeString('en-US', { hour12: false }));
                    const formattedEndTime = formatTime(latestEndTime.toLocaleTimeString('en-US', { hour12: false }));

                    // 更新任务时间段输入框的值
                    const taskTimeCells = rows[i].cells[1].querySelectorAll('input');
                    taskTimeCells[0].value = formattedStartTime;
                    taskTimeCells[1].value = formattedEndTime;

                    break;
                }
            }
        }

        // 格式化时间函数
        function formatTime(time) {
            return time.replace(/(\d{2}):(\d{2})/, '$1:$2');
        }


        // 绘制任务甘特图函数
        function drawTaskGantt(taskPlan) {
            const chartDom = document.getElementById('taskChart');
            const myChart = echarts.init(chartDom);

            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['任务']
                },
                xAxis: {
                    type: 'time',
                    splitLine: {
                        show: true
                    },
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'category',
                    data: taskPlan.map(task => task.task_name),
                    inverse: true,
                    splitLine: {
                        show: true
                    }
                },
                series: [{
                    name: '任务',
                    type: 'custom',
                    renderItem: (params, api) => {
                        const yIndex = api.value(0);
                        const start = api.coord([api.value(1), yIndex]);
                        const end = api.coord([api.value(2), yIndex]);
                        const height = api.size([0, 1])[1] * 0.6;

                        return {
                            type: 'rect',
                            shape: {
                                x: start[0],
                                y: start[1] - height / 2,
                                width: end[0] - start[0],
                                height: height
                            },
                            style: api.style()
                        };
                    },
                    itemStyle: {
                        color: '#007bff'
                    },
                    encode: {
                        y: 0,
                        x: [1, 2]
                    },
                    data: taskPlan.map(task => {
                        const [startTime, endTime] = task.time_slot.split(' - ');
                        const startDate = new Date(`1970-01-01T${startTime}`);
                        const endDate = new Date(`1970-01-01T${endTime}`);
                        return [task.task_name, startDate, endDate];
                    })
                }]
            };

            myChart.setOption(option);
        }
        // 新增函数，用于处理时间更新
        function updateTaskTime(input, taskName, type) {
            const newTime = input.value;
            console.log(`任务 ${taskName} 的 ${type === 'start' ? '开始' : '结束'} 时间更新为 ${newTime}`);
            // 这里可以添加发送更新请求到后端的逻辑
        }

        // 检查所有子任务时间冲突
        function checkAllSubTasksTimeConflict() {
            const taskPlanTable = document.getElementById('taskPlanTable');
            const rows = taskPlanTable.getElementsByTagName('tr');
            let allSubTasks = [];

            // 遍历表格行，收集所有任务的子任务时间信息
            for (let i = 1; i < rows.length; i++) { // 从 1 开始，跳过表头
                const subTaskCells = rows[i].cells[2].querySelectorAll('.tag');
                subTaskCells.forEach(subTaskCell => {
                    const startInput = subTaskCell.querySelector('input:nth-of-type(1)');
                    const endInput = subTaskCell.querySelector('input:nth-of-type(2)');
                    allSubTasks.push({
                        startTime: startInput.value,
                        endTime: endInput.value
                    });
                });
            }

            // 检查时间是否冲突
            const conflictResult = hasTimeConflict(allSubTasks);
            if (conflictResult.conflict) {
                const prevTask = conflictResult.prevTask;
                const currentTask = conflictResult.currentTask;
                const conflictMessage = `子任务时间存在冲突！冲突子任务时间为 ${prevTask.startTime} - ${prevTask.endTime}，当前子任务时间为 ${currentTask.startTime} - ${currentTask.endTime}，请重新调整！`;
                showMessageInSection(conflictMessage, 'error', true);
                hasConflict = true;
            } else {
                // 没有冲突，隐藏消息
  
                showMessageInSection('', 'error', false);
                hasConflict = false;
            }
        }

        // 撤销删除子任务函数
        async function undoRemoveSubTask(index) {
            try {
                const deletedSubtask = deletedSubtasks[index];

                // 从 timeSlot 中解析开始和结束时间，并添加默认值处理
                let startTime = '';
                let endTime = '';
                if (deletedSubtask.timeSlot) {
                    const timeParts = deletedSubtask.timeSlot.split(' - ');
                    startTime = timeParts[0]?.trim() || '';
                    endTime = timeParts[1]?.trim() || '';
                }

                const childId = parseInt(childIdInput.value) || 4;

                // 构建请求体，优先使用子任务ID
                const requestBody = {
                    child_id: childId,
                    task_name: deletedSubtask.taskName,
                    sub_task_content: deletedSubtask.subTaskContent || deletedSubtask.subTaskName
                };

                // 如果有子任务ID，则添加到请求体中
                if (deletedSubtask.subTaskId) {
                    requestBody.sub_task_id = deletedSubtask.subTaskId;
                }

                console.log('撤销删除子任务请求:', requestBody);

                // 调用后端API撤销删除子任务
                const response = await fetch(`http://localhost:${port}/api/restore_subtask`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        // 撤销成功，重新添加到任务表格
                        const taskPlanTable = document.getElementById('taskPlanTable');
                        if (taskPlanTable) {
                            const rows = taskPlanTable.getElementsByTagName('tr');

                            for (let i = 1; i < rows.length; i++) { // 跳过表头
                                const taskNameCell = rows[i].cells[0];
                                if (taskNameCell && taskNameCell.textContent.includes(deletedSubtask.taskName)) {
                                    const subTaskCell = rows[i].cells[2];
                                    if (subTaskCell) {
                                        const newTag = document.createElement('div');
                                        newTag.className = 'tag';
                                        newTag.innerHTML = `
                                            <div class="sub-task-content">${deletedSubtask.subTaskName}</div>
                                            <div class="sub-task-time">
                                                <input type="time" value="${startTime}" step="60" onchange="updateSubTaskTime(this, '${deletedSubtask.taskName}', '${deletedSubtask.subTaskName}', 'start', '${deletedSubtask.subTaskId || 'null'}', '${deletedSubtask.taskid || 'null'}', '${deletedSubtask.plan_id || 'null'}')"> - 
                                                <input type="time" value="${endTime}" step="60" onchange="updateSubTaskTime(this,  '${deletedSubtask.taskName}', '${deletedSubtask.subTaskName}', 'end', '${deletedSubtask.subTaskId || 'null'}', '${deletedSubtask.taskid || 'null'}', '${deletedSubtask.plan_id || 'null'}')">
                                            </div>
                                            <span class="tag-delete" onclick="removeSubTask(this, '${deletedSubtask.taskName}', '${deletedSubtask.subTaskName}','${startTime}', '${endTime}', '${deletedSubtask.subTaskId || 'null'}', '${deletedSubtask.taskid || 'null'}', '${deletedSubtask.subTaskId || 'null'}')">×</span>
                                        `;
                                        subTaskCell.appendChild(newTag);
                                        break;
                                    }
                                }
                            }
                        }

                        // 从已删除子任务数组中移除
                        deletedSubtasks.splice(index, 1);
                        // 重新渲染已删除子任务表格
                        renderDeletedSubtasks();

                        // 更新任务时间范围
                        if (typeof updateTaskTimeRange === 'function') {
                            updateTaskTimeRange(deletedSubtask.taskName);
                        }

                        // 检查所有子任务时间冲突
                        if (typeof checkAllSubTasksTimeConflict === 'function') {
                            checkAllSubTasksTimeConflict();
                        }

                        // 显示成功消息
                        showMessage(`✅ 子任务"${deletedSubtask.subTaskName}"撤销删除成功`, 'success');
                        commitChangeAction(`撤销删除子任务 ${deletedSubtask.subTaskName} （所属任务 ${deletedSubtask.taskName}）`);

                        console.log('撤销删除子任务成功:', result);
                    } else {
                        showMessage(`❌ 撤销删除失败: ${result.message}`, 'error');
                        console.error('撤销删除子任务失败:', result.message);
                    }
                } else {
                    const errorText = await response.text();
                    showMessage(`❌ 撤销删除失败: HTTP ${response.status}`, 'error');
                    console.error('撤销删除子任务HTTP错误:', response.status, errorText);
                }

            } catch (error) {
                console.error('撤销删除子任务失败:', error);
                showMessage(`❌ 撤销删除失败: ${error.message}`, 'error');
            }
        }

        // 渲染已删除子任务表格
        function renderDeletedSubtasks() {
            const deletedSubtasksBody = document.getElementById('deletedSubtasksBody');
            let tableHtml = '';

            deletedSubtasks.forEach((deletedSubtask, index) => {
                tableHtml += '<tr>';
                tableHtml += `<td>${deletedSubtask.taskName}</td>`;
                tableHtml += `<td>${deletedSubtask.subTaskName}</td>`;
                tableHtml += `<td>${deletedSubtask.timeSlot}</td>`;
                tableHtml += `<td><button onclick="undoRemoveSubTask(${index})">撤销</button></td>`;
                tableHtml += '</tr>';
            });
            deletedSubtasksBody.innerHTML = tableHtml;
        }



        // 假设服务器通过 Socket 发送任务计划数据
        socket.on('task_plan', function (data) {
            console.log('收到任务计划:', data);
            showTaskPlan(data);

            // 隐藏加载提示
            const taskPlanLoading = document.getElementById('taskPlanLoading');
            taskPlanLoading.style.display = 'none';

            // 移除自动保存逻辑，改为用户手动点击确认任务按钮时才保存
            // saveTaskPlanToDatabase(data);

            // 存储任务计划数据，供确认按钮使用
            window.currentTaskPlan = data;

            // 显示确认任务按钮
            showConfirmTaskButton();
        });

        // 显示确认任务按钮
        function showConfirmTaskButton() {
            // 检查是否已存在确认按钮
            let confirmButton = document.getElementById('confirm-task-btn');
            if (!confirmButton) {
                // 创建确认任务按钮
                confirmButton = document.createElement('button');
                confirmButton.id = 'confirm-task-btn';
                confirmButton.className = 'btn btn-checkTask';
                confirmButton.style.cssText = 'margin: 20px auto; display: block; background: linear-gradient(45deg, #ff6b6b, #ffa500);';
                confirmButton.textContent = '确认任务';

                // 添加点击事件
                confirmButton.addEventListener('click', confirmTaskPlan);

                // 将按钮添加到合适的位置（任务生成按钮后面）
                const taskGenerateBtn = document.getElementById('task-generate-btn');
                if (taskGenerateBtn && taskGenerateBtn.parentNode) {
                    taskGenerateBtn.parentNode.insertBefore(confirmButton, taskGenerateBtn.nextSibling);
                }
            }

            // 显示按钮
            confirmButton.style.display = 'block';
        }

        // 隐藏确认任务按钮
        function hideConfirmTaskButton() {
            const confirmButton = document.getElementById('confirm-task-btn');
            if (confirmButton) {
                confirmButton.style.display = 'none';
            }
        }

        // 确认任务计划
        async function confirmTaskPlan() {
            if (!window.currentTaskPlan) {
                showMessage('❌ 没有可确认的任务计划', 'error');
                return;
            }

            try {
                console.log('用户点击确认任务，开始保存任务计划到数据库:', window.currentTaskPlan);

                const childId = parseInt(childIdInput.value) || 4; // 从输入框获取ID，默认为4

                const response = await fetch(`http://localhost:${port}/api/confirm_task_plan`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        child_id: childId,
                        task_plan: window.currentTaskPlan
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('任务计划保存结果:', result);

                if (result.success) {
                    showMessage(`✅ 任务计划已确认并保存到数据库`, 'success');

                    // 重新加载数据库中的实际任务数据
                    await reloadTasksFromDatabase(childId);

                    // 隐藏确认按钮
                    hideConfirmTaskButton();

                    // 清除当前任务计划数据
                    window.currentTaskPlan = null;
                } else {
                    showMessage(`❌ 确认任务计划失败: ${result.message}`, 'error');
                }

            } catch (error) {
                console.error('确认任务计划失败:', error);
                showMessage(`❌ 确认任务计划失败: ${error.message}`, 'error');
            }
        }

        async function showMessage(message,type) {
            console.log("showMessage:",message)
        }

        // 保存任务计划到数据库（保留原函数供其他地方调用）
        async function saveTaskPlanToDatabase(taskPlan,planID) {
            try {
                console.log('开始保存任务计划到数据库:', taskPlan);

                const childId = parseInt(childIdInput.value) || 4; // 从输入框获取ID，默认为4
                PlanID=parseInt(planID)


                console.log("检测数据类型：")
                console.log(JSON.stringify({
                        child_id: childId,
                        task_plan: taskPlan,
                        planID: PlanID
                    }))

                const response = await fetch(`http://localhost:${port}/api/confirm_task_plan`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        child_id: childId,
                        task_plan: taskPlan,
                        planID: PlanID
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('任务计划保存结果:', result);

                if (result.success) {
                    showMessage(`✅ 任务计划已保存到数据库`, 'success');

                    // 更新前端任务表，添加数据库ID
                    if (result.data && result.data.created_tasks) {
                        updateTaskPlanWithDatabaseIds(result.data.created_tasks);
                    }
                } else {
                    showMessage(`❌ 保存任务计划失败: ${result.message}`, 'error');
                }

            } catch (error) {
                console.error('保存任务计划失败:', error);
                showMessage(`❌ 保存任务计划失败: ${error.message}`, 'error');
            }
        }

        // 从数据库重新加载任务数据
        async function reloadTasksFromDatabase(childId) {
            try {
                console.log('开始从数据库重新加载任务数据...');

                const response = await fetch(`http://localhost:${port}/api/get_today_tasks/${childId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('从数据库获取的任务数据:', result);

                if (result.success && result.data) {
                    // 使用数据库中的实际数据重新渲染任务表格
                    showTaskPlan(result.data);
                    console.log('任务数据重新加载完成');
                } else {
                    console.error('获取任务数据失败:', result.message);
                    showMessage(`❌ 重新加载任务数据失败: ${result.message}`, 'error');
                }

            } catch (error) {
                console.error('重新加载任务数据失败:', error);
                showMessage(`❌ 重新加载任务数据失败: ${error.message}`, 'error');
            }
        }

        // 更新任务计划的数据库ID（保留用于兼容性）
        function updateTaskPlanWithDatabaseIds(savedTasks) {
            console.log('开始更新前端任务表的数据库ID:', savedTasks);

            console.log("tempTaskPlan.id:",tempTaskPlan.id)
            savedTasks.forEach(task => {
                task.id = tempTaskPlan.id;
            });

            if (!savedTasks || !Array.isArray(savedTasks)) {
                console.warn('savedTasks 不是有效的数组');
                return;
            }

            // 获取任务表格
            const taskPlanTable = document.getElementById('taskPlanTable');
            if (!taskPlanTable) {
                console.warn('未找到任务表格');
                return;
            }

            const rows = taskPlanTable.getElementsByTagName('tr');

            // 遍历保存的任务数据
            savedTasks.forEach(savedTask => {
                const taskName = savedTask.task_name;
                const subTasks = savedTask.sub_tasks || [];
               
                console.log(`更新任务 "${taskName}" 的子任务ID:`, subTasks);


                // 在表格中找到对应的任务行
                for (let i = 1; i < rows.length; i++) { // 跳过表头
                    const taskNameCell = rows[i].cells[0];
                    if (taskNameCell && taskNameCell.textContent.includes(taskName)) {
                        const subTaskCell = rows[i].cells[2]; // 子任务列
                        if (subTaskCell) {
                            // 获取该行的所有子任务标签
                            const subTaskTags = subTaskCell.querySelectorAll('.tag');

                            // 更新每个子任务标签的删除按钮，添加子任务ID
                            subTaskTags.forEach((tag, tagIndex) => {
                                const deleteSpan = tag.querySelector('.tag-delete');
                                if (deleteSpan && tagIndex < subTasks.length) {
                                    const subTask = subTasks[tagIndex];
                                    const subTaskId = subTask.id;

                                    // 获取当前的onclick属性
                                    const currentOnclick = deleteSpan.getAttribute('onclick');
                                    if (currentOnclick) {
                                        // 替换最后的null为实际的子任务ID
                                        const updatedOnclick = currentOnclick.replace(/,\s*null\s*\)$/, `, ${subTaskId})`);
                                        deleteSpan.setAttribute('onclick', updatedOnclick);

                                        console.log(`更新子任务 "${subTask.task_content}" 的ID为: ${subTaskId}`);
                                    }
                                }
                            });
                        }
                        break;
                    }
                }
            });

            console.log('任务ID更新完成');
        }

// 音频播放功能
        function playAudioFromBase64(audioBase64, text, format = 'mp3') {
            try {
                console.log('🎵 开始播放音频:', text, '格式:', format);
                console.log('🎵 Base64数据长度:', audioBase64.length);

                // 验证Base64数据
                if (!audioBase64 || audioBase64.length === 0) {
                    throw new Error('Base64音频数据为空');
                }

                // 停止当前播放的音频
                if (currentAudio) {
                    console.log('🎵 停止当前播放的音频');
                    currentAudio.pause();
                    currentAudio = null;
                }

                // 根据格式确定MIME类型
                let mimeType = 'audio/mpeg'; // 默认MP3
                if (format === 'wav') {
                    mimeType = 'audio/wav';
                } else if (format === 'mp3') {
                    mimeType = 'audio/mpeg';
                }

                console.log('🎵 使用MIME类型:', mimeType);

                // 创建音频数据URL
                const audioBlob = base64ToBlob(audioBase64, mimeType);
                const audioUrl = URL.createObjectURL(audioBlob);

                console.log('🎵 音频Blob大小:', audioBlob.size, 'bytes');
                console.log('🎵 音频URL:', audioUrl);

                // 验证Blob大小
                if (audioBlob.size === 0) {
                    throw new Error('生成的音频Blob为空');
                }

                // 创建音频元素
                currentAudio = new Audio(audioUrl);

                // 设置音频事件
                currentAudio.onloadstart = function () {
                    console.log('音频开始加载');
                    showAudioPlayer(text, '正在加载...');
                };

                currentAudio.oncanplay = function () {
                    console.log('音频可以播放');
                    updateAudioPlayerStatus('准备播放');
                };

                currentAudio.onplay = function () {
                    console.log('音频开始播放');
                    updateAudioPlayerStatus('正在播放');
                };

                currentAudio.onerror = function (e) {
                    console.error('音频播放错误:', e);
                    console.error('错误详情:', currentAudio.error);
                    showtmpMessage('音频播放错误: ' + (currentAudio.error ? currentAudio.error.message : '未知错误'), 'error');
                };

                currentAudio.onloadeddata = function () {
                    console.log('音频数据加载完成');
                };

                currentAudio.oncanplaythrough = function () {
                    console.log('音频可以完整播放');
                };

                currentAudio.onended = function () {
                    console.log('音频播放完成');
                    hideAudioPlayer();
                    URL.revokeObjectURL(audioUrl);
                    currentAudio = null;
                };

                currentAudio.onerror = function (e) {
                    console.error('音频播放错误:', e);
                    updateAudioPlayerStatus('播放失败');
                    setTimeout(hideAudioPlayer, 2000);
                };

                // 自动播放
                currentAudio.play().catch(error => {
                    console.error('自动播放失败:', error);
                    updateAudioPlayerStatus('播放失败 - 请手动点击播放');
                });

            } catch (error) {
                console.error('播放音频时出错:', error);
            }
        }

        // Base64转Blob
        function base64ToBlob(base64, mimeType) {
            const byteCharacters = atob(base64);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
        }

        // 显示音频播放器
        function showAudioPlayer(text, status) {
            const audioPlayer = document.getElementById('audioPlayer');
            const audioText = document.getElementById('audioText');

            audioText.textContent = text;
            audioPlayer.style.display = 'block';

            if (status) {
                updateAudioPlayerStatus(status);
            }
        }

        // 更新音频播放器状态
        function updateAudioPlayerStatus(status) {
            const audioText = document.getElementById('audioText');
            const originalText = audioText.textContent;
            audioText.innerHTML = `${originalText}<br><small style="opacity: 0.7;">${status}</small>`;
        }

        // 隐藏音频播放器
        function hideAudioPlayer() {
            const audioPlayer = document.getElementById('audioPlayer');
            audioPlayer.style.display = 'none';
        }

        // 监听来自服务器的音频播放事件
        socket.on('play_audio', function (data) {
            console.log('🎵 收到音频播放请求:', data.text);
            console.log('🎵 音频数据长度:', data.audio_data ? data.audio_data.length : 0);
            console.log('🎵 音频格式:', data.format || 'mp3');
            console.log('🎵 完整数据:', data);

            // 显示明显的提示
            showtmpMessage('🎵 收到TTS音频播放请求: ' + data.text, 'success');

            // 立即显示音频播放器
            showAudioPlayer(data.text, '正在准备播放...');

            try {
                // 验证音频数据
                if (!data.audio_data || data.audio_data.length === 0) {
                    throw new Error('音频数据为空');
                }

                // 传递格式参数
                playAudioFromBase64(data.audio_data, data.text, data.format || 'mp3');
                showtmpMessage('🎵 正在播放语音: ' + data.text, 'success');
            } catch (error) {
                console.error('🎵 播放音频失败:', error);
                showtmpMessage('🎵 音频播放失败: ' + data.text + ' - ' + error.message, 'error');
                updateAudioPlayerStatus('播放失败: ' + error.message);
            }
        });

        // 监听TTS成功事件
        socket.on('tts_success', function (data) {
            console.log('TTS成功:', data.message);
            showtmpMessage('语音播放成功: ' + data.text, 'success');
        });

        // 监听TTS错误事件
        socket.on('tts_error', function (data) {
            console.error('TTS错误:', data.error);
            showtmpMessage('语音合成失败: ' + data.text, 'error');
        });




        
      
        // setInterval(() => {
            
        //         // 连接ASR服务（如果未连接）
        //     if (!isConnected) {
        //         console.log('🔗 ASR服务未连接，正在连接...');
        //         connectService();
        //     }

        //     await startRecognition();
        // }, 5000);

     




    </script>
</body>

</html>