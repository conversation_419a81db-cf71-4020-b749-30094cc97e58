# 数据库迁移脚本：为任务表添加时间字段
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from sqlalchemy import text
from core.user_management.database.connection import get_db_session_context

logger = logging.getLogger(__name__)

def migrate_add_task_time_fields():
    """
    为 daily_tasks 表添加 start_time 和 end_time 字段
    """
    try:
        with get_db_session_context() as session:
            # 检查字段是否已存在 (SQLite版本)
            check_columns_sql = "PRAGMA table_info(daily_tasks)"

            columns_info = session.execute(text(check_columns_sql)).fetchall()
            existing_columns = [col[1] for col in columns_info]  # col[1] 是列名

            start_time_exists = 'start_time' in existing_columns
            end_time_exists = 'end_time' in existing_columns
            
            if start_time_exists and end_time_exists:
                logger.info("时间字段已存在，跳过迁移")
                return True
            
            # 添加 start_time 字段 (SQLite版本)
            if not start_time_exists:
                add_start_time_sql = """
                ALTER TABLE daily_tasks
                ADD COLUMN start_time DATETIME
                """
                session.execute(text(add_start_time_sql))
                logger.info("已添加 start_time 字段")

            # 添加 end_time 字段 (SQLite版本)
            if not end_time_exists:
                add_end_time_sql = """
                ALTER TABLE daily_tasks
                ADD COLUMN end_time DATETIME
                """
                session.execute(text(add_end_time_sql))
                logger.info("已添加 end_time 字段")
            
            session.commit()
            logger.info("数据库迁移完成：已添加任务时间字段")
            return True
            
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        return False

def rollback_task_time_fields():
    """
    回滚：删除 start_time 和 end_time 字段
    """
    try:
        with get_db_session_context() as session:
            # 删除 start_time 字段
            drop_start_time_sql = """
            ALTER TABLE daily_tasks 
            DROP COLUMN IF EXISTS start_time
            """
            session.execute(text(drop_start_time_sql))
            
            # 删除 end_time 字段
            drop_end_time_sql = """
            ALTER TABLE daily_tasks 
            DROP COLUMN IF EXISTS end_time
            """
            session.execute(text(drop_end_time_sql))
            
            session.commit()
            logger.info("数据库回滚完成：已删除任务时间字段")
            return True
            
    except Exception as e:
        logger.error(f"数据库回滚失败: {e}")
        return False

if __name__ == "__main__":
    # 执行迁移
    success = migrate_add_task_time_fields()
    if success:
        print("✅ 数据库迁移成功")
    else:
        print("❌ 数据库迁移失败")
