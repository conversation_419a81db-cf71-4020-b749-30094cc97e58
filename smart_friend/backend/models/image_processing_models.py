"""
图像预处理相关的数据模型
定义图像预处理算法的请求和响应模型
"""
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from enum import Enum


class GrayscaleMethod(str, Enum):
    """灰度化方法枚举"""
    AVERAGE = "average"  # 平均值法
    WEIGHTED = "weighted"  # 加权平均法 (0.299*R + 0.587*G + 0.114*B)
    LUMINOSITY = "luminosity"  # 亮度法
    OPENCV = "opencv"  # OpenCV默认方法


class BlurMethod(str, Enum):
    """模糊方法枚举"""
    GAUSSIAN = "gaussian"  # 高斯模糊
    BOX = "box"  # 盒式滤波
    MEDIAN = "median"  # 中值模糊


class EnhancementType(str, Enum):
    """图像增强类型枚举"""
    BRIGHTNESS = "brightness"  # 亮度调整
    CONTRAST = "contrast"  # 对比度调整
    SATURATION = "saturation"  # 饱和度调整
    GAMMA = "gamma"  # 伽马校正
    AUTO_ENHANCE = "auto_enhance"  # 自动增强


class ImageProcessingRequest(BaseModel):
    """图像处理基础请求模型"""
    image_data: str = Field(..., description="Base64编码的图像数据")
    return_format: str = Field("jpeg", description="返回图像格式 (jpeg/png)")
    quality: int = Field(85, ge=1, le=100, description="JPEG质量 (1-100)")


class GrayscaleRequest(ImageProcessingRequest):
    """图像灰度化请求模型"""
    method: GrayscaleMethod = Field(GrayscaleMethod.WEIGHTED, description="灰度化方法")
    preserve_alpha: bool = Field(False, description="是否保留透明度通道")


class BlurRequest(ImageProcessingRequest):
    """高斯模糊请求模型"""
    method: BlurMethod = Field(BlurMethod.GAUSSIAN, description="模糊方法")
    kernel_size: int = Field(5, ge=3, le=31, description="核大小 (3-31，必须为奇数)")
    sigma_x: float = Field(1.0, ge=0.1, le=10.0, description="X方向标准差")
    sigma_y: Optional[float] = Field(None, ge=0.1, le=10.0, description="Y方向标准差，默认与sigma_x相同")


class EnhancementRequest(ImageProcessingRequest):
    """图像增强请求模型"""
    enhancement_type: EnhancementType = Field(..., description="增强类型")
    brightness: Optional[float] = Field(0.0, ge=-100.0, le=100.0, description="亮度调整 (-100到100)")
    contrast: Optional[float] = Field(1.0, ge=0.1, le=3.0, description="对比度调整 (0.1到3.0)")
    saturation: Optional[float] = Field(1.0, ge=0.0, le=3.0, description="饱和度调整 (0.0到3.0)")
    gamma: Optional[float] = Field(1.0, ge=0.1, le=3.0, description="伽马值 (0.1到3.0)")
    auto_enhance_strength: Optional[float] = Field(1.0, ge=0.1, le=2.0, description="自动增强强度 (0.1到2.0)")


class ImageProcessingResponse(BaseModel):
    """图像处理响应模型"""
    success: bool = Field(..., description="处理是否成功")
    message: str = Field(..., description="响应消息")
    processed_image: Optional[str] = Field(None, description="Base64编码的处理后图像")
    processing_time: Optional[float] = Field(None, description="处理耗时（秒）")
    original_size: Optional[str] = Field(None, description="原始图像尺寸")
    processed_size: Optional[str] = Field(None, description="处理后图像尺寸")
    algorithm_info: Optional[Dict[str, Any]] = Field(None, description="算法信息和参数")


class ImageProcessingHealthResponse(BaseModel):
    """图像处理服务健康检查响应模型"""
    service_status: str = Field(..., description="服务状态")
    opencv_version: str = Field(..., description="OpenCV版本")
    available_methods: Dict[str, List[str]] = Field(..., description="可用的处理方法")
    timestamp: str = Field(..., description="检查时间戳")


class BatchProcessingRequest(BaseModel):
    """批量处理请求模型"""
    images: List[str] = Field(..., description="Base64编码的图像数据列表")
    processing_type: str = Field(..., description="处理类型 (grayscale/blur/enhancement)")
    parameters: Dict[str, Any] = Field(..., description="处理参数")
    return_format: str = Field("jpeg", description="返回图像格式")
    quality: int = Field(85, ge=1, le=100, description="JPEG质量")


class BatchProcessingResponse(BaseModel):
    """批量处理响应模型"""
    success: bool = Field(..., description="批量处理是否成功")
    message: str = Field(..., description="响应消息")
    processed_images: List[str] = Field(..., description="处理后的图像列表")
    failed_indices: List[int] = Field(..., description="处理失败的图像索引")
    total_processing_time: float = Field(..., description="总处理时间（秒）")
    individual_times: List[float] = Field(..., description="每张图像的处理时间")
