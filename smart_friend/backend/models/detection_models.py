"""
检测相关的数据模型
定义坐姿检测和桌面检测的请求和响应模型
"""

from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum

class DetectionType(str, Enum):
    """检测类型枚举"""
    POSTURE = "posture"
    DESKTOP = "desktop"
    BOTH = "both"

class ImageFormat(str, Enum):
    """图像格式枚举"""
    JPEG = "jpeg"
    PNG = "png"
    JPG = "jpg"

class QualityLevel(str, Enum):
    """质量等级枚举"""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"

# 基础请求模型
class BaseDetectionRequest(BaseModel):
    """基础检测请求模型"""
    image: str = Field(..., description="base64编码的图像数据")
    timestamp: Optional[datetime] = Field(default_factory=datetime.now, description="请求时间戳")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ImageDetectionRequest(BaseDetectionRequest):
    """图像检测请求模型"""
    detection_type: DetectionType = Field(default=DetectionType.BOTH, description="检测类型")
    image_format: Optional[ImageFormat] = Field(default=ImageFormat.JPEG, description="图像格式")
    
class PostureDetectionRequest(BaseDetectionRequest):
    """坐姿检测请求模型"""
    sensitivity: Optional[float] = Field(default=0.5, ge=0.0, le=1.0, description="检测敏感度")
    include_visualization: Optional[bool] = Field(default=False, description="是否包含可视化结果")

class DesktopDetectionRequest(BaseDetectionRequest):
    """桌面检测请求模型"""
    confidence_threshold: Optional[float] = Field(default=0.25, ge=0.0, le=1.0, description="置信度阈值")
    include_segmentation: Optional[bool] = Field(default=True, description="是否包含分割结果")

# 坐姿检测结果模型
class PostureAnalysis(BaseModel):
    """坐姿分析详细结果"""
    head_tilt: Optional[float] = Field(None, description="头部倾斜角度")
    shoulder_level: Optional[float] = Field(None, description="肩膀水平度")
    body_straightness: Optional[float] = Field(None, description="身体挺直度")
    is_good_posture: Optional[bool] = Field(None, description="是否为良好坐姿")
    confidence_score: Optional[float] = Field(None, description="检测置信度")
    learning_state: Optional[str] = Field(None, description="学习状态")
    attention_level: Optional[str] = Field(None, description="专注程度")

class PostureDetectionResult(BaseModel):
    """坐姿检测结果模型"""
    success: bool = Field(..., description="检测是否成功")
    message: str = Field(..., description="结果消息")
    result: Optional[Dict[str, Any]] = Field(None, description="原始检测结果")
    analysis: Optional[PostureAnalysis] = Field(None, description="分析结果")
    visualization_image: Optional[str] = Field(None, description="可视化图像(base64)")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")
    timestamp: Optional[datetime] = Field(default_factory=datetime.now, description="检测时间")

# 桌面检测结果模型
class DetectedObject(BaseModel):
    """检测到的物体"""
    class_name: str = Field(..., description="物体类别名称")
    confidence: float = Field(..., description="检测置信度")
    bbox: List[float] = Field(..., description="边界框坐标[x1,y1,x2,y2]")
    area: Optional[float] = Field(None, description="物体面积")
    is_learning_object: Optional[bool] = Field(None, description="是否为学习用品")

class DesktopLayoutAnalysis(BaseModel):
    """桌面布局分析"""
    overall_score: float = Field(..., description="总体评分(0-100)")
    quality_level: QualityLevel = Field(..., description="质量等级")
    learning_objects: int = Field(..., description="学习用品数量")
    total_objects: int = Field(..., description="总物体数量")
    organization_score: Optional[float] = Field(None, description="整理度评分")
    cleanliness_score: Optional[float] = Field(None, description="清洁度评分")
    focus_score: Optional[float] = Field(None, description="专注度评分")

class DesktopDetectionResult(BaseModel):
    """桌面检测结果模型"""
    success: bool = Field(..., description="检测是否成功")
    message: str = Field(..., description="结果消息")
    overall_score: Optional[float] = Field(None, description="总体评分")
    quality_level: Optional[str] = Field(None, description="质量等级")
    learning_objects: Optional[int] = Field(None, description="学习用品数量")
    total_objects: Optional[int] = Field(None, description="总物体数量")
    suggestions: Optional[List[str]] = Field(None, description="改进建议列表")
    detected_objects: Optional[List[DetectedObject]] = Field(None, description="检测到的物体列表")
    layout_analysis: Optional[DesktopLayoutAnalysis] = Field(None, description="布局分析结果")
    detailed_analysis: Optional[Dict[str, Any]] = Field(None, description="详细分析数据")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")
    timestamp: Optional[datetime] = Field(default_factory=datetime.now, description="检测时间")

# 综合检测结果模型
class CombinedDetectionResult(BaseModel):
    """综合检测结果模型"""
    success: bool = Field(..., description="整体检测是否成功")
    message: str = Field(..., description="整体结果消息")
    posture_result: Optional[PostureDetectionResult] = Field(None, description="坐姿检测结果")
    desktop_result: Optional[DesktopDetectionResult] = Field(None, description="桌面检测结果")
    overall_assessment: Optional[Dict[str, Any]] = Field(None, description="综合评估")
    recommendations: Optional[List[str]] = Field(None, description="综合建议")
    total_processing_time: Optional[float] = Field(None, description="总处理时间(秒)")
    timestamp: Optional[datetime] = Field(default_factory=datetime.now, description="检测时间")

# 健康检查模型
class DetectorStatus(BaseModel):
    """检测器状态"""
    name: str = Field(..., description="检测器名称")
    initialized: bool = Field(..., description="是否已初始化")
    ready: bool = Field(..., description="是否就绪")
    last_check: Optional[datetime] = Field(None, description="最后检查时间")
    error_message: Optional[str] = Field(None, description="错误信息")

class DetectionHealthResponse(BaseModel):
    """检测服务健康状态响应"""
    status: str = Field(..., description="服务状态")
    ready: bool = Field(..., description="服务是否就绪")
    detectors: List[DetectorStatus] = Field(..., description="检测器状态列表")
    supported_detection_types: List[str] = Field(..., description="支持的检测类型")
    system_info: Optional[Dict[str, Any]] = Field(None, description="系统信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")

# 批量检测模型
class BatchDetectionRequest(BaseModel):
    """批量检测请求模型"""
    images: List[str] = Field(..., description="base64编码的图像数据列表")
    detection_type: DetectionType = Field(default=DetectionType.BOTH, description="检测类型")
    batch_id: Optional[str] = Field(None, description="批次ID")
    
class BatchDetectionResult(BaseModel):
    """批量检测结果模型"""
    success: bool = Field(..., description="批量检测是否成功")
    message: str = Field(..., description="结果消息")
    batch_id: Optional[str] = Field(None, description="批次ID")
    total_images: int = Field(..., description="总图像数量")
    successful_detections: int = Field(..., description="成功检测数量")
    failed_detections: int = Field(..., description="失败检测数量")
    results: List[CombinedDetectionResult] = Field(..., description="检测结果列表")
    total_processing_time: Optional[float] = Field(None, description="总处理时间(秒)")
    timestamp: datetime = Field(default_factory=datetime.now, description="检测时间")

# 检测历史记录模型
class DetectionRecord(BaseModel):
    """检测记录模型"""
    id: Optional[str] = Field(None, description="记录ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    detection_type: DetectionType = Field(..., description="检测类型")
    result: CombinedDetectionResult = Field(..., description="检测结果")
    image_hash: Optional[str] = Field(None, description="图像哈希值")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

class DetectionHistoryResponse(BaseModel):
    """检测历史响应模型"""
    total_count: int = Field(..., description="总记录数")
    records: List[DetectionRecord] = Field(..., description="检测记录列表")
    page: Optional[int] = Field(None, description="页码")
    page_size: Optional[int] = Field(None, description="页面大小")
    has_more: Optional[bool] = Field(None, description="是否有更多记录")

# 统计分析模型
class DetectionStatistics(BaseModel):
    """检测统计模型"""
    total_detections: int = Field(..., description="总检测次数")
    posture_detections: int = Field(..., description="坐姿检测次数")
    desktop_detections: int = Field(..., description="桌面检测次数")
    average_posture_score: Optional[float] = Field(None, description="平均坐姿评分")
    average_desktop_score: Optional[float] = Field(None, description="平均桌面评分")
    success_rate: float = Field(..., description="成功率")
    date_range: Optional[Dict[str, str]] = Field(None, description="统计日期范围")
    timestamp: datetime = Field(default_factory=datetime.now, description="统计时间")
