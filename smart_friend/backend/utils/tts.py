import pygame
from websockets.asyncio.client import ClientConnection
import websockets
import asyncio
import json
import os
import tempfile
import time
import uuid
import warnings
import threading
import re
from typing import Union, Optional, Callable, List, Dict, Any

# 导入日志管理模块
from utils.logging_manager import get_logger

# 导入音频播放器
from utils.audio import AudioPlayer

# 获取日志记录器
logger = get_logger("TTsManager")

def preprocess_text_for_tts(text: str) -> str:
    """
    预处理文本以优化TTS输出，避免读出标点符号和语法格式

    Args:
        text (str): 原始文本

    Returns:
        str: 处理后的文本
    """
    # 移除Markdown代码块标记
    text = re.sub(r'```[\w]*\n|```', ' ', text)

    # 移除或替换常见的标点符号，避免TTS读出它们
    replacements = {
        '，': '，', # 保留中文逗号，但TTS会自动处理为停顿
        '。': '。', # 保留中文句号，但TTS会自动处理为停顿
        '、': ' ', # 顿号替换为空格
        '；': '，', # 分号替换为逗号
        '：': ' ', # 冒号替换为空格
        '"': ' ', # 引号替换为空格
        '"': ' ', # 引号替换为空格
        ''': ' ', # 引号替换为空格
        ''': ' ', # 引号替换为空格
        '【': ' ', # 方括号替换为空格
        '】': ' ', # 方括号替换为空格
        '（': ' ', # 圆括号替换为空格
        '）': ' ', # 圆括号替换为空格
        '《': ' ', # 书名号替换为空格
        '》': ' ', # 书名号替换为空格
        '—': ' ', # 破折号替换为空格
        '-': ' ', # 连字符替换为空格
        '_': ' ', # 下划线替换为空格
        '*': ' ', # 星号替换为空格
        '#': ' ', # 井号替换为空格
        '`': ' ', # 反引号替换为空格
        '+': '加', # 加号替换为"加"
        '=': '等于', # 等号替换为"等于"
        '/': ' ', # 斜杠替换为空格
        '\\': ' ', # 反斜杠替换为空格
        '|': ' ', # 竖线替换为空格
        '<': ' ', # 小于号替换为空格
        '>': ' ', # 大于号替换为空格
        '.': '。', # 英文句号替换为中文句号
        ',': '，', # 英文逗号替换为中文逗号
        '?': '？', # 英文问号替换为中文问号
        '!': '！', # 英文感叹号替换为中文感叹号
        ';': '，', # 英文分号替换为中文逗号
        ':': ' ', # 英文冒号替换为空格
        '(': ' ', # 英文圆括号替换为空格
        ')': ' ', # 英文圆括号替换为空格
        '[': ' ', # 英文方括号替换为空格
        ']': ' ', # 英文方括号替换为空格
        '{': ' ', # 英文花括号替换为空格
        '}': ' ', # 英文花括号替换为空格
    }

    for char, replacement in replacements.items():
        text = text.replace(char, replacement)

    # 移除连续的空格
    text = re.sub(r'\s+', ' ', text)

    # 移除行首行尾的空格
    text = text.strip()

    return text

# 抑制pygame的欢迎消息
os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'

# WebSocket协议常量
PROTOCOL_VERSION = 0b0001
DEFAULT_HEADER_SIZE = 0b0001

# 消息类型
FULL_CLIENT_REQUEST = 0b0001
AUDIO_ONLY_RESPONSE = 0b1011
FULL_SERVER_RESPONSE = 0b1001
ERROR_INFORMATION = 0b1111

# 消息类型特定标志
MsgTypeFlagNoSeq = 0b0000  # 无序列的非终端包
MsgTypeFlagPositiveSeq = 0b1  # 序列 > 0 的非终端包
MsgTypeFlagLastNoSeq = 0b10  # 无序列的最后一个包
MsgTypeFlagNegativeSeq = 0b11  # 负载包含事件号 (int32)
MsgTypeFlagWithEvent = 0b100

# 消息序列化
NO_SERIALIZATION = 0b0000
JSON = 0b0001

# 消息压缩
COMPRESSION_NO = 0b0000
COMPRESSION_GZIP = 0b0001

# 事件常量
EVENT_NONE = 0
EVENT_Start_Connection = 1
EVENT_FinishConnection = 2
EVENT_ConnectionStarted = 50  # 成功建连
EVENT_ConnectionFailed = 51  # 建连失败（可能是无法通过权限认证）
EVENT_ConnectionFinished = 52  # 连接结束

# 上行Session事件
EVENT_StartSession = 100
EVENT_FinishSession = 102

# 下行Session事件
EVENT_SessionStarted = 150
EVENT_SessionFinished = 152
EVENT_SessionFailed = 153

# 上行通用事件
EVENT_TaskRequest = 200

# 下行TTS事件
EVENT_TTSSentenceStart = 350
EVENT_TTSSentenceEnd = 351
EVENT_TTSResponse = 352


class Header:
    """
    WebSocket协议头部结构

    用于构建WebSocket通信所需的头部信息，包含协议版本、消息类型等关键元数据。
    此结构是WebSocket通信的基础组成部分。

    Attributes:
        header_size (int): 头部大小
        protocol_version (int): 协议版本号
        message_type (int): 消息类型，如FULL_CLIENT_REQUEST等
        message_type_specific_flags (int): 消息类型特定标志
        serial_method (int): 序列化方法（如JSON、无序列化）
        compression_type (int): 压缩类型（如GZIP、无压缩）
        reserved_data (int): 保留数据
    """
    def __init__(self,
                 protocol_version=PROTOCOL_VERSION,
                 header_size=DEFAULT_HEADER_SIZE,
                 message_type: int = 0,
                 message_type_specific_flags: int = 0,
                 serial_method: int = NO_SERIALIZATION,
                 compression_type: int = COMPRESSION_NO,
                 reserved_data=0):
        self.header_size = header_size
        self.protocol_version = protocol_version
        self.message_type = message_type
        self.message_type_specific_flags = message_type_specific_flags
        self.serial_method = serial_method
        self.compression_type = compression_type
        self.reserved_data = reserved_data

    def as_bytes(self) -> bytes:
        """
        将头部信息转换为字节序列

        Returns:
            bytes: 4字节的头部二进制数据
        """
        return bytes([
            (self.protocol_version << 4) | self.header_size,
            (self.message_type << 4) | self.message_type_specific_flags,
            (self.serial_method << 4) | self.compression_type,
            self.reserved_data
        ])


class Optional:
    """
    WebSocket协议可选项结构

    包含会话ID、事件类型等可选信息，用于扩展和补充Header中的基本信息。
    为WebSocket通信提供额外的控制和元数据。

    Attributes:
        event (int): 事件类型，如EVENT_StartSession等
        sessionId (str): 会话ID，用于标识特定的会话
        errorCode (int): 错误代码，在出错时使用
        connectionId (str): 连接ID，标识特定的连接
        response_meta_json (str): 响应元数据JSON字符串
        sequence (int): 序列号，用于有序消息
    """
    def __init__(self, event: int = EVENT_NONE, sessionId: str = None, sequence: int = None):
        self.event = event
        self.sessionId = sessionId
        self.errorCode: int = 0
        self.connectionId: Union[str, None] = None
        self.response_meta_json: Union[str, None] = None
        self.sequence = sequence

    def as_bytes(self) -> bytes:
        """
        将可选信息转换为字节序列

        Returns:
            bytes: 可选信息的二进制数据
        """
        option_bytes = bytearray()
        if self.event != EVENT_NONE:
            option_bytes.extend(self.event.to_bytes(4, "big", signed=True))
        if self.sessionId is not None:
            session_id_bytes = str.encode(self.sessionId)
            size = len(session_id_bytes).to_bytes(4, "big", signed=True)
            option_bytes.extend(size)
            option_bytes.extend(session_id_bytes)
        if self.sequence is not None:
            option_bytes.extend(self.sequence.to_bytes(4, "big", signed=True))
        return option_bytes


class Response:
    """
    WebSocket响应解析结构

    用于解析和存储服务器返回的WebSocket响应数据，包括头部信息、可选项和负载数据。
    是处理从服务器接收的响应的核心结构。

    Attributes:
        optional (Optional): 响应的可选信息
        header (Header): 响应的头部信息
        payload (bytes): 响应的有效负载数据
    """
    def __init__(self, header: Header, optional: Optional):
        self.optional = optional
        self.header = header
        self.payload: Union[bytes, None] = None

    def __str__(self):
        return super().__str__()


class WebSocketManager:
    """
    WebSocket连接管理器

    负责管理与TTS服务的WebSocket通信，包括建立连接、发送消息、接收响应等功能。
    提供了一个稳定、可靠的通信层，支持会话管理和流式数据传输。

    Attributes:
        url (str): WebSocket服务地址
        headers (dict): HTTP请求头
        ws (ClientConnection): WebSocket连接对象
        is_connected (bool): 是否已连接
        lock (asyncio.Lock): 异步锁，用于保证并发安全
        session_id (str): 当前会话ID
    """
    def __init__(self, url: str, headers: dict):
        """
        初始化WebSocket连接管理器

        Args:
            url (str): WebSocket服务地址
            headers (dict): 连接请求头
        """
        self.url = url
        self.headers = headers
        self.ws = None
        self.is_connected = False
        self.lock = asyncio.Lock()
        self.session_id = None

    async def connect(self) -> bool:
        """
        建立WebSocket连接

        尝试与TTS服务建立WebSocket连接，并进行必要的初始化通信。

        Returns:
            bool: 连接是否成功
        """
        try:
            self.ws = await websockets.connect(self.url, additional_headers=self.headers, max_size=1000000000)
            # 开始连接
            await self._start_connection()
            res = parser_response(await self.ws.recv())

            if res.optional.event != EVENT_ConnectionStarted:
                logger.error("WebSocket连接失败")
                return False

            self.is_connected = True
            logger.info("WebSocket连接成功建立")
            return True
        except Exception as e:
            logger.error(f"WebSocket连接异常: {e}")
            return False

    async def _start_connection(self):
        """
        发送开始连接请求

        向服务器发送初始化连接的请求，是建立通信的第一步。
        """
        header = Header(message_type=FULL_CLIENT_REQUEST,
                      message_type_specific_flags=MsgTypeFlagWithEvent).as_bytes()
        optional = Optional(event=EVENT_Start_Connection).as_bytes()
        payload = str.encode("{}")
        return await send_event(self.ws, header, optional, payload)

    async def disconnect(self):
        """
        断开WebSocket连接

        安全地关闭与服务器的WebSocket连接，包括发送结束连接请求。
        """
        if self.ws and self.is_connected:
            try:
                await self._finish_connection()
                res = parser_response(await self.ws.recv())
                await self.ws.close()
                logger.info("WebSocket连接已关闭")
            except Exception as e:
                logger.error(f"断开WebSocket连接异常: {e}")
            finally:
                self.is_connected = False
                self.ws = None

    async def _finish_connection(self):
        """
        发送结束连接请求

        向服务器发送终止连接的请求，是安全断开连接的重要步骤。
        """
        header = Header(message_type=FULL_CLIENT_REQUEST,
                      message_type_specific_flags=MsgTypeFlagWithEvent,
                      serial_method=JSON
                      ).as_bytes()
        optional = Optional(event=EVENT_FinishConnection).as_bytes()
        payload = str.encode('{}')
        return await send_event(self.ws, header, optional, payload)

    async def start_session(self, speaker: str) -> str:
        """
        开始TTS会话

        创建一个新的TTS会话，用于后续的文本转语音请求。

        Args:
            speaker (str): 发音人标识符

        Returns:
            str: 会话ID

        Raises:
            RuntimeError: WebSocket未连接或会话创建失败时抛出
        """
        if not self.is_connected or not self.ws:
            raise RuntimeError("WebSocket未连接")

        async with self.lock:
            session_id = uuid.uuid4().__str__().replace('-', '')
            header = Header(message_type=FULL_CLIENT_REQUEST,
                          message_type_specific_flags=MsgTypeFlagWithEvent,
                          serial_method=JSON
                          ).as_bytes()
            optional = Optional(event=EVENT_StartSession,
                              sessionId=session_id).as_bytes()
            payload = get_payload_bytes(event=EVENT_StartSession, speaker=speaker)
            await send_event(self.ws, header, optional, payload)

            res = parser_response(await self.ws.recv())
            if res.optional.event != EVENT_SessionStarted:
                raise RuntimeError('开始会话失败!')

            self.session_id = session_id
            logger.info(f"TTS会话已开启: {session_id}")
            return session_id

    async def finish_session(self, session_id: str):
        """
        结束TTS会话

        关闭一个已存在的TTS会话，释放相关资源。

        Args:
            session_id (str): 要结束的会话ID

        Raises:
            RuntimeError: WebSocket未连接时抛出
        """
        if not self.is_connected or not self.ws:
            raise RuntimeError("WebSocket未连接")

        async with self.lock:
            header = Header(message_type=FULL_CLIENT_REQUEST,
                          message_type_specific_flags=MsgTypeFlagWithEvent,
                          serial_method=JSON
                          ).as_bytes()
            optional = Optional(event=EVENT_FinishSession,
                              sessionId=session_id).as_bytes()
            payload = str.encode('{}')
            await send_event(self.ws, header, optional, payload)
            logger.info(f"TTS会话已结束: {session_id}")
            return

    async def send_text(self, text: str, speaker: str, session_id: str):
        """
        发送文本进行TTS转换

        向服务器发送要转换为语音的文本。

        Args:
            text (str): 要转换的文本
            speaker (str): 发音人标识符
            session_id (str): 会话ID

        Raises:
            RuntimeError: WebSocket未连接时抛出
        """
        if not self.is_connected or not self.ws:
            raise RuntimeError("WebSocket未连接")

        async with self.lock:
            header = Header(message_type=FULL_CLIENT_REQUEST,
                          message_type_specific_flags=MsgTypeFlagWithEvent,
                          serial_method=JSON).as_bytes()
            optional = Optional(event=EVENT_TaskRequest,
                              sessionId=session_id).as_bytes()
            payload = get_payload_bytes(
                event=EVENT_TaskRequest, text=text, speaker=speaker)
            logger.debug(f"发送文本进行TTS转换: {text[:20]}...")
            return await send_event(self.ws, header, optional, payload)


# 读取响应内容的辅助函数
def read_res_content(res: bytes, offset: int):
    """
    读取响应字节数组中的字符串内容

    Args:
        res (bytes): 响应字节数组
        offset (int): 起始偏移量

    Returns:
        tuple: (解析出的内容字符串, 新的偏移量)
    """
    content_size = int.from_bytes(res[offset: offset + 4], "big", signed=True)
    offset += 4
    content = str(res[offset: offset + content_size])
    offset += content_size
    return content, offset


def read_res_payload(res: bytes, offset: int):
    """
    读取响应字节数组中的负载内容

    Args:
        res (bytes): 响应字节数组
        offset (int): 起始偏移量

    Returns:
        tuple: (解析出的负载数据, 新的偏移量)
    """
    payload_size = int.from_bytes(res[offset: offset + 4], "big", signed=True)
    offset += 4
    payload = res[offset: offset + payload_size]
    offset += payload_size
    return payload, offset


def parser_response(res) -> Response:
    """
    解析WebSocket响应

    将原始字节序列解析为结构化的Response对象，处理不同类型的响应格式。

    Args:
        res: 响应字节数组

    Returns:
        Response: 解析后的响应对象

    Raises:
        RuntimeError: 当响应为错误字符串时抛出
    """
    if isinstance(res, str):
        raise RuntimeError(res)
    response = Response(Header(), Optional())
    # 解析结果
    # header
    header = response.header
    num = 0b00001111
    header.protocol_version = res[0] >> 4 & num
    header.header_size = res[0] & 0x0f
    header.message_type = (res[1] >> 4) & num
    header.message_type_specific_flags = res[1] & 0x0f
    header.serialization_method = res[2] >> num
    header.message_compression = res[2] & 0x0f
    header.reserved = res[3]
    #
    offset = 4
    optional = response.optional
    if header.message_type == FULL_SERVER_RESPONSE or AUDIO_ONLY_RESPONSE:
        # read event
        if header.message_type_specific_flags == MsgTypeFlagWithEvent:
            optional.event = int.from_bytes(
                res[offset:offset+4], "big", signed=True)
            offset += 4
            if optional.event == EVENT_NONE:
                return response
            # read connectionId
            elif optional.event == EVENT_ConnectionStarted:
                optional.connectionId, offset = read_res_content(res, offset)
            elif optional.event == EVENT_ConnectionFailed:
                optional.response_meta_json, offset = read_res_content(
                    res, offset)
            elif (optional.event == EVENT_SessionStarted
                  or optional.event == EVENT_SessionFailed
                  or optional.event == EVENT_SessionFinished):
                optional.sessionId, offset = read_res_content(res, offset)
                optional.response_meta_json, offset = read_res_content(
                    res, offset)
            else:
                optional.sessionId, offset = read_res_content(res, offset)
                response.payload, offset = read_res_payload(res, offset)

    elif header.message_type == ERROR_INFORMATION:
        optional.errorCode = int.from_bytes(
            res[offset:offset + 4], "big", signed=True)
        offset += 4
        response.payload, offset = read_res_payload(res, offset)
    return response


async def send_event(ws, header: bytes, optional: Union[bytes, None] = None,
                    payload: bytes = None):
    """
    发送WebSocket事件

    构造完整的WebSocket请求并发送给服务器。

    Args:
        ws: WebSocket连接对象
        header (bytes): 头部字节序列
        optional (bytes, optional): 可选信息字节序列
        payload (bytes, optional): 负载字节序列
    """
    full_client_request = bytearray(header)
    if optional is not None:
        full_client_request.extend(optional)
    if payload is not None:
        payload_size = len(payload).to_bytes(4, 'big', signed=True)
        full_client_request.extend(payload_size)
        full_client_request.extend(payload)
    await ws.send(full_client_request)


def get_payload_bytes(uid='1234', event=EVENT_NONE, text='', speaker='', audio_format='mp3',
                    audio_sample_rate=24000):
    """
    生成TTS请求负载

    根据指定参数生成用于TTS请求的JSON负载。

    Args:
        uid (str): 用户ID
        event (int): 事件类型
        text (str): 要转换的文本
        speaker (str): 发音人
        audio_format (str): 音频格式
        audio_sample_rate (int): 音频采样率

    Returns:
        bytes: 负载字节序列
    """
    return str.encode(json.dumps(
        {
            "user": {"uid": uid},
            "event": event,
            "namespace": "BidirectionalTTS",
            "req_params": {
                "text": text,
                "speaker": speaker,
                "audio_params": {
                    "format": audio_format,
                    "sample_rate": audio_sample_rate
                }
            }
        }
    ))


class TTsManager:
    """
    文本转语音管理器 (Text-To-Speech Manager)

    提供全面的文本转语音功能，包括同步和异步语音合成、流式处理、文件保存和播放等。
    本类设计为可以被外部AI Agent调用，支持流式文本输入和流式语音输出。

    主要功能:
    1. 同步和异步文本转语音转换
    2. 流式文本输入和语音输出
    3. 音频文件保存与播放
    4. 健壮的WebSocket连接管理
    5. 支持多线程异步调用

    Attributes:
        app_id (str): TTS服务应用ID
        token (str): TTS服务访问令牌
        speaker (str): 发音人
        ws_header (dict): WebSocket请求头
        url (str): WebSocket服务地址
        debug (bool): 是否开启调试模式
        ws_manager (WebSocketManager): WebSocket连接管理器
        audio_player (AudioPlayer): 音频播放器
        stream_active (bool): 流式合成是否活跃
        stream_lock (asyncio.Lock): 流式处理锁
        stream_processing_event (asyncio.Event): 流式处理事件
        stream_session_id (str): 流式会话ID
        stream_text_buffer (list): 流式文本缓冲区
        stream_worker_task (asyncio.Task): 流式处理任务
    """

    def __init__(self, app_id: str = "5311525929", token: str = "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23", speaker: str = 'zh_female_shuangkuaisisi_moon_bigtts'):
        """
        初始化TTS管理器

        创建并配置TTS管理器实例，准备WebSocket连接和音频播放器。

        Args:
            app_id (str): 语音服务应用ID，默认为"5311525929"
            token (str): 语音服务访问令牌，默认为"DRNTjbbfC1QcfDrTndiSSBdTr23F0-23"
            speaker (str, optional): 发音人，默认为'zh_female_shuangkuaisisi_moon_bigtts'

        Raises:
            ValueError: 当必要参数缺失时抛出
        """
        # 检查必要参数
        if not app_id:
            raise ValueError("必须提供TTS服务的APP ID")
        if not token:
            raise ValueError("必须提供TTS服务的访问令牌")

        self.app_id = app_id
        self.token = token
        self.speaker = speaker
        self.ws_header = {
            "X-Api-App-Key": app_id,
            "X-Api-Access-Key": token,
            "X-Api-Resource-Id": 'volc.service_type.10029',
            "X-Api-Connect-Id": str(uuid.uuid4()),
        }
        self.url = 'wss://openspeech.bytedance.com/api/v3/tts/bidirection'
        self.debug = False

        # WebSocket连接管理器
        self.ws_manager = None

        # 音频播放器
        self.audio_player = AudioPlayer()

        # 流式合成相关
        self.stream_active = False
        try:
            self.stream_lock = asyncio.Lock()
            self.stream_processing_event = asyncio.Event()
        except RuntimeError:
            # 为测试环境创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            self.stream_lock = asyncio.Lock()
            self.stream_processing_event = asyncio.Event()
        self.stream_session_id = None
        self.stream_text_buffer = []
        self.stream_worker_task = None

        logger.info("TTS管理器已初始化")

    def set_debug(self, debug: bool):
        """
        设置调试模式

        启用或禁用详细日志输出。

        Args:
            debug (bool): 是否启用调试模式
        """
        self.debug = debug
        logger.info(f"调试模式已{'启用' if debug else '禁用'}")

    def set_speaker(self, speaker: str):
        """
        设置发音人

        更改TTS使用的发音人。

        Args:
            speaker (str): 发音人ID
        """
        self.speaker = speaker
        logger.info(f"发音人已设置为: {speaker}")

    async def _ensure_ws_connected(self) -> bool:
        """
        确保WebSocket已连接

        检查WebSocket连接状态，如果未连接则尝试建立连接。

        Returns:
            bool: 是否成功连接

        Note:
            此方法用于内部确保连接可用，外部方法不需要显式调用。
        """
        if self.ws_manager and self.ws_manager.is_connected:
            return True

        if not self.ws_manager:
            self.ws_manager = WebSocketManager(self.url, self.ws_header)

        return await self.ws_manager.connect()

    async def _text_to_speech_internal(self, text: str) -> bytes:
        """
        内部方法：执行文本到语音的转换

        异步执行文本到语音的核心转换过程，包括建立连接、发送请求、接收响应。

        Args:
            text (str): 要转换的文本

        Returns:
            bytes: 合成的音频数据

        Raises:
            RuntimeError: 连接失败或处理异常时抛出
        """
        audio_data = bytearray()

        # 确保WebSocket已连接
        if not await self._ensure_ws_connected():
            raise RuntimeError("无法连接到TTS服务")

        try:
            # 开始会话
            session_id = await self.ws_manager.start_session(self.speaker)

            # 发送文本并结束会话
            await self.ws_manager.send_text(text, self.speaker, session_id)
            await self.ws_manager.finish_session(session_id)

            # 接收所有音频数据
            while True:
                if not self.ws_manager.ws:
                    break

                res = parser_response(await self.ws_manager.ws.recv())
                if self.debug:
                    logger.debug(f'响应事件: {res.optional.event}')

                if res.optional.event == EVENT_TTSResponse and res.header.message_type == AUDIO_ONLY_RESPONSE:
                    audio_data.extend(res.payload)
                elif res.optional.event in [EVENT_TTSSentenceStart, EVENT_TTSSentenceEnd]:
                    continue
                else:
                    break
        except Exception as e:
            logger.error(f"语音合成异常: {e}")
            raise

        return bytes(audio_data)

    def text_to_speech_file(self, text: str, output_path: str) -> bool:
        """
        将文本转换为语音并保存为文件

        将输入文本转换为语音，并保存到指定路径的音频文件中。

        Args:
            text (str): 要转换的文本
            output_path (str): 输出文件路径

        Returns:
            bool: 是否成功保存
        """
        try:
            # 创建目录（如果不存在）
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

            # 执行语音合成
            audio_data = asyncio.run(self._text_to_speech_internal(text))

            # 写入文件
            with open(output_path, 'wb') as f:
                f.write(audio_data)

            logger.info(f"语音已保存到: {output_path}")
            return True
        except Exception as e:
            logger.error(f"保存语音文件失败: {e}")
            return False

    def text_to_speech_play(self, text: str) -> bool:
        """
        将文本转换为语音并播放

        将输入文本转换为语音，并通过系统扬声器直接播放。

        Args:
            text (str): 要转换的文本

        Returns:
            bool: 是否成功播放
        """
        try:
            # 预处理文本，避免读出标点符号
            processed_text = preprocess_text_for_tts(text)
            logger.debug(f"原始文本: {text[:50]}...")
            logger.debug(f"处理后文本: {processed_text[:50]}...")

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # 合成语音
                audio_data = asyncio.run(self._text_to_speech_internal(processed_text))

                # 保存到临时文件
                with open(temp_path, 'wb') as f:
                    f.write(audio_data)

                # 播放音频
                logger.info(f"开始播放音频: {text[:30]}...")
                
                # 初始化pygame音频系统(如果尚未初始化)
                if not pygame.mixer.get_init():
                    pygame.mixer.init()
                
                # 直接使用pygame播放
                sound = pygame.mixer.Sound(temp_path)
                sound.play()
                
                # 等待播放完成
                pygame.time.wait(int(sound.get_length() * 1000))
                
                return True
            finally:
                # 删除临时文件
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass
        except Exception as e:
            logger.error(f"语音播放失败: {e}")
            return False

    def transcribe_audio(self, audio_path: str) -> str:
        """
        语音转写功能

        将音频文件中的语音转写为文本。注意：此为占位方法，需要集成实际的ASR服务。

        Args:
            audio_path (str): 音频文件路径

        Returns:
            str: 转写的文本
        """
        logger.warning("语音转写功能未实现，需要集成ASR服务")
        return "语音转写功能未实现，需要集成ASR服务"

    async def start_streaming_synthesis(self) -> bool:
        """
        开始流式语音合成

        启动流式语音合成会话，准备接收文本并实时合成语音。
        此方法开启了一个持续的会话，可以通过add_streaming_text添加文本。

        Returns:
            bool: 是否成功启动
        """
        if self.stream_active:
            logger.warning("流式语音合成已经在运行中")
            return False

        async with self.stream_lock:
            # 确保WebSocket已连接
            if not await self._ensure_ws_connected():
                logger.error("无法连接到TTS服务")
                return False

            try:
                # 开始会话
                self.stream_session_id = await self.ws_manager.start_session(self.speaker)
                self.stream_active = True
                self.stream_text_buffer = []

                # 启动音频播放器的流式播放
                self.audio_player.start_stream_playback()

                # 启动处理线程
                self.stream_processing_event = asyncio.Event()
                self.stream_worker_task = asyncio.create_task(self._stream_worker())

                logger.info("流式语音合成已启动")
                return True
            except Exception as e:
                logger.error(f"启动流式语音合成失败: {e}")
                await self._cleanup_stream_resources()
                return False

    async def stop_streaming_synthesis(self) -> bool:
        """
        停止流式语音合成

        结束流式语音合成会话，停止处理和播放。

        Returns:
            bool: 是否成功停止
        """
        if not self.stream_active:
            return True

        async with self.stream_lock:
            try:
                # 标记为非活动状态
                self.stream_active = False

                # 触发处理事件以便正在等待的工作线程可以退出
                try:
                    # 如果是AsyncMock，需要await
                    await self.stream_processing_event.set()
                except (RuntimeError, TypeError):
                    # 如果是真实Event对象，直接调用
                    self.stream_processing_event.set()

                # 等待工作线程完成
                if self.stream_worker_task:
                    try:
                        await asyncio.wait_for(self.stream_worker_task, timeout=5.0)
                    except asyncio.TimeoutError:
                        logger.warning("等待流处理线程超时")

                # 清理资源
                await self._cleanup_stream_resources()

                logger.info("流式语音合成已停止")
                return True
            except Exception as e:
                logger.error(f"停止流式语音合成失败: {e}")
                return False

    async def _cleanup_stream_resources(self):
        """
        清理流式合成相关资源

        在流式合成结束后清理相关资源，包括结束会话、停止播放等。
        """
        # 结束会话
        if self.stream_session_id and self.ws_manager and self.ws_manager.is_connected:
            try:
                await self.ws_manager.finish_session(self.stream_session_id)
            except Exception as e:
                logger.error(f"结束流会话失败: {e}")

        # 停止音频播放
        self.audio_player.stop_stream_playback()

        # 重置状态
        self.stream_session_id = None
        self.stream_text_buffer = []
        self.stream_worker_task = None

    async def add_streaming_text(self, text: str) -> bool:
        """
        添加文本到流式合成队列

        向正在进行的流式合成会话添加文本片段。
        支持逐字或逐句添加文本，适合实时生成的场景。

        Args:
            text (str): 要合成的文本片段

        Returns:
            bool: 是否成功添加
        """
        if not self.stream_active:
            logger.error("流式语音合成未启动")
            return False

        async with self.stream_lock:
            self.stream_text_buffer.append(text)
            try:
                # 如果是AsyncMock，需要await
                await self.stream_processing_event.set()
            except (RuntimeError, TypeError):
                # 如果是真实Event对象，直接调用
                self.stream_processing_event.set()
            return True

    async def _stream_worker(self):
        """
        流式合成工作线程

        在后台持续运行，将文本缓冲区中的内容转换为语音。
        负责将短文本片段组合成更有意义的单位进行处理，提高效率和自然度。
        实现了自适应等待时间，优化处理效率。

        Note:
            此为内部方法，由start_streaming_synthesis启动并管理。
        """
        try:
            accumulated_text = ""
            last_process_time = time.time()
            wait_time = 0.1  # 初始等待时间

            while self.stream_active:
                # 检查是否有新文本需要处理
                if self.stream_text_buffer:
                    # 取出并清空缓冲区
                    async with self.stream_lock:
                        text_to_process = "".join(self.stream_text_buffer)
                        self.stream_text_buffer = []

                    # 累积文本
                    accumulated_text += text_to_process
                    last_process_time = time.time()

                    # 如果累积了足够的文本或者包含标点，就处理一次
                    if len(accumulated_text) >= 10 or any(p in accumulated_text for p in "，。！？,.!?"):
                        if self.stream_active and accumulated_text.strip():
                            # 发送文本进行合成
                            await self._process_stream_text(accumulated_text)
                            accumulated_text = ""

                # 如果累积文本停留时间超过0.5秒，也进行处理
                elif accumulated_text and time.time() - last_process_time > 0.5:
                    if self.stream_active and accumulated_text.strip():
                        await self._process_stream_text(accumulated_text)
                        accumulated_text = ""

                # 等待新文本或者结束信号
                try:
                    # 使用超时等待，这样即使没有新的事件，也能定期检查累积文本
                    await asyncio.wait_for(self.stream_processing_event.wait(), timeout=wait_time)
                    self.stream_processing_event.clear()
                except asyncio.TimeoutError:
                    # 超时只是为了定期检查，不是错误
                    pass

                # 动态调整等待时间
                if self.stream_text_buffer:
                    # 有新文本时，降低等待时间以更快处理
                    wait_time = 0.05
                else:
                    # 没有新文本时，增加等待时间以减少CPU使用
                    wait_time = min(0.5, wait_time * 1.5)

        except Exception as e:
            logger.error(f"流式合成工作线程异常: {e}")
        finally:
            logger.info("流式合成工作线程结束")

    async def _process_stream_text(self, text: str):
        """
        处理流式文本进行合成

        将文本发送到TTS服务并处理返回的音频数据。

        Args:
            text (str): 要处理的文本

        Note:
            此方法由_stream_worker调用，不应直接使用。
        """
        if not text.strip() or not self.stream_active:
            return

        try:
            if not self.ws_manager or not self.ws_manager.is_connected:
                logger.error("WebSocket连接已断开")
                return

            # 预处理文本，避免读出标点符号
            processed_text = preprocess_text_for_tts(text)
            logger.debug(f"原始流式文本: {text[:30]}...")
            logger.debug(f"处理后流式文本: {processed_text[:30]}...")

            # 发送文本进行合成
            await self.ws_manager.send_text(processed_text, self.speaker, self.stream_session_id)

            # 接收音频数据
            received_audio = False
            while self.stream_active:
                if not self.ws_manager.ws:
                    break

                res = parser_response(await self.ws_manager.ws.recv())

                if res.optional.event == EVENT_TTSResponse and res.header.message_type == AUDIO_ONLY_RESPONSE:
                    # 将音频数据添加到播放队列
                    self.audio_player.add_audio_chunk(res.payload)
                    received_audio = True
                elif res.optional.event in [EVENT_TTSSentenceStart, EVENT_TTSSentenceEnd]:
                    continue
                else:
                    # 非音频响应，结束接收
                    break

            if not received_audio:
                logger.warning(f"未收到音频数据，文本：{processed_text}")

        except Exception as e:
            logger.error(f"处理流式文本异常: {e}")

    def shutdown(self):
        """
        关闭TTS管理器，释放资源

        完全关闭TTS管理器，包括停止流式合成、关闭连接等。
        在应用程序结束前调用此方法以确保资源被正确释放。
        """
        try:
            # 停止任何正在进行的流式合成
            if self.stream_active:
                try:
                    # 尝试使用现有事件循环
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 如果循环正在运行，创建任务
                        future = asyncio.run_coroutine_threadsafe(self.stop_streaming_synthesis(), loop)
                        future.result(timeout=2.0)  # 等待最多2秒
                    else:
                        # 如果循环未运行，直接运行
                        loop.run_until_complete(self.stop_streaming_synthesis())
                except (RuntimeError, asyncio.InvalidStateError):
                    # 如果获取事件循环失败或事件循环已关闭，创建新的事件循环
                    try:
                        asyncio.run(self.stop_streaming_synthesis())
                    except RuntimeError:
                        logger.warning("无法停止流式合成：事件循环已关闭")
                        self.stream_active = False

            # 关闭WebSocket连接
            if self.ws_manager and self.ws_manager.is_connected:
                try:
                    # 尝试使用现有事件循环
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 如果循环正在运行，创建任务
                        future = asyncio.run_coroutine_threadsafe(self.ws_manager.disconnect(), loop)
                        future.result(timeout=2.0)  # 等待最多2秒
                    else:
                        # 如果循环未运行，直接运行
                        loop.run_until_complete(self.ws_manager.disconnect())
                except (RuntimeError, asyncio.InvalidStateError):
                    # 如果获取事件循环失败或事件循环已关闭，创建新的事件循环
                    try:
                        asyncio.run(self.ws_manager.disconnect())
                    except RuntimeError:
                        logger.warning("无法断开WebSocket连接：事件循环已关闭")
                        # 直接标记为断开连接
                        if self.ws_manager:
                            self.ws_manager.is_connected = False
                            self.ws_manager.ws = None
        except Exception as e:
            logger.error(f"关闭TTS管理器时出错: {e}")
        finally:
            # 确保标记为关闭状态
            self.stream_active = False
            if self.ws_manager:
                self.ws_manager.is_connected = False

            logger.info("TTS管理器已关闭")


# 示例用法
if __name__ == "__main__":
    import os
    from pathlib import Path
    import sys

    # 添加项目根目录到Python路径
    sys.path.insert(0, str(Path(__file__).parent.parent))

    # 导入配置管理器
    from utils.config_manager import get_config

    # 获取配置
    config = get_config()
    appId = config.get('API', 'tts_app_id', fallback="5311525929")
    token = config.get('API', 'tts_token', fallback="DRNTjbbfC1QcfDrTndiSSBdTr23F0-23")

    # 如果配置为空，则使用环境变量或默认值
    if not appId:
        appId = os.environ.get('LIFEBUDDY_API_TTS_APP_ID') or '5311525929'
    if not token:
        token = os.environ.get('LIFEBUDDY_API_TTS_TOKEN') or 'DRNTjbbfC1QcfDrTndiSSBdTr23F0-23'

    # 创建TTS管理器
    tts_manager = TTsManager(appId, token)

    # 示例1：将文本转换为语音并保存为文件
    tts_manager.text_to_speech_file("你好，这是一个测试。", "output.mp3")

    # 示例2：将文本转换为语音并直接播放
    tts_manager.text_to_speech_play("这是直接播放的语音测试。")

    # 示例3：流式语音合成（异步使用方式）
    async def stream_example():
        await tts_manager.start_streaming_synthesis()

        # 模拟逐字发送文本
        text = "这是一个流式语音合成的测试，可以逐字逐句地处理文本并进行播放。"
        for i in range(0, len(text), 2):
            chunk = text[i:i+2]
            await tts_manager.add_streaming_text(chunk)
            await asyncio.sleep(0.2)  # 模拟文本生成的间隔

        # 等待所有文本处理完成
        await asyncio.sleep(2)

        # 停止流式合成
        await tts_manager.stop_streaming_synthesis()

    # 运行异步示例（取消注释以测试）
    # asyncio.run(stream_example())
