#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter

router = APIRouter()

@router.get("/status")
async def status():
    return {"status": "active", "module": "backend/utils/camera_api.py"}

def create_socketio_handlers(sio):
    pass

async def connect_asr_service():
    return {"success": True, "message": "ASR service connected"}

def init_socketio_service():
    class MockSocketIOService:
        def __init__(self):
            self.sio = None
        def get_asgi_app(self, app):
            return app
    return MockSocketIOService()
