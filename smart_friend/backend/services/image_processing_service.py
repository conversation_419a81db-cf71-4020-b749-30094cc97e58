"""
图像预处理核心服务
负责具体的图像处理算法实现
"""
import cv2
import numpy as np
import base64
from io import BytesIO
from PIL import Image, ImageEnhance
import logging
from typing import Optional, Dict, Any

from backend.models.image_processing_models import GrayscaleMethod, BlurMethod, EnhancementType

logger = logging.getLogger(__name__)


class ImageProcessingService:
    """图像处理服务类"""
    
    @staticmethod
    def decode_base64_image(image_data: str) -> np.ndarray:
        """解码Base64图像数据为OpenCV格式"""
        try:
            # 移除可能的数据URL前缀
            if ',' in image_data:
                image_data = image_data.split(',')[1]
            
            # 解码Base64
            image_bytes = base64.b64decode(image_data)
            
            # 转换为PIL图像
            pil_image = Image.open(BytesIO(image_bytes))
            
            # 转换为OpenCV格式 (BGR)
            opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            return opencv_image
        except Exception as e:
            logger.error(f"解码图像失败: {e}")
            raise ValueError(f"无效的图像数据: {str(e)}")

    @staticmethod
    def encode_image_to_base64(image: np.ndarray, format: str = "jpeg", quality: int = 85) -> str:
        """将OpenCV图像编码为Base64字符串"""
        try:
            # 转换为PIL图像
            if len(image.shape) == 3:
                # 彩色图像：BGR -> RGB
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                # 灰度图像
                pil_image = Image.fromarray(image)
            
            # 编码为指定格式
            buffer = BytesIO()
            if format.lower() == "jpeg":
                pil_image.save(buffer, format="JPEG", quality=quality)
            else:
                pil_image.save(buffer, format="PNG")
            
            # 转换为Base64
            encoded_string = base64.b64encode(buffer.getvalue()).decode('utf-8')
            return encoded_string
        except Exception as e:
            logger.error(f"编码图像失败: {e}")
            raise ValueError(f"图像编码失败: {str(e)}")

    @staticmethod
    def apply_grayscale(image: np.ndarray, method: GrayscaleMethod) -> np.ndarray:
        """应用灰度化处理"""
        try:
            if method == GrayscaleMethod.OPENCV:
                # OpenCV默认方法
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            elif method == GrayscaleMethod.AVERAGE:
                # 平均值法
                gray = np.mean(image, axis=2).astype(np.uint8)
            elif method == GrayscaleMethod.WEIGHTED:
                # 加权平均法 (0.299*R + 0.587*G + 0.114*B)
                # OpenCV使用BGR格式，所以是 0.114*B + 0.587*G + 0.299*R
                gray = (0.114 * image[:, :, 0] + 0.587 * image[:, :, 1] + 0.299 * image[:, :, 2]).astype(np.uint8)
            elif method == GrayscaleMethod.LUMINOSITY:
                # 亮度法（与加权平均相同）
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                raise ValueError(f"不支持的灰度化方法: {method}")
            
            return gray
        except Exception as e:
            logger.error(f"灰度化处理失败: {e}")
            raise ValueError(f"灰度化处理失败: {str(e)}")

    @staticmethod
    def apply_blur(image: np.ndarray, method: BlurMethod, kernel_size: int, sigma_x: float, sigma_y: Optional[float] = None) -> np.ndarray:
        """应用模糊处理"""
        try:
            # 确保核大小为奇数
            if kernel_size % 2 == 0:
                kernel_size += 1
            
            if sigma_y is None:
                sigma_y = sigma_x
            
            if method == BlurMethod.GAUSSIAN:
                # 高斯模糊
                blurred = cv2.GaussianBlur(image, (kernel_size, kernel_size), sigma_x, sigmaY=sigma_y)
            elif method == BlurMethod.BOX:
                # 盒式滤波
                blurred = cv2.boxFilter(image, -1, (kernel_size, kernel_size))
            elif method == BlurMethod.MEDIAN:
                # 中值模糊
                blurred = cv2.medianBlur(image, kernel_size)
            else:
                raise ValueError(f"不支持的模糊方法: {method}")
            
            return blurred
        except Exception as e:
            logger.error(f"模糊处理失败: {e}")
            raise ValueError(f"模糊处理失败: {str(e)}")

    @staticmethod
    def apply_enhancement(image: np.ndarray, enhancement_type: EnhancementType, **kwargs) -> np.ndarray:
        """应用图像增强处理"""
        try:
            # 转换为PIL图像进行增强处理
            if len(image.shape) == 3:
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                pil_image = Image.fromarray(image)
            
            if enhancement_type == EnhancementType.BRIGHTNESS:
                # 亮度调整
                brightness = kwargs.get('brightness', 0.0)
                # 将-100到100的范围转换为PIL的0.0到2.0范围
                factor = 1.0 + (brightness / 100.0)
                enhancer = ImageEnhance.Brightness(pil_image)
                enhanced = enhancer.enhance(factor)
                
            elif enhancement_type == EnhancementType.CONTRAST:
                # 对比度调整
                contrast = kwargs.get('contrast', 1.0)
                enhancer = ImageEnhance.Contrast(pil_image)
                enhanced = enhancer.enhance(contrast)
                
            elif enhancement_type == EnhancementType.SATURATION:
                # 饱和度调整
                saturation = kwargs.get('saturation', 1.0)
                enhancer = ImageEnhance.Color(pil_image)
                enhanced = enhancer.enhance(saturation)
                
            elif enhancement_type == EnhancementType.GAMMA:
                # 伽马校正
                gamma = kwargs.get('gamma', 1.0)
                # 转换回numpy数组进行伽马校正
                img_array = np.array(pil_image, dtype=np.float32) / 255.0
                img_array = np.power(img_array, 1.0 / gamma)
                img_array = (img_array * 255).astype(np.uint8)
                enhanced = Image.fromarray(img_array)
                
            elif enhancement_type == EnhancementType.AUTO_ENHANCE:
                # 自动增强（组合多种增强）
                strength = kwargs.get('auto_enhance_strength', 1.0)
                
                # 轻微增强对比度
                contrast_enhancer = ImageEnhance.Contrast(pil_image)
                enhanced = contrast_enhancer.enhance(1.0 + 0.2 * strength)
                
                # 轻微增强饱和度
                color_enhancer = ImageEnhance.Color(enhanced)
                enhanced = color_enhancer.enhance(1.0 + 0.1 * strength)
                
                # 轻微增强锐度
                sharpness_enhancer = ImageEnhance.Sharpness(enhanced)
                enhanced = sharpness_enhancer.enhance(1.0 + 0.1 * strength)
            else:
                raise ValueError(f"不支持的增强类型: {enhancement_type}")
            
            # 转换回OpenCV格式
            if len(image.shape) == 3:
                result = cv2.cvtColor(np.array(enhanced), cv2.COLOR_RGB2BGR)
            else:
                result = np.array(enhanced)
            
            return result
        except Exception as e:
            logger.error(f"图像增强失败: {e}")
            raise ValueError(f"图像增强失败: {str(e)}")

    @classmethod
    def get_available_methods(cls) -> Dict[str, list]:
        """获取可用的处理方法"""
        return {
            "grayscale": [method.value for method in GrayscaleMethod],
            "blur": [method.value for method in BlurMethod],
            "enhancement": [method.value for method in EnhancementType]
        }

    @classmethod
    def get_opencv_version(cls) -> str:
        """获取OpenCV版本"""
        return cv2.__version__
