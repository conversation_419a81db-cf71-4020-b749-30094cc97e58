ai_child/
├── api/                           # API层 - 处理HTTP请求和响应
│   └── test_planning/             # 测试规划模块
│       ├── endpoints/             # API端点定义
│       ├── schemas.py             # 数据验证和序列化模型
│       └── services/              # API服务逻辑
├── database/                      # 数据访问层 - 处理数据存储和检索
│   ├── sqlite_connection.py       # SQLite关系型数据库连接管理
│   ├── influxdb_connection.py     # InfluxDB时序数据库连接管理
│   └── __init__.py                # 数据库模块导出
├── services/                      # 业务逻辑层 - 实现核心业务功能
│   └── daily_learning_service.py  # 每日学习数据服务
├── config.py                      # 应用配置
└── main.py                        # 应用入口点