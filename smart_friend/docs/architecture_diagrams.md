# Smart Friend FastAPI 架构流程图

## 📋 架构概览图

### Mermaid 源码

```mermaid
graph TB
    %% 客户端层
    Client[🌐 客户端<br/>Web/Mobile/API调用]
    
    %% FastAPI应用层
    subgraph FastAPI["🚀 FastAPI 应用层"]
        MainApp[📱 main.py<br/>FastAPI(title='AI Child Learning Assistant')]
        CORS[🔒 CORS中间件<br/>跨域处理]
        Routes[🛣️ 路由管理器]
    end
    
    %% API端点层
    subgraph Endpoints["📡 API 端点层 (/api/v1)"]
        DailyLearningAPI[📚 每日学习 API<br/>/daily-learning/*<br/>• POST /learning-records<br/>• GET /learning-records/{id}<br/>• DELETE /learning-records<br/>• GET /learning-statistics/{id}<br/>• GET /health]
        
        PlanningAPI[📋 学习计划 API<br/>/planning/*<br/>• POST /plans<br/>• GET /plans/{child_id}<br/>• GET /plans/detail/{plan_id}<br/>• PUT /plans/{plan_id}<br/>• DELETE /plans<br/>• GET /plans/statistics/{id}<br/>• GET /health]
        
        HealthAPI[❤️ 系统健康检查<br/>• GET /<br/>• GET /health]
    end
    
    %% 服务层
    subgraph Services["⚙️ 服务层 (Business Logic)"]
        DailyLearningService[📊 DailyLearningService<br/>service/daily_learning_service.py<br/>• add_learning_record()<br/>• get_learning_records()<br/>• delete_learning_records()<br/>• get_learning_statistics()]
        
        PlanningService[📝 PlanningService<br/>service/planning_service.py<br/>• create_plan()<br/>• get_plans()<br/>• update_plan()<br/>• delete_plans()<br/>• get_plan_statistics()]
    end
    
    %% 数据模型层
    subgraph Schemas["📋 数据模型层 (Pydantic)"]
        LearningSchemas[📚 学习记录模型<br/>core/planning/schemas.py<br/>• DailyLearningCreate<br/>• DailyLearningResponse<br/>• LearningStatistics<br/>• DeleteLearningRequest]
        
        PlanningSchemas[📋 计划管理模型<br/>core/planning/schemas.py<br/>• StudyPlanCreate<br/>• StudyPlanUpdate<br/>• StudyPlanResponse<br/>• PlanStatistics<br/>• SubTask]
    end
    
    %% 数据库连接层
    subgraph Database["🗄️ 数据库层"]
        InfluxDBManager[📈 InfluxDB 管理器<br/>core/planning/database/<br/>influxdb_connection.py<br/>• write_point()<br/>• query_data()<br/>• delete_data()<br/>• check_connection()]
        
        InfluxDB[(🏪 InfluxDB 数据库<br/>Bucket: daily_learning<br/>Measurements:<br/>• daily_learning<br/>• study_plans)]
    end
    
    %% 配置层
    subgraph Config["⚙️ 配置层"]
        Settings[🔧 应用配置<br/>config.py<br/>• PROJECT_NAME<br/>• API_V1_STR<br/>• INFLUXDB_*<br/>• DATABASE_URL]
    end
    
    %% 数据流向
    Client --> MainApp
    MainApp --> CORS
    CORS --> Routes
    Routes --> DailyLearningAPI
    Routes --> PlanningAPI
    Routes --> HealthAPI
    
    DailyLearningAPI --> DailyLearningService
    PlanningAPI --> PlanningService
    
    DailyLearningService --> LearningSchemas
    PlanningService --> PlanningSchemas
    
    DailyLearningService --> InfluxDBManager
    PlanningService --> InfluxDBManager
    
    InfluxDBManager --> InfluxDB
    
    %% 配置依赖
    MainApp -.-> Settings
    InfluxDBManager -.-> Settings
    
    %% 样式
    classDef clientStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef appStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef apiStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef serviceStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef schemaStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dbStyle fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef configStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class Client clientStyle
    class MainApp,CORS,Routes appStyle
    class DailyLearningAPI,PlanningAPI,HealthAPI apiStyle
    class DailyLearningService,PlanningService serviceStyle
    class LearningSchemas,PlanningSchemas schemaStyle
    class InfluxDBManager,InfluxDB dbStyle
    class Settings configStyle
```

## 🔄 API 请求处理流程图

### Mermaid 源码

```mermaid
sequenceDiagram
    participant Client as 🌐 客户端
    participant FastAPI as 🚀 FastAPI App
    participant Router as 🛣️ 路由器
    participant Endpoint as 📡 API端点
    participant Service as ⚙️ 服务层
    participant Schema as 📋 数据模型
    participant DB as 🗄️ InfluxDB
    
    Note over Client,DB: 示例：创建学习计划请求流程
    
    Client->>FastAPI: POST /api/v1/planning/plans
    Note right of Client: Content-Type: application/json<br/>{"child_id": 123, "task_name": "语文作业", ...}
    
    FastAPI->>FastAPI: CORS 中间件处理
    FastAPI->>Router: 路由匹配
    Router->>Endpoint: planning.create_plan()
    
    Endpoint->>Schema: StudyPlanCreate 数据验证
    Schema-->>Endpoint: 验证通过/失败
    
    alt 数据验证成功
        Endpoint->>Service: PlanningService.create_plan()
        Service->>Service: 生成 plan_id (UUID)
        Service->>Service: 准备 tags 和 fields
        Service->>DB: InfluxDB.write_point()
        DB-->>Service: 写入结果
        Service-->>Endpoint: plan_id / None
        
        alt 写入成功
            Endpoint-->>Router: {"success": true, "plan_id": "uuid", ...}
        else 写入失败
            Endpoint-->>Router: HTTPException(500, "创建失败")
        end
    else 数据验证失败
        Endpoint-->>Router: HTTPException(422, "数据格式错误")
    end
    
    Router-->>FastAPI: 响应数据
    FastAPI-->>Client: JSON 响应
    
    Note over Client,DB: 其他请求类型遵循类似流程
    
    rect rgb(240, 248, 255)
        Note over Client,DB: 查询流程 (GET)
        Client->>FastAPI: GET /api/v1/planning/plans/123
        FastAPI->>Endpoint: get_plans()
        Endpoint->>Service: PlanningService.get_plans()
        Service->>DB: InfluxDB.query_data()
        DB-->>Service: 原始数据
        Service->>Service: _process_query_results()
        Service-->>Endpoint: 处理后的计划列表
        Endpoint->>Schema: StudyPlanResponse 序列化
        Schema-->>Endpoint: 验证后的响应数据
        Endpoint-->>Client: JSON 响应
    end
```

## 📝 使用说明

### 在线渲染工具

1. **Mermaid Live Editor**: https://mermaid.live/
   - 复制上面的 Mermaid 代码
   - 粘贴到编辑器中
   - 可以导出为 PNG、SVG、PDF 等格式

2. **GitHub/GitLab**: 
   - 在 Markdown 文件中直接使用 ```mermaid 代码块
   - 平台会自动渲染流程图

3. **VS Code 插件**:
   - 安装 "Mermaid Markdown Syntax Highlighting" 插件
   - 在 Markdown 文件中预览流程图

### 本地工具

1. **Mermaid CLI**:
   ```bash
   npm install -g @mermaid-js/mermaid-cli
   mmdc -i architecture.mmd -o architecture.png
   ```

2. **Draw.io**: 可以导入 Mermaid 代码并编辑

## 📊 文件说明

- `architecture_diagrams.md`: 本文件，包含所有流程图源码
- 建议保存位置: `docs/` 目录下
- 可以随时修改 Mermaid 代码来更新流程图
