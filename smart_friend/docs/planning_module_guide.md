# 学习计划模块使用指南

## 📋 模块概述

学习计划模块是一个基于 InfluxDB 的完整 CRUD 操作系统，用于管理学生的学习计划。该模块遵循现有的 FastAPI 项目架构，提供了完整的 API 接口。

## 🏗️ 架构设计

### 文件结构
```
smart_friend/
├── service/
│   └── planning_service.py          # 计划服务层
├── core/planning/
│   ├── endpoints/
│   │   └── planning.py              # API 端点
│   └── schemas.py                   # 数据模型（已更新）
├── main.py                          # 主应用（已更新）
└── docs/
    └── planning_module_guide.md     # 本文档
```

### 数据库配置
- **数据库**: InfluxDB
- **Bucket**: `daily_learning` (与现有配置相同)
- **Measurement**: `study_plans`
- **连接配置**: 使用现有的 InfluxDB 连接配置

## 📊 数据模型

### 计划数据格式
基于您提供的计划格式，支持以下字段：

```json
{
    "plan_id": "uuid-string",
    "child_id": 123,
    "task_name": "语文作业",
    "time_slot": "18:00 - 19:30",
    "subject": "语文",
    "sub_tasks": [
        {
            "task": "作业单",
            "source": "prompt3语文第1项"
        },
        {
            "task": "大集结：默写古诗36、37",
            "source": "prompt3语文第2项(1)"
        }
    ],
    "customization": "针对读字任务完成差的情况，在完成易读错字任务时，每读完一行，大声说出每个字的读音",
    "difficulty": "易读错字的准确认读",
    "solution": "采用朗读竞赛的方式，家长和孩子一起读，看谁读得又快又准",
    "confidence_index": 3,
    "plan_date": "2024-01-15T10:00:00Z",
    "status": "pending",
    "notes": "备注信息",
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T10:00:00Z"
}
```

### 状态值
- `pending`: 待执行
- `in_progress`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消

## 🚀 API 接口

### 基础路径
所有计划相关的 API 都在 `/api/v1/planning` 路径下。

### 1. 创建计划
```http
POST /api/v1/planning/plans
Content-Type: application/json

{
    "child_id": 123,
    "task_name": "语文作业",
    "time_slot": "18:00 - 19:30",
    "subject": "语文",
    "sub_tasks": [
        {
            "task": "作业单",
            "source": "prompt3语文第1项"
        }
    ],
    "customization": "定制说明",
    "difficulty": "难点描述",
    "solution": "解决方案",
    "confidence_index": 3,
    "plan_date": "2024-01-15T10:00:00Z",
    "status": "pending",
    "notes": "备注"
}
```

### 2. 获取计划列表
```http
GET /api/v1/planning/plans/{child_id}?subject=语文&status=pending&limit=50
```

### 3. 获取单个计划详情
```http
GET /api/v1/planning/plans/detail/{plan_id}
```

### 4. 更新计划
```http
PUT /api/v1/planning/plans/{plan_id}
Content-Type: application/json

{
    "status": "completed",
    "notes": "已完成"
}
```

### 5. 删除计划
```http
DELETE /api/v1/planning/plans
Content-Type: application/json

{
    "child_id": 123,
    "plan_id": "uuid-string"  // 可选，不提供则删除该学生的所有计划
}
```

### 6. 获取统计信息
```http
GET /api/v1/planning/plans/statistics/{child_id}?days=7&subject=语文
```

### 7. 健康检查
```http
GET /api/v1/planning/health
```

## 💡 使用示例

### Python 客户端示例
```python
import requests
import json

base_url = "http://localhost:8000/api/v1/planning"

# 创建计划
plan_data = {
    "child_id": 123,
    "task_name": "数学作业",
    "time_slot": "19:00 - 20:00",
    "subject": "数学",
    "sub_tasks": [
        {
            "task": "练习册第10页",
            "source": "数学练习册"
        }
    ],
    "confidence_index": 4
}

response = requests.post(f"{base_url}/plans", json=plan_data)
print(response.json())

# 获取计划列表
response = requests.get(f"{base_url}/plans/123")
plans = response.json()
print(f"找到 {len(plans)} 个计划")

# 获取统计信息
response = requests.get(f"{base_url}/plans/statistics/123?days=7")
stats = response.json()
print(f"完成率: {stats['completion_rate']:.1f}%")
```

### cURL 示例
```bash
# 创建计划
curl -X POST "http://localhost:8000/api/v1/planning/plans" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 123,
    "task_name": "英语作业",
    "time_slot": "20:00 - 21:00",
    "subject": "英语",
    "sub_tasks": [
      {
        "task": "背诵单词",
        "source": "英语课本"
      }
    ],
    "confidence_index": 3
  }'

# 获取计划
curl "http://localhost:8000/api/v1/planning/plans/123?limit=10"

# 获取统计
curl "http://localhost:8000/api/v1/planning/plans/statistics/123?days=7"
```

## 🔧 部署和配置

### 1. 确保依赖已安装
```bash
pip install influxdb-client fastapi uvicorn pydantic
```

### 2. 配置 InfluxDB
确保 `config.py` 中的 InfluxDB 配置正确：
```python
INFLUXDB_ENABLED = True
INFLUXDB_URL = "http://your-influxdb-url:8086"
INFLUXDB_TOKEN = "your-token"
INFLUXDB_ORG = "your-org"
INFLUXDB_BUCKET = "daily_learning"
```

### 3. 启动服务
```bash
cd /root/projects/smart_friend
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 4. 访问 API 文档
启动后访问：http://localhost:8000/docs

## 🧪 测试

### 运行测试脚本
```bash
cd /root/projects/smart_friend
python test_planning.py
```

### 健康检查
```bash
curl http://localhost:8000/api/v1/planning/health
```

## 📈 数据分析

### 支持的统计维度
1. **计划完成情况**: 总计划数、已完成、待执行、进行中
2. **学科分布**: 各学科的计划数量分布
3. **信心指数**: 平均执行信心指数
4. **完成率**: 计划完成百分比

### Flux 查询示例
```flux
from(bucket: "daily_learning")
  |> range(start: -7d)
  |> filter(fn: (r) => r._measurement == "study_plans")
  |> filter(fn: (r) => r.child_id == "123")
  |> filter(fn: (r) => r.status == "completed")
  |> count()
```

## 🔍 故障排除

### 常见问题
1. **InfluxDB 连接超时**: 检查网络连接和 InfluxDB 服务状态
2. **导入错误**: 确保所有依赖包已正确安装
3. **数据格式错误**: 检查请求数据是否符合 schema 定义

### 日志查看
```bash
tail -f logs/app.log
```

## 🔄 与现有模块的集成

该计划模块与现有的 `daily_learning` 模块完全兼容：
- 使用相同的 InfluxDB 配置
- 遵循相同的架构模式
- 可以关联学习记录和计划数据进行综合分析

## 📝 后续扩展

可以考虑的功能扩展：
1. 计划模板管理
2. 计划执行提醒
3. 计划与学习记录的关联分析
4. 智能计划推荐
5. 计划执行报告生成
