# TTS Utils Module - 文本转语音工具模块

## 概述

TTS Utils 是一个完全重构的模块化文本转语音工具，将原来的三个文件（`audio.py`、`tts_utilslyk.py`、`tts.py`）合并为一个统一的 `tts_utils.py` 模块。

## 设计原则

- **单一职责原则**：每个类负责特定功能
- **开闭原则**：通过继承扩展功能
- **依赖倒置**：依赖抽象而非具体实现
- **模块化架构**：清晰的层次结构和职责分离

## 模块结构

### 1. 协议处理层
- `TTSProtocolConstants`: 协议常量管理
- `TTSProtocolHeader`: 协议头部处理
- `TTSProtocolOptional`: 协议可选字段处理
- `TTSProtocolResponse`: 协议响应封装
- `TTSProtocolProcessor`: 协议解析和消息构建

### 2. 文本预处理层
- `TextPreprocessor`: 文本预处理，优化TTS输出

### 3. 音频播放层
- `BaseAudioPlayer`: 音频播放器抽象基类
- `PygameAudioPlayer`: 基于Pygame的音频播放器
- `PydubAudioPlayer`: 基于Pydub的音频播放器（带Pygame回退）
- `AudioPlayerFactory`: 音频播放器工厂

### 4. 网络连接层
- `WebSocketManager`: WebSocket连接管理

### 5. TTS客户端层
- `BaseTTSClient`: TTS客户端抽象基类
- `SimpleTTSClient`: 简单TTS客户端
- `AdvancedTTSManager`: 高级TTS管理器（支持流式处理）

### 6. 工厂和便捷函数层
- `TTSFactory`: TTS客户端工厂
- `create_tts_client()`: 便捷客户端创建函数
- `quick_tts_play()`: 快速播放函数
- `quick_tts_file()`: 快速文件保存函数

## 主要特性

### ✅ 模块化架构
- 清晰的层次结构
- 职责分离
- 易于维护和扩展

### ✅ 多种使用方式
- 便捷函数：适合简单需求
- 工厂模式：适合复杂应用
- 直接实例化：适合定制需求

### ✅ 流式语音合成
- 支持实时文本输入
- 适合AI生成文本场景
- 异步处理

### ✅ 多种音频播放器
- Pygame播放器：轻量级
- Pydub播放器：高质量（带回退机制）
- 自动选择最佳播放器

### ✅ 完整错误处理
- 优雅的错误处理
- 详细的日志记录
- 回退机制

### ✅ 文本预处理
- 移除Markdown格式
- 处理特殊字符
- 优化TTS输出

## 使用示例

### 简单使用
```python
from utils.tts_utils import quick_tts_play, quick_tts_file

# 快速播放
quick_tts_play("你好，世界！")

# 快速保存文件
quick_tts_file("你好，世界！", "output.mp3")
```

### 工厂模式
```python
from utils.tts_utils import TTSFactory

# 创建简单客户端
client = TTSFactory.create_simple_client(app_id, token)
client.text_to_speech_play("测试文本")

# 创建高级管理器
manager = TTSFactory.create_advanced_manager(app_id, token)
manager.text_to_speech_file("测试文本", "output.mp3")
```

### 流式语音合成
```python
import asyncio
from utils.tts_utils import TTSFactory

async def streaming_example():
    manager = TTSFactory.create_advanced_manager()
    
    # 启动流式合成
    await manager.start_streaming_synthesis()
    
    # 逐步添加文本
    text_chunks = ["你好，", "这是", "流式", "语音合成", "测试。"]
    for chunk in text_chunks:
        await manager.add_streaming_text(chunk)
        await asyncio.sleep(0.2)
    
    # 停止流式合成
    await manager.stop_streaming_synthesis()

# 运行示例
asyncio.run(streaming_example())
```

### 类集成
```python
from utils.tts_utils import TTSFactory

class AIAssistant:
    def __init__(self):
        self.tts_manager = TTSFactory.create_advanced_manager()
    
    def speak(self, text: str):
        return self.tts_manager.text_to_speech_play(text)
    
    async def stream_speak(self, text_generator):
        await self.tts_manager.start_streaming_synthesis()
        for text_chunk in text_generator:
            await self.tts_manager.add_streaming_text(text_chunk)
        await self.tts_manager.stop_streaming_synthesis()
```

## 依赖要求

```
pygame>=2.5.0
websockets>=12.0
aiofiles>=23.0.0
pydub>=0.25.0  # 可选，不可用时自动回退到pygame
```

## 配置

使用默认的Doubao/Volcano TTS API凭据：
- App ID: `5311525929`
- Access Key: `DRNTjbbfC1QcfDrTndiSSBdTr23F0-23`

## 兼容性

- Python 3.8+
- 支持异步操作
- 跨平台兼容
- 优雅的依赖回退机制

## 测试

运行测试脚本：
```bash
python test_tts_utils.py
python tts_usage_example.py
```

## 迁移指南

### 从旧模块迁移

**旧代码：**
```python
from utils.audio import AudioPlayer
from utils.tts import TTsManager
```

**新代码：**
```python
from utils.tts_utils import TTSFactory

# 替换AudioPlayer
audio_player = TTSFactory.create_audio_player()

# 替换TTsManager
tts_manager = TTSFactory.create_advanced_manager()
```

## 扩展指南

### 添加新的音频播放器
```python
from utils.tts_utils import BaseAudioPlayer

class CustomAudioPlayer(BaseAudioPlayer):
    def play_file(self, file_path: str) -> bool:
        # 实现自定义播放逻辑
        pass
    
    def start_stream_playback(self):
        # 实现流式播放
        pass
```

### 添加新的TTS客户端
```python
from utils.tts_utils import BaseTTSClient

class CustomTTSClient(BaseTTSClient):
    def text_to_speech_file(self, text: str, output_path: str) -> bool:
        # 实现自定义TTS逻辑
        pass
```

## 注意事项

1. **网络连接**：TTS功能需要网络连接到Doubao/Volcano服务
2. **凭据管理**：请妥善保管API凭据
3. **错误处理**：始终检查函数返回值
4. **资源管理**：流式合成后记得调用停止方法
5. **依赖回退**：Pydub不可用时会自动使用Pygame

## 更新日志

### v2.0.0 (2025-06-19)
- 完全重构为模块化架构
- 合并三个文件为单一模块
- 实现单一职责原则
- 添加工厂模式和便捷函数
- 支持流式语音合成
- 完善错误处理和日志记录
- 添加依赖回退机制
