# 📁 文件上传安全验证功能指南

## 🛡️ 功能概述

文件上传安全验证模块提供了完整的白名单验证机制，包括文件类型、存储路径、文件大小等多重安全检查，有效防止恶意文件上传和路径遍历攻击。

## 🏗️ 架构设计

### 模块结构
```
core/file_upload/
├── __init__.py              # 模块初始化
├── config.py                # 配置文件（白名单定义）
├── validator.py             # 文件验证器
└── endpoints.py             # API端点

service/
└── file_upload_service.py   # 文件上传服务
```

## 🔒 安全特性

### 1. 文件类型白名单验证
- **扩展名检查**: 只允许预定义的安全文件扩展名
- **MIME类型验证**: 验证文件的真实MIME类型
- **文件头部检查**: 验证文件内容与扩展名是否匹配
- **危险文件黑名单**: 阻止可执行文件和脚本文件

### 2. 存储路径白名单验证
- **路径遍历防护**: 防止 `../` 等路径遍历攻击
- **允许路径列表**: 只能上传到预定义的安全目录
- **路径规范化**: 自动处理路径格式统一

### 3. 文件大小限制
- **分类大小限制**: 不同文件类型有不同的大小限制
- **自定义限制**: 支持API调用时指定大小限制
- **实时大小检查**: 上传过程中验证文件大小

### 4. 文件内容安全检查
- **文件签名验证**: 检查文件头部是否符合声明的类型
- **内容完整性**: 生成文件哈希值确保完整性
- **病毒扫描接口**: 预留病毒扫描功能接口

## 📋 配置说明

### 允许的文件类型
```python
ALLOWED_EXTENSIONS = {
    # 图片文件
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg',
    # 文档文件
    '.pdf', '.doc', '.docx', '.txt', '.md', '.rtf',
    # 表格文件
    '.xls', '.xlsx', '.csv',
    # 其他安全类型...
}
```

### 允许的存储路径
```python
ALLOWED_UPLOAD_PATHS = [
    'uploads/images',
    'uploads/documents', 
    'uploads/videos',
    'uploads/audio',
    'uploads/user_files',
    'uploads/learning_materials'
]
```

### 文件大小限制
```python
MAX_FILE_SIZE = {
    'image': 10 * 1024 * 1024,      # 10MB
    'document': 50 * 1024 * 1024,   # 50MB
    'video': 500 * 1024 * 1024,     # 500MB
    'audio': 100 * 1024 * 1024,     # 100MB
    'default': 10 * 1024 * 1024     # 10MB
}
```

## 🚀 API 使用示例

### 1. 单文件上传
```bash
curl -X POST "http://localhost:8001/api/v1/files/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@example.jpg" \
  -F "upload_path=uploads/images" \
  -F "user_id=123" \
  -F "allowed_types=.jpg,.png,.gif"
```

### 2. 批量文件上传
```bash
curl -X POST "http://localhost:8001/api/v1/files/upload-multiple" \
  -H "Content-Type: multipart/form-data" \
  -F "files=@file1.jpg" \
  -F "files=@file2.png" \
  -F "upload_path=uploads/images" \
  -F "user_id=123"
```

### 3. 文件验证（不上传）
```bash
curl -X POST "http://localhost:8001/api/v1/files/validate" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@example.pdf" \
  -F "upload_path=uploads/documents"
```

### 4. 获取上传配置
```bash
curl "http://localhost:8001/api/v1/files/config"
```

### 5. 删除文件
```bash
curl -X DELETE "http://localhost:8001/api/v1/files/delete?file_path=uploads/images/example.jpg&user_id=123"
```

## 🔧 Python 客户端示例

```python
import requests

# 单文件上传
def upload_file(file_path, upload_path="uploads/temp", user_id=None):
    url = "http://localhost:8001/api/v1/files/upload"
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {
            'upload_path': upload_path,
            'user_id': user_id,
            'allowed_types': '.jpg,.png,.pdf'
        }
        
        response = requests.post(url, files=files, data=data)
        return response.json()

# 使用示例
result = upload_file('example.jpg', 'uploads/images', 123)
print(result)
```

## ⚠️ 安全注意事项

### 1. 服务器配置
- 确保上传目录没有执行权限
- 配置Web服务器不解析上传目录中的脚本
- 定期清理临时文件

### 2. 文件存储
- 使用随机文件名避免文件名冲突
- 定期备份重要上传文件
- 监控磁盘空间使用情况

### 3. 访问控制
- 实现用户权限验证
- 记录文件上传日志
- 限制上传频率防止滥用

## 🛠️ 自定义配置

### 修改允许的文件类型
```python
# 在 core/file_upload/config.py 中修改
ALLOWED_EXTENSIONS.add('.new_extension')
ALLOWED_MIME_TYPES.add('application/new-type')
```

### 添加新的上传路径
```python
# 在 core/file_upload/config.py 中修改
ALLOWED_UPLOAD_PATHS.append('uploads/new_category')
```

### 自定义文件大小限制
```python
# 在 core/file_upload/config.py 中修改
MAX_FILE_SIZE['new_category'] = 200 * 1024 * 1024  # 200MB
```

## 📊 监控和日志

### 上传日志格式
```json
{
    "user_id": 123,
    "original_filename": "document.pdf",
    "saved_filename": "document_1234567890.pdf",
    "file_path": "uploads/documents/document_1234567890.pdf",
    "file_size": 1048576,
    "file_type": "document",
    "mime_type": "application/pdf",
    "file_hash": "sha256_hash_value",
    "upload_time": "2024-01-15T10:00:00Z",
    "status": "success"
}
```

### 健康检查
```bash
curl "http://localhost:8001/api/v1/files/health"
```

## 🔍 故障排除

### 常见错误
1. **文件类型不支持**: 检查文件扩展名是否在白名单中
2. **路径不允许**: 确认上传路径在允许列表中
3. **文件过大**: 检查文件大小是否超出限制
4. **权限错误**: 确认上传目录有写入权限

### 调试模式
```python
# 在配置中启用详细日志
import logging
logging.getLogger('core.file_upload').setLevel(logging.DEBUG)
```

## 🚀 部署建议

1. **生产环境配置**:
   - 使用专门的文件存储服务
   - 配置CDN加速文件访问
   - 启用文件压缩和缓存

2. **安全加固**:
   - 定期更新文件类型白名单
   - 监控异常上传行为
   - 实施文件访问审计

3. **性能优化**:
   - 使用异步文件处理
   - 实施文件分片上传
   - 配置合适的并发限制

## 🔄 文件冲突处理功能

### 功能概述
文件冲突处理功能提供了完整的文件存在性检查和冲突解决方案，确保文件上传的安全性和用户体验。

### 冲突处理策略

#### 1. 自动重命名 (rename) - 默认策略
```python
# 自动为冲突文件生成新名称
conflict_strategy = "rename"
# 示例：document.pdf -> document_1642234567.pdf
```

#### 2. 覆盖替换 (replace)
```python
# 覆盖现有文件，可选择创建备份
conflict_strategy = "replace"
create_backup = True  # 推荐开启备份
```

#### 3. 取消上传 (cancel)
```python
# 检测到冲突时取消上传
conflict_strategy = "cancel"
```

### 新增API端点

#### 带冲突处理的文件上传
```bash
curl -X POST "http://localhost:8011/api/v1/files/upload-with-conflict-handling" \
  -F "file=@document.pdf" \
  -F "upload_path=uploads/documents" \
  -F "user_id=123" \
  -F "conflict_strategy=rename" \
  -F "create_backup=true" \
  -F "check_content=true"
```

#### 仅检查冲突
```bash
curl -X POST "http://localhost:8011/api/v1/files/check-conflict" \
  -H "Content-Type: application/json" \
  -d '{
    "filename": "document.pdf",
    "upload_path": "uploads/documents",
    "file_size": 1024000
  }'
```

### Python代码示例

#### 服务层使用
```python
from core.file_upload.schemas import FileConflictStrategy
from service.file_upload_service import FileUploadService

service = FileUploadService()

# 上传文件，自动处理冲突
result = await service.upload_file_with_conflict_handling(
    file=upload_file,
    upload_path="uploads/documents",
    user_id=123,
    conflict_strategy=FileConflictStrategy.RENAME,
    create_backup=True,
    check_content=True
)

if result.success:
    print(f"上传成功: {result.file_info['saved_filename']}")
    if result.conflict_info and result.conflict_info.conflict_detected:
        print(f"冲突已处理: {result.operation_log}")
else:
    print(f"上传失败: {result.message}")
```

### 响应格式示例

#### 成功响应（有冲突处理）
```json
{
  "success": true,
  "message": "文件上传成功",
  "file_info": {
    "user_id": 123,
    "original_filename": "document.pdf",
    "saved_filename": "document_1642234567.pdf",
    "file_path": "/uploads/documents/document_1642234567.pdf",
    "file_size": 1024000,
    "conflict_handled": true,
    "strategy_used": "rename"
  },
  "conflict_info": {
    "conflict_detected": true,
    "existing_file": {
      "file_name": "document.pdf",
      "file_size": 512000,
      "created_time": "2024-01-14T15:20:00Z",
      "modified_time": "2024-01-14T15:20:00Z",
      "is_identical": false
    },
    "suggested_strategy": "rename",
    "suggested_filename": "document_1642234567.pdf"
  },
  "operation_log": "检测到文件冲突; 文件已重命名; 文件成功保存"
}
```

### 安全特性

#### 1. 文件存在性检查
- 检查目标路径是否已存在同名文件
- 返回现有文件的详细信息（大小、时间、哈希值）

#### 2. 内容相同性检查
- 自动检测文件内容是否相同
- 相同内容文件跳过上传，避免重复存储
- 提供文件哈希值比较

#### 3. 备份机制
- 覆盖重要文件前自动创建备份
- 备份文件存储在 `.backup` 目录
- 包含时间戳的备份文件名

#### 4. 操作日志
- 记录所有冲突处理操作
- 详细的操作步骤日志
- 便于审计和故障排除

### 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| conflict_strategy | string | "rename" | 冲突处理策略 |
| create_backup | boolean | false | 是否创建备份 |
| check_content | boolean | true | 是否检查内容相同性 |

### 故障排除

#### 冲突处理相关问题
1. **冲突检测失败**: 检查目标目录读取权限
2. **重命名失败**: 确认目标目录写入权限
3. **备份创建失败**: 检查磁盘空间和权限
4. **内容检查失败**: 验证文件完整性

#### 调试日志
```bash
# 查看冲突处理日志
grep "conflict" logs/app.log

# 查看文件操作日志
grep "file_upload" logs/app.log | grep "conflict"
```
