# Enhanced OpenManus Framework Guide

## Overview

The Enhanced OpenManus Framework integrates multiple AI capabilities including:
- **Jina Embedding v4** for semantic search and similarity matching
- **Intent Classification** using the provided 100-data classification dataset
- **Summary Report Generation** using the structured report template
- **Dataset Management** with embedding-based search capabilities
- **Doubao API Integration** for natural language processing

## Key Components

### 1. Jina Embedding Client (`JinaEmbeddingClient`)

Handles text embedding generation using <PERSON><PERSON>'s latest embedding model.

```python
embedding_client = JinaEmbeddingClient()
embeddings = embedding_client.get_embeddings(["Hello world", "How are you?"])
single_embedding = embedding_client.get_single_embedding("Hello world")
```

**Configuration:**
- `JINA_API_KEY`: Your Jina API key
- `JINA_MODEL`: Model name (default: "jina-embeddings-v3")

### 2. Dataset Manager (`DatasetManager`)

Manages datasets with embedding-based search capabilities.

```python
dataset_manager = DatasetManager()

# Add new entry
entry_id = dataset_manager.add_dataset_entry(
    "How to solve math problems",
    {"subject": "math", "grade": 5}
)

# Search similar entries
results = dataset_manager.search_similar_entries("math homework help", top_k=5)
```

**Features:**
- SQLite database storage
- Automatic embedding generation
- Cosine similarity search
- Metadata support

### 3. Intent Classification System (`IntentClassifier`)

Classifies user intents using the provided 100-data classification dataset.

```python
intent_classifier = IntentClassifier()

# Classify intent
result = intent_classifier.classify_intent("Can you help me with homework?")
# Returns: {
#   "predicted_class": 1,
#   "predicted_intention": "study_create_plan",
#   "confidence": 0.85,
#   "similar_examples": [...]
# }
```

**Intent Classes:**
- 0: `daily_chat` - General conversation
- 1: `study_create_plan` - Creating study plans
- 2: `study_modify_plan` - Modifying existing plans
- 3: `study_execute_task` - Executing study tasks
- 4: `study_review_progress` - Reviewing progress

### 4. Summary Report Manager (`SummaryReportManager`)

Generates structured summary reports using the provided template.

```python
summary_manager = SummaryReportManager()

# Create report
report = summary_manager.create_summary_report({
    "completion_rate_percentage": "85%",
    "most_focused_times_or_tasks": "Morning sessions"
})

# Save report
report_id = summary_manager.save_report(report)

# Generate AI summary
ai_summary = summary_manager.generate_ai_summary(data, doubao_client)
```

**Report Sections:**
1. Child Profile and Past Performance
2. Task Plan and Completion
3. Focus and Behaviour Insights
4. Quality of Homework
5. Time and Activity Usage
6. Homework Plan Adjustments
7. Personalized Suggestions
8. Encouragement and Motivation

### 5. Enhanced OpenManus Planner

The main orchestrator that combines all components.

```python
planner = OpenManusPlanner()

# Available tasks:
response = planner.process_task("research_and_summarize", {"query": "math help"})
response = planner.process_task("intent_classification", {"text": "I need help"})
response = planner.process_task("semantic_search", {"query": "homework", "top_k": 5})
response = planner.process_task("generate_summary_report", {"data": {...}})
```

## Setup and Installation

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure API Keys

Set environment variables:
```bash
export DOUBAO_API_KEY="your_doubao_key"
export JINA_API_KEY="your_jina_key"
```

Or modify the configuration in the code:
```python
DOUBAO_API_KEY = "your_doubao_key"
JINA_API_KEY = "your_jina_key"
```

### 3. Initialize the System

```python
from openmanus import initialize_system

planner = initialize_system()
```

This will:
- Create the SQLite database
- Load and process the intent classification data
- Generate embeddings for the dataset
- Set up all components

## Usage Examples

### Basic Chat with Intent Classification

```python
planner = OpenManusPlanner()

user_input = "Can you help me organize my study schedule?"
intent_result = planner.intent_classifier.classify_intent(user_input)
print(f"Intent: {intent_result['predicted_intention']}")

response = planner.process_task("research_and_summarize", {"query": user_input})
print(f"Response: {response}")
```

### Semantic Search

```python
# Search for similar content
results = planner.dataset_manager.search_similar_entries("math homework", top_k=3)
for result in results:
    print(f"Similarity: {result['similarity']:.2f} - {result['content']}")
```

### Generate Summary Report

```python
learning_data = {
    "completion_rate_percentage": "90%",
    "most_focused_times_or_tasks": "Morning math sessions",
    "total_study_time_and_active_engagement": "2.5 hours"
}

report_result = planner.generate_summary_report(learning_data)
print(f"Report ID: {report_result['output']['report_id']}")
print(f"AI Summary: {report_result['output']['ai_summary']}")
```

## Interactive Commands

When running the main script, you can use these commands:

- `/intent <text>` - Classify intent of text
- `/search <query>` - Search dataset semantically
- `/report` - Generate sample summary report
- `/stats` - Show intent classification statistics
- Regular text - Normal chat with automatic intent classification

## Database Schema

The system uses SQLite with these tables:

### intent_data
- `id` (TEXT PRIMARY KEY)
- `content` (TEXT)
- `class_id` (INTEGER)
- `intention` (TEXT)
- `metadata` (TEXT JSON)
- `embedding` (BLOB)
- `created_at` (TIMESTAMP)

### dataset_entries
- `id` (TEXT PRIMARY KEY)
- `content` (TEXT)
- `embedding` (BLOB)
- `metadata` (TEXT JSON)
- `created_at` (TIMESTAMP)

### summary_reports
- `id` (TEXT PRIMARY KEY)
- `report_data` (TEXT JSON)
- `created_at` (TIMESTAMP)

## Configuration Options

### Mock Mode
Set `USE_MOCK_MODE = True` to use mock responses instead of API calls for testing.

### Fallback Mode
Set `FALLBACK_TO_MOCK = True` to automatically fallback to mock responses if APIs fail.

### Embedding Dimensions
Jina v3 embeddings have 768 dimensions. Adjust if using different models.

## Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure your Doubao and Jina API keys are valid and have sufficient credits.

2. **Database Errors**: Check file permissions for the SQLite database location.

3. **Embedding Errors**: Verify Jina API connectivity and model availability.

4. **Intent Classification Low Accuracy**: Run `generate_embeddings_for_intent_data()` to ensure embeddings are generated.

### Debug Mode

Enable detailed logging by setting environment variable:
```bash
export DEBUG=1
```

## Performance Considerations

- **Embedding Generation**: Batch process embeddings for better performance
- **Database Queries**: Index frequently searched fields
- **Memory Usage**: Large embedding datasets may require memory optimization
- **API Rate Limits**: Implement rate limiting for production use

## Future Enhancements

- Support for more embedding models
- Advanced intent classification with fine-tuning
- Real-time learning from user interactions
- Multi-language support
- Integration with external knowledge bases
