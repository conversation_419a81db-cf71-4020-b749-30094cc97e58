graph TB
    %% 客户端层
    Client[🌐 客户端<br/>Web/Mobile/API调用]
    
    %% FastAPI应用层
    subgraph FastAPI["🚀 FastAPI 应用层"]
        MainApp[📱 main.py<br/>FastAPI(title='AI Child Learning Assistant')]
        CORS[🔒 CORS中间件<br/>跨域处理]
        Routes[🛣️ 路由管理器]
    end
    
    %% API端点层
    subgraph Endpoints["📡 API 端点层 (/api/v1)"]
        DailyLearningAPI[📚 每日学习 API<br/>/daily-learning/*<br/>• POST /learning-records<br/>• GET /learning-records/{id}<br/>• DELETE /learning-records<br/>• GET /learning-statistics/{id}<br/>• GET /health]
        
        PlanningAPI[📋 学习计划 API<br/>/planning/*<br/>• POST /plans<br/>• GET /plans/{child_id}<br/>• GET /plans/detail/{plan_id}<br/>• PUT /plans/{plan_id}<br/>• DELETE /plans<br/>• GET /plans/statistics/{id}<br/>• GET /health]
        
        HealthAPI[❤️ 系统健康检查<br/>• GET /<br/>• GET /health]
    end
    
    %% 服务层
    subgraph Services["⚙️ 服务层 (Business Logic)"]
        DailyLearningService[📊 DailyLearningService<br/>service/daily_learning_service.py<br/>• add_learning_record()<br/>• get_learning_records()<br/>• delete_learning_records()<br/>• get_learning_statistics()]
        
        PlanningService[📝 PlanningService<br/>service/planning_service.py<br/>• create_plan()<br/>• get_plans()<br/>• update_plan()<br/>• delete_plans()<br/>• get_plan_statistics()]
    end
    
    %% 数据模型层
    subgraph Schemas["📋 数据模型层 (Pydantic)"]
        LearningSchemas[📚 学习记录模型<br/>core/planning/schemas.py<br/>• DailyLearningCreate<br/>• DailyLearningResponse<br/>• LearningStatistics<br/>• DeleteLearningRequest]
        
        PlanningSchemas[📋 计划管理模型<br/>core/planning/schemas.py<br/>• StudyPlanCreate<br/>• StudyPlanUpdate<br/>• StudyPlanResponse<br/>• PlanStatistics<br/>• SubTask]
    end
    
    %% 数据库连接层
    subgraph Database["🗄️ 数据库层"]
        InfluxDBManager[📈 InfluxDB 管理器<br/>core/planning/database/<br/>influxdb_connection.py<br/>• write_point()<br/>• query_data()<br/>• delete_data()<br/>• check_connection()]
        
        InfluxDB[(🏪 InfluxDB 数据库<br/>Bucket: daily_learning<br/>Measurements:<br/>• daily_learning<br/>• study_plans)]
    end
    
    %% 配置层
    subgraph Config["⚙️ 配置层"]
        Settings[🔧 应用配置<br/>config.py<br/>• PROJECT_NAME<br/>• API_V1_STR<br/>• INFLUXDB_*<br/>• DATABASE_URL]
    end
    
    %% 数据流向
    Client --> MainApp
    MainApp --> CORS
    CORS --> Routes
    Routes --> DailyLearningAPI
    Routes --> PlanningAPI
    Routes --> HealthAPI
    
    DailyLearningAPI --> DailyLearningService
    PlanningAPI --> PlanningService
    
    DailyLearningService --> LearningSchemas
    PlanningService --> PlanningSchemas
    
    DailyLearningService --> InfluxDBManager
    PlanningService --> InfluxDBManager
    
    InfluxDBManager --> InfluxDB
    
    %% 配置依赖
    MainApp -.-> Settings
    InfluxDBManager -.-> Settings
    
    %% 样式
    classDef clientStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef appStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef apiStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef serviceStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef schemaStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dbStyle fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef configStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class Client clientStyle
    class MainApp,CORS,Routes appStyle
    class DailyLearningAPI,PlanningAPI,HealthAPI apiStyle
    class DailyLearningService,PlanningService serviceStyle
    class LearningSchemas,PlanningSchemas schemaStyle
    class InfluxDBManager,InfluxDB dbStyle
    class Settings configStyle
