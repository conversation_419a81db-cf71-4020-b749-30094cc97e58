# Smart Friend - OpenManus Integration

This document explains how the OpenManus functionality has been integrated into the main.py file.

## Overview

The main.py file now cleanly integrates with openmanus.py to provide full OpenManus functionality while preserving the core concepts of both systems.

## Key Changes Made

### 1. Clean Integration
- Removed complex router definitions that were causing import errors
- Created a simple FastAPI application that calls OpenManus functions
- Preserved the core OpenManus functionality without modification

### 2. Main.py Structure
- **Initialize OpenManus**: The `initialize_openmanus()` function sets up the OpenManus system
- **Global Planner**: A global `openmanus_planner` instance is maintained for reuse
- **API Endpoints**: Clean REST API endpoints that call OpenManus functions
- **Health Checks**: Status endpoints to monitor system health

### 3. API Endpoints Added

#### `/api/v1/openmanus/chat` (POST)
- Main chat endpoint using OpenManus planner
- Processes user input and returns AI responses
- Includes intent classification and metadata

#### `/api/v1/openmanus/classify-intent` (POST)
- Classifies user intent using OpenManus
- Returns predicted intention and confidence score

#### `/api/v1/openmanus/create-plan` (POST)
- Creates task plans using OpenManus
- Supports educational planning with context

#### `/api/v1/openmanus/status` (GET)
- Returns system status and performance metrics
- Useful for monitoring and debugging

## Usage

### Starting the Application
```bash
cd smart_friend
python main.py
```

The server will start on `http://localhost:8003` with:
- API Documentation: `http://localhost:8003/docs`
- Frontend (if available): `http://localhost:8003/static/aiChild.html`

### Example API Calls

#### Chat with OpenManus
```bash
curl -X POST "http://localhost:8003/api/v1/openmanus/chat" \
     -H "Content-Type: application/json" \
     -d '{"message": "Help me create a study plan for mathematics"}'
```

#### Classify Intent
```bash
curl -X POST "http://localhost:8003/api/v1/openmanus/classify-intent" \
     -H "Content-Type: application/json" \
     -d '{"text": "I need help with my homework"}'
```

#### Create a Plan
```bash
curl -X POST "http://localhost:8003/api/v1/openmanus/create-plan" \
     -H "Content-Type: application/json" \
     -d '{"objective": "Learn basic algebra", "resources": "textbook, online videos"}'
```

#### Check Status
```bash
curl "http://localhost:8003/api/v1/openmanus/status"
```

## Features Preserved

### From OpenManus (openmanus.py)
- ✅ Intent classification system
- ✅ Doubao API integration
- ✅ Local embedding system (Jina)
- ✅ Task planning capabilities
- ✅ Dataset management
- ✅ Caching system
- ✅ Performance metrics
- ✅ All AI tools and reasoning

### From Main.py
- ✅ FastAPI application structure
- ✅ Health check endpoints
- ✅ Static file serving
- ✅ Logging system
- ✅ Browser auto-opening
- ✅ Port configuration

## System Requirements

- Python 3.8+
- FastAPI
- sentence-transformers
- scikit-learn
- requests
- All dependencies from openmanus.py

## Configuration

The system uses environment variables for configuration:
- `DOUBAO_API_KEY`: Your Doubao API key
- `DOUBAO_BASE_URL`: Doubao API base URL
- `DOUBAO_MODEL_NAME`: Model name to use
- `ENABLE_OPENMANUS`: Set to "true" to enable OpenManus

## Testing

The integration has been tested and verified:
- ✅ OpenManus initialization works
- ✅ Intent classification functions correctly
- ✅ Doubao API connection successful
- ✅ Embedding system operational
- ✅ All core functionality preserved

## Next Steps

1. **Run the application**: `python main.py`
2. **Test the endpoints**: Use the API documentation at `/docs`
3. **Integrate with frontend**: Connect your frontend to the new API endpoints
4. **Monitor performance**: Use the `/status` endpoint for system monitoring

The integration is complete and ready for use!
