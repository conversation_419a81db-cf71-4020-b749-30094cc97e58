import os
import requests
import json
import hashlib
import pickle
import sqlite3
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer

# ===== Configuration =====
DOUBAO_API_KEY: str = os.getenv("DOUBAO_API_KEY", "945a7413-a966-4215-bdbc-80ed84e92555")
DOUBAO_BASE_URL: str = os.getenv("DOUBAO_BASE_URL", "https://ark.cn-beijing.volces.com/api/v3")
DOUBAO_MODEL_NAME: str = os.getenv("DOUBAO_MODEL_NAME", "doubao-1-5-vision-pro-32k-250115")
DOUBAO_TEMPERATURE: float = float(os.getenv("DOUBAO_TEMPERATURE", "0.7"))
DOUBAO_TOP_P: float = float(os.getenv("DOUBAO_TOP_P", "0.9"))
DOUBAO_MAX_TOKENS: int = int(os.getenv("DOUBAO_MAX_TOKENS", "4000"))
DOUBAO_TIMEOUT: int = int(os.getenv("DOUBAO_TIMEOUT", "180"))

# Jina Embedding Configuration - FIXED TO 384 DIMENSIONS
USE_LOCAL_EMBEDDINGS: bool = True  # Use local embeddings instead of external API
JINA_EMBEDDING_MODEL: str = "all-MiniLM-L6-v2"  # 384-dimensional model for consistency
EMBEDDING_DIM: int = 384  # Fixed to 384 dimensions for all embeddings

# Data paths
DATA_DIR = Path(__file__).parent
INTENT_DATA_PATH = DATA_DIR / "Intent_classification_100_data.json"
DATABASE_PATH = DATA_DIR / "smart_friend.db"
CACHE_DIR = DATA_DIR / "cache"
EMBEDDING_CACHE_PATH = CACHE_DIR / "embeddings.db"

# Caching configuration
ENABLE_EMBEDDING_CACHE = True
CACHE_EXPIRY_HOURS = 24

# Try different possible endpoints (the correct one depends on your specific setup)
DOUBAO_API_ENDPOINTS = [
    f"{DOUBAO_BASE_URL}/chat/completions",
    "https://open.volcengineapi.com/api/v3/chat/completions",
    "https://ark.cn-beijing.volces.com/api/v1/chat/completions"
]

USE_MOCK_MODE = False  # Set to False to use real Doubao API
FALLBACK_TO_MOCK = False  # If API fails, fallback to mock responses

# ===== Data Classes =====
@dataclass
class IntentData:
    content: str
    class_id: int
    intention: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None

# Removed SummaryReport dataclass - using simple text reports instead

@dataclass
class DatasetEntry:
    id: str
    content: str
    embedding: List[float]
    metadata: Dict[str, Any]
    created_at: str

# Local embedding configuration
CACHE_DIR = Path(__file__).parent / "cache"
EMBEDDING_CACHE_PATH = CACHE_DIR / "embeddings.db"
CACHE_EXPIRY_HOURS = 24

class LocalEmbeddingClient:
    def __init__(self):
        self.model = SentenceTransformer(JINA_EMBEDDING_MODEL)
        self.embedding_dim = EMBEDDING_DIM
        self.cache_enabled = ENABLE_EMBEDDING_CACHE
        self.cache_path = EMBEDDING_CACHE_PATH
        self.init_cache()

    def init_cache(self):
        if not self.cache_enabled:
            return
        CACHE_DIR.mkdir(exist_ok=True)
        conn = sqlite3.connect(self.cache_path)
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS embedding_cache (
                text_hash TEXT PRIMARY KEY,
                text TEXT NOT NULL,
                embedding BLOB NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                model_version TEXT DEFAULT 'jina-v2-base'
            )
        ''')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_created_at ON embedding_cache(created_at)')
        conn.commit()
        conn.close()

    def get_cache_key(self, text: str) -> str:
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def get_cached_embedding(self, text: str) -> Optional[List[float]]:
        if not self.cache_enabled:
            return None
        cache_key = self.get_cache_key(text)
        try:
            conn = sqlite3.connect(self.cache_path)
            cursor = conn.cursor()
            cursor.execute(f'''
                SELECT embedding FROM embedding_cache
                WHERE text_hash = ? AND datetime(created_at, '+{CACHE_EXPIRY_HOURS} hours') > datetime('now')
            ''', (cache_key,))
            result = cursor.fetchone()
            conn.close()
            if result:
                return pickle.loads(result[0])
        except Exception as e:
            print(f"⚠️ Cache read error: {e}")
        return None

    def cache_embedding(self, text: str, embedding: List[float]):
        if not self.cache_enabled:
            return
        try:
            cache_key = self.get_cache_key(text)
            conn = sqlite3.connect(self.cache_path)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO embedding_cache (text_hash, text, embedding, model_version)
                VALUES (?, ?, ?, ?)
            ''', (
                cache_key, text, pickle.dumps(embedding), 'jina-v2-base'
            ))
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"⚠️ Cache write error: {e}")

    def clear_expired_cache(self):
        try:
            conn = sqlite3.connect(self.cache_path)
            cursor = conn.cursor()
            cursor.execute(f'''
                DELETE FROM embedding_cache
                WHERE datetime(created_at, '+{CACHE_EXPIRY_HOURS} hours') <= datetime('now')
            ''')
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            if deleted_count > 0:
                print(f"🧹 Cleared {deleted_count} expired cache entries")
        except Exception as e:
            print(f"⚠️ Cache cleanup error: {e}")

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        self.clear_expired_cache()
        embeddings = []
        uncached_texts = []
        uncached_indices = []

        for i, text in enumerate(texts):
            cached = self.get_cached_embedding(text)
            if cached:
                embeddings.append(cached)
            else:
                embeddings.append(None)
                uncached_texts.append(text)
                uncached_indices.append(i)

        if not uncached_texts:
            return embeddings

        print(f"📊 Cache stats: {len(texts) - len(uncached_texts)}/{len(texts)} cached. Generating {len(uncached_texts)} new embeddings.")
        new_embeddings = self.model.encode(uncached_texts).tolist()

        for idx, emb in zip(uncached_indices, new_embeddings):
            embeddings[idx] = emb
            self.cache_embedding(uncached_texts[uncached_indices.index(idx)], emb)

        return embeddings

    def get_single_embedding(self, text: str) -> List[float]:
        return self.get_embeddings([text])[0]

    def check_health(self) -> bool:
        """Check if local embedding system is ready"""
        return True  # Local embeddings are always available

    def get_cache_key(self, text: str) -> str:
        """Generate cache key for text"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def get_cached_embedding(self, text: str) -> Optional[List[float]]:
        """Get embedding from cache if available and not expired"""
        if not self.cache_enabled:
            return None

        cache_key = self.get_cache_key(text)

        try:
            conn = sqlite3.connect(EMBEDDING_CACHE_PATH)
            cursor = conn.cursor()

            # Check for cached embedding within expiry time
            cursor.execute('''
                SELECT embedding FROM embedding_cache
                WHERE text_hash = ?
                AND datetime(created_at, '+{} hours') > datetime('now')
            '''.format(CACHE_EXPIRY_HOURS), (cache_key,))

            result = cursor.fetchone()
            conn.close()

            if result:
                embedding = pickle.loads(result[0])
                return embedding

        except Exception as e:
            print(f"⚠️ Cache read error: {e}")

        return None

    def cache_embedding(self, text: str, embedding: List[float]):
        """Cache embedding for future use"""
        if not self.cache_enabled:
            return

        cache_key = self.get_cache_key(text)

        try:
            conn = sqlite3.connect(EMBEDDING_CACHE_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO embedding_cache
                (text_hash, text, embedding, model_version)
                VALUES (?, ?, ?, ?)
            ''', (
                cache_key,
                text,
                pickle.dumps(embedding),
                'jina-v3'
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"⚠️ Cache write error: {e}")

    def clear_expired_cache(self):
        """Clear expired cache entries"""
        if not self.cache_enabled:
            return

        try:
            conn = sqlite3.connect(EMBEDDING_CACHE_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                DELETE FROM embedding_cache
                WHERE datetime(created_at, '+{} hours') <= datetime('now')
            '''.format(CACHE_EXPIRY_HOURS))

            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()

            if deleted_count > 0:
                print(f"🧹 Cleared {deleted_count} expired cache entries")

        except Exception as e:
            print(f"⚠️ Cache cleanup error: {e}")

# ===== API Client =====
class DoubaoClient:
    def __init__(self):
        self.api_url = f"{DOUBAO_BASE_URL}/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {DOUBAO_API_KEY}",
            "Content-Type": "application/json"
        }
        self.current_endpoint_index = 0

    def try_api_endpoints(self, base_payload: dict) -> Tuple[Optional[requests.Response], Optional[str]]:
        """Try different API endpoints and models until one works"""
        models_to_try = [
            DOUBAO_MODEL_NAME,
            "doubao-pro-4k",
            "doubao-lite-4k",
            "doubao-pro-32k",
            "doubao-lite-32k"
        ]

        for i, endpoint in enumerate(DOUBAO_API_ENDPOINTS):
            for model in models_to_try:
                try:
                    payload = base_payload.copy()
                    payload["model"] = model

                    print(f"Trying endpoint {i+1}/{len(DOUBAO_API_ENDPOINTS)} with model '{model}': {endpoint}")
                    response = requests.post(
                        endpoint,
                        headers=self.headers,
                        json=payload,
                        timeout=DOUBAO_TIMEOUT
                    )

                    if response.status_code == 200:
                        print(f"✅ Success with endpoint: {endpoint} and model: {model}")
                        self.current_endpoint_index = i
                        return response, None
                    else:
                        error_text = response.text[:300]
                        print(f"❌ Failed with status {response.status_code}: {error_text}")

                        # If it's a model access issue, try next model
                        if "does not exist or you do not have access" in error_text:
                            continue

                except Exception as e:
                    print(f"Error with endpoint {endpoint} and model {model}: {e}")
                    continue

        return None, "All endpoints and models failed"

    def chat(self, prompt: str) -> Dict[str, Any]:
        if USE_MOCK_MODE:
            print(f"[MOCK MODE] Prompt: {prompt[:100]}...")
            if "summarize" in prompt.lower():
                return {"choices": [{"message": {"content": "Mock Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
            return {"choices": [{"message": {"content": "Mock Response: This is a comprehensive answer based on the provided information."}}]}

        payload = {
            "model": DOUBAO_MODEL_NAME,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": DOUBAO_TEMPERATURE,
            "top_p": DOUBAO_TOP_P,
            "max_tokens": DOUBAO_MAX_TOKENS
        }

        try:
            print(f"Payload: {json.dumps(payload, indent=2)}")

            # Try different endpoints
            response, error = self.try_api_endpoints(payload)

            if response is None:
                print(f"All API endpoints failed: {error}")
                print("\n" + "="*60)
                print("🔧 TROUBLESHOOTING GUIDE:")
                print("="*60)
                print("1. Check if your API key is valid and active, Verify that")
                print("="*60)

                if FALLBACK_TO_MOCK:
                    print("Falling back to mock response...")
                    if "summarize" in prompt.lower():
                        return {"choices": [{"message": {"content": "Fallback Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
                    return {"choices": [{"message": {"content": "Fallback Response: This is a comprehensive answer based on the provided information."}}]}
                return {"error": error}

            data = response.json()
            print("Raw Doubao response:", json.dumps(data, indent=2))
            return data

        except requests.exceptions.HTTPError as e:
            print(f"HTTP Error: {e}")
            print(f"Response content: {e.response.text if e.response else 'No response content'}")
            if FALLBACK_TO_MOCK:
                print("Falling back to mock response...")
                if "summarize" in prompt.lower():
                    return {"choices": [{"message": {"content": "Fallback Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
                return {"choices": [{"message": {"content": "Fallback Response: This is a comprehensive answer based on the provided information."}}]}
            return {"error": f"HTTP {e.response.status_code if e.response else 'Unknown'} - {e}"}
        except requests.exceptions.ConnectionError as e:
            print(f"Connection Error: {e}")
            if FALLBACK_TO_MOCK:
                print("Falling back to mock response...")
                if "summarize" in prompt.lower():
                    return {"choices": [{"message": {"content": "Fallback Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
                return {"choices": [{"message": {"content": "Fallback Response: This is a comprehensive answer based on the provided information."}}]}
            return {"error": "Could not connect to the API. Please check your internet connection."}
        except requests.exceptions.Timeout as e:
            print(f"Timeout Error: {e}")
            if FALLBACK_TO_MOCK:
                print("Falling back to mock response...")
                if "summarize" in prompt.lower():
                    return {"choices": [{"message": {"content": "Fallback Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
                return {"choices": [{"message": {"content": "Fallback Response: This is a comprehensive answer based on the provided information."}}]}
            return {"error": "API request timed out."}
        except Exception as e:
            print(f"Unexpected Error: {e}")
            if FALLBACK_TO_MOCK:
                print("Falling back to mock response...")
                if "summarize" in prompt.lower():
                    return {"choices": [{"message": {"content": "Fallback Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
                return {"choices": [{"message": {"content": "Fallback Response: This is a comprehensive answer based on the provided information."}}]}
            return {"error": str(e)}

# ===== Dataset Manager =====
class DatasetManager:
    def __init__(self):
        self.embedding_client = LocalEmbeddingClient()
        self.init_database()

    def init_database(self):
        """Initialize SQLite database for storing embeddings and data"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS intent_data (
                id TEXT PRIMARY KEY,
                content TEXT NOT NULL,
                class_id INTEGER,
                intention TEXT,
                metadata TEXT,
                embedding BLOB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dataset_entries (
                id TEXT PRIMARY KEY,
                content TEXT NOT NULL,
                embedding BLOB,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS summary_reports (
                id TEXT PRIMARY KEY,
                report_data TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

    def load_intent_data(self) -> List[IntentData]:
        """Load intent classification data from JSON file"""
        try:
            with open(INTENT_DATA_PATH, 'r', encoding='utf-8') as f:
                data = json.load(f)

            intent_data = []
            for item in data:
                intent_item = IntentData(
                    content=item['content'],
                    class_id=item['class_id'],
                    intention=item['intention'],
                    metadata=item['metadata']
                )
                intent_data.append(intent_item)

            print(f"✅ Loaded {len(intent_data)} intent classification entries")
            return intent_data

        except Exception as e:
            print(f"❌ Error loading intent data: {e}")
            return []

    def generate_embeddings_for_intent_data(self):
        """Generate and store embeddings for intent classification data"""
        intent_data = self.load_intent_data()
        if not intent_data:
            return

        # Extract texts for embedding
        texts = [item.content for item in intent_data]

        # Generate embeddings
        embeddings = self.embedding_client.get_embeddings(texts)

        # Store in database
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        for item, embedding in zip(intent_data, embeddings):
            item_id = f"intent_{item.class_id}_{hash(item.content) % 10000}"
            cursor.execute('''
                INSERT OR REPLACE INTO intent_data
                (id, content, class_id, intention, metadata, embedding)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                item_id,
                item.content,
                item.class_id,
                item.intention,
                json.dumps(item.metadata),
                json.dumps(embedding).encode('utf-8')
            ))

        conn.commit()
        conn.close()
        print(f"✅ Stored embeddings for {len(intent_data)} intent entries")

    def add_dataset_entry(self, content: str, metadata: Dict[str, Any]) -> str:
        """Add a new entry to the dataset with embedding"""
        embedding = self.embedding_client.get_single_embedding(content)
        entry_id = f"entry_{hash(content) % 100000}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO dataset_entries (id, content, embedding, metadata)
            VALUES (?, ?, ?, ?)
        ''', (
            entry_id,
            content,
            json.dumps(embedding).encode('utf-8'),
            json.dumps(metadata)
        ))

        conn.commit()
        conn.close()

        print(f"✅ Added dataset entry: {entry_id}")
        return entry_id

    def search_similar_entries(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for similar entries using embedding similarity"""
        query_embedding = self.embedding_client.get_single_embedding(query)

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Get all entries with embeddings
        cursor.execute('SELECT id, content, embedding, metadata FROM dataset_entries')
        entries = cursor.fetchall()
        conn.close()

        if not entries:
            return []

        # Calculate similarities
        similarities = []
        for entry_id, content, embedding_blob, metadata in entries:
            try:
                stored_embedding = json.loads(embedding_blob.decode('utf-8'))
                similarity = cosine_similarity([query_embedding], [stored_embedding])[0][0]
                similarities.append({
                    'id': entry_id,
                    'content': content,
                    'metadata': json.loads(metadata),
                    'similarity': float(similarity)
                })
            except Exception as e:
                print(f"Error processing entry {entry_id}: {e}")
                continue

        # Sort by similarity and return top_k
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        return similarities[:top_k]

# ===== Simple Report Generator =====
class SimpleReportGenerator:
    def __init__(self):
        pass

    def generate_simple_report(self, data: Dict[str, Any]) -> str:
        """Generate a simple text-based report"""
        report_lines = [
            "=== Learning Progress Report ===",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "Key Metrics:",
        ]

        for key, value in data.items():
            if isinstance(value, str):
                report_lines.append(f"  {key.replace('_', ' ').title()}: {value}")

        report_lines.extend([
            "",
            "This is a simplified report. For detailed analysis,",
            "please review the individual metrics above.",
            "=== End of Report ==="
        ])

        return "\n".join(report_lines)

# ===== Intent Classification System =====
class IntentClassifier:
    def __init__(self):
        self.embedding_client = LocalEmbeddingClient()
        self.dataset_manager = DatasetManager()
        self.intent_classes = {
            0: "daily_chat",
            1: "study_create_plan",
            2: "study_modify_plan",
            3: "study_execute_task",
            4: "study_review_progress"
        }

    def classify_intent(self, text: str, threshold: float = 0.7) -> Dict[str, Any]:
        """Classify user intent using embedding similarity"""
        query_embedding = self.embedding_client.get_single_embedding(text)

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Get all intent data with embeddings
        cursor.execute('''
            SELECT content, class_id, intention, embedding
            FROM intent_data
        ''')

        intent_entries = cursor.fetchall()
        conn.close()

        if not intent_entries:
            print("⚠️ No intent data found. Please run generate_embeddings_for_intent_data() first.")
            return {
                "predicted_class": 0,
                "predicted_intention": "daily_chat",
                "confidence": 0.0,
                "similar_examples": []
            }

        # Calculate similarities
        similarities = []
        for content, class_id, intention, embedding_blob in intent_entries:
            try:
                stored_embedding = json.loads(embedding_blob.decode('utf-8'))
                similarity = cosine_similarity([query_embedding], [stored_embedding])[0][0]
                similarities.append({
                    'content': content,
                    'class_id': class_id,
                    'intention': intention,
                    'similarity': float(similarity)
                })
            except Exception as e:
                print(f"Error processing intent entry: {e}")
                continue

        # Sort by similarity
        similarities.sort(key=lambda x: x['similarity'], reverse=True)

        # Get top match
        top_match = similarities[0] if similarities else None

        if top_match and top_match['similarity'] >= threshold:
            predicted_class = top_match['class_id']
            predicted_intention = top_match['intention']
            confidence = top_match['similarity']
        else:
            # Default to daily_chat if confidence is low
            predicted_class = 0
            predicted_intention = "daily_chat"
            confidence = top_match['similarity'] if top_match else 0.0

        return {
            "predicted_class": predicted_class,
            "predicted_intention": predicted_intention,
            "confidence": confidence,
            "similar_examples": similarities[:3]  # Top 3 similar examples
        }

    def add_training_example(self, text: str, class_id: int, intention: str, metadata: Dict[str, Any] = None):
        """Add a new training example to the intent classification dataset"""
        if metadata is None:
            metadata = {}

        embedding = self.embedding_client.get_single_embedding(text)

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        example_id = f"intent_{class_id}_{hash(text) % 10000}"
        cursor.execute('''
            INSERT OR REPLACE INTO intent_data
            (id, content, class_id, intention, metadata, embedding)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            example_id,
            text,
            class_id,
            intention,
            json.dumps(metadata),
            json.dumps(embedding).encode('utf-8')
        ))

        conn.commit()
        conn.close()

        print(f"✅ Added training example: {example_id}")
        return example_id

    def get_intent_statistics(self) -> Dict[str, Any]:
        """Get statistics about the intent classification dataset"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT class_id, intention, COUNT(*) as count
            FROM intent_data
            GROUP BY class_id, intention
            ORDER BY class_id
        ''')

        stats = {}
        total_examples = 0

        for class_id, intention, count in cursor.fetchall():
            stats[intention] = {
                'class_id': class_id,
                'count': count
            }
            total_examples += count

        conn.close()

        return {
            'total_examples': total_examples,
            'classes': stats,
            'class_distribution': {k: v['count']/total_examples for k, v in stats.items()}
        }

# ===== Enhanced OpenManus Planner =====
class OpenManusPlanner:
    def __init__(self):
        self.client = DoubaoClient()
        self.embedding_client = LocalEmbeddingClient()
        self.dataset_manager = DatasetManager()
        self.report_generator = SimpleReportGenerator()
        self.intent_classifier = IntentClassifier()

        # Performance optimization: Cache frequently used results
        self._response_cache = {}
        self._plan_cache = {}
        self._max_cache_size = 100

        # Enhanced tool registry with performance optimizations
        self.tools = {
            "retriever": self.retrieve_information,
            "summarizer": self.summarize_content,
            "response_generator": self.generate_response,
            "intent_classifier": self.classify_user_intent,
            "dataset_search": self.search_dataset,
            "report_generator": self.generate_simple_report,
            "embedding_generator": self.generate_embeddings,
            "task_planner": self.create_task_plan,
            "context_analyzer": self.analyze_context,
            "learning_tracker": self.track_learning_progress,
            "content_personalizer": self.personalize_content,
            "multi_step_reasoner": self.multi_step_reasoning
        }

        # Initialize performance metrics
        self._performance_metrics = {
            "total_requests": 0,
            "cache_hits": 0,
            "average_response_time": 0.0,
            "successful_plans": 0,
            "failed_plans": 0
        }

    def call_doubao_api(self, prompt: str) -> str:
        response = self.client.chat(prompt)
        if "error" in response:
            return f"Error: {response['error']}"
        if "choices" in response and len(response["choices"]) > 0:
            return response["choices"][0]["message"]["content"]
        else:
            print(f"Unexpected response format: {response}")
            return "Error: Unexpected API response format."

    def call_openmanus(self, task: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Intelligent planning with optimized tool selection"""

        # Check plan cache for performance
        cache_key = f"{task}_{hash(str(sorted(parameters.items())))}"
        if cache_key in self._plan_cache:
            self._performance_metrics["cache_hits"] += 1
            return self._plan_cache[cache_key]

        # Use intelligent tool selection
        optimal_sequence = self.optimize_tool_selection(task, parameters)
        plan_result = self._create_optimized_plan(task, parameters, optimal_sequence)

        # Cache the plan for future use
        if len(self._plan_cache) < self._max_cache_size:
            self._plan_cache[cache_key] = plan_result

        return plan_result

    def _create_optimized_plan(self, task: str, parameters: Dict[str, Any], tool_sequence: List[str]) -> Dict[str, Any]:
        """Create an optimized execution plan based on intelligent tool selection"""
        plan_steps = []

        for i, tool_name in enumerate(tool_sequence):
            step_params = self._get_tool_parameters(tool_name, parameters, i, plan_steps)
            plan_steps.append({
                "tool": tool_name,
                "parameters": step_params
            })

        return {"plan": plan_steps}

    def _get_tool_parameters(self, tool_name: str, base_params: Dict[str, Any], step_index: int, previous_steps: List[Dict]) -> Dict[str, Any]:
        """Get optimized parameters for each tool based on context and previous steps"""
        context_reqs = self._get_tool_context_requirements()
        tool_reqs = context_reqs.get(tool_name, [])

        params = {}

        # Add required parameters based on tool requirements
        for req in tool_reqs:
            if req == "query" and "query" in base_params:
                params["query"] = base_params["query"]
            elif req == "text" and "query" in base_params:
                params["text"] = base_params["query"]
            elif req == "intent" and step_index > 0:
                # Reference previous intent classification if available
                params["intent"] = "{{intent_classifier.output}}"
            elif req == "context" and step_index > 0:
                # Reference previous context analysis if available
                params["context"] = "{{context_analyzer.output}}"
            elif req == "content" and step_index > 0:
                # Reference previous content generation
                for prev_step in reversed(previous_steps):
                    if prev_step["tool"] in ["retriever", "summarizer", "task_planner"]:
                        params["content"] = f"{{{prev_step['tool']}.output}}"
                        break
            elif req in base_params:
                params[req] = base_params[req]

        # Add tool-specific optimizations
        if tool_name == "dataset_search":
            params["top_k"] = base_params.get("top_k", 5)
        elif tool_name == "content_personalizer":
            params["user_context"] = base_params.get("user_context", {})
        elif tool_name == "learning_tracker":
            params["tracking_data"] = {
                "type": "interaction",
                "content": base_params.get("query", ""),
                "intent": base_params.get("intent", "unknown")
            }

        return params

    def _select_task_based_on_intent(self, intent: str, user_input: str) -> str:
        """Select the most appropriate task type based on detected intent and input analysis"""

        # Enhanced intent-to-task mapping with context awareness
        intent_task_mapping = {
            "daily_chat": "conversational_response",
            "study_create_plan": "research_and_plan",
            "study_modify_plan": "plan_modification",
            "task_submission": "content_analysis",
            "study_summary_request": "research_and_summarize",
            "task_completion": "educational_assessment"
        }

        base_task = intent_task_mapping.get(intent, "conversational_response")

        # Context-aware task refinement
        if "search" in user_input.lower() or "find" in user_input.lower():
            return "semantic_search"
        elif "report" in user_input.lower() or "summary" in user_input.lower():
            return "research_and_summarize"
        elif "plan" in user_input.lower() and "create" in user_input.lower():
            return "research_and_plan"
        elif "plan" in user_input.lower() and ("modify" in user_input.lower() or "change" in user_input.lower()):
            return "plan_modification"

        return base_task

    def retrieve_information(self, query: str) -> Dict[str, Any]:
        print(f"Retrieving info for: {query}")
        content = f"Information about {query}: This is a detailed explanation covering key aspects."
        return {"output": content, "status": "success"}

    def summarize_content(self, content: str, length: str = "medium") -> Dict[str, Any]:
        print(f"Summarizing content ({length})")
        prompt = f"Summarize the following content in {length} length:\n\n{content}"
        summary = self.call_doubao_api(prompt)
        return {"output": summary, "status": "success"}

    def generate_response(self, question: str, summary: str = None, examples: str = None,
                         intent: str = None, style: str = "general") -> Dict[str, Any]:
        print(f"Generating final response (style: {style})")

        # Create style-specific system prompts
        style_prompts = {
            "conversational": "You are a friendly AI assistant for children. Be warm, engaging, and age-appropriate. Use simple language and show enthusiasm.",
            "educational_planning": "You are an educational planning assistant. Help create structured, achievable study plans. Be encouraging and provide clear, actionable steps.",
            "plan_adjustment": "You are a helpful assistant for modifying study plans. Be understanding of challenges and provide flexible, practical solutions.",
            "general": "You are a helpful AI assistant. Provide clear, accurate, and helpful responses."
        }

        system_prompt = style_prompts.get(style, style_prompts["general"])

        # Build context information
        context_parts = []

        if summary:
            context_parts.append(f"Summary: {summary}")

        if examples:
            context_parts.append(f"Similar examples: {examples}")

        if intent:
            context_parts.append(f"User intent: {intent}")

        # Create the full prompt
        if context_parts:
            context_text = "\n".join(context_parts)
            prompt = f"{system_prompt}\n\nContext:\n{context_text}\n\nUser question: {question}\n\nResponse:"
        else:
            prompt = f"{system_prompt}\n\nUser question: {question}\n\nResponse:"

        answer = self.call_doubao_api(prompt)
        return {"output": answer, "status": "success"}

    def classify_user_intent(self, text: str) -> Dict[str, Any]:
        """Classify user intent using the intent classification system"""
        print(f"Classifying intent for: {text[:50]}...")
        result = self.intent_classifier.classify_intent(text)
        return {"output": result, "status": "success"}

    def search_dataset(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        """Search for similar entries in the dataset"""
        print(f"Searching dataset for: {query[:50]}...")
        results = self.dataset_manager.search_similar_entries(query, top_k)
        return {"output": results, "status": "success"}

    def generate_simple_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a simple text-based report"""
        print("Generating simple report...")
        report_text = self.report_generator.generate_simple_report(data)

        return {
            "output": {
                "report_text": report_text,
                "timestamp": datetime.now().isoformat()
            },
            "status": "success"
        }

    def generate_embeddings(self, texts: List[str]) -> Dict[str, Any]:
        """Generate embeddings for a list of texts"""
        print(f"Generating embeddings for {len(texts)} texts...")
        embeddings = self.embedding_client.get_embeddings(texts)
        return {"output": embeddings, "status": "success"}

    def create_task_plan(self, objective: str, resources: str = None, modification: str = None, existing_context: str = None) -> Dict[str, Any]:
        """Create detailed task plans for educational objectives"""
        print(f"🎯 Creating task plan for: {objective[:50]}...")

        # Build context for task planning
        context_parts = []
        if resources:
            context_parts.append(f"Available resources: {resources}")
        if existing_context:
            context_parts.append(f"Existing context: {existing_context}")
        if modification:
            context_parts.append(f"Modification request: {modification}")

        context_text = "\n".join(context_parts) if context_parts else "No additional context"

        # Create comprehensive task planning prompt
        planning_prompt = f"""You are an expert educational task planner. Create a detailed, actionable plan.

Context:
{context_text}

Objective: {objective}

Create a structured plan with:
1. Clear learning objectives
2. Step-by-step tasks
3. Time estimates
4. Resources needed
5. Success criteria
6. Potential challenges and solutions

Make it age-appropriate and engaging for children."""

        plan_response = self.call_doubao_api(planning_prompt)

        return {
            "output": {
                "objective": objective,
                "detailed_plan": plan_response,
                "context_used": context_text,
                "timestamp": datetime.now().isoformat()
            },
            "status": "success"
        }

    def analyze_context(self, query: str, context: Dict[str, Any] = None, intent: str = None, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze context to provide better understanding for other tools"""
        print(f"🔍 Analyzing context for: {query[:50]}...")

        analysis_parts = []
        analysis_parts.append(f"Query: {query}")

        if intent:
            analysis_parts.append(f"Detected intent: {intent}")

        if context:
            analysis_parts.append(f"User context: {context}")

        if data:
            analysis_parts.append(f"Additional data: {data}")

        # Create context analysis prompt
        analysis_prompt = f"""Analyze the following context to provide insights for educational assistance:

{chr(10).join(analysis_parts)}

Provide analysis including:
1. Key themes and topics
2. User's likely educational level
3. Emotional tone and engagement level
4. Specific learning needs identified
5. Recommended approach for response

Keep analysis concise but insightful."""

        analysis_response = self.call_doubao_api(analysis_prompt)

        return {
            "output": {
                "query": query,
                "analysis": analysis_response,
                "context_factors": len(analysis_parts),
                "timestamp": datetime.now().isoformat()
            },
            "status": "success"
        }

    def track_learning_progress(self, interaction: str = None, intent: str = None, plan_creation: str = None,
                               plan: str = None, modification_request: str = None) -> Dict[str, Any]:
        """Track learning progress and provide insights"""
        print("📊 Tracking learning progress...")

        # Determine tracking type
        tracking_data = {}
        if interaction and intent:
            tracking_data["type"] = "interaction"
            tracking_data["content"] = interaction
            tracking_data["intent"] = intent
        elif plan_creation and plan:
            tracking_data["type"] = "plan_creation"
            tracking_data["content"] = plan_creation
            tracking_data["plan"] = plan
        elif modification_request:
            tracking_data["type"] = "plan_modification"
            tracking_data["content"] = modification_request

        # Generate learning insights
        insights_prompt = f"""Analyze this learning interaction and provide insights:

Tracking Data: {tracking_data}

Provide insights on:
1. Learning engagement level
2. Progress indicators
3. Areas of strength
4. Areas needing improvement
5. Recommended next steps

Keep insights constructive and encouraging."""

        insights_response = self.call_doubao_api(insights_prompt)

        return {
            "output": {
                "tracking_data": tracking_data,
                "insights": insights_response,
                "timestamp": datetime.now().isoformat()
            },
            "status": "success"
        }

    def personalize_content(self, content: str, user_context: Dict[str, Any] = None, query: str = None) -> Dict[str, Any]:
        """Personalize content based on user context and preferences"""
        print("🎨 Personalizing content...")

        # Build personalization context
        personalization_factors = []
        if user_context:
            if "grade" in user_context:
                personalization_factors.append(f"Grade level: {user_context['grade']}")
            if "age" in user_context:
                personalization_factors.append(f"Age: {user_context['age']}")
            if "interests" in user_context:
                personalization_factors.append(f"Interests: {user_context['interests']}")
            if "learning_style" in user_context:
                personalization_factors.append(f"Learning style: {user_context['learning_style']}")

        context_text = "\n".join(personalization_factors) if personalization_factors else "No specific user context"

        # Create personalization prompt
        personalization_prompt = f"""Personalize the following content for a child learner:

User Context:
{context_text}

Original Content:
{content}

Query Context: {query or "General content"}

Personalize by:
1. Adjusting language complexity appropriately
2. Adding relevant examples and analogies
3. Making content more engaging and interactive
4. Incorporating age-appropriate elements
5. Ensuring educational value is maintained

Provide personalized content that is engaging and appropriate."""

        personalized_response = self.call_doubao_api(personalization_prompt)

        return {
            "output": {
                "original_content": content,
                "personalized_content": personalized_response,
                "personalization_factors": personalization_factors,
                "timestamp": datetime.now().isoformat()
            },
            "status": "success"
        }

    def multi_step_reasoning(self, query: str, context: str = None, reasoning: str = None, history: str = None) -> Dict[str, Any]:
        """Perform multi-step reasoning for complex educational problems"""
        print("🧠 Performing multi-step reasoning...")

        # Build reasoning context
        reasoning_context = []
        reasoning_context.append(f"Query: {query}")

        if context:
            reasoning_context.append(f"Context: {context}")
        if reasoning:
            reasoning_context.append(f"Previous reasoning: {reasoning}")
        if history:
            reasoning_context.append(f"History: {history}")

        context_text = "\n".join(reasoning_context)

        # Create multi-step reasoning prompt
        reasoning_prompt = f"""Perform multi-step reasoning for this educational query:

{context_text}

Break down the reasoning into clear steps:
1. Problem identification and analysis
2. Key concepts and knowledge required
3. Step-by-step solution approach
4. Potential challenges and solutions
5. Learning opportunities and extensions
6. Final recommendations

Provide clear, logical reasoning that a child can follow and understand."""

        reasoning_response = self.call_doubao_api(reasoning_prompt)

        return {
            "output": {
                "query": query,
                "reasoning_steps": reasoning_response,
                "context_used": context_text,
                "timestamp": datetime.now().isoformat()
            },
            "status": "success"
        }

    def execute_plan(self, plan: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Enhanced plan execution with performance monitoring and error handling"""
        results = {}
        execution_start_time = datetime.now()

        try:
            for step_index, step in enumerate(plan):
                step_start_time = datetime.now()
                tool = step["tool"]
                params = step["parameters"]
                resolved = {}

                # Enhanced parameter resolution with error handling
                for k, v in params.items():
                    if isinstance(v, str) and v.startswith("{{") and v.endswith("}}"):
                        ref_path = v[2:-2]
                        ref_parts = ref_path.split(".")
                        ref_tool = ref_parts[0]

                        if ref_tool in results:
                            if len(ref_parts) > 1:
                                # Handle nested references like {{tool.output.field}}
                                resolved_value = results[ref_tool]
                                for part in ref_parts[1:]:
                                    if isinstance(resolved_value, dict) and part in resolved_value:
                                        resolved_value = resolved_value[part]
                                    else:
                                        resolved_value = resolved_value  # Use the whole value if path not found
                                resolved[k] = resolved_value
                            else:
                                resolved[k] = results[ref_tool]["output"]
                        else:
                            print(f"⚠️ Warning: Reference {ref_tool} not found, using empty string")
                            resolved[k] = ""
                    else:
                        resolved[k] = v

                # Execute tool with error handling
                if tool in self.tools:
                    print(f"🔧 Executing step {step_index + 1}/{len(plan)}: {tool}")
                    try:
                        tool_result = self.tools[tool](**resolved)
                        results[tool] = tool_result

                        # Performance tracking
                        step_duration = (datetime.now() - step_start_time).total_seconds()
                        print(f"✅ Step completed in {step_duration:.2f}s")

                    except Exception as tool_error:
                        print(f"❌ Error in tool {tool}: {tool_error}")
                        results[tool] = {
                            "output": f"Error in {tool}: {str(tool_error)}",
                            "status": "error",
                            "error": str(tool_error)
                        }
                        # Continue execution with error result

                else:
                    error_msg = f"Unknown tool: {tool}"
                    print(f"❌ {error_msg}")
                    results[tool] = {
                        "output": error_msg,
                        "status": "error",
                        "error": error_msg
                    }

            # Update performance metrics
            total_duration = (datetime.now() - execution_start_time).total_seconds()
            self._performance_metrics["successful_plans"] += 1
            self._update_average_response_time(total_duration)

            print(f"🎉 Plan execution completed in {total_duration:.2f}s")

        except Exception as e:
            self._performance_metrics["failed_plans"] += 1
            print(f"❌ Plan execution failed: {e}")
            results["execution_error"] = {
                "output": f"Plan execution failed: {str(e)}",
                "status": "error",
                "error": str(e)
            }

        return results

    def _update_average_response_time(self, duration: float):
        """Update average response time metric"""
        current_avg = self._performance_metrics["average_response_time"]
        total_requests = self._performance_metrics["total_requests"]

        if total_requests == 0:
            self._performance_metrics["average_response_time"] = duration
        else:
            # Calculate running average
            new_avg = (current_avg * total_requests + duration) / (total_requests + 1)
            self._performance_metrics["average_response_time"] = new_avg

        self._performance_metrics["total_requests"] += 1

    def process_user_input(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Enhanced complete input processing pipeline with performance optimization:
        1. Check response cache for performance
        2. Classify intent using Jina embeddings
        3. Generate optimized plan using OpenManus
        4. Execute plan with enhanced Doubao integration
        5. Cache and return structured response
        """
        processing_start_time = datetime.now()
        print(f"🔄 Processing user input: {user_input[:50]}...")

        # Performance optimization: Check response cache
        cache_key = f"response_{hash(user_input + str(context or {}))}"
        if cache_key in self._response_cache:
            self._performance_metrics["cache_hits"] += 1
            print("⚡ Using cached response")
            cached_response = self._response_cache[cache_key]
            cached_response["from_cache"] = True
            return cached_response

        try:
            # Step 1: Intent Classification with performance tracking
            print("🎯 Step 1: Intent Classification")
            intent_result = self.classify_user_intent(user_input)
            intent_data = intent_result["output"]
            predicted_intent = intent_data["predicted_intention"]
            confidence = intent_data["confidence"]

            print(f"   Intent: {predicted_intent} (confidence: {confidence:.2f})")

            # Step 2: Context-aware task selection
            print("📋 Step 2: Task Selection")
            task_type = self._select_task_based_on_intent(predicted_intent, user_input)
            print(f"   Selected task: {task_type}")

            # Step 3: Generate enhanced plan using OpenManus
            print("🧠 Step 3: Plan Generation")
            parameters = {
                "query": user_input,
                "intent": predicted_intent,
                "confidence": confidence,
                "context": context or {}
            }

            plan_result = self.call_openmanus(task_type, parameters)
            plan = plan_result.get("plan", [])
            print(f"   Generated plan with {len(plan)} steps")

            # Step 4: Execute plan with enhanced error handling
            print("🔧 Step 4: Plan Execution")
            execution_results = self.execute_plan(plan)

            # Step 5: Generate final response using enhanced Doubao integration
            print("💬 Step 5: Response Generation")
            final_response = self._generate_enhanced_final_response(
                user_input, intent_data, execution_results, context
            )

            # Calculate processing time
            processing_duration = (datetime.now() - processing_start_time).total_seconds()
            print(f"✅ Processing completed in {processing_duration:.2f}s")

            # Prepare response
            response = {
                "success": True,
                "user_input": user_input,
                "intent": intent_data,
                "task_type": task_type,
                "plan": plan,
                "execution_results": execution_results,
                "final_response": final_response,
                "processing_timestamp": datetime.now().isoformat(),
                "processing_duration": processing_duration,
                "performance_metrics": self._get_performance_summary(),
                "from_cache": False
            }

            # Cache the response for future use
            if len(self._response_cache) < self._max_cache_size:
                self._response_cache[cache_key] = response.copy()

            return response

        except Exception as e:
            error_duration = (datetime.now() - processing_start_time).total_seconds()
            print(f"❌ Processing failed after {error_duration:.2f}s: {e}")

            return {
                "success": False,
                "user_input": user_input,
                "error": str(e),
                "processing_timestamp": datetime.now().isoformat(),
                "processing_duration": error_duration,
                "fallback_response": f"I apologize, but I encountered an error processing your request: {str(e)}. Please try again."
            }

    def process_voice_input(self, voice_text: str, child_id: int = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        wxysmart-compatible voice input processing method
        Integrates wxysmart input/output format with OpenManus performance

        Args:
            voice_text: Voice recognition text input
            child_id: Student ID (optional)
            context: Additional context information

        Returns:
            Dict: wxysmart-compatible response format with OpenManus enhancements
        """
        start_time = datetime.now()

        try:
            print(f"🎤 Processing voice input: {voice_text}")

            # Prepare enhanced context with child_id
            enhanced_context = context or {}
            if child_id:
                enhanced_context.update({
                    'child_id': child_id,
                    'interaction_type': 'voice',
                    'timestamp': datetime.now().isoformat()
                })

            # Use the enhanced process_user_input method
            openmanus_result = self.process_user_input(voice_text, enhanced_context)

            # Convert to wxysmart-compatible format
            processing_duration = (datetime.now() - start_time).total_seconds()

            if openmanus_result.get("success", False):
                # Extract intent information
                intent_data = openmanus_result.get('intent', {})
                predicted_intent = intent_data.get('predicted_intention', 'daily_chat')
                confidence = intent_data.get('confidence', 0.0)

                # Success response in wxysmart format
                response = {
                    'success': True,
                    'message': openmanus_result.get('final_response', ''),
                    'intent_info': {
                        'intent': predicted_intent,
                        'confidence': confidence,
                        'tool_used': 'openmanus_enhanced',
                        'classification_level': 'openmanus_jina_embeddings'
                    },
                    'execution_info': {
                        'task_type': openmanus_result.get('task_type', 'unknown'),
                        'plan_steps': len(openmanus_result.get('plan', [])),
                        'execution_results': openmanus_result.get('execution_results', {})
                    },
                    'performance_info': {
                        'processing_duration': processing_duration,
                        'openmanus_duration': openmanus_result.get('processing_duration', 0.0),
                        'cache_used': openmanus_result.get('from_cache', False),
                        'total_requests': self._performance_metrics["total_requests"]
                    },
                    'child_context': {
                        'child_id': child_id,
                        'last_interaction': datetime.now().isoformat()
                    } if child_id else {},
                    'timestamp': datetime.now().isoformat(),
                    'response_type': 'voice_processed'
                }

                print(f"✅ Voice processing completed successfully in {processing_duration:.2f}s")
                return response

            else:
                # Error response in wxysmart format
                error_response = {
                    'success': False,
                    'message': openmanus_result.get('fallback_response', 'Processing failed'),
                    'error': openmanus_result.get('error', 'Unknown error'),
                    'intent_info': {
                        'intent': 'error',
                        'confidence': 0.0,
                        'tool_used': 'openmanus_enhanced',
                        'classification_level': 'error'
                    },
                    'performance_info': {
                        'processing_duration': processing_duration,
                        'error_occurred': True
                    },
                    'child_context': {
                        'child_id': child_id,
                        'last_interaction': datetime.now().isoformat()
                    } if child_id else {},
                    'timestamp': datetime.now().isoformat(),
                    'response_type': 'voice_error'
                }

                print(f"❌ Voice processing failed in {processing_duration:.2f}s")
                return error_response

        except Exception as e:
            error_duration = (datetime.now() - start_time).total_seconds()
            print(f"❌ Exception in process_voice_input: {e}")

            # Exception response in wxysmart format
            return {
                'success': False,
                'message': 'I encountered an error while processing your voice input. Please try again.',
                'error': str(e),
                'intent_info': {
                    'intent': 'exception',
                    'confidence': 0.0,
                    'tool_used': 'openmanus_enhanced',
                    'classification_level': 'exception'
                },
                'performance_info': {
                    'processing_duration': error_duration,
                    'exception_occurred': True
                },
                'child_context': {
                    'child_id': child_id,
                    'last_interaction': datetime.now().isoformat()
                } if child_id else {},
                'timestamp': datetime.now().isoformat(),
                'response_type': 'voice_exception'
            }

    def get_wxysmart_compatible_tools(self) -> Dict[str, Dict[str, Any]]:
        """
        Get tools in wxysmart-compatible format for integration

        Returns:
            Dict: Tools formatted for wxysmart compatibility
        """
        wxysmart_tools = {
            'smart_chat': {
                'function': self.generate_response,
                'description': 'Intelligent conversation and question answering using OpenManus',
                'parameters': ['user_input', 'child_id']
            },
            'intent_classification': {
                'function': self.classify_user_intent,
                'description': 'Advanced intent classification using Jina embeddings (99 categories)',
                'parameters': ['text']
            },
            'task_planning': {
                'function': self.create_task_plan,
                'description': 'Create optimized task plans using OpenManus intelligence',
                'parameters': ['task_description', 'context']
            },
            'content_search': {
                'function': self.search_dataset,
                'description': 'Search and retrieve relevant content from knowledge base',
                'parameters': ['query', 'limit']
            },
            'learning_analysis': {
                'function': self.track_learning_progress,
                'description': 'Analyze and track learning progress with performance metrics',
                'parameters': ['user_data', 'context']
            },
            'content_personalization': {
                'function': self.personalize_content,
                'description': 'Personalize content based on user preferences and learning style',
                'parameters': ['content', 'user_profile']
            }
        }

        return wxysmart_tools

    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive performance summary for wxysmart integration

        Returns:
            Dict: Performance metrics and system status
        """
        return {
            'openmanus_metrics': self._performance_metrics,
            'system_status': {
                'doubao_client': 'ready' if self.client else 'not_ready',
                'embedding_client': 'ready' if self.embedding_client else 'not_ready',
                'dataset_manager': 'ready' if self.dataset_manager else 'not_ready',
                'intent_classifier': 'ready' if self.intent_classifier else 'not_ready'
            },
            'cache_status': {
                'response_cache_size': len(self._response_cache),
                'plan_cache_size': len(self._plan_cache),
                'max_cache_size': self._max_cache_size
            },
            'integration_info': {
                'wxysmart_compatible': True,
                'voice_input_supported': True,
                'intent_categories': 99,
                'embedding_model': JINA_EMBEDDING_MODEL,
                'embedding_dimension': EMBEDDING_DIM
            },
            'timestamp': datetime.now().isoformat()
        }

    def analyze_intent_with_wxysmart_format(self, text: str) -> Dict[str, Any]:
        """
        Analyze intent and return in wxysmart-compatible format

        Args:
            text: Input text to analyze

        Returns:
            Dict: Intent analysis in wxysmart format
        """
        try:
            # Use OpenManus intent classification
            intent_result = self.classify_user_intent(text)

            if intent_result.get("success", False):
                intent_data = intent_result["output"]

                # Convert to wxysmart format
                return {
                    'intent': intent_data.get('predicted_intention', 'daily_chat'),
                    'confidence': intent_data.get('confidence', 0.0),
                    'text': text,
                    'top_intents': intent_data.get('top_matches', [])[:3],  # Top 3 matches
                    'classification_method': 'openmanus_jina_embeddings',
                    'embedding_dimension': EMBEDDING_DIM,
                    'total_categories': 99
                }
            else:
                # Fallback response
                return {
                    'intent': 'daily_chat',
                    'confidence': 0.5,
                    'text': text,
                    'top_intents': [],
                    'classification_method': 'fallback',
                    'error': intent_result.get('error', 'Classification failed')
                }

        except Exception as e:
            return {
                'intent': 'daily_chat',
                'confidence': 0.0,
                'text': text,
                'top_intents': [],
                'classification_method': 'error',
                'error': str(e)
            }

    def _select_task_based_on_intent(self, intent: str, user_input: str = None) -> str:
        """Select appropriate task type based on classified intent"""
        # user_input parameter kept for future extensibility
        _ = user_input  # Acknowledge parameter to avoid warnings
        intent_to_task = {
            "daily_chat": "conversational_response",
            "study_create_plan": "research_and_plan",
            "study_modify_plan": "plan_modification"
        }

        return intent_to_task.get(intent, "research_and_summarize")

    def _generate_enhanced_final_response(self, user_input: str, intent_data: Dict,
                                        execution_results: Dict, context: Dict = None) -> str:
        """Generate enhanced final response using Doubao with comprehensive context"""

        # Prepare enhanced context for Doubao
        context_info = []

        # Add intent information
        context_info.append(f"User Intent: {intent_data['predicted_intention']} (confidence: {intent_data['confidence']:.2f})")

        # Add execution results context with enhanced processing
        context_processors = {
            "context_analyzer": lambda r: f"Context Analysis: {r['output'].get('analysis', r['output'])}",
            "task_planner": lambda r: f"Task Plan: {r['output'].get('detailed_plan', r['output'])}",
            "multi_step_reasoner": lambda r: f"Reasoning Steps: {r['output'].get('reasoning_steps', r['output'])}",
            "learning_tracker": lambda r: f"Learning Insights: {r['output'].get('insights', r['output'])}",
            "content_personalizer": lambda r: f"Personalized Content: {r['output'].get('personalized_content', r['output'])}",
            "retrieve_information": lambda r: f"Retrieved Information: {r['output']}",
            "search_dataset": lambda r: f"Similar Examples Found: {len(r['output']) if isinstance(r['output'], list) else 'Multiple'} relevant entries",
            "summarize_content": lambda r: f"Summary: {r['output']}",
            "report_generator": lambda r: f"Report Generated: {r['output'].get('report_text', r['output'])}"
        }

        for tool_name, processor in context_processors.items():
            if tool_name in execution_results and execution_results[tool_name].get("status") == "success":
                try:
                    processed_context = processor(execution_results[tool_name])
                    context_info.append(processed_context)
                except Exception as e:
                    print(f"⚠️ Warning: Error processing context for {tool_name}: {e}")

        # Add user context if provided
        if context:
            context_info.append(f"Additional Context: {context}")

        # Create enhanced system prompt for Doubao
        system_prompt = f"""You are an advanced AI learning assistant for children. You have access to comprehensive context and analysis.

Context Information:
{chr(10).join(context_info)}

Instructions:
1. Provide a helpful, age-appropriate response tailored to the detected intent
2. Use the context analysis and reasoning to inform your response
3. Make your response engaging and educational when appropriate
4. If task plans or learning insights are available, incorporate them naturally
5. Maintain a warm, encouraging tone suitable for children
6. Ensure your response directly addresses the user's input

Response Guidelines:
- For daily_chat: Be conversational, friendly, and engaging
- For study_create_plan: Provide structured, actionable educational guidance
- For study_modify_plan: Offer flexible, understanding assistance with adjustments
- Always prioritize clarity and age-appropriateness"""

        full_prompt = f"{system_prompt}\n\nUser Input: {user_input}\n\nResponse:"

        # Generate response using Doubao with enhanced error handling
        try:
            response = self.call_doubao_api(full_prompt)

            # Validate response quality
            if len(response.strip()) < 10:
                print("⚠️ Warning: Generated response seems too short, using fallback")
                response = self._generate_fallback_response(user_input, intent_data)

        except Exception as e:
            print(f"❌ Error generating response: {e}")
            response = self._generate_fallback_response(user_input, intent_data)

        return response

    def _generate_fallback_response(self, user_input: str, intent_data: Dict) -> str:
        """Generate a fallback response when main response generation fails"""
        intent = intent_data.get('predicted_intention', 'daily_chat')

        fallback_responses = {
            'daily_chat': f"Hi there! I heard you say '{user_input}'. I'm here to help you learn and have fun! What would you like to explore today?",
            'study_create_plan': f"I'd love to help you create a study plan! You mentioned '{user_input}'. Let's work together to make a great learning schedule that's just right for you!",
            'study_modify_plan': f"I understand you want to make some changes to your study plan. You said '{user_input}'. I'm here to help you adjust things so they work better for you!"
        }

        return fallback_responses.get(intent, f"Thank you for your message: '{user_input}'. I'm here to help you learn and grow! How can I assist you today?")

    def _get_performance_summary(self) -> Dict[str, Any]:
        """Get current performance metrics summary"""
        return {
            "total_requests": self._performance_metrics["total_requests"],
            "cache_hit_rate": (self._performance_metrics["cache_hits"] / max(1, self._performance_metrics["total_requests"])) * 100,
            "average_response_time": round(self._performance_metrics["average_response_time"], 2),
            "successful_plans": self._performance_metrics["successful_plans"],
            "failed_plans": self._performance_metrics["failed_plans"],
            "success_rate": (self._performance_metrics["successful_plans"] / max(1, self._performance_metrics["successful_plans"] + self._performance_metrics["failed_plans"])) * 100
        }

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status for monitoring"""
        return {
            "status": "operational",
            "performance_metrics": self._get_performance_summary(),
            "cache_status": {
                "response_cache_size": len(self._response_cache),
                "plan_cache_size": len(self._plan_cache),
                "max_cache_size": self._max_cache_size
            },
            "components": {
                "doubao_client": "connected" if self.client else "disconnected",
                "embedding_client": "ready" if self.embedding_client else "not_ready",
                "dataset_manager": "ready" if self.dataset_manager else "not_ready",
                "intent_classifier": "ready" if self.intent_classifier else "not_ready"
            },
            "timestamp": datetime.now().isoformat()
        }

    def process_task(self, task: str, parameters: Dict[str, Any]) -> str:
        """Legacy method for backward compatibility with enhanced performance"""
        try:
            plan = self.call_openmanus(task, parameters).get("plan", [])
            results = self.execute_plan(plan)

            # Try to get response from various possible tools
            response_tools = ["response_generator", "content_personalizer", "task_planner", "multi_step_reasoner"]
            for tool in response_tools:
                if tool in results and results[tool].get("status") == "success":
                    output = results[tool]["output"]
                    if isinstance(output, dict):
                        # Handle complex outputs
                        return output.get("personalized_content") or output.get("detailed_plan") or output.get("reasoning_steps") or str(output)
                    return str(output)

            return "Task completed successfully."

        except Exception as e:
            print(f"❌ Error in process_task: {e}")
            return f"Error processing task: {str(e)}"

    def clear_caches(self):
        """Clear all caches to free memory - useful for main.py optimization"""
        self._response_cache.clear()
        self._plan_cache.clear()
        
    def fix_dimension_mismatch(self):
        """Fix dimension mismatch by clearing old 768-dimensional embeddings and regenerating with 384D"""


        # Clear embedding client cache
        if hasattr(self.embedding_client, 'clear_cache'):
            self.embedding_client.clear_cache()

        # Clear main database embeddings
        try:
            conn = sqlite3.connect(DATABASE_PATH)
            cursor = conn.cursor()
            cursor.execute('DELETE FROM intent_data WHERE embedding IS NOT NULL')
            cursor.execute('DELETE FROM dataset_entries WHERE embedding IS NOT NULL')
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"⚠️ Warning clearing main database: {e}")

        # Clear system caches
        self.clear_caches()

        # Regenerate embeddings with correct dimensions
        print("Generating embeddings with 384 dimensions...")
        self.dataset_manager.generate_embeddings_for_intent_data()

        print("✅ Dimension mismatch fixed - system ready with 384D embeddings")

    def get_tool_selection_metrics(self) -> Dict[str, Any]:
        """Get metrics to help LLM make better tool selection decisions"""
        return {
            "available_tools": list(self.tools.keys()),
            "tool_success_rates": self._get_tool_performance(),
            "recommended_sequences": self._get_optimal_tool_sequences(),
            "context_requirements": self._get_tool_context_requirements()
        }

    def _get_tool_performance(self) -> Dict[str, float]:
        """Get performance metrics for each tool to guide selection"""
        # This would be enhanced with actual usage statistics
        return {
            "context_analyzer": 0.95,
            "task_planner": 0.90,
            "multi_step_reasoner": 0.88,
            "content_personalizer": 0.92,
            "learning_tracker": 0.85,
            "dataset_search": 0.80,
            "retriever": 0.85,
            "summarizer": 0.90,
            "response_generator": 0.95,
            "intent_classifier": 0.93,
            "report_generator": 0.87,
            "embedding_generator": 0.98
        }

    def _get_optimal_tool_sequences(self) -> Dict[str, List[str]]:
        """Define optimal tool sequences for different task types"""
        return {
            "conversational_response": [
                "context_analyzer",
                "content_personalizer",
                "learning_tracker"
            ],
            "research_and_plan": [
                "context_analyzer",
                "multi_step_reasoner",
                "task_planner",
                "content_personalizer",
                "learning_tracker"
            ],
            "content_analysis": [
                "context_analyzer",
                "summarizer",
                "content_personalizer"
            ],
            "educational_assessment": [
                "learning_tracker",
                "multi_step_reasoner",
                "report_generator"
            ]
        }

    def _get_tool_context_requirements(self) -> Dict[str, List[str]]:
        """Define what context each tool needs to function optimally"""
        return {
            "context_analyzer": ["query", "intent"],
            "task_planner": ["objective", "context"],
            "multi_step_reasoner": ["query", "context"],
            "content_personalizer": ["content", "user_context"],
            "learning_tracker": ["tracking_data"],
            "dataset_search": ["query"],
            "retriever": ["query"],
            "summarizer": ["content"],
            "response_generator": ["context", "intent"],
            "intent_classifier": ["text"],
            "report_generator": ["data", "format"],
            "embedding_generator": ["texts"]
        }

    def optimize_tool_selection(self, task_type: str, context: Dict[str, Any]) -> List[str]:
        """Intelligently select and order tools based on task type and context"""
        base_sequences = self._get_optimal_tool_sequences()

        if task_type not in base_sequences:
            # Default sequence for unknown task types
            return ["context_analyzer", "response_generator"]

        sequence = base_sequences[task_type].copy()

        # Optimize based on context
        if context.get('user_context'):
            if "content_personalizer" not in sequence:
                sequence.insert(-1, "content_personalizer")

        if context.get('requires_data_search'):
            if "dataset_search" not in sequence:
                sequence.insert(1, "dataset_search")

        if context.get('needs_summary'):
            if "summarizer" not in sequence:
                sequence.insert(-1, "summarizer")

        return sequence

    def get_performance_insights(self) -> Dict[str, Any]:
        """Get actionable performance insights for system optimization"""
        metrics = self._get_performance_summary()

        insights = {
            "bottlenecks": [],
            "optimization_suggestions": [],
            "cache_efficiency": metrics['cache_hit_rate'],
            "response_quality": "good" if metrics['success_rate'] > 90 else "needs_improvement"
        }

        # Identify bottlenecks
        if metrics['average_response_time'] > 30:
            insights["bottlenecks"].append("slow_response_time")
            insights["optimization_suggestions"].append("increase_cache_size")

        if metrics['cache_hit_rate'] < 20:
            insights["bottlenecks"].append("low_cache_efficiency")
            insights["optimization_suggestions"].append("optimize_caching_strategy")

        if metrics['success_rate'] < 95:
            insights["bottlenecks"].append("high_failure_rate")
            insights["optimization_suggestions"].append("improve_error_handling")

        return insights

    def optimize_for_production(self):
        """Optimize settings for production use with main.py"""
        # Reduce cache sizes for memory efficiency
        self._max_cache_size = 50

        # Clear old caches if they're too large
        if len(self._response_cache) > self._max_cache_size:
            # Keep only the most recent entries
            items = list(self._response_cache.items())
            self._response_cache = dict(items[-self._max_cache_size:])

        if len(self._plan_cache) > self._max_cache_size:
            items = list(self._plan_cache.items())
            self._plan_cache = dict(items[-self._max_cache_size:])

        print("⚡ Optimized for production use")

    def validate_system_readiness(self) -> Dict[str, bool]:
        """Validate that all system components are ready for production use"""
        readiness = {
            "intent_classifier": False,
            "embedding_client": False,
            "doubao_client": False,
            "dataset_manager": False
        }

        try:
            # Check intent classifier
            if hasattr(self, 'intent_classifier') and self.intent_classifier:
                readiness["intent_classifier"] = True

            # Check embedding client
            if hasattr(self, 'embedding_client') and self.embedding_client:
                readiness["embedding_client"] = True

            # Check Doubao client
            if hasattr(self, 'client') and self.client:
                readiness["doubao_client"] = True

            # Check dataset manager
            if hasattr(self, 'dataset_manager') and self.dataset_manager:
                readiness["dataset_manager"] = True

        except Exception as e:
            print(f"⚠️ System readiness check failed: {e}")

        return readiness

# ===== Initialization and Setup Functions =====
def initialize_system(optimize_for_production: bool = True, fix_dimensions: bool = True) -> OpenManusPlanner:
    """Initialize the OpenManus system with core functionality focused on performance"""

    try:
        # Initialize core components
        planner = OpenManusPlanner()

        # Fix dimension mismatch if requested
        if fix_dimensions:
            planner.fix_dimension_mismatch()

        # Optimize for production if requested
        if optimize_for_production:
            planner.optimize_for_production()

        # Setup intent classification data
        planner.dataset_manager.generate_embeddings_for_intent_data()

        # Validate system readiness
        readiness = planner.validate_system_readiness()
        ready_components = sum(readiness.values())

        if ready_components >= 3:  # At least 3/4 components ready
            print(f"✅ OpenManus system ready ({ready_components}/4 components)")
        else:
            print(f"⚠️ OpenManus system partially ready ({ready_components}/4 components)")

        return planner

    except Exception as e:
        print(f"❌ Error initializing OpenManus system: {e}")
        # Return minimal system for basic functionality
        return OpenManusPlanner()

# ===== Main Execution =====
if __name__ == "__main__":
    # Initialize system for production use
    planner = initialize_system()

    # Simple CLI for testing core functionality
    print("OpenManus System Ready. Type 'quit' to exit.")

    while True:
        user_input = input("\nQuery: ").strip()

        if user_input.lower() in ('exit', 'quit', 'q'):
            break

        if user_input:
            try:
                result = planner.process_user_input(user_input)
                print(f"\nResponse: {result['final_response']}")
                print(f"Intent: {result['intent']['predicted_intention']} ({result['intent']['confidence']:.2f})")
                print(f"Processing Time: {result.get('processing_duration', 0):.2f}s")
            except Exception as e:
                print(f"Error: {e}")

# ===== System Initialization =====
def initialize_system(auto_start_docker: bool = False) -> OpenManusPlanner:
    """
    Initialize the OpenManus system and return a configured planner instance.

    Args:
        auto_start_docker: Whether to automatically start Docker services (not used in this implementation)

    Returns:
        OpenManusPlanner: Configured planner instance ready for use
    """
    print("🚀 Initializing OpenManus System...")

    try:
        # Create the main planner instance
        planner = OpenManusPlanner()

        # Initialize dataset manager and generate embeddings if needed
        print("📊 Setting up dataset manager...")
        dataset_manager = planner.dataset_manager

        # Check if intent data exists and generate embeddings
        if INTENT_DATA_PATH.exists():
            print("🧠 Loading intent classification data...")
            dataset_manager.generate_embeddings_for_intent_data()
        else:
            print("⚠️ Intent classification data not found. Some features may be limited.")

        # Test embedding system
        print("🔍 Testing embedding system...")
        test_embedding = planner.embedding_client.get_single_embedding("Hello world")
        if test_embedding and len(test_embedding) == EMBEDDING_DIM:
            print(f"✅ Embedding system working (dimension: {len(test_embedding)})")
        else:
            print("⚠️ Embedding system may have issues")

        # Test Doubao API connection
        print("🤖 Testing Doubao API connection...")
        test_response = planner.client.chat("Hello, this is a test message.")
        if "error" not in test_response:
            print("✅ Doubao API connection successful")
        else:
            print(f"⚠️ Doubao API test failed: {test_response.get('error', 'Unknown error')}")

        print("✅ OpenManus system initialized successfully!")
        return planner

    except Exception as e:
        print(f"❌ Failed to initialize OpenManus system: {e}")
        raise e

def get_system_status() -> Dict[str, Any]:
    """Get the current status of the OpenManus system"""
    try:
        # Check if data files exist
        intent_data_exists = INTENT_DATA_PATH.exists()
        database_exists = DATABASE_PATH.exists()
        cache_dir_exists = CACHE_DIR.exists()

        # Test embedding client
        embedding_client = LocalEmbeddingClient()
        embedding_healthy = embedding_client.check_health()

        # Test Doubao client
        doubao_client = DoubaoClient()
        doubao_test = doubao_client.chat("test")
        doubao_healthy = "error" not in doubao_test

        return {
            "status": "healthy" if (embedding_healthy and doubao_healthy) else "degraded",
            "components": {
                "embedding_system": {
                    "status": "healthy" if embedding_healthy else "error",
                    "model": JINA_EMBEDDING_MODEL,
                    "dimension": EMBEDDING_DIM
                },
                "doubao_api": {
                    "status": "healthy" if doubao_healthy else "error",
                    "model": DOUBAO_MODEL_NAME,
                    "base_url": DOUBAO_BASE_URL
                },
                "data_files": {
                    "intent_data": intent_data_exists,
                    "database": database_exists,
                    "cache_dir": cache_dir_exists
                }
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }