#!/usr/bin/env python3
# 新目录结构API测试脚本
import sys
from pathlib import Path
import requests
import json
from datetime import datetime
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# API基础URL - 注意新的路径结构
BASE_URL = "http://localhost:8002/api/v1/user-management"

def test_api_health():
    """测试API健康状态"""
    print("=== 测试新目录结构API健康状态 ===")
    try:
        # 测试根路径
        response = requests.get("http://localhost:8002/docs")
        if response.status_code == 200:
            print("✓ API文档页面正常访问")
            return True
        else:
            print(f"✗ API文档页面异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到API服务，请确保服务已启动")
        print("启动命令: uvicorn main:app --reload --host 0.0.0.0 --port 8002")
        return False


def test_new_structure_parent_crud():
    """测试新结构下的家长CRUD操作"""
    print("\n=== 测试新结构家长CRUD操作 ===")
    
    # 生成随机ID避免冲突
    random_id = random.randint(10000, 99999)
    
    # 1. 创建家长
    print("\n1. 创建家长...")
    parent_data = {
        "name": "新结构测试家长",
        "gender": "female",
        "age": 32,
        "phone": f"139{random_id % 100000000:08d}",
        "email": f"new_structure_parent_{random_id}@example.com",
        "wechat_id": f"new_wechat_{random_id}",
        "notes": "新目录结构测试家长"
    }
    
    response = requests.post(f"{BASE_URL}/parents/", json=parent_data)
    if response.status_code == 201:
        parent = response.json()
        parent_id = parent["id"]
        print(f"✓ 成功创建家长: {parent['name']} (ID: {parent_id})")
    else:
        print(f"✗ 创建家长失败: {response.status_code} - {response.text}")
        return False
    
    # 2. 获取家长信息
    print("\n2. 获取家长信息...")
    response = requests.get(f"{BASE_URL}/parents/{parent_id}")
    if response.status_code == 200:
        parent = response.json()
        print(f"✓ 成功获取家长信息: {parent['name']}")
    else:
        print(f"✗ 获取家长信息失败: {response.status_code}")
        return False
    
    # 3. 获取家长列表
    print("\n3. 获取家长列表...")
    response = requests.get(f"{BASE_URL}/parents/?limit=5")
    if response.status_code == 200:
        parents = response.json()
        print(f"✓ 成功获取家长列表: {len(parents)} 个家长")
    else:
        print(f"✗ 获取家长列表失败: {response.status_code}")
        return False
    
    return parent_id


def test_new_structure_child_crud():
    """测试新结构下的小孩CRUD操作"""
    print("\n=== 测试新结构小孩CRUD操作 ===")
    
    # 1. 创建小孩
    print("\n1. 创建小孩...")
    child_data = {
        "name": "新结构测试小孩",
        "nickname": "新小测",
        "gender": "male",
        "age": 7,
        "academic_level": "primary_1",
        "school_name": "新结构测试小学",
        "height": 120.0,
        "weight": 22.0,
        "overall_health_status": "excellent",
        "personality_traits": "聪明好学",
        "learning_style": "听觉学习者",
        "behavior_level": "excellent",
        "attention_span_minutes": 40,
        "favorite_subjects": "数学，英语",
        "notes": "新目录结构测试小孩"
    }
    
    response = requests.post(f"{BASE_URL}/children/", json=child_data)
    if response.status_code == 201:
        child = response.json()
        child_id = child["id"]
        print(f"✓ 成功创建小孩: {child['name']} (ID: {child_id})")
    else:
        print(f"✗ 创建小孩失败: {response.status_code} - {response.text}")
        return False
    
    # 2. 获取小孩信息
    print("\n2. 获取小孩信息...")
    response = requests.get(f"{BASE_URL}/children/{child_id}")
    if response.status_code == 200:
        child = response.json()
        print(f"✓ 成功获取小孩信息: {child['name']}")
    else:
        print(f"✗ 获取小孩信息失败: {response.status_code}")
        return False
    
    # 3. 获取小孩列表
    print("\n3. 获取小孩列表...")
    response = requests.get(f"{BASE_URL}/children/?limit=5")
    if response.status_code == 200:
        children = response.json()
        print(f"✓ 成功获取小孩列表: {len(children)} 个小孩")
    else:
        print(f"✗ 获取小孩列表失败: {response.status_code}")
        return False
    
    return child_id


def test_new_structure_relationship_management(parent_id, child_id):
    """测试新结构下的关系管理"""
    print("\n=== 测试新结构关系管理 ===")
    
    # 1. 创建家长-小孩关系
    print("\n1. 创建家长-小孩关系...")
    relationship_data = {
        "child_id": child_id,
        "parent_id": parent_id,
        "relationship_type": "mother",
        "is_primary_contact": True,
        "notes": "新结构母子关系"
    }
    
    response = requests.post(f"{BASE_URL}/relationships/", json=relationship_data)
    if response.status_code == 201:
        relationship = response.json()
        relationship_id = relationship["id"]
        print(f"✓ 成功创建关系: {relationship['relationship_type']} (ID: {relationship_id})")
    else:
        print(f"✗ 创建关系失败: {response.status_code} - {response.text}")
        return False
    
    # 2. 获取小孩的家长
    print("\n2. 获取小孩的家长...")
    response = requests.get(f"{BASE_URL}/relationships/child/{child_id}/parents")
    if response.status_code == 200:
        parents = response.json()
        print(f"✓ 小孩的家长数量: {len(parents)}")
        for parent in parents:
            print(f"   - {parent['name']} ({parent['gender']})")
    else:
        print(f"✗ 获取小孩家长失败: {response.status_code}")
        return False
    
    # 3. 获取家长的小孩
    print("\n3. 获取家长的小孩...")
    response = requests.get(f"{BASE_URL}/relationships/parent/{parent_id}/children")
    if response.status_code == 200:
        children = response.json()
        print(f"✓ 家长的小孩数量: {len(children)}")
        for child in children:
            print(f"   - {child['name']} ({child['age']}岁)")
    else:
        print(f"✗ 获取家长小孩失败: {response.status_code}")
        return False
    
    return relationship_id


def main():
    """主函数"""
    print("=== 新目录结构API测试 ===")
    print(f"API基础路径: {BASE_URL}")
    
    # 检查API服务状态
    if not test_api_health():
        return
    
    try:
        # 测试家长CRUD
        parent_id = test_new_structure_parent_crud()
        if not parent_id:
            print("\n❌ 家长CRUD测试失败")
            return
        
        # 测试小孩CRUD
        child_id = test_new_structure_child_crud()
        if not child_id:
            print("\n❌ 小孩CRUD测试失败")
            return
        
        # 测试关系管理
        relationship_id = test_new_structure_relationship_management(parent_id, child_id)
        if not relationship_id:
            print("\n❌ 关系管理测试失败")
            return
        
        print("\n🎉 新目录结构所有API测试通过！")
        print(f"\n📊 测试结果:")
        print(f"   - 创建的家长ID: {parent_id}")
        print(f"   - 创建的小孩ID: {child_id}")
        print(f"   - 创建的关系ID: {relationship_id}")
        
        print(f"\n📚 新API文档地址: http://localhost:8002/docs")
        print(f"📍 新API路径结构:")
        print(f"   - 家长管理: {BASE_URL}/parents/")
        print(f"   - 小孩管理: {BASE_URL}/children/")
        print(f"   - 关系管理: {BASE_URL}/relationships/")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
