#!/usr/bin/env python3
"""
豆包模型测试启动脚本

使用方法:
python run_doubao_test.py --child_id <儿童ID>
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 强制刷新输出
import os
os.environ['PYTHONUNBUFFERED'] = '1'

def main():
    """主函数"""
    print("=" * 80, flush=True)
    print("🤖 豆包模型Prompt测试工具", flush=True)
    print("=" * 80, flush=True)

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="豆包模型Prompt测试工具")
    parser.add_argument("--child_id", type=int, required=True, help="儿童ID")
    parser.add_argument("--days_back", type=int, default=7, help="回溯天数 (默认7天)")
    parser.add_argument("--no_save", action="store_true", help="不保存测试结果")

    try:
        args = parser.parse_args()
    except SystemExit:
        print("\n使用方法:", flush=True)
        print("  python run_doubao_test.py --child_id <儿童ID>", flush=True)
        print("\n示例:", flush=True)
        print("  python run_doubao_test.py --child_id 1", flush=True)
        print("  python run_doubao_test.py --child_id 1 --days_back 7 --no_save", flush=True)
        return

    # 检查依赖
    try:
        from service.doubao_service import get_doubao_service
        from core.prompt_generation.service import PromptGenerationService
        from core.prompt_generation.schemas import TaskPromptRequest
        import json
        print("✅ 豆包服务模块已加载", flush=True)
    except ImportError as e:
        print(f"❌ 缺少依赖项: {e}", flush=True)
        print("请确保项目依赖已正确安装", flush=True)
        return

    # 确保结果目录存在
    results_dir = Path("tests/results")
    results_dir.mkdir(parents=True, exist_ok=True)

    print(f"🚀 启动豆包模型测试 (儿童ID: {args.child_id}, 回溯天数: {args.days_back})...", flush=True)

    # 运行测试
    try:
        # 直接使用新的豆包服务API进行测试
        success = run_doubao_test_with_new_service(
            child_id=args.child_id,
            days_back=args.days_back,
            save_results=not args.no_save,
            results_dir=results_dir
        )

        if success:
            print("\n✅ 测试完成！", flush=True)
            print(f"📁 测试结果保存在: {results_dir.absolute()}", flush=True)
        else:
            print("\n❌ 测试失败！", flush=True)

    except Exception as e:
        print(f"❌ 运行测试失败: {e}", flush=True)
        import traceback
        traceback.print_exc()
        print("\n❌ 测试失败！", flush=True)
        print("请检查错误信息并重试", flush=True)


def run_doubao_test_with_new_service(child_id: int, days_back: int, save_results: bool, results_dir: Path) -> bool:
    """使用新的豆包服务API运行测试"""
    try:
        from service.doubao_service import get_doubao_service
        from core.prompt_generation.service import PromptGenerationService
        from core.prompt_generation.schemas import TaskPromptRequest
        import json
        from datetime import datetime

        print("=" * 80, flush=True)
        print("🧪 豆包模型Prompt测试开始", flush=True)
        print("=" * 80, flush=True)

        # 1. 初始化服务
        print("🔧 初始化服务...", flush=True)
        doubao_service = get_doubao_service()
        prompt_service = PromptGenerationService()
        print("✅ 服务初始化成功", flush=True)

        # 2. 生成prompt
        print(f"\n📋 正在生成儿童ID {child_id} 的任务计划表prompt...", flush=True)

        request = TaskPromptRequest(
            child_id=child_id,
            days_back=days_back,
            include_yesterday_tasks=True,
            template_type="task_prompt"
        )

        prompt_result = prompt_service.generate_task_prompt(request)

        if not prompt_result or not prompt_result.final_prompt:
            print("❌ Prompt生成失败: 未找到相关数据", flush=True)
            return False

        prompt = prompt_result.final_prompt
        print(f"✅ Prompt生成成功!", flush=True)
        print(f"📊 Prompt长度: {len(prompt)} 字符", flush=True)

        # 3. 调用豆包模型
        print("\n" + "=" * 50, flush=True)
        print("🤖 调用豆包模型API", flush=True)
        print("=" * 50, flush=True)

        print(f"🚀 正在调用豆包模型API...", flush=True)
        print(f"📡 URL: {doubao_service.base_url}/chat/completions", flush=True)
        print(f"🤖 模型: {doubao_service.model_name}", flush=True)
        print(f"📝 Prompt长度: {len(prompt)} 字符", flush=True)

        api_result = doubao_service.simple_chat(prompt)

        # 4. 展示结果
        print("\n" + "=" * 50, flush=True)
        print("📊 测试结果", flush=True)
        print("=" * 50, flush=True)

        if api_result.get("success"):
            print("✅ 豆包模型调用成功!", flush=True)

            # 显示使用情况
            if api_result.get("usage"):
                usage = api_result["usage"]
                print(f"� Token使用情况:", flush=True)
                print(f"   - 输入Token: {usage.get('prompt_tokens', 'N/A')}", flush=True)
                print(f"   - 输出Token: {usage.get('completion_tokens', 'N/A')}", flush=True)
                print(f"   - 总Token: {usage.get('total_tokens', 'N/A')}", flush=True)

            # 显示生成的任务计划
            response_text = api_result.get("response_text", "")
            print(f"\n🎯 生成的任务计划表:", flush=True)
            print("-" * 50, flush=True)
            print(response_text, flush=True)
            print("-" * 50, flush=True)

            # 尝试解析JSON格式
            analyze_response_format(response_text)

        else:
            print("❌ 豆包模型调用失败!", flush=True)
            print(f"错误信息: {api_result.get('error', '未知错误')}", flush=True)
            return False

        # 5. 保存结果
        if save_results:
            save_test_results(child_id, prompt, api_result, results_dir)

        print("\n" + "=" * 80, flush=True)
        print("🧪 测试完成", flush=True)
        print("=" * 80, flush=True)

        return True

    except Exception as e:
        print(f"❌ 测试执行异常: {str(e)}", flush=True)
        import traceback
        traceback.print_exc()
        return False


def analyze_response_format(response_text: str):
    """分析响应格式"""
    print(f"\n🔍 响应格式分析:", flush=True)

    # 检查是否包含JSON格式
    if "```json" in response_text.lower() or "[" in response_text:
        print("✅ 响应包含JSON格式内容", flush=True)

        # 尝试提取和解析JSON
        try:
            import json
            # 简单的JSON提取逻辑
            start_idx = response_text.find("[")
            end_idx = response_text.rfind("]") + 1

            if start_idx != -1 and end_idx > start_idx:
                json_str = response_text[start_idx:end_idx]
                parsed_json = json.loads(json_str)
                print(f"✅ JSON解析成功，包含 {len(parsed_json)} 个任务", flush=True)

                # 检查必要字段
                required_fields = ["task_name", "time_slot", "subject"]
                for i, task in enumerate(parsed_json):
                    missing_fields = [field for field in required_fields if field not in task]
                    if missing_fields:
                        print(f"⚠️  任务 {i+1} 缺少字段: {missing_fields}", flush=True)
                    else:
                        print(f"✅ 任务 {i+1} 格式正确: {task.get('task_name', 'N/A')}", flush=True)

        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}", flush=True)
    else:
        print("⚠️  响应不包含JSON格式，可能需要调整prompt", flush=True)


def save_test_results(child_id: int, prompt: str, api_result: dict, results_dir: Path):
    """保存测试结果到文件"""
    try:
        import json
        from datetime import datetime

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"doubao_test_results_{child_id}_{timestamp}.json"
        filepath = results_dir / filename

        # 准备保存的数据
        test_data = {
            "test_info": {
                "child_id": child_id,
                "timestamp": timestamp,
                "model_name": api_result.get("model", "unknown"),
                "test_type": "new_service_api"
            },
            "prompt": prompt,
            "api_result": api_result
        }

        # 保存到文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)

        print(f"💾 测试结果已保存到: {filepath}", flush=True)

    except Exception as e:
        print(f"⚠️  保存测试结果失败: {e}", flush=True)





if __name__ == "__main__":
    main()
