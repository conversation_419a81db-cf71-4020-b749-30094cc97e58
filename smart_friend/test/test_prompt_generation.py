#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prompt生成功能测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import json
from datetime import datetime, timezone, timedelta

from core.prompt_generation.service import PromptGenerationService
from core.prompt_generation.schemas import PromptGenerationRequest


async def test_prompt_generation():
    """测试prompt生成功能"""
    print("=" * 60)
    print("测试Prompt生成功能")
    print("=" * 60)
    
    # 创建服务实例
    service = PromptGenerationService()
    
    # 测试用例1: 基本prompt生成
    print("\n1. 测试基本prompt生成")
    print("-" * 40)
    
    request = PromptGenerationRequest(
        child_id=1,  # 假设存在ID为1的儿童
        days_back=7,
        include_today_homework=True,
        include_recent_completion=True
    )
    
    try:
        result = service.generate_prompt(request)
        
        if result:
            print("✓ Prompt生成成功")
            print(f"儿童姓名: {result.child_profile.name}")
            print(f"今日作业数量: {len(result.today_homework)}")
            print(f"近期记录数量: {len(result.recent_completion)}")
            print(f"生成时间: {result.generated_at}")
            
            print("\n生成的Prompt:")
            print("-" * 30)
            print(result.structured_prompt[:500] + "..." if len(result.structured_prompt) > 500 else result.structured_prompt)
            
            print("\n数据摘要:")
            print("-" * 30)
            for key, value in result.data_summary.items():
                print(f"{key}: {value}")
                
        else:
            print("✗ Prompt生成失败 - 可能是儿童ID不存在")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    
    # 测试用例2: 带学科筛选的prompt生成
    print("\n\n2. 测试带学科筛选的prompt生成")
    print("-" * 40)
    
    request_with_filter = PromptGenerationRequest(
        child_id=1,
        days_back=5,
        include_today_homework=True,
        include_recent_completion=True,
        subject_filter="数学"
    )
    
    try:
        result = service.generate_prompt(request_with_filter)
        
        if result:
            print("✓ 带筛选的Prompt生成成功")
            print(f"筛选学科: 数学")
            print(f"今日数学作业数量: {len(result.today_homework)}")
            print(f"近期数学记录数量: {len(result.recent_completion)}")
        else:
            print("✗ 带筛选的Prompt生成失败")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    
    # 测试用例3: 测试各个组件
    print("\n\n3. 测试各个数据组件")
    print("-" * 40)
    
    try:
        # 测试获取儿童档案
        child_profile = service._get_child_profile(1)
        if child_profile:
            print("✓ 儿童档案获取成功")
            print(f"  姓名: {child_profile.name}")
            print(f"  年龄: {child_profile.age}")
            print(f"  学习风格: {child_profile.learning_style}")
        else:
            print("✗ 儿童档案获取失败")
        
        # 测试获取今日作业
        today_homework = service._get_today_homework(1)
        print(f"✓ 今日作业获取成功，共{len(today_homework)}项")
        
        # 测试获取近期完成情况
        recent_completion = service._get_recent_completion(1, 7)
        print(f"✓ 近期完成情况获取成功，共{len(recent_completion)}条记录")
        
    except Exception as e:
        print(f"✗ 组件测试失败: {e}")
    
    # 测试用例4: 自定义模板
    print("\n\n4. 测试自定义模板")
    print("-" * 40)
    
    custom_template = """
学生信息：{child_name}（{child_nickname}），{child_age}岁
学习特点：{learning_style}
擅长科目：{good_at_subjects}
薄弱科目：{weak_at_subjects}
今日任务数量：{today_homework_count}
近期记录数量：{recent_records_count}
"""
    
    request_custom = PromptGenerationRequest(
        child_id=1,
        days_back=3,
        prompt_template=custom_template
    )
    
    try:
        result = service.generate_prompt(request_custom)
        
        if result:
            print("✓ 自定义模板Prompt生成成功")
            print("\n自定义模板结果:")
            print("-" * 30)
            print(result.structured_prompt)
        else:
            print("✗ 自定义模板Prompt生成失败")
            
    except Exception as e:
        print(f"✗ 自定义模板测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


def test_data_preview():
    """测试数据预览功能"""
    print("\n\n" + "=" * 60)
    print("测试数据预览功能")
    print("=" * 60)
    
    service = PromptGenerationService()
    
    try:
        # 获取各部分数据
        child_profile = service._get_child_profile(1)
        today_homework = service._get_today_homework(1)
        recent_completion = service._get_recent_completion(1, 7)
        
        preview_data = {
            "child_profile": child_profile.model_dump() if child_profile else None,
            "today_homework": [h.model_dump() for h in today_homework],
            "recent_completion": [r.model_dump() for r in recent_completion],
            "data_counts": {
                "today_homework_count": len(today_homework),
                "recent_completion_count": len(recent_completion)
            }
        }
        
        print("✓ 数据预览生成成功")
        print(f"数据结构: {json.dumps(preview_data, ensure_ascii=False, indent=2)[:1000]}...")
        
    except Exception as e:
        print(f"✗ 数据预览失败: {e}")


if __name__ == "__main__":
    # 运行异步测试
    asyncio.run(test_prompt_generation())
    
    # 运行数据预览测试
    test_data_preview()
