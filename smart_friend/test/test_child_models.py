#!/usr/bin/env python3
# 小孩用户信息模型测试脚本
import sys
from pathlib import Path
from datetime import datetime
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent  # 向上一级到项目根目录
sys.path.insert(0, str(project_root))

from backend.services.child_service import ChildService
from schemas.child_schemas import (
    ParentCreate, ChildCreate, ChildParentRelationshipCreate,
    ChildAcademicRecordCreate,
    GenderEnum, RelationshipEnum, AcademicLevelEnum,
    BehaviorLevelEnum
)


def test_basic_operations():
    """测试基本的CRUD操作"""
    print("=== 开始测试小孩用户信息模型 ===")
    
    # 初始化服务
    service = ChildService()
    
    # 1. 创建家长
    print("\n1. 创建家长...")
    random_id = random.randint(1000, 9999)
    parent_data = ParentCreate(
        name="李四",
        gender=GenderEnum.MALE,
        age=36,
        phone=f"138001380{random_id % 100:02d}",
        email=f"lisi{random_id}@example.com",
        occupation="产品经理",
        education_level="硕士",
        home_address="北京市海淀区某某小区",
        notes="测试家长数据"
    )
    
    parent = service.create_parent(parent_data)
    if parent:
        print(f"✓ 成功创建家长: {parent.name} (ID: {parent.id})")
    else:
        print("✗ 创建家长失败")
        return False
    
    # 2. 创建小孩
    print("\n2. 创建小孩...")
    child_data = ChildCreate(
        name="李小华",
        nickname="小华",
        gender=GenderEnum.FEMALE,
        age=7,
        academic_level=AcademicLevelEnum.PRIMARY_1,
        school_name="北京市某某小学",
        class_name="一年级二班",
        height=120.0,
        weight=22.0,
        overall_health_status="good",
        personality_traits="文静内向，喜欢阅读",
        learning_style="听觉学习者",
        behavior_level=BehaviorLevelEnum.EXCELLENT,
        attention_span_minutes=25,
        favorite_subjects="语文，英语",
        notes="测试小孩数据"
    )
    
    child = service.create_child(child_data)
    if child:
        print(f"✓ 成功创建小孩: {child.name} (ID: {child.id})")
    else:
        print("✗ 创建小孩失败")
        return False
    
    # 3. 创建家长-小孩关系
    print("\n3. 创建家长-小孩关系...")
    relationship_data = ChildParentRelationshipCreate(
        child_id=child.id,
        parent_id=parent.id,
        relationship_type=RelationshipEnum.FATHER,
        is_primary_contact=True,
        notes="父子关系"
    )
    
    relationship = service.create_child_parent_relationship(relationship_data)
    if relationship:
        print(f"✓ 成功创建关系: {relationship.relationship_type} (ID: {relationship.id})")
    else:
        print("✗ 创建关系失败")
        return False
    
    # 4. 创建学业记录
    print("\n4. 创建学业记录...")
    academic_data = ChildAcademicRecordCreate(
        child_id=child.id,
        academic_year="2023-2024",
        semester="上学期",
        record_date=datetime.now(),
        record_type="期中考试",
        subject="数学",
        subject_category="主科",
        score=85.0,
        max_score=100.0,
        percentage=85.0,
        grade_level="B",
        class_rank=15,
        grade_rank=25,
        notes="期中考试成绩"
    )
    
    academic_record = service.create_academic_record(academic_data)
    if academic_record:
        print(f"✓ 成功创建学业记录: {academic_record.subject} (ID: {academic_record.id})")
    else:
        print("✗ 创建学业记录失败")
        return False

    # 5. 查询测试
    print("\n5. 查询测试...")

    # 查询小孩信息（不包含关系）
    child_info = service.get_child(child.id, include_relations=False)
    if child_info:
        print(f"✓ 查询小孩信息成功: {child_info.name}")

    # 查询小孩的学业记录
    academic_records = service.get_child_academic_records(child.id)
    print(f"✓ 小孩的学业记录数量: {len(academic_records)}")

    # 查询家长的小孩
    parent_children = service.get_parent_children(parent.id)
    print(f"✓ 家长的小孩数量: {len(parent_children)}")

    # 查询小孩的家长
    child_parents = service.get_child_parents(child.id)
    print(f"✓ 小孩的家长数量: {len(child_parents)}")
    
    print("\n=== 所有测试通过！ ===")
    return True


def test_enum_values():
    """测试枚举值"""
    print("\n=== 测试枚举值 ===")
    
    print("性别枚举:", [e.value for e in GenderEnum])
    print("关系枚举:", [e.value for e in RelationshipEnum])
    print("学业等级枚举:", [e.value for e in AcademicLevelEnum])
    print("行为等级枚举:", [e.value for e in BehaviorLevelEnum])


def main():
    """主函数"""
    try:
        # 测试枚举值
        test_enum_values()
        
        # 测试基本操作
        success = test_basic_operations()
        
        if success:
            print("\n🎉 所有测试都成功完成！")
        else:
            print("\n❌ 测试过程中出现错误")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
