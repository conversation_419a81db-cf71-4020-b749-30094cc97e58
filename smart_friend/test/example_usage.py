#!/usr/bin/env python3
# 小孩用户信息系统使用示例
import sys
from pathlib import Path
from datetime import datetime
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent  # 向上一级到项目根目录
sys.path.insert(0, str(project_root))

from backend.services.child_service import ChildService
from schemas.child_schemas import (
    ParentCreate, ChildCreate, ChildParentRelationshipCreate,
    ChildAcademicRecordCreate,
    GenderEnum, RelationshipEnum, AcademicLevelEnum, 
    BehaviorLevelEnum
)


def create_sample_family():
    """创建示例家庭数据"""
    print("=== 创建示例家庭数据 ===")

    service = ChildService()
    random_id = random.randint(10000, 99999)

    # 创建父亲
    father_data = ParentCreate(
        name="王建国",
        gender=GenderEnum.MALE,
        age=38,
        phone=f"138001{random_id % 10000:04d}",
        email=f"wangjianguo{random_id}@example.com",
        wechat_id=f"wjg_dad_{random_id}",
        notes="孩子的父亲，工程师"
    )
    
    father = service.create_parent(father_data)
    print(f"✓ 创建父亲: {father.name}")
    
    # 创建母亲
    mother_data = ParentCreate(
        name="李美丽",
        gender=GenderEnum.FEMALE,
        age=35,
        phone=f"138002{random_id % 10000:04d}",
        email=f"limeili{random_id}@example.com",
        wechat_id=f"lml_mom_{random_id}",
        notes="孩子的母亲，教师"
    )
    
    mother = service.create_parent(mother_data)
    print(f"✓ 创建母亲: {mother.name}")
    
    # 创建小孩
    child_data = ChildCreate(
        name="王小宝",
        nickname="小宝",
        gender=GenderEnum.MALE,
        age=9,
        academic_level=AcademicLevelEnum.PRIMARY_3,
        school_name="北京市实验小学",
        height=135.0,
        weight=30.0,
        overall_health_status="good",
        allergies="花粉过敏",
        personality_traits="活泼好动，喜欢探索新事物，有时候比较调皮",
        learning_style="动手实践型学习者",
        behavior_level=BehaviorLevelEnum.GOOD,
        attention_span_minutes=35,
        hobbies="踢足球，搭积木，看科普书籍，玩电子游戏",
        favorite_subjects="数学，科学，体育",
        disliked_subjects="语文写作，英语背诵",
        good_at_subjects="数学计算，科学实验，体育运动",
        weak_at_subjects="语文阅读理解，英语口语，美术绘画",
        notes="一个聪明活泼的孩子，理科思维较强，但文科需要加强"
    )
    
    child = service.create_child(child_data)
    print(f"✓ 创建小孩: {child.name}")
    
    # 创建父子关系
    father_relationship = ChildParentRelationshipCreate(
        child_id=child.id,
        parent_id=father.id,
        relationship_type=RelationshipEnum.FATHER,
        is_primary_contact=True,
        notes="父子关系，父亲为主要联系人"
    )
    
    service.create_child_parent_relationship(father_relationship)
    print(f"✓ 创建父子关系")
    
    # 创建母子关系
    mother_relationship = ChildParentRelationshipCreate(
        child_id=child.id,
        parent_id=mother.id,
        relationship_type=RelationshipEnum.MOTHER,
        is_primary_contact=False,
        notes="母子关系"
    )
    
    service.create_child_parent_relationship(mother_relationship)
    print(f"✓ 创建母子关系")
    
    # 创建数学成绩记录
    math_record = ChildAcademicRecordCreate(
        child_id=child.id,
        academic_year="2023-2024",
        semester="上学期",
        record_date=datetime.now(),
        record_type="期中考试",
        subject="数学",
        subject_category="主科",
        score=92.0,
        max_score=100.0,
        percentage=92.0,
        grade_level="A",
        class_rank=5,
        grade_rank=15,
        notes="数学成绩优秀，逻辑思维能力强"
    )
    
    service.create_academic_record(math_record)
    print(f"✓ 创建数学成绩记录")
    
    # 创建语文成绩记录
    chinese_record = ChildAcademicRecordCreate(
        child_id=child.id,
        academic_year="2023-2024",
        semester="上学期",
        record_date=datetime.now(),
        record_type="期中考试",
        subject="语文",
        subject_category="主科",
        score=78.0,
        max_score=100.0,
        percentage=78.0,
        grade_level="C",
        class_rank=25,
        grade_rank=45,
        notes="语文成绩一般，需要加强阅读理解和写作能力"
    )
    
    service.create_academic_record(chinese_record)
    print(f"✓ 创建语文成绩记录")
    
    return child.id, father.id, mother.id


def display_child_profile(child_id):
    """显示小孩的详细档案"""
    print(f"\n=== 小孩详细档案 (ID: {child_id}) ===")
    
    service = ChildService()
    
    # 获取小孩基本信息
    child = service.get_child(child_id)
    if not child:
        print("未找到小孩信息")
        return
    
    print(f"姓名: {child.name} ({child.nickname})")
    print(f"性别: {child.gender}")
    print(f"年龄: {child.age}岁")
    print(f"学业等级: {child.academic_level}")
    print(f"学校: {child.school_name}")
    print(f"身高: {child.height}cm, 体重: {child.weight}kg")
    print(f"健康状况: {child.overall_health_status}")
    print(f"过敏史: {child.allergies}")
    print(f"性格特点: {child.personality_traits}")
    print(f"学习风格: {child.learning_style}")
    print(f"行为等级: {child.behavior_level}")
    print(f"注意力持续时间: {child.attention_span_minutes}分钟")
    print(f"兴趣爱好: {child.hobbies}")
    print(f"喜欢的学科: {child.favorite_subjects}")
    print(f"不喜欢的学科: {child.disliked_subjects}")
    print(f"擅长的科目: {child.good_at_subjects}")
    print(f"不擅长的科目: {child.weak_at_subjects}")
    print(f"备注: {child.notes}")
    
    # 获取家长信息
    parents = service.get_child_parents(child_id)
    print(f"\n家长信息:")
    for parent in parents:
        print(f"  - {parent.name} ({parent.gender}, {parent.age}岁)")
        print(f"    联系方式: {parent.phone}, {parent.email}")
    
    # 获取学业记录
    academic_records = service.get_child_academic_records(child_id)
    print(f"\n学业记录:")
    for record in academic_records:
        print(f"  - {record.subject}: {record.score}/{record.max_score} ({record.grade_level})")
        print(f"    班级排名: {record.class_rank}, 年级排名: {record.grade_rank}")
        print(f"    备注: {record.notes}")


def main():
    """主函数"""
    try:
        # 创建示例家庭
        child_id, father_id, mother_id = create_sample_family()
        
        # 显示小孩详细档案
        display_child_profile(child_id)
        
        print(f"\n🎉 示例数据创建完成！")
        print(f"小孩ID: {child_id}")
        print(f"父亲ID: {father_id}")
        print(f"母亲ID: {mother_id}")
        
    except Exception as e:
        print(f"\n❌ 运行过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
