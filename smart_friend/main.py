#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Smart Friend - Standalone Main Entry Point

This is the standalone main entry point for the Smart Friend application.
It integrates OpenManus functionality directly and runs on localhost:8008.

Usage:
    python main.py

Server will start on: http://localhost:8008
API Documentation: http://localhost:8008/docs
Web Interface: http://localhost:8008/static/aiChild.html
"""

import os
import sys
import time
import asyncio
import threading
import uvicorn
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel, Field

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import OpenManus functionality
from openmanus import OpenManusPlanner

# Setup basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global OpenManus planner instance
openmanus_planner = None

# Request/Response Models
class VoiceInputRequest(BaseModel):
    """Voice input request model"""
    voice_text: str = Field(..., description="Voice recognition text content")
    child_id: Optional[int] = Field(None, description="Student ID")

class ChatRequest(BaseModel):
    """Chat request model"""
    message: str = Field(..., description="User message")
    child_id: Optional[int] = Field(None, description="Student ID")

class AgentResponse(BaseModel):
    """Agent response model"""
    success: bool = Field(..., description="Success status")
    message: str = Field(..., description="Response message")
    child_id: Optional[int] = Field(None, description="Student ID")
    timestamp: Optional[str] = Field(None, description="Timestamp")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional data")
    intent_info: Optional[Dict[str, Any]] = Field(None, description="Intent recognition info")
    summary: Optional[str] = Field(None, description="Generated summary")

def initialize_openmanus():
    """Initialize OpenManus framework"""
    global openmanus_planner

    logger.info("🧠 Initializing OpenManus AI framework...")

    try:
        # Initialize OpenManus system directly
        openmanus_planner = OpenManusPlanner()
        logger.info("✅ OpenManus framework initialized successfully")

        # Test basic functionality
        test_result = openmanus_planner.classify_user_intent("Hello, test message")
        logger.info(f"✅ OpenManus test successful: {test_result['output']['predicted_intention']}")

        return True

    except Exception as e:
        logger.error(f"❌ OpenManus initialization failed: {e}")
        logger.warning("⚠️ Some OpenManus features may be limited")
        return False

def get_openmanus_planner():
    """Get the global OpenManus planner instance"""
    global openmanus_planner
    if openmanus_planner is None:
        initialize_openmanus()
    return openmanus_planner

# Create the FastAPI application
app = FastAPI(
    title="Smart Friend - Standalone",
    description="AI Smart Learning Assistant with OpenManus Integration",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json"
)

# Setup CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Main API Endpoints
@app.post("/api/v1/smart-agent/voice-input", response_model=AgentResponse)
async def process_voice_input(request: VoiceInputRequest):
    """
    Process voice input and generate summary

    This is the main entry point that receives voice recognition text,
    processes it through OpenManus, and generates a summary.
    """
    try:
        logger.info(f"接收到语音输入: {request.voice_text[:50]}...")

        planner = get_openmanus_planner()
        if not planner:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="OpenManus not initialized"
            )

        # Process the voice input through OpenManus
        result = planner.process_user_input(request.voice_text)

        # Generate a summary of the interaction
        summary_data = {
            "user_input": request.voice_text,
            "intent": result.get("intent", {}),
            "response": result.get("final_response", ""),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        # Create summary using OpenManus
        summary_result = planner.generate_simple_report(summary_data)
        summary_text = summary_result.get("output", {}).get("report_text", "No summary available")

        return AgentResponse(
            success=True,
            message=result.get("final_response", "Response generated successfully"),
            child_id=request.child_id,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            data=result.get("metadata", {}),
            intent_info=result.get("intent", {}),
            summary=summary_text
        )

    except Exception as e:
        logger.error(f"处理语音输入异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理语音输入失败: {str(e)}"
        )

@app.post("/api/v1/chat", response_model=AgentResponse)
async def chat_endpoint(request: ChatRequest):
    """Chat endpoint using OpenManus planner"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="OpenManus not initialized"
            )

        # Use OpenManus to process the request
        result = planner.process_user_input(request.message)

        # Generate summary
        summary_data = {
            "user_input": request.message,
            "intent": result.get("intent", {}),
            "response": result.get("final_response", ""),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        summary_result = planner.generate_simple_report(summary_data)
        summary_text = summary_result.get("output", {}).get("report_text", "No summary available")

        return AgentResponse(
            success=True,
            message=result.get("final_response", "No response generated"),
            child_id=request.child_id,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            data=result.get("metadata", {}),
            intent_info=result.get("intent", {}),
            summary=summary_text
        )

    except Exception as e:
        logger.error(f"Error in chat: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chat failed: {str(e)}"
        )

@app.post("/api/v1/openmanus/classify-intent")
async def classify_intent(request: dict):
    """Classify user intent using OpenManus"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"error": "OpenManus not initialized"}

        text = request.get("text", "")
        if not text:
            return {"error": "No text provided"}

        result = planner.classify_user_intent(text)

        return {
            "success": True,
            "intent": result.get("output", {}),
            "status": result.get("status", "unknown")
        }

    except Exception as e:
        logger.error(f"Error in intent classification: {e}")
        return {"error": str(e)}

@app.post("/api/v1/openmanus/create-plan")
async def create_plan(request: dict):
    """Create a task plan using OpenManus"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"error": "OpenManus not initialized"}

        objective = request.get("objective", "")
        resources = request.get("resources", "")
        context = request.get("context", "")

        if not objective:
            return {"error": "No objective provided"}

        result = planner.create_task_plan(
            objective=objective,
            resources=resources,
            existing_context=context
        )

        return {
            "success": True,
            "plan": result.get("output", {}),
            "status": result.get("status", "unknown")
        }

    except Exception as e:
        logger.error(f"Error in plan creation: {e}")
        return {"error": str(e)}

@app.get("/api/v1/openmanus/status")
async def openmanus_status():
    """Get OpenManus system status"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"status": "not_initialized", "ready": False}

        # Get performance metrics if available
        metrics = getattr(planner, '_performance_metrics', {})

        return {
            "status": "ready",
            "ready": True,
            "metrics": metrics
        }

    except Exception as e:
        logger.error(f"Error in OpenManus status: {e}")
        return {"status": "error", "ready": False, "error": str(e)}

# Static file serving
try:
    app.mount("/static", StaticFiles(directory="templates"), name="static")
    app.mount("/templates", StaticFiles(directory="templates"), name="templates")
except Exception as e:
    logger.warning(f"Could not mount static files: {e}")

# Basic web endpoints
@app.get("/")
async def root():
    """Root path - return AI Child page"""
    try:
        return FileResponse("templates/aiChild.html")
    except Exception:
        return {"message": "Smart Friend API is running", "docs": "/docs"}

@app.get("/demo")
async def demo_page():
    """Demo page"""
    try:
        return FileResponse("static/doubao_demo.html")
    except Exception:
        return {"message": "Demo page not available", "docs": "/docs"}

@app.get("/aiChild")
async def ai_child_page():
    """Return AI Child page"""
    try:
        return FileResponse("templates/aiChild.html")
    except Exception:
        return {"message": "AI Child page not available", "docs": "/docs"}

@app.get("/health")
async def health_check():
    """Health check"""
    return {"status": "healthy", "service": "Smart Friend Standalone"}

def main(port):
    """Main function - for debugging and starting the application"""
    print("🚀 Starting Smart Friend Standalone FastAPI Application...")
    print(f"📋 Application Title: {app.title}")
    print(f"📋 Application Version: 1.0.0")
    print(f"📍 API Documentation: http://localhost:{port}/docs")
    print(f"📍 Frontend Page: http://localhost:{port}/static/aiChild.html")

    # Initialize OpenManus
    print("\n🧠 Initializing OpenManus...")
    openmanus_ready = initialize_openmanus()

    if openmanus_ready:
        print("✅ OpenManus initialized successfully")
    else:
        print("⚠️ OpenManus initialization failed - some features may be limited")

    print(f"\n🌐 Starting server...")
    print(f"📍 Access URL: http://localhost:{port}")
    print(f"📖 API Documentation: http://localhost:{port}/docs")
    print(f"🌐 Frontend Page: http://localhost:{port}/static/aiChild.html")
    print(f"🎤 Voice Input API: http://localhost:{port}/api/v1/smart-agent/voice-input")
    print(f"💬 Chat API: http://localhost:{port}/api/v1/chat")

    return app


def open_browser_after_startup(port):
    """Open browser after server startup - only open once"""
    import time
    import webbrowser
    import requests
    import os

    print(f"🔄 Waiting for server to start on port {port}...")

    # Wait for server to start - longer wait time to ensure it's fully ready
    max_attempts = 60  # Increased to 60 seconds
    server_ready = False

    for attempt in range(max_attempts):
        try:
            response = requests.get(f"http://localhost:{port}/health", timeout=3)
            if response.status_code == 200:
                server_ready = True
                print("✅ Server fully ready!")
                break
        except Exception:
            pass

        if attempt % 5 == 0:  # Print status every 5 seconds
            print(f"⏳ Waiting for server to start... ({attempt + 1}/{max_attempts})")
        time.sleep(1)

    if not server_ready:
        print(f"❌ Server startup timed out, please manually visit http://localhost:{port}")
        return

    # Extra wait to ensure all services are fully ready
    time.sleep(1)

    # Check if HTML file exists and open browser - only open once
    html_path = os.path.abspath(os.path.join('templates', 'aiChild.html'))
    if os.path.exists(html_path):
        try:
            print("🌐 Opening browser...")
            webbrowser.open_new_tab(f'http://localhost:{port}/static/aiChild.html')
            print("✅ Browser successfully opened and connected to application!")
        except Exception as e:
            print(f"⚠️ Unable to open browser: {e}")
            print(f"📖 Please manually visit: http://localhost:{port}/static/aiChild.html")
    else:
        print(f"⚠️ HTML file does not exist: {html_path}")
        print(f"📖 Please manually visit: http://localhost:{port}/docs")

if __name__ == "__main__":
    import uvicorn
    import threading

    # Use port 8008 as requested
    mainport = 8008

    print(f"🚀 Initializing Smart Friend Standalone Application...")

    # Call main function for initialization check
    app_instance = main(mainport)

    # Start a single background thread to handle browser opening - ensure only one
    print(f"🔄 Starting browser monitor thread...")
    browser_thread = threading.Thread(
        target=open_browser_after_startup,
        args=(mainport,),
        name="BrowserOpener"
    )
    browser_thread.daemon = True
    browser_thread.start()

    # Start server - this will block until server is closed
    print(f"🚀 Starting server on localhost:{mainport}...")
    print(f"📍 Application will run at http://localhost:{mainport}")
    print(f"🌐 Browser will automatically open: http://localhost:{mainport}/static/aiChild.html")

    try:
        uvicorn.run(app_instance, host="0.0.0.0", port=mainport)
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Server startup failed: {e}")