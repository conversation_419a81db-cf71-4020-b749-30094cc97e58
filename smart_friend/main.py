#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Smart Friend - Main Entry Point

This is the main entry point for the Smart Friend application.
By default, it runs with OpenManus enhancements enabled.

For different modes:
- Standard mode: python main_standard.py
- OpenManus mode: python main_openmanus.py (or python main.py)

Usage:
    python main.py

Server will start on: http://localhost:8003
API Documentation: http://localhost:8003/docs
Web Interface: http://localhost:8003/static/aiChild.html
"""

import os
import sys
import time
import asyncio
import threading
import uvicorn
from pathlib import Path

# Default to OpenManus mode
os.environ.setdefault("APP_MODE", "openmanus")
os.environ.setdefault("ENABLE_OPENMANUS", "true")

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Global OpenManus planner instance
openmanus_planner = None

try:
    from app_factory import create_app
    from config.app_config import get_app_config, is_openmanus_enabled
    from backend.utils.logging import setup_logger
    from service.socketio_service import init_socketio_service
    from api.v1.endpoints.asr_socketio_api import create_socketio_handlers, connect_asr_service

    # Initialize logging
    logger = setup_logger('main')

    # Set APP_MODE in environment if not set
    if "APP_MODE" not in os.environ:
        os.environ["APP_MODE"] = "openmanus"

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("⚠️ Falling back to legacy main.py structure...")
    # Continue with legacy structure below

# Global variables for ASR
asr_client = None
asr_connected = False
is_recording = False
last_speech_time = time.time()
silence_timeout = 1.5


def initialize_openmanus():
    """Initialize OpenManus framework if enabled"""
    global openmanus_planner

    if not is_openmanus_enabled():
        return True

    logger.info("🧠 Initializing OpenManus AI framework...")

    try:
        from openmanus import initialize_system

        # Initialize OpenManus system
        openmanus_planner = initialize_system(auto_start_docker=False)
        logger.info("✅ OpenManus framework initialized successfully")

        # Test basic functionality
        test_result = openmanus_planner.classify_user_intent("Hello, test message")
        logger.info(f"✅ OpenManus test successful: {test_result['output']['predicted_intention']}")

        return True

    except Exception as e:
        logger.error(f"❌ OpenManus initialization failed: {e}")
        logger.warning("⚠️ Some OpenManus features may be limited")
        return False

def get_openmanus_planner():
    """Get the global OpenManus planner instance"""
    global openmanus_planner
    if openmanus_planner is None:
        initialize_openmanus()
    return openmanus_planner

# Create the FastAPI application using app factory
app = create_app()

# Add OpenManus integration endpoints to the existing app
@app.post("/api/v1/openmanus/chat")
async def openmanus_chat(request: dict):
    """Chat endpoint using OpenManus planner"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"error": "OpenManus not initialized"}

        user_input = request.get("message", "")
        if not user_input:
            return {"error": "No message provided"}

        # Use OpenManus to process the request
        result = planner.process_user_input(user_input)

        return {
            "success": True,
            "response": result.get("final_response", "No response generated"),
            "intent": result.get("intent", {}),
            "metadata": result.get("metadata", {})
        }

    except Exception as e:
        logger.error(f"Error in OpenManus chat: {e}")
        return {"error": str(e)}

@app.post("/api/v1/openmanus/classify-intent")
async def classify_intent(request: dict):
    """Classify user intent using OpenManus"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"error": "OpenManus not initialized"}

        text = request.get("text", "")
        if not text:
            return {"error": "No text provided"}

        result = planner.classify_user_intent(text)

        return {
            "success": True,
            "intent": result.get("output", {}),
            "status": result.get("status", "unknown")
        }

    except Exception as e:
        logger.error(f"Error in intent classification: {e}")
        return {"error": str(e)}

@app.post("/api/v1/openmanus/create-plan")
async def create_plan(request: dict):
    """Create a task plan using OpenManus"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"error": "OpenManus not initialized"}

        objective = request.get("objective", "")
        resources = request.get("resources", "")
        context = request.get("context", "")

        if not objective:
            return {"error": "No objective provided"}

        result = planner.create_task_plan(
            objective=objective,
            resources=resources,
            existing_context=context
        )

        return {
            "success": True,
            "plan": result.get("output", {}),
            "status": result.get("status", "unknown")
        }

    except Exception as e:
        logger.error(f"Error in plan creation: {e}")
        return {"error": str(e)}

@app.get("/api/v1/openmanus/status")
async def openmanus_status():
    """Get OpenManus system status"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"status": "not_initialized", "ready": False}

        # Get performance metrics if available
        metrics = getattr(planner, '_performance_metrics', {})

        return {
            "status": "ready",
            "ready": True,
            "metrics": metrics
        }

    except Exception as e:
        logger.error(f"Error getting OpenManus status: {e}")
        return {"status": "error", "ready": False, "error": str(e)}

def main(port):
    """主函数 - 用于调试和启动应用"""
    print("🚀 启动 Smart Friend FastAPI 应用...")
    print(f"📋 应用标题: {app.title}")
    print(f"📋 应用版本: 1.0.0")
    print(f"📍 API 文档地址: http://localhost:{port}/docs")
    print(f"📍 前端页面地址: http://localhost:{port}/static/aiChild.html")

    # Initialize OpenManus
    print("\n🧠 Initializing OpenManus...")
    openmanus_ready = initialize_openmanus()

    if openmanus_ready:
        print("✅ OpenManus initialized successfully")
    else:
        print("⚠️ OpenManus initialization failed - some features may be limited")

    # 初始化Socket.IO服务
    socketio_service = init_socketio_service()

    # 集成ASR Socket.IO处理器
    create_socketio_handlers(socketio_service.sio)

    # 自动连接ASR服务（同步调用）
    import asyncio
    try:
        asr_result = asyncio.run(connect_asr_service())
        if asr_result["success"]:
            print(f"✅ ASR服务自动连接成功: {asr_result['message']}")
        else:
            print(f"❌ ASR服务自动连接失败: {asr_result['message']}")
    except Exception as e:
        print(f"❌ ASR服务连接出错: {e}")

    socket_app = socketio_service.get_asgi_app(app)
    print("🔌 Socket.IO服务已初始化（包含ASR功能）")

    print(f"\n🌐 启动服务器...")

    print(f"📍 访问地址: http://localhost:{port}")
    print(f"📖 API文档: http://localhost:{port}/docs")
    print(f"📖 API文档: http://**************:{port}/docs")
    print(f"🌐 前端页面: http://localhost:{port}/static/aiChild.html")

    import os
    import webbrowser
    # 构建 HTML 文件的绝对路径
    html_path = os.path.abspath(os.path.join('templates', 'aiChild.html'))
    # 检查文件是否存在
    if os.path.exists(html_path):
        # 在默认浏览器中打开 HTML 文件
        webbrowser.open_new_tab(f'http://localhost:{port}/static/aiChild.html')
    else:
        print(f"❌ 未找到 HTML 文件: {html_path}")

    return socket_app


if __name__ == "__main__":
    import uvicorn
    mainport=8003
    # 调用main函数进行初始化检查
    app_instance = main(mainport)

    # 启动服务器
    uvicorn.run(app_instance, host="0.0.0.0", port=mainport)