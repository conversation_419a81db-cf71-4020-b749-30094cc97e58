#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Smart Friend - Main Entry Point

This is the main entry point for the Smart Friend application.
By default, it runs with OpenManus enhancements enabled.

Usage:
    python main.py

Server will start on: http://localhost:8003
API Documentation: http://localhost:8003/docs
Web Interface: http://localhost:8003/static/aiChild.html
"""

import os
import sys
import time
import asyncio
import threading
import uvicorn
import webbrowser
from pathlib import Path
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

# Default to OpenManus mode
os.environ.setdefault("APP_MODE", "openmanus")
os.environ.setdefault("ENABLE_OPENMANUS", "true")

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Global OpenManus planner instance
openmanus_planner = None

def setup_logging():
    """Setup basic logging"""
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('main')

logger = setup_logging()

def initialize_openmanus():
    """Initialize OpenManus framework"""
    global openmanus_planner

    logger.info("🧠 Initializing OpenManus AI framework...")

    try:
        from openmanus import initialize_system

        # Initialize OpenManus system
        openmanus_planner = initialize_system(auto_start_docker=False)
        logger.info("✅ OpenManus framework initialized successfully")

        # Test basic functionality
        test_result = openmanus_planner.classify_user_intent("Hello, test message")
        logger.info(f"✅ OpenManus test successful: {test_result['output']['predicted_intention']}")

        return True

    except Exception as e:
        logger.error(f"❌ OpenManus initialization failed: {e}")
        logger.warning("⚠️ Some OpenManus features may be limited")
        return False

def get_openmanus_planner():
    """Get the global OpenManus planner instance"""
    global openmanus_planner
    if openmanus_planner is None:
        initialize_openmanus()
    return openmanus_planner

# Create FastAPI application
app = FastAPI(
    title="Smart Friend AI Assistant",
    description="AI-powered educational assistant with OpenManus integration",
    version="1.0.0"
)

# API endpoints for OpenManus integration
@app.post("/api/v1/openmanus/chat")
async def openmanus_chat(request: dict):
    """Chat endpoint using OpenManus planner"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"error": "OpenManus not initialized"}

        user_input = request.get("message", "")
        if not user_input:
            return {"error": "No message provided"}

        # Use OpenManus to process the request
        result = planner.process_user_input(user_input)

        return {
            "success": True,
            "response": result.get("final_response", "No response generated"),
            "intent": result.get("intent", {}),
            "metadata": result.get("metadata", {})
        }

    except Exception as e:
        logger.error(f"Error in OpenManus chat: {e}")
        return {"error": str(e)}

@app.post("/api/v1/openmanus/classify-intent")
async def classify_intent(request: dict):
    """Classify user intent using OpenManus"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"error": "OpenManus not initialized"}

        text = request.get("text", "")
        if not text:
            return {"error": "No text provided"}

        result = planner.classify_user_intent(text)

        return {
            "success": True,
            "intent": result.get("output", {}),
            "status": result.get("status", "unknown")
        }

    except Exception as e:
        logger.error(f"Error in intent classification: {e}")
        return {"error": str(e)}

@app.post("/api/v1/openmanus/create-plan")
async def create_plan(request: dict):
    """Create a task plan using OpenManus"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"error": "OpenManus not initialized"}

        objective = request.get("objective", "")
        resources = request.get("resources", "")
        context = request.get("context", "")

        if not objective:
            return {"error": "No objective provided"}

        result = planner.create_task_plan(
            objective=objective,
            resources=resources,
            existing_context=context
        )

        return {
            "success": True,
            "plan": result.get("output", {}),
            "status": result.get("status", "unknown")
        }

    except Exception as e:
        logger.error(f"Error in plan creation: {e}")
        return {"error": str(e)}

@app.get("/api/v1/openmanus/status")
async def openmanus_status():
    """Get OpenManus system status"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"status": "not_initialized", "ready": False}

        # Get performance metrics if available
        metrics = getattr(planner, '_performance_metrics', {})

        return {
            "status": "ready",
            "ready": True,
            "metrics": metrics
        }

    except Exception as e:
        logger.error(f"Error getting OpenManus status: {e}")
        return {"status": "error", "ready": False, "error": str(e)}

# Static file serving
templates_dir = Path(__file__).parent / "templates"
if templates_dir.exists():
    app.mount("/static", StaticFiles(directory=str(templates_dir)), name="static")
    app.mount("/templates", StaticFiles(directory=str(templates_dir)), name="templates")

# Basic web endpoints
@app.get("/")
async def root():
    """Root endpoint - returns AI Child page if available"""
    templates_dir = Path(__file__).parent / "templates"
    aiChild_path = templates_dir / "aiChild.html"

    if aiChild_path.exists():
        return FileResponse(str(aiChild_path))
    else:
        return {"message": "Smart Friend AI Assistant", "status": "running", "docs": "/docs"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    planner = get_openmanus_planner()
    return {
        "status": "healthy",
        "openmanus_ready": planner is not None,
        "timestamp": time.time()
    }

def main(port: int = 8003):
    """Main function to initialize and start the application"""
    print("🚀 Starting Smart Friend FastAPI Application...")
    print(f"📋 Application: {app.title}")
    print(f"📋 Version: 1.0.0")
    print(f"📍 API Documentation: http://localhost:{port}/docs")
    print(f"📍 Frontend: http://localhost:{port}/static/aiChild.html")

    # Initialize OpenManus
    print("\n🧠 Initializing OpenManus...")
    openmanus_ready = initialize_openmanus()

    if openmanus_ready:
        print("✅ OpenManus initialized successfully")
    else:
        print("⚠️ OpenManus initialization failed - some features may be limited")

    print(f"\n🌐 Starting server on port {port}...")
    print(f"📍 Access URL: http://localhost:{port}")
    print(f"📖 API Documentation: http://localhost:{port}/docs")
    print(f"🌐 Frontend: http://localhost:{port}/static/aiChild.html")

    # Try to open browser if HTML file exists
    templates_dir = Path(__file__).parent / "templates"
    html_path = templates_dir / "aiChild.html"

    if html_path.exists():
        try:
            webbrowser.open_new_tab(f'http://localhost:{port}/static/aiChild.html')
            print("🌐 Browser opened automatically")
        except Exception as e:
            print(f"⚠️ Could not open browser: {e}")
    else:
        print(f"⚠️ HTML file not found: {html_path}")

    return app


if __name__ == "__main__":
    # Default port
    main_port = 8003

    # Initialize the application
    app_instance = main(main_port)

    # Start the server
    print(f"🚀 Starting uvicorn server on 0.0.0.0:{main_port}")
    uvicorn.run(app_instance, host="0.0.0.0", port=main_port)