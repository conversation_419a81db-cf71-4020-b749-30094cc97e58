#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Smart Friend - Main Entry Point

This is the main entry point for the Smart Friend application.
By default, it runs with OpenManus enhancements enabled.

For different modes:
- Standard mode: python main_standard.py
- OpenManus mode: python main_openmanus.py (or python main.py)

Usage:
    python main.py

Server will start on: http://localhost:8003
API Documentation: http://localhost:8003/docs
Web Interface: http://localhost:8003/static/aiChild.html
"""

import os
import sys
import time
import asyncio
import threading
import uvicorn
from pathlib import Path

# Default to OpenManus mode
os.environ["APP_MODE"] = "openmanus"
os.environ["ENABLE_OPENMANUS"] = "true"

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Global OpenManus planner instance
openmanus_planner = None

try:
    from app_factory import create_app
    from config.app_config import get_app_config, is_openmanus_enabled
    from backend.utils.logging import setup_logger
    from service.socketio_service import init_socketio_service
    from api.v1.endpoints.asr_socketio_api import create_socketio_handlers, connect_asr_service

    # Initialize logging
    logger = setup_logger('main')

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("⚠️ Falling back to legacy main.py structure...")
    # Continue with legacy structure below

# Global variables for ASR
asr_client = None
asr_connected = False
is_recording = False
last_speech_time = time.time()
silence_timeout = 1.5


def initialize_openmanus():
    """Initialize OpenManus framework if enabled"""
    global openmanus_planner

    if not is_openmanus_enabled():
        return True

    logger.info("🧠 Initializing OpenManus AI framework...")

    try:
        from openmanus import initialize_system

        # Initialize OpenManus system
        openmanus_planner = initialize_system(auto_start_docker=False)
        logger.info("✅ OpenManus framework initialized successfully")

        # Test basic functionality
        test_result = openmanus_planner.classify_user_intent("Hello, test message")
        logger.info(f"✅ OpenManus test successful: {test_result['output']['predicted_intention']}")

        return True

    except Exception as e:
        logger.error(f"❌ OpenManus initialization failed: {e}")
        logger.warning("⚠️ Some OpenManus features may be limited")
        return False

def get_openmanus_planner():
    """Get the global OpenManus planner instance"""
    global openmanus_planner
    if openmanus_planner is None:
        initialize_openmanus()
    return openmanus_planner

# Create the FastAPI application using app factory
app = create_app()

# Add OpenManus integration endpoints to the existing app
@app.post("/api/v1/openmanus/chat")
async def openmanus_chat(request: dict):
    """Chat endpoint using OpenManus planner"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"error": "OpenManus not initialized"}

        user_input = request.get("message", "")
        if not user_input:
            return {"error": "No message provided"}

        # Use OpenManus to process the request
        result = planner.process_user_input(user_input)

        return {
            "success": True,
            "response": result.get("final_response", "No response generated"),
            "intent": result.get("intent", {}),
            "metadata": result.get("metadata", {})
        }

    except Exception as e:
        logger.error(f"Error in OpenManus chat: {e}")
        return {"error": str(e)}

@app.post("/api/v1/openmanus/classify-intent")
async def classify_intent(request: dict):
    """Classify user intent using OpenManus"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"error": "OpenManus not initialized"}

        text = request.get("text", "")
        if not text:
            return {"error": "No text provided"}

        result = planner.classify_user_intent(text)

        return {
            "success": True,
            "intent": result.get("output", {}),
            "status": result.get("status", "unknown")
        }

    except Exception as e:
        logger.error(f"Error in intent classification: {e}")
        return {"error": str(e)}

@app.post("/api/v1/openmanus/create-plan")
async def create_plan(request: dict):
    """Create a task plan using OpenManus"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"error": "OpenManus not initialized"}

        objective = request.get("objective", "")
        resources = request.get("resources", "")
        context = request.get("context", "")

        if not objective:
            return {"error": "No objective provided"}

        result = planner.create_task_plan(
            objective=objective,
            resources=resources,
            existing_context=context
        )

        return {
            "success": True,
            "plan": result.get("output", {}),
            "status": result.get("status", "unknown")
        }

    except Exception as e:
        logger.error(f"Error in plan creation: {e}")
        return {"error": str(e)}

@app.get("/api/v1/openmanus/status")
async def openmanus_status():
    """Get OpenManus system status"""
    try:
        planner = get_openmanus_planner()
        if not planner:
            return {"status": "not_initialized", "ready": False}

        # Get performance metrics if available
        metrics = getattr(planner, '_performance_metrics', {})

        return {
            "status": "ready",
            "ready": True,
            "metrics": metrics
        }

    except Exception as e:
        logger.error(f"Error in OpenManus status: {e}")
        return {"status": "error", "ready": False, "error": str(e)}

# Note: All routers are already included via app_factory.py
# The app_factory handles all the router includes automatically

# Additional routes for specific functionality
try:
    # 任务计划API路由
    from api.v1.endpoints.task_plan_api import router as task_plan_router
    app.include_router(
        task_plan_router,
        tags=["task-plan"]
    )

    # 任务确认API路由
    from api.task_confirm_api import router as task_confirm_router
    app.include_router(
        task_confirm_router,
        prefix="/api",
        tags=["task-confirm"]
    )

    # 子任务操作API路由
    from api.subtask_api import router as subtask_router
    app.include_router(
        subtask_router,
        prefix="/api",
        tags=["subtask"]
    )

    # 任务计划管理API路由
    from backend.api.task_plans import router as task_plans_router
    app.include_router(
        task_plans_router,
        tags=["task-plans"]
    )

    # wxysmart-compatible Smart Agent API路由 (NEW)
    from api.v1.endpoints.smart_agent_api import router as smart_agent_router
    app.include_router(
        smart_agent_router,
        prefix="/api/v1",
        tags=["smart-agent"]
    )
    logger.info("✅ Smart Agent API (wxysmart-compatible) loaded successfully")

except ImportError as e:
    logger.warning(f"Some additional routers could not be imported: {e}")

# Additional endpoints (only add if not already defined in app_factory)
@app.get("/demo")
async def demo_page():
    """豆包API演示页面"""
    from fastapi.responses import FileResponse
    return FileResponse("static/doubao_demo.html")

@app.get("/aiChild")
async def ai_child_page():
    """返回AI Child页面"""
    from fastapi.responses import FileResponse
    return FileResponse("templates/aiChild.html")

def main(port):
    """主函数 - 用于调试和启动应用"""
    print("🚀 启动 Smart Friend FastAPI 应用...")
    print(f"📋 应用标题: {app.title}")
    print(f"📋 应用版本: 1.0.0")
    print(f"📍 API 文档地址: http://localhost:{port}/docs")
    print(f"📍 前端页面地址: http://localhost:{port}/static/aiChild.html")

    # Initialize OpenManus
    print("\n🧠 Initializing OpenManus...")
    openmanus_ready = initialize_openmanus()

    if openmanus_ready:
        print("✅ OpenManus initialized successfully")
    else:
        print("⚠️ OpenManus initialization failed - some features may be limited")

    # 初始化Socket.IO服务
    socketio_service = init_socketio_service()

    # 集成ASR Socket.IO处理器
    create_socketio_handlers(socketio_service.sio)

    # 自动连接ASR服务（同步调用）
    import asyncio
    try:
        asr_result = asyncio.run(connect_asr_service())
        if asr_result["success"]:
            print(f"✅ ASR服务自动连接成功: {asr_result['message']}")
        else:
            print(f"❌ ASR服务自动连接失败: {asr_result['message']}")
    except Exception as e:
        print(f"❌ ASR服务连接出错: {e}")

    socket_app = socketio_service.get_asgi_app(app)
    print("🔌 Socket.IO服务已初始化（包含ASR功能）")

    # # 打印所有注册的路由
    # print("\n📡 已注册的路由:")
    # route_count = 0
    # file_route_count = 0

    # for route in app.routes:
    #     if hasattr(route, 'path'):
    #         route_count += 1
    #         print(f"  - {route.path}")
    #         if '/files' in route.path:
    #             file_route_count += 1
    #             print(f"    ✅ 文件上传路由: {route.path}")

    # print(f"\n📊 路由统计:")
    # print(f"  - 总路由数: {route_count}")
    # print(f"  - 文件上传路由数: {file_route_count}")

    print(f"\n🌐 启动服务器...")

    print(f"📍 访问地址: http://localhost:{port}")
    print(f"📖 API文档: http://localhost:{port}/docs")
    print(f"📖 API文档: http://**************:{port}/docs")
    config = get_app_config()
    print(f"🔧 文件上传API: http://localhost:{port}{config.API_V1_STR}/files/")
    print(f"🌐 前端页面: http://localhost:{port}/static/aiChild.html")

    return socket_app


def open_browser_after_startup(port):
    """在服务器启动后打开浏览器 - 只打开一次"""
    import time
    import webbrowser
    import requests
    import os

    print(f"🔄 等待服务器在端口 {port} 启动...")

    # 等待服务器启动 - 更长的等待时间确保完全就绪
    max_attempts = 60  # 增加到60秒
    server_ready = False

    for attempt in range(max_attempts):
        try:
            response = requests.get(f"http://localhost:{port}/health", timeout=3)
            if response.status_code == 200:
                server_ready = True
                print("✅ 服务器完全就绪!")
                break
        except Exception:
            pass

        if attempt % 5 == 0:  # 每5秒打印一次状态
            print(f"⏳ 等待服务器启动... ({attempt + 1}/{max_attempts})")
        time.sleep(1)

    if not server_ready:
        print("❌ 服务器启动超时，请手动访问 http://localhost:8003")
        return

    # 额外等待1秒确保所有服务完全就绪
    time.sleep(1)

    # 检查HTML文件是否存在并打开浏览器 - 只打开一次
    html_path = os.path.abspath(os.path.join('templates', 'aiChild.html'))
    if os.path.exists(html_path):
        try:
            print("🌐 正在打开浏览器...")
            webbrowser.open_new_tab(f'http://localhost:{port}/static/aiChild.html')
            print("✅ 浏览器已成功打开并连接到应用!")
        except Exception as e:
            print(f"⚠️ 无法打开浏览器: {e}")
            print(f"📖 请手动访问: http://localhost:{port}/static/aiChild.html")
    else:
        print(f"⚠️ HTML文件不存在: {html_path}")
        print(f"📖 请手动访问: http://localhost:{port}/docs")

if __name__ == "__main__":
    import uvicorn
    import threading

    # 固定使用端口8003 - 团队协作端口
    mainport = 8003

    print(f"🚀 正在初始化 Smart Friend 应用...")

    # 调用main函数进行初始化检查
    app_instance = main(mainport)

    # 启动单个后台线程来处理浏览器打开 - 确保只有一个
    print(f"🔄 启动浏览器监控线程...")
    browser_thread = threading.Thread(
        target=open_browser_after_startup,
        args=(mainport,),
        name="BrowserOpener"
    )
    browser_thread.daemon = True
    browser_thread.start()

    # 启动服务器 - 这会阻塞直到服务器关闭
    print(f"🚀 启动服务器在 localhost:{mainport}...")
    print(f"📍 应用将在 http://localhost:{mainport} 运行")
    print(f"🌐 浏览器将自动打开: http://localhost:{mainport}/static/aiChild.html")

    try:
        uvicorn.run(app_instance, host="0.0.0.0", port=mainport)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")