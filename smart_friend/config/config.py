#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration settings for Smart Friend application
"""

import os
from pathlib import Path
from typing import Optional

class Settings:
    """Application settings"""
    
    # Project Information
    PROJECT_NAME: str = "Smart Friend - AI Learning Assistant"
    VERSION: str = "2.0.0"
    DESCRIPTION: str = "AI智能小孩学习助手API - 包含用户信息管理、学习数据分析和计划管理"
    
    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8010
    DEBUG: bool = True
    
    # Database Configuration
    DATABASE_URL: str = "sqlite:///./smart_friend.db"
    
    # OpenManus Configuration
    OPENMANUS_MODEL: str = "all-MiniLM-L6-v2"
    OPENMANUS_CACHE_DIR: str = "cache"
    
    # File Upload Configuration
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: set = {".jpg", ".jpeg", ".png", ".gif", ".pdf", ".txt", ".docx"}
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # API Keys (should be set via environment variables)
    DOUBAO_API_KEY: Optional[str] = os.getenv("DOUBAO_API_KEY")
    VOLCANO_ACCESS_KEY: Optional[str] = os.getenv("VOLCANO_ACCESS_KEY")
    VOLCANO_SECRET_KEY: Optional[str] = os.getenv("VOLCANO_SECRET_KEY")
    
    # CORS Configuration
    CORS_ORIGINS: list = ["*"]
    CORS_METHODS: list = ["*"]
    CORS_HEADERS: list = ["*"]
    
    # Cache Configuration
    CACHE_TTL: int = 3600  # 1 hour
    CACHE_MAX_SIZE: int = 1000
    
    # Performance Configuration
    MAX_WORKERS: int = 4
    REQUEST_TIMEOUT: int = 30
    
    # Security Configuration
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    
    def __init__(self):
        """Initialize settings"""
        # Create necessary directories
        Path(self.UPLOAD_DIR).mkdir(exist_ok=True)
        Path(self.OPENMANUS_CACHE_DIR).mkdir(exist_ok=True)
        Path("logs").mkdir(exist_ok=True)
        Path("temp_audio").mkdir(exist_ok=True)

# Global settings instance
settings = Settings()
