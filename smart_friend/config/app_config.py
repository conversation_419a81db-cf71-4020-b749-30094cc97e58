#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Application Configuration Management

This module provides configuration management for the Smart Friend application,
supporting both standard and OpenManus-enhanced modes.
"""

import os
from enum import Enum
from typing import Optional

try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings
    except ImportError:
        # Fallback for older pydantic versions
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)


class AppMode(str, Enum):
    """Application operation modes"""
    STANDARD = "standard"
    OPENMANUS = "openmanus"


class AppConfig:
    """Application configuration settings"""

    def __init__(self):
        # Application Mode
        app_mode_str = os.environ.get("APP_MODE", "openmanus").lower()
        if app_mode_str == "openmanus":
            self.APP_MODE = AppMode.OPENMANUS
        else:
            self.APP_MODE = AppMode.STANDARD

        # Server Configuration
        self.HOST = os.environ.get("HOST", "0.0.0.0")
        self.PORT = int(os.environ.get("PORT", "8003"))
        self.DEBUG = os.environ.get("DEBUG", "false").lower() in ('true', '1', 'yes')

        # API Configuration
        self.API_V1_STR = os.environ.get("API_V1_STR", "/api/v1")
        self.PROJECT_NAME = os.environ.get("PROJECT_NAME", "AI Child Learning Assistant")
        self.PROJECT_DESCRIPTION = os.environ.get("PROJECT_DESCRIPTION", "AI智能小孩学习助手API - 包含用户信息管理、学习数据分析和计划管理")
        self.VERSION = os.environ.get("VERSION", "1.0.0")

        # OpenManus Configuration (only used when APP_MODE = OPENMANUS)
        self.ENABLE_OPENMANUS = os.environ.get("ENABLE_OPENMANUS", "true").lower() in ('true', '1', 'yes')
        self.OPENMANUS_INTENT_CLASSIFICATION = os.environ.get("OPENMANUS_INTENT_CLASSIFICATION", "false").lower() in ('true', '1', 'yes')
        self.OPENMANUS_SEMANTIC_SEARCH = os.environ.get("OPENMANUS_SEMANTIC_SEARCH", "false").lower() in ('true', '1', 'yes')
        self.OPENMANUS_CONTEXT_AWARENESS = os.environ.get("OPENMANUS_CONTEXT_AWARENESS", "false").lower() in ('true', '1', 'yes')

        # Database Configuration
        self.DATABASE_URL = os.environ.get("DATABASE_URL", "sqlite:///./smart_friend.db")

        # External Services
        self.DOUBAO_API_KEY = os.environ.get("DOUBAO_API_KEY", "")
        self.DOUBAO_BASE_URL = os.environ.get("DOUBAO_BASE_URL", "")
        self.DOUBAO_MODEL_NAME = os.environ.get("DOUBAO_MODEL_NAME", "")

        # Logging Configuration
        self.LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")
        self.LOG_FORMAT = os.environ.get("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")


def get_app_config() -> AppConfig:
    """Get application configuration based on environment"""
    return AppConfig()


def is_openmanus_enabled() -> bool:
    """Check if OpenManus features are enabled"""
    config = get_app_config()
    return config.APP_MODE == AppMode.OPENMANUS and config.ENABLE_OPENMANUS


def get_app_description() -> str:
    """Get application description based on mode"""
    config = get_app_config()
    
    if config.APP_MODE == AppMode.OPENMANUS:
        return f"{config.PROJECT_DESCRIPTION} - Enhanced with OpenManus AI Framework"
    else:
        return config.PROJECT_DESCRIPTION


def get_app_tags() -> dict:
    """Get API tags based on application mode"""
    base_tags = [
        {"name": "user-management", "description": "用户信息管理"},
        {"name": "daily-learning", "description": "每日学习数据"},
        {"name": "planning", "description": "学习计划管理"},
        {"name": "doubao", "description": "豆包模型API"},
        {"name": "asr", "description": "语音识别"},
        {"name": "tts", "description": "文本转语音"},
        {"name": "voice-interaction", "description": "语音交互"},
        {"name": "daily-tasks", "description": "每日任务"},
        {"name": "multimodal", "description": "多模态输入"},
        {"name": "detection", "description": "检测服务"},
    ]
    
    if is_openmanus_enabled():
        base_tags.append({
            "name": "openmanus", 
            "description": "OpenManus AI Framework - 智能对话与意图分析"
        })
    
    return {"tags_metadata": base_tags}
