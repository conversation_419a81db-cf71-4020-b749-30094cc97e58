#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Application Configuration Management

This module provides configuration management for the Smart Friend application,
supporting both standard and OpenManus-enhanced modes.
"""

import os
from enum import Enum
from typing import Optional

try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings
    except ImportError:
        # Fallback for older pydantic versions
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)


class AppMode(str, Enum):
    """Application operation modes"""
    STANDARD = "standard"
    OPENMANUS = "openmanus"


class AppConfig(BaseSettings):
    """Application configuration settings"""
    
    # Application Mode
    APP_MODE: AppMode = AppMode.STANDARD
    
    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8003
    DEBUG: bool = False
    
    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "AI Child Learning Assistant"
    PROJECT_DESCRIPTION: str = "AI智能小孩学习助手API - 包含用户信息管理、学习数据分析和计划管理"
    VERSION: str = "1.0.0"
    
    # OpenManus Configuration (only used when APP_MODE = OPENMANUS)
    ENABLE_OPENMANUS: bool = False
    OPENMANUS_INTENT_CLASSIFICATION: bool = False
    OPENMANUS_SEMANTIC_SEARCH: bool = False
    OPENMANUS_CONTEXT_AWARENESS: bool = False
    
    # Database Configuration
    DATABASE_URL: str = "sqlite:///./smart_friend.db"
    
    # External Services
    DOUBAO_API_KEY: str = ""
    DOUBAO_BASE_URL: str = ""
    DOUBAO_MODEL_NAME: str = ""
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

    def __init__(self, **kwargs):
        # Initialize with environment variables
        for key in dir(self):
            if not key.startswith('_') and key.isupper():
                env_value = os.environ.get(key)
                if env_value is not None:
                    # Convert string values to appropriate types
                    attr_type = type(getattr(self, key))
                    if attr_type == bool:
                        setattr(self, key, env_value.lower() in ('true', '1', 'yes'))
                    elif attr_type == int:
                        setattr(self, key, int(env_value))
                    else:
                        setattr(self, key, env_value)

        # Apply any kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)


def get_app_config() -> AppConfig:
    """Get application configuration based on environment"""
    return AppConfig()


def is_openmanus_enabled() -> bool:
    """Check if OpenManus features are enabled"""
    config = get_app_config()
    return config.APP_MODE == AppMode.OPENMANUS and config.ENABLE_OPENMANUS


def get_app_description() -> str:
    """Get application description based on mode"""
    config = get_app_config()
    
    if config.APP_MODE == AppMode.OPENMANUS:
        return f"{config.PROJECT_DESCRIPTION} - Enhanced with OpenManus AI Framework"
    else:
        return config.PROJECT_DESCRIPTION


def get_app_tags() -> dict:
    """Get API tags based on application mode"""
    base_tags = [
        {"name": "user-management", "description": "用户信息管理"},
        {"name": "daily-learning", "description": "每日学习数据"},
        {"name": "planning", "description": "学习计划管理"},
        {"name": "doubao", "description": "豆包模型API"},
        {"name": "asr", "description": "语音识别"},
        {"name": "tts", "description": "文本转语音"},
        {"name": "voice-interaction", "description": "语音交互"},
        {"name": "daily-tasks", "description": "每日任务"},
        {"name": "multimodal", "description": "多模态输入"},
        {"name": "detection", "description": "检测服务"},
    ]
    
    if is_openmanus_enabled():
        base_tags.append({
            "name": "openmanus", 
            "description": "OpenManus AI Framework - 智能对话与意图分析"
        })
    
    return {"tags_metadata": base_tags}
