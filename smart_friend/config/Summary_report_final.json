{"summary_report_draft": {"1_child_profile_and_past_performance": {"learning_style": "", "strengths_and_learning_preferences": "", "previous_completion_rates_and_scores": "", "consistent_strength_areas": "", "consistent_difficulty_areas": ""}, "2_task_plan_and_completion": {"tasks_planned_and_completed": "", "completion_rate_percentage": "", "missed_or_delayed_tasks_with_reasons": "", "comparison_to_past_days": "", "ai_evaluation_score_and_confidence": "", "overall_productivity_or_mood_summary": ""}, "3_focus_and_behaviour_insights": {"most_focused_times_or_tasks": "", "distraction_times_and_causes": "", "focus_trend_comparison": "", "focus_improvement_tip": ""}, "4_quality_of_homework": {"highest_and_lowest_scoring_tasks": "", "confidence_and_coverage_levels": "", "improvement_in_content_quality": "", "common_errors": "", "helper_tip_for_difficult_tasks": ""}, "5_time_and_activity_usage": {"total_study_time_and_active_engagement": "", "number_of_distractions": "", "relevant_vs_irrelevant_apps": "", "fun_metaphor_or_productivity_comment": ""}, "6_homework_plan_adjustments": {"tasks_changed_or_skipped": "", "impact_of_changes_on_progress": "", "child_friendly_reason_for_change": ""}, "7_personalized_suggestions_from_past_performance": {"focus_energy_time_tips": "", "subjects_needing_attention": "", "methods_to_boost_task_quality": "", "parent_support_suggestions": "", "learning_goal_for_next_session": ""}, "8_encouragement_and_motivation": {"celebrated_efforts_or_improvements": "", "reinforced_good_habits": "", "positive_supportive_message": "", "gentle_encouragement_if_needed": ""}}}