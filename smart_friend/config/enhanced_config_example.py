"""
Enhanced OpenManus Configuration Example

Copy this file to config.py and update with your actual API keys and settings.
"""

import os
from pathlib import Path

# ===== API Configuration =====

# Doubao API Settings
DOUBAO_API_KEY = os.getenv("DOUBAO_API_KEY", "your_doubao_api_key_here")
DOUBAO_BASE_URL = os.getenv("DOUBAO_BASE_URL", "https://ark.cn-beijing.volces.com/api/v3")
DOUBAO_MODEL_NAME = os.getenv("DOUBAO_MODEL_NAME", "doubao-1-5-vision-pro-32k-250115")
DOUBAO_TEMPERATURE = float(os.getenv("DOUBAO_TEMPERATURE", "0.7"))
DOUBAO_TOP_P = float(os.getenv("DOUBAO_TOP_P", "0.9"))
DOUBAO_MAX_TOKENS = int(os.getenv("DOUBAO_MAX_TOKENS", "4000"))
DOUBAO_TIMEOUT = int(os.getenv("DOUBAO_TIMEOUT", "180"))

# Jina Embedding API Settings
JINA_API_KEY = os.getenv("JINA_API_KEY", "your_jina_api_key_here")
JINA_BASE_URL = "https://api.jina.ai/v1/embeddings"
JINA_MODEL = "jina-embeddings-v3"  # or "jina-embeddings-v2" for older version

# ===== System Configuration =====

# Mode Settings
USE_MOCK_MODE = False  # Set to True for testing without API calls
FALLBACK_TO_MOCK = True  # Fallback to mock if API fails
DEBUG_MODE = os.getenv("DEBUG", "False").lower() == "true"

# Data Paths
BASE_DIR = Path(__file__).parent.parent
DATA_DIR = BASE_DIR
INTENT_DATA_PATH = DATA_DIR / "Intent_classification_100_data.json"
SUMMARY_REPORT_PATH = DATA_DIR / "config" / "Summary_report_final.json"
DATABASE_PATH = DATA_DIR / "smart_friend_enhanced.db"

# ===== Database Configuration =====

# SQLite Settings
DB_TIMEOUT = 30  # seconds
DB_CHECK_SAME_THREAD = False

# ===== Embedding Configuration =====

# Embedding Dimensions (adjust based on model)
EMBEDDING_DIMENSIONS = {
    "jina-embeddings-v3": 768,
    "jina-embeddings-v2": 768,
    "jina-embeddings-v1": 512
}

# Similarity Thresholds
INTENT_CLASSIFICATION_THRESHOLD = 0.7
SEMANTIC_SEARCH_THRESHOLD = 0.5

# ===== Intent Classification Configuration =====

# Intent Classes Mapping
INTENT_CLASSES = {
    0: "daily_chat",
    1: "study_create_plan", 
    2: "study_modify_plan",
    3: "study_execute_task",
    4: "study_review_progress"
}

# Intent Class Descriptions
INTENT_DESCRIPTIONS = {
    "daily_chat": "General conversation and casual interactions",
    "study_create_plan": "Creating new study plans or schedules",
    "study_modify_plan": "Modifying existing study plans",
    "study_execute_task": "Executing specific study tasks",
    "study_review_progress": "Reviewing learning progress and performance"
}

# ===== Summary Report Configuration =====

# Report Sections
SUMMARY_REPORT_SECTIONS = [
    "child_profile_and_past_performance",
    "task_plan_and_completion", 
    "focus_and_behaviour_insights",
    "quality_of_homework",
    "time_and_activity_usage",
    "homework_plan_adjustments",
    "personalized_suggestions_from_past_performance",
    "encouragement_and_motivation"
]

# Report Generation Settings
MAX_REPORT_HISTORY = 100  # Maximum number of reports to keep
REPORT_RETENTION_DAYS = 90  # Days to keep reports

# ===== Search Configuration =====

# Default Search Parameters
DEFAULT_SEARCH_TOP_K = 5
MAX_SEARCH_RESULTS = 20
MIN_SIMILARITY_SCORE = 0.3

# ===== Performance Configuration =====

# Batch Processing
EMBEDDING_BATCH_SIZE = 50
MAX_CONCURRENT_REQUESTS = 5

# Caching
ENABLE_EMBEDDING_CACHE = True
CACHE_EXPIRY_HOURS = 24

# ===== Logging Configuration =====

# Log Levels
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Log Files
LOG_DIR = BASE_DIR / "logs"
LOG_FILE = LOG_DIR / "enhanced_openmanus.log"
ERROR_LOG_FILE = LOG_DIR / "errors.log"

# ===== API Rate Limiting =====

# Rate Limits (requests per minute)
DOUBAO_RATE_LIMIT = 60
JINA_RATE_LIMIT = 100

# Retry Configuration
MAX_RETRIES = 3
RETRY_DELAY = 1  # seconds
BACKOFF_FACTOR = 2

# ===== Security Configuration =====

# API Key Validation
VALIDATE_API_KEYS_ON_STARTUP = True

# Database Security
ENCRYPT_EMBEDDINGS = False  # Set to True for production
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY", "your_encryption_key_here")

# ===== Feature Flags =====

# Enable/Disable Features
ENABLE_INTENT_CLASSIFICATION = True
ENABLE_SEMANTIC_SEARCH = True
ENABLE_SUMMARY_REPORTS = True
ENABLE_EMBEDDING_GENERATION = True
ENABLE_AI_SUMMARY = True

# Experimental Features
ENABLE_REAL_TIME_LEARNING = False
ENABLE_MULTI_LANGUAGE = False
ENABLE_ADVANCED_ANALYTICS = False

# ===== Environment-Specific Settings =====

ENVIRONMENT = os.getenv("ENVIRONMENT", "development")

if ENVIRONMENT == "production":
    # Production settings
    USE_MOCK_MODE = False
    FALLBACK_TO_MOCK = False
    DEBUG_MODE = False
    VALIDATE_API_KEYS_ON_STARTUP = True
    ENCRYPT_EMBEDDINGS = True
    
elif ENVIRONMENT == "testing":
    # Testing settings
    USE_MOCK_MODE = True
    FALLBACK_TO_MOCK = True
    DEBUG_MODE = True
    DATABASE_PATH = BASE_DIR / "test_smart_friend.db"
    
else:
    # Development settings (default)
    USE_MOCK_MODE = False
    FALLBACK_TO_MOCK = True
    DEBUG_MODE = True

# ===== Validation Functions =====

def validate_config():
    """Validate configuration settings"""
    errors = []
    
    # Check required API keys
    if not USE_MOCK_MODE:
        if DOUBAO_API_KEY == "your_doubao_api_key_here":
            errors.append("DOUBAO_API_KEY not configured")
        if JINA_API_KEY == "your_jina_api_key_here":
            errors.append("JINA_API_KEY not configured")
    
    # Check file paths
    if not INTENT_DATA_PATH.exists():
        errors.append(f"Intent data file not found: {INTENT_DATA_PATH}")
    if not SUMMARY_REPORT_PATH.exists():
        errors.append(f"Summary report template not found: {SUMMARY_REPORT_PATH}")
    
    # Check directories
    LOG_DIR.mkdir(exist_ok=True)
    
    if errors:
        raise ValueError(f"Configuration errors: {', '.join(errors)}")
    
    return True

def get_config_summary():
    """Get a summary of current configuration"""
    return {
        "environment": ENVIRONMENT,
        "use_mock_mode": USE_MOCK_MODE,
        "fallback_to_mock": FALLBACK_TO_MOCK,
        "debug_mode": DEBUG_MODE,
        "doubao_model": DOUBAO_MODEL_NAME,
        "jina_model": JINA_MODEL,
        "database_path": str(DATABASE_PATH),
        "features_enabled": {
            "intent_classification": ENABLE_INTENT_CLASSIFICATION,
            "semantic_search": ENABLE_SEMANTIC_SEARCH,
            "summary_reports": ENABLE_SUMMARY_REPORTS,
            "embedding_generation": ENABLE_EMBEDDING_GENERATION,
            "ai_summary": ENABLE_AI_SUMMARY
        }
    }

# ===== Export Configuration =====

__all__ = [
    # API Configuration
    'DOUBAO_API_KEY', 'DOUBAO_BASE_URL', 'DOUBAO_MODEL_NAME',
    'JINA_API_KEY', 'JINA_BASE_URL', 'JINA_MODEL',
    
    # System Configuration
    'USE_MOCK_MODE', 'FALLBACK_TO_MOCK', 'DEBUG_MODE',
    'DATABASE_PATH', 'INTENT_DATA_PATH', 'SUMMARY_REPORT_PATH',
    
    # Feature Configuration
    'INTENT_CLASSES', 'INTENT_DESCRIPTIONS',
    'SUMMARY_REPORT_SECTIONS', 'DEFAULT_SEARCH_TOP_K',
    
    # Functions
    'validate_config', 'get_config_summary'
]
