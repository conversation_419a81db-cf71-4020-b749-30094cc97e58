# 任务确认API
from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
import logging
from pydantic import BaseModel

from service.daily_task_service import DailyTaskService
from core.daily_tasks.models.daily_task_models import TaskPlan,TaskItem,DailyTask
logger = logging.getLogger(__name__)

router = APIRouter()

class TaskConfirmRequest(BaseModel):
    """任务确认请求模型"""
    child_id: int
    task_plan: List[Dict[str, Any]]

class TasksaveRequest(BaseModel):
    """任务确认请求模型"""
    child_id: int
    task_plan: List[Dict[str, Any]]
    planID: int

@router.post("/check_task_data")
async def check_task_data():
    """
    确认任务数据接口
    
    这个接口对应前端的确认任务按钮，用于处理任务确认逻辑
    """
    try:
        logger.info("收到任务确认请求")
        
        # 这里可以添加具体的任务确认逻辑
        # 比如检查任务完成情况、更新任务状态等
        
        return {
            "success": True,
            "message": "任务确认成功",
            "data": {}
        }
        
    except Exception as e:
        logger.error(f"任务确认失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"任务确认失败: {str(e)}"
        )

@router.post("/sumbitFinishTask")
async def submit_finish_task():
    """
    提交完成任务接口
    
    这个接口对应前端的提交检测按钮
    """
    try:
        logger.info("收到任务提交请求")
        
        # 这里可以添加具体的任务提交逻辑
        # 比如标记任务为完成状态、计算积分等
        
        return {
            "success": True,
            "message": "任务提交成功",
            "data": {}
        }
        
    except Exception as e:
        logger.error(f"任务提交失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"任务提交失败: {str(e)}"
        )

@router.post("/confirm_task_plan")
async def confirm_task_plan( request: TasksaveRequest):
    """
    确认任务计划接口

    将前端个性化任务计划展示的任务相关信息存入数据库，
    并返回带有任务ID的完整数据给前端
    """
    try:
        logger.info(f"收到任务计划确认请求，学生ID: {request.child_id}")

        service = DailyTaskService()
        result = await service.confirm_task_plan(request.child_id, request.task_plan,request.planID)

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "任务计划确认失败")
            )

        # 获取存储后的任务数据（包含ID）
        tasks_result = await service.get_today_tasks_with_subtasks(request.child_id)
        if tasks_result.get("success"):
            result["tasks_with_ids"] = tasks_result.get("data", [])

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认任务计划失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"确认任务计划失败: {str(e)}"
        )

@router.post("/store_and_get_tasks")
async def store_and_get_tasks(request: TaskConfirmRequest):
    """
    存储任务计划并立即返回带ID的任务数据

    用于在任务生成后立即存储到数据库并获取带ID的数据，
    这样前端可以直接显示带ID的任务而不需要用户手动确认
    """
    try:
        logger.info(f"存储并获取任务数据，学生ID: {request.child_id}")

        service = DailyTaskService()

        # 1. 存储任务计划
        store_result = await service.confirm_task_plan(request.child_id, request.task_plan)

        if not store_result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=store_result.get("message", "存储任务计划失败")
            )

        # 2. 获取存储后的任务数据（包含ID）
        tasks_result = await service.get_today_tasks_with_subtasks(request.child_id)

        if not tasks_result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=tasks_result.get("message", "获取任务数据失败")
            )

        return {
            "success": True,
            "message": "任务存储成功",
            "data": tasks_result.get("data", []),
            "store_info": store_result.get("data", {})
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"存储并获取任务数据失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"存储并获取任务数据失败: {str(e)}"
        )

@router.get("/get_today_tasks/{child_id}")
async def get_today_tasks(child_id: int):
    """
    获取今日任务及其子任务接口

    用于在任务确认后重新加载数据库中的实际任务数据
    """
    try:
        logger.info(f"获取学生{child_id}的今日任务")

        service = DailyTaskService()
        result = await service.get_today_tasks_with_subtasks(child_id)

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "获取今日任务失败")
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取今日任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取今日任务失败: {str(e)}"
        )
