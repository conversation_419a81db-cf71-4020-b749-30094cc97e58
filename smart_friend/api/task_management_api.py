# 任务管理API - 删除和撤销删除功能
from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any, Optional
import logging
from pydantic import BaseModel

from service.daily_task_service import DailyTaskService

logger = logging.getLogger(__name__)

router = APIRouter()

class DeleteTaskRequest(BaseModel):
    """删除任务请求模型"""
    task_id: int
    reason: Optional[str] = None

class RestoreTaskRequest(BaseModel):
    """撤销删除任务请求模型"""
    task_id: int
    reason: Optional[str] = None

@router.delete("/delete_task")
async def delete_task(request: DeleteTaskRequest):
    """
    删除任务接口（软删除）
    
    将指定的任务及其子任务软删除（设置is_active=False）
    """
    try:
        logger.info(f"删除任务: {request.task_id}, 原因: {request.reason}")

        service = DailyTaskService()
        result = await service.delete_task(task_id=request.task_id)

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "删除任务失败")
            )

        return {
            "success": True,
            "message": "任务删除成功",
            "data": result.get("data", {})
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除任务失败: {str(e)}"
        )

@router.post("/restore_task")
async def restore_task(request: RestoreTaskRequest):
    """
    撤销删除任务接口
    
    将已删除的任务及其子任务恢复（设置is_active=True）
    """
    try:
        logger.info(f"撤销删除任务: {request.task_id}, 原因: {request.reason}")

        service = DailyTaskService()
        result = await service.restore_task(task_id=request.task_id)

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=result.get("message", "撤销删除任务失败")
            )

        return {
            "success": True,
            "message": "任务恢复成功",
            "data": result.get("data", {})
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"撤销删除任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"撤销删除任务失败: {str(e)}"
        )

@router.get("/deleted_tasks/{child_id}")
async def get_deleted_tasks(child_id: int, days_back: int = 7):
    """
    获取已删除的任务列表
    
    Args:
        child_id: 学生ID
        days_back: 回溯天数，默认7天
    """
    try:
        logger.info(f"获取学生 {child_id} 的已删除任务列表，回溯 {days_back} 天")

        service = DailyTaskService()
        deleted_tasks = await service.get_deleted_tasks(child_id=child_id, days_back=days_back)

        return {
            "success": True,
            "message": "获取已删除任务列表成功",
            "data": {
                "deleted_tasks": deleted_tasks,
                "count": len(deleted_tasks),
                "days_back": days_back
            }
        }

    except Exception as e:
        logger.error(f"获取已删除任务列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取已删除任务列表失败: {str(e)}"
        )
