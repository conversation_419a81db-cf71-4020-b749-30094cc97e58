#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TTS API端点

基于FastAPI的文本转语音（TTS）服务API接口
提供完整的TTS功能，包括文本播放、文件生成、状态监控等
"""

import os
import sys
import logging
import time
import base64
from typing import Optional
from fastapi import APIRouter, HTTPException, status, Depends, File, UploadFile
from fastapi.responses import FileResponse, StreamingResponse

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
sys.path.insert(0, project_root)

# 导入TTS服务和模型
try:
    from service.tts_service import (
        get_tts_service, init_tts_service, play_tts_text,
        generate_tts_file, get_tts_status, TTSService
    )
    TTS_SERVICE_AVAILABLE = True
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.warning(f"TTS服务模块导入失败: {e}")
    TTS_SERVICE_AVAILABLE = False

    # 创建模拟的TTS服务类
    class TTSService:
        def __init__(self):
            self.temp_audio_dir = "/tmp"

        def play_text(self, text, async_mode=True):
            return False

        def generate_file(self, text, output_path=None):
            return None

        def get_status(self):
            return {
                'initialized': False,
                'tts_available': False,
                'temp_audio_dir': self.temp_audio_dir,
                'temp_files_count': 0
            }

        def cleanup_temp_files(self, max_files):
            pass

    def get_tts_service():
        return TTSService()

    def init_tts_service():
        return False

from service.models.tts_models import (
    TTSPlayRequest, TTSPlayResponse, TTSGenerateRequest, TTSGenerateResponse,
    TTSStatus, TTSHealthResponse, TTSCleanupRequest, TTSCleanupResponse,
    TTSConfigRequest, TTSAPIResponse, TTSErrorResponse, TTSBatchRequest,
    TTSBatchResponse, TTSSpeakersResponse, TTSSpeakerInfo
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/tts",
    # tags=["TTS"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)


def get_tts_service_dependency() -> TTSService:
    """获取TTS服务依赖"""
    try:
        service = get_tts_service()
        logger.debug(f"获取TTS服务实例: {service}")

        if not service.initialized:
            logger.info("TTS服务未初始化，正在初始化...")
            # 尝试初始化服务
            init_success = service.initialize()
            logger.info(f"TTS服务初始化结果: {init_success}")

        # 检查服务状态
        status = service.get_status()
        logger.debug(f"TTS服务状态: {status}")

        return service
    except Exception as e:
        logger.error(f"获取TTS服务失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        # 即使服务不可用，也返回一个基础实例用于测试
        return get_tts_service()


@router.post("/init", response_model=TTSAPIResponse)
async def initialize_tts_service():
    """
    初始化TTS服务
    
    初始化TTS客户端和相关资源
    """
    try:
        start_time = time.time()
        success = init_tts_service()
        duration = time.time() - start_time
        
        if success:
            logger.info("TTS服务初始化成功")
            return TTSAPIResponse(
                success=True,
                message="TTS服务初始化成功",
                data={"duration": duration}
            )
        else:
            logger.error("TTS服务初始化失败")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="TTS服务初始化失败"
            )
            
    except Exception as e:
        logger.error(f"初始化TTS服务时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"初始化TTS服务失败: {str(e)}"
        )


@router.post("/play", response_model=TTSPlayResponse)
async def play_text_to_speech(
    request: TTSPlayRequest,
    tts_service: TTSService = Depends(get_tts_service_dependency)
):
    """
    播放文本转语音
    
    将文本转换为语音并播放，支持同步和异步模式
    """
    try:
        start_time = time.time()
        
        if request.return_audio:
            # 生成音频文件并返回音频数据
            audio_file = tts_service.generate_file(request.text)
            
            if audio_file and os.path.exists(audio_file):
                with open(audio_file, 'rb') as f:
                    audio_data = f.read()
                
                audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                duration = time.time() - start_time
                
                logger.info(f"TTS音频数据生成成功: {request.text[:30]}... (大小: {len(audio_data)} bytes)")
                
                return TTSPlayResponse(
                    success=True,
                    message=f"语音数据生成成功: {request.text}",
                    audio_data=audio_base64,
                    audio_format="mp3",
                    audio_size=len(audio_data),
                    file_path=audio_file,
                    duration=duration
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="音频文件生成失败"
                )
        else:
            # 播放语音并生成文件供web播放
            success = tts_service.play_text(request.text, request.async_mode)
            duration = time.time() - start_time

            if success:
                # 同时生成音频文件供web播放
                audio_file = tts_service.generate_file(request.text)
                audio_url = None
                if audio_file:
                    filename = os.path.basename(audio_file)
                    audio_url = f"/api/v1/tts/download/{filename}"

                logger.info(f"TTS播放请求已处理: {request.text}")
                return TTSPlayResponse(
                    success=True,
                    message=f"语音播放请求已发送: {request.text}",
                    duration=duration,
                    file_path=audio_file,
                    audio_url=audio_url
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="语音播放失败"
                )
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理TTS播放请求时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理TTS播放请求失败: {str(e)}"
        )


@router.post("/generate", response_model=TTSGenerateResponse)
async def generate_audio_file(
    request: TTSGenerateRequest,
    tts_service: TTSService = Depends(get_tts_service_dependency)
):
    """
    生成TTS音频文件
    
    将文本转换为音频文件并保存
    """
    try:
        start_time = time.time()
        
        # 生成音频文件
        audio_file = tts_service.generate_file(request.text, request.output_path)
        duration = time.time() - start_time
        
        if audio_file and os.path.exists(audio_file):
            file_size = os.path.getsize(audio_file)
            logger.info(f"TTS音频文件生成成功: {audio_file}")
            
            return TTSGenerateResponse(
                success=True,
                message="音频文件生成成功",
                file_path=audio_file,
                file_size=file_size,
                duration=duration
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="音频文件生成失败"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理TTS生成请求时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理TTS生成请求失败: {str(e)}"
        )


@router.get("/download/{filename}")
async def download_audio_file(
    filename: str,
    tts_service: TTSService = Depends(get_tts_service_dependency)
):
    """
    下载TTS音频文件
    
    根据文件名下载生成的音频文件
    """
    try:
        file_path = os.path.join(tts_service.temp_audio_dir, filename)
        
        if not os.path.exists(file_path):
            logger.error(f"音频文件不存在: {file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="音频文件不存在"
            )
        
        logger.info(f"下载TTS音频文件: {file_path}")
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type="audio/mpeg"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载TTS音频文件时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载文件失败: {str(e)}"
        )


@router.get("/status", response_model=TTSHealthResponse)
async def get_tts_status(
    tts_service: TTSService = Depends(get_tts_service_dependency)
):
    """
    获取TTS服务状态
    
    返回TTS服务的详细状态信息
    """
    try:
        status_info = tts_service.get_status()
        
        return TTSHealthResponse(
            is_healthy=status_info.get('initialized', False) and status_info.get('tts_available', False),
            status=TTSStatus(**status_info)
        )
        
    except Exception as e:
        logger.error(f"获取TTS服务状态时出错: {e}")
        return TTSHealthResponse(
            is_healthy=False,
            status=TTSStatus(
                initialized=False,
                tts_available=False
            ),
            error=str(e)
        )


@router.post("/cleanup", response_model=TTSCleanupResponse)
async def cleanup_temp_files(
    request: TTSCleanupRequest,
    tts_service: TTSService = Depends(get_tts_service_dependency)
):
    """
    清理TTS临时文件
    
    删除旧的临时音频文件，保留指定数量的最新文件
    """
    try:
        # 获取清理前的文件数量
        before_count = 0
        if tts_service.temp_audio_dir and os.path.exists(tts_service.temp_audio_dir):
            before_count = len([f for f in os.listdir(tts_service.temp_audio_dir) 
                              if f.endswith(('.mp3', '.wav'))])
        
        # 执行清理
        tts_service.cleanup_temp_files(request.max_files)
        
        # 获取清理后的文件数量
        after_count = 0
        if tts_service.temp_audio_dir and os.path.exists(tts_service.temp_audio_dir):
            after_count = len([f for f in os.listdir(tts_service.temp_audio_dir) 
                             if f.endswith(('.mp3', '.wav'))])
        
        files_removed = before_count - after_count
        
        logger.info(f"TTS临时文件清理完成，删除 {files_removed} 个文件，保留 {after_count} 个文件")
        
        return TTSCleanupResponse(
            success=True,
            message=f"临时文件清理完成，保留最新 {request.max_files} 个文件",
            files_removed=files_removed,
            files_remaining=after_count
        )
        
    except Exception as e:
        logger.error(f"清理TTS临时文件时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理临时文件失败: {str(e)}"
        )


@router.post("/batch", response_model=TTSBatchResponse)
async def batch_generate_audio(
    request: TTSBatchRequest,
    tts_service: TTSService = Depends(get_tts_service_dependency)
):
    """
    批量生成TTS音频文件

    批量处理多个文本，生成对应的音频文件
    """
    try:
        start_time = time.time()
        results = []
        success_count = 0
        failed_count = 0

        for i, text in enumerate(request.texts):
            try:
                # 生成音频文件
                audio_file = tts_service.generate_file(text)

                if audio_file and os.path.exists(audio_file):
                    file_size = os.path.getsize(audio_file)
                    results.append({
                        "index": i,
                        "text": text,
                        "success": True,
                        "file_path": audio_file,
                        "file_size": file_size
                    })
                    success_count += 1
                else:
                    results.append({
                        "index": i,
                        "text": text,
                        "success": False,
                        "error": "音频文件生成失败"
                    })
                    failed_count += 1

            except Exception as e:
                results.append({
                    "index": i,
                    "text": text,
                    "success": False,
                    "error": str(e)
                })
                failed_count += 1

        duration = time.time() - start_time

        logger.info(f"批量TTS处理完成: 成功 {success_count}, 失败 {failed_count}")

        return TTSBatchResponse(
            success=True,
            message=f"批量处理完成: 成功 {success_count}, 失败 {failed_count}",
            results=results,
            total_count=len(request.texts),
            success_count=success_count,
            failed_count=failed_count,
            duration=duration
        )

    except Exception as e:
        logger.error(f"批量TTS处理时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量处理失败: {str(e)}"
        )


@router.get("/speakers", response_model=TTSSpeakersResponse)
async def get_available_speakers():
    """
    获取可用的TTS发音人列表

    返回系统支持的所有发音人信息
    """
    try:
        # 预定义的发音人列表（实际项目中可能从配置或API获取）
        speakers = [
            TTSSpeakerInfo(
                speaker_id="zh_female_shuangkuaisisi_moon_bigtts",
                speaker_name="双快思思",
                language="zh-CN",
                gender="female",
                description="中文女声，语速较快，适合播报"
            ),
            TTSSpeakerInfo(
                speaker_id="zh_male_wennuan_moon_bigtts",
                speaker_name="温暖男声",
                language="zh-CN",
                gender="male",
                description="中文男声，温暖亲切"
            ),
            TTSSpeakerInfo(
                speaker_id="zh_female_qingxin_moon_bigtts",
                speaker_name="清新女声",
                language="zh-CN",
                gender="female",
                description="中文女声，清新自然"
            )
        ]

        return TTSSpeakersResponse(
            success=True,
            speakers=speakers,
            total_count=len(speakers)
        )

    except Exception as e:
        logger.error(f"获取TTS发音人列表时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取发音人列表失败: {str(e)}"
        )


@router.post("/config", response_model=TTSAPIResponse)
async def update_tts_config(
    request: TTSConfigRequest,
    tts_service: TTSService = Depends(get_tts_service_dependency)
):
    """
    更新TTS配置

    更新TTS服务的配置参数，如发音人、应用ID等
    """
    try:
        # 更新配置
        if request.app_id:
            tts_service.app_id = request.app_id
        if request.token:
            tts_service.token = request.token
        if request.speaker:
            tts_service.speaker = request.speaker

        # 重新初始化TTS客户端
        success = tts_service.initialize()

        if success:
            logger.info("TTS配置更新成功")
            return TTSAPIResponse(
                success=True,
                message="TTS配置更新成功",
                data={
                    "app_id": tts_service.app_id,
                    "speaker": tts_service.speaker
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="TTS配置更新后重新初始化失败"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新TTS配置时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新TTS配置失败: {str(e)}"
        )
