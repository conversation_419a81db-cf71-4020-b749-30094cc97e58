#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OpenManus集成API端点

整合OpenManus、<PERSON><PERSON><PERSON>和Jina embeddings，提供完整的智能对话服务
"""

import os
import sys
import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
sys.path.insert(0, project_root)

from openmanus import OpenManusPlanner

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/openmanus",
    tags=["OpenManus Integration"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)

# 全局OpenManus实例
openmanus_planner = None

def get_openmanus_planner():
    """获取OpenManus规划器实例"""
    global openmanus_planner
    if openmanus_planner is None:
        openmanus_planner = OpenManusPlanner()
    return openmanus_planner


# 请求和响应模型
class ChatRequest(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = None
    user_id: Optional[str] = None

class ChatResponse(BaseModel):
    success: bool
    user_input: str
    intent: Dict[str, Any]
    task_type: str
    final_response: str
    processing_timestamp: str
    message: Optional[str] = None
    error: Optional[str] = None

class IntentClassificationRequest(BaseModel):
    text: str

class IntentClassificationResponse(BaseModel):
    success: bool
    text: str
    intent: Dict[str, Any]
    message: Optional[str] = None

class HealthResponse(BaseModel):
    is_healthy: bool
    components: Dict[str, bool]
    message: str


@router.post("/chat", response_model=ChatResponse)
async def chat_with_openmanus(request: ChatRequest):
    """
    智能对话接口
    
    使用OpenManus、Doubao和Jina embeddings进行完整的对话处理：
    1. 使用Jina embeddings进行意图分类
    2. 使用OpenManus生成执行计划
    3. 使用Doubao生成最终回复
    """
    try:
        planner = get_openmanus_planner()
        
        # 处理用户输入
        result = planner.process_user_input(request.message, request.context)
        
        return ChatResponse(
            success=True,
            user_input=result["user_input"],
            intent=result["intent"],
            task_type=result["task_type"],
            final_response=result["final_response"],
            processing_timestamp=result["processing_timestamp"],
            message="对话处理成功"
        )
        
    except Exception as e:
        logger.error(f"对话处理失败: {e}")
        return ChatResponse(
            success=False,
            user_input=request.message,
            intent={},
            task_type="",
            final_response="",
            processing_timestamp="",
            error=str(e),
            message="对话处理失败"
        )


@router.post("/classify-intent", response_model=IntentClassificationResponse)
async def classify_intent(request: IntentClassificationRequest):
    """
    意图分类接口
    
    使用Jina embeddings对用户输入进行意图分类
    """
    try:
        planner = get_openmanus_planner()
        
        # 分类意图
        result = planner.classify_user_intent(request.text)
        intent_data = result["output"]
        
        return IntentClassificationResponse(
            success=True,
            text=request.text,
            intent=intent_data,
            message="意图分类成功"
        )
        
    except Exception as e:
        logger.error(f"意图分类失败: {e}")
        return IntentClassificationResponse(
            success=False,
            text=request.text,
            intent={},
            message=f"意图分类失败: {str(e)}"
        )


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    健康检查接口
    
    检查OpenManus系统各组件的健康状态
    """
    try:
        planner = get_openmanus_planner()
        
        # 检查各组件状态
        components = {
            "doubao_client": True,  # 假设Doubao客户端正常
            "jina_embeddings": planner.embedding_client.check_health(),
            "intent_classifier": True,  # 意图分类器总是可用
            "dataset_manager": True,   # 数据集管理器总是可用
        }
        
        # 测试Doubao连接
        try:
            test_response = planner.client.chat("Hello")
            components["doubao_client"] = "error" not in test_response
        except:
            components["doubao_client"] = False
        
        is_healthy = all(components.values())
        
        return HealthResponse(
            is_healthy=is_healthy,
            components=components,
            message="健康检查完成" if is_healthy else "部分组件不健康"
        )
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return HealthResponse(
            is_healthy=False,
            components={},
            message=f"健康检查失败: {str(e)}"
        )


@router.get("/stats")
async def get_system_stats():
    """
    获取系统统计信息
    """
    try:
        planner = get_openmanus_planner()
        
        # 获取意图分类统计
        intent_stats = planner.intent_classifier.get_intent_statistics()
        
        # 获取缓存统计
        cache_stats = {"cached_embeddings": 0}
        if planner.embedding_client.cache_enabled:
            try:
                import sqlite3
                conn = sqlite3.connect(planner.embedding_client.cache_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM embedding_cache")
                cache_stats["cached_embeddings"] = cursor.fetchone()[0]
                conn.close()
            except:
                pass
        
        return {
            "success": True,
            "intent_classification": intent_stats,
            "embedding_cache": cache_stats,
            "message": "统计信息获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return {
            "success": False,
            "message": f"获取统计信息失败: {str(e)}"
        }


@router.post("/initialize")
async def initialize_system():
    """
    初始化系统
    
    重新初始化OpenManus系统，用于系统重启或配置更新后
    """
    try:
        global openmanus_planner
        openmanus_planner = OpenManusPlanner()
        
        # 生成意图数据的embeddings
        openmanus_planner.dataset_manager.generate_embeddings_for_intent_data()
        
        return {
            "success": True,
            "message": "系统初始化成功"
        }
        
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        return {
            "success": False,
            "message": f"系统初始化失败: {str(e)}"
        }
