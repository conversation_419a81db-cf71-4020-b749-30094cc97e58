#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ASR API端点

基于FastAPI的自动语音识别（ASR）服务API接口
提供完整的ASR功能，包括连接管理、语音识别、状态监控等
"""

import os
import sys
import logging
import time
import base64
import threading
from typing import Optional, List
from fastapi import APIRouter, HTTPException, status, Depends, File, UploadFile
from fastapi.responses import StreamingResponse

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
sys.path.insert(0, project_root)

# 导入ASR工具和服务
try:
    from backend.utils.asr_utils import (
        VolcanoASRClient, ASRManager, ASRUtils, ASRClientBase
    )
    ASR_UTILS_AVAILABLE = True
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.warning(f"ASR工具模块导入失败: {e}")
    ASR_UTILS_AVAILABLE = False

    # 创建模拟的ASR客户端类
    class VolcanoASRClient:
        def __init__(self, app_key, access_key, model_name="bigmodel",
                     sample_rate=16000, channels=1, callback=None):
            self.app_key = app_key
            self.access_key = access_key
            self.model_name = model_name
            self.sample_rate = sample_rate
            self.channels = channels
            self.callback = callback
            self.connect_id = "mock_connection_" + str(int(time.time()))
            self.is_recognizing = False
            self.connected = False

        def connect(self):
            logger.info("模拟ASR连接成功")
            self.connected = True
            return True

        def disconnect(self):
            logger.info("模拟ASR断开连接")
            self.connected = False
            self.is_recognizing = False

        def start_recognition(self):
            if not self.connected:
                logger.error("ASR未连接，无法启动识别")
                return False
            logger.info("模拟ASR识别启动")
            self.is_recognizing = True

            # 模拟一些识别结果
            if self.callback:
                import threading
                def simulate_results():
                    time.sleep(1)
                    self.callback("模拟识别结果", False)
                    time.sleep(2)
                    self.callback("这是一个测试语音识别", True)

                thread = threading.Thread(target=simulate_results, daemon=True)
                thread.start()

            return True

        def stop_recognition(self):
            logger.info("模拟ASR识别停止")
            self.is_recognizing = False

        def send_audio(self, audio_data):
            if self.is_recognizing:
                logger.info(f"模拟接收音频数据: {len(audio_data)} bytes")
                # 模拟处理音频数据
                if self.callback and len(audio_data) > 0:
                    self.callback("收到音频数据", False)

from service.models.asr_models import (
    ASRConnectRequest, ASRConnectResponse, ASRAudioRequest, ASRRecognitionResponse,
    ASRResultsResponse, ASRStatus, ASRHealthResponse, ASRConfigRequest,
    ASRAPIResponse, ASRErrorResponse, ASRResult, ASRBatchRequest, ASRBatchResponse,
    ASRModelsResponse, ASRModelInfo, ASRStatisticsResponse
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/asr",
    # tags=["ASR"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)

# 全局ASR客户端和状态管理
_asr_client = None
_asr_connected = False
_asr_results = []
_asr_results_lock = threading.Lock()
_asr_statistics = {
    "total_requests": 0,
    "successful_requests": 0,
    "failed_requests": 0,
    "start_time": time.time()
}

# ASR配置
ASR_CONFIG = {
    "app_key": "**********",
    "access_key": "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23",
    "model_name": "bigmodel",
    "sample_rate": 16000,
    "channels": 1
}


def asr_result_callback(text: str, is_final: bool):
    """ASR识别结果回调函数"""
    global _asr_results
    
    timestamp = time.time()
    result = ASRResult(
        text=text,
        is_final=is_final,
        timestamp=timestamp
    )
    
    with _asr_results_lock:
        _asr_results.append(result)
        # 保留最近100个结果
        if len(_asr_results) > 100:
            _asr_results = _asr_results[-100:]
    
    logger.info(f"ASR识别结果: {text} (final: {is_final})")


def check_asr_availability():
    """检查ASR服务可用性"""
    # 即使ASR工具不可用，也允许使用模拟实现进行测试
    pass


@router.post("/connect", response_model=ASRConnectResponse)
async def connect_asr_service(request: ASRConnectRequest = None):
    """
    连接ASR服务

    建立与ASR服务的连接，初始化语音识别客户端
    """
    global _asr_client, _asr_connected
    
    try:
        check_asr_availability()
        _asr_statistics["total_requests"] += 1
        
        # 如果已经连接，先断开旧连接以确保连接状态正确
        if _asr_connected and _asr_client:
            logger.info("检测到现有ASR连接，先断开以重新建立连接...")
            try:
                _asr_client.disconnect()
                _asr_connected = False
                time.sleep(1.0)  # 等待连接完全关闭
            except Exception as e:
                logger.warning(f"断开旧连接时出错: {e}")
                _asr_connected = False
        
        # 使用请求参数或默认配置
        config = ASR_CONFIG.copy()
        if request:
            if request.app_key:
                config["app_key"] = request.app_key
            if request.access_key:
                config["access_key"] = request.access_key
            if request.model_name:
                config["model_name"] = request.model_name
            if request.sample_rate:
                config["sample_rate"] = request.sample_rate
            if request.channels:
                config["channels"] = request.channels
        
        # 创建ASR客户端
        _asr_client = VolcanoASRClient(
            app_key=config["app_key"],
            access_key=config["access_key"],
            model_name=config["model_name"],
            sample_rate=config["sample_rate"],
            channels=config["channels"],
            callback=asr_result_callback
        )

        # 连接到ASR服务
        success = _asr_client.connect()
        if success:
            _asr_connected = True
            _asr_statistics["successful_requests"] += 1
            # logger.info("成功连接到火山引擎ASR服务")

            # 将ASR客户端设置到Socket.IO服务中
            try:
                from service.socketio_service import get_socketio_service
                socketio_service = get_socketio_service()
                socketio_service.set_asr_client(_asr_client)
                logger.info("ASR客户端已设置到Socket.IO服务")
            except Exception as e:
                logger.warning(f"设置ASR客户端到Socket.IO服务失败: {e}")

            return ASRConnectResponse(
                success=True,
                message="成功连接到语音识别服务",
                connection_id=getattr(_asr_client, 'connect_id', None),
                model_info={
                    "model_name": config["model_name"],
                    "sample_rate": config["sample_rate"],
                    "channels": config["channels"]
                }
            )
        else:
            _asr_statistics["failed_requests"] += 1
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="连接到语音识别服务失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        _asr_statistics["failed_requests"] += 1
        logger.error(f"连接ASR服务时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"连接失败: {str(e)}"
        )


@router.post("/disconnect", response_model=ASRAPIResponse)
async def disconnect_asr_service():
    """
    断开ASR服务连接
    
    断开与ASR服务的连接，释放相关资源
    """
    global _asr_client, _asr_connected
    
    try:
        check_asr_availability()
        
        if _asr_client:
            logger.info("正在断开ASR连接...")
            try:
                if hasattr(_asr_client, 'stop_recognition'):
                    _asr_client.stop_recognition()
                _asr_client.disconnect()
                logger.info("ASR连接已断开")
            except Exception as e:
                logger.warning(f"断开ASR连接时出错: {e}")
            finally:
                _asr_connected = False
                _asr_client = None

            return ASRAPIResponse(
                success=True,
                message="ASR连接已断开"
            )
        else:
            _asr_connected = False
            return ASRAPIResponse(
                success=True,
                message="ASR服务未连接"
            )
        
    except Exception as e:
        logger.error(f"断开ASR连接时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"断开连接失败: {str(e)}"
        )


@router.post("/start", response_model=ASRAPIResponse)
async def start_recognition():
    """
    启动语音识别
    
    开始语音识别会话，准备接收音频数据
    """
    global _asr_client, _asr_connected, _asr_results
    
    try:
        check_asr_availability()
        
        if not _asr_connected or not _asr_client:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请先连接到语音识别服务"
            )
        
        # 清空之前的识别结果
        with _asr_results_lock:
            _asr_results.clear()
        
        # 检查当前识别状态
        if hasattr(_asr_client, 'is_recognizing') and _asr_client.is_recognizing:
            logger.warning("ASR已在识别状态，先停止当前识别")
            _asr_client.stop_recognition()
            time.sleep(0.5)  # 等待停止完成

        # 启动语音识别
        logger.info("正在启动语音识别...")
        success = _asr_client.start_recognition()

        if success:
            logger.info("✅ 语音识别已启动")
            return ASRAPIResponse(
                success=True,
                message="语音识别已启动"
            )
        else:
            # 获取更详细的错误信息
            error_msg = "启动语音识别失败"
            if hasattr(_asr_client, 'last_error'):
                error_msg += f": {_asr_client.last_error}"

            logger.error(f"❌ {error_msg}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=error_msg
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动语音识别时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动语音识别失败: {str(e)}"
        )


@router.post("/stop", response_model=ASRAPIResponse)
async def stop_recognition():
    """
    停止语音识别
    
    停止当前的语音识别会话
    """
    global _asr_client, _asr_connected
    
    try:
        check_asr_availability()
        
        if _asr_client and _asr_connected:
            _asr_client.stop_recognition()
            logger.info("语音识别已停止")
            return ASRAPIResponse(
                success=True,
                message="语音识别已停止"
            )
        else:
            return ASRAPIResponse(
                success=True,
                message="语音识别服务未运行"
            )
        
    except Exception as e:
        logger.error(f"停止语音识别时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"停止语音识别失败: {str(e)}"
        )


@router.post("/send_audio", response_model=ASRAPIResponse)
async def send_audio_data(request: ASRAudioRequest):
    """
    发送音频数据
    
    向ASR服务发送Base64编码的音频数据进行识别
    """
    global _asr_client, _asr_connected
    
    try:
        check_asr_availability()
        
        if not _asr_connected or not _asr_client:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请先连接并启动语音识别服务"
            )
        
        if not request.audio_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="未提供音频数据"
            )
        
        # 解码base64音频数据
        try:
            audio_bytes = base64.b64decode(request.audio_data)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"音频数据解码失败: {str(e)}"
            )
        
        # 发送音频数据到ASR服务
        _asr_client.send_audio(audio_bytes)
        
        return ASRAPIResponse(
            success=True,
            message="音频数据已发送"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送音频数据时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送音频数据失败: {str(e)}"
        )


@router.get("/results", response_model=ASRResultsResponse)
async def get_recognition_results():
    """
    获取ASR识别结果
    
    返回当前会话的所有识别结果
    """
    try:
        check_asr_availability()
        
        with _asr_results_lock:
            # 获取所有结果的副本
            results = _asr_results.copy()
        
        has_final = any(result.is_final for result in results)
        
        return ASRResultsResponse(
            success=True,
            results=results,
            count=len(results),
            has_final=has_final
        )
        
    except Exception as e:
        logger.error(f"获取ASR结果时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取识别结果失败: {str(e)}"
        )


@router.get("/status", response_model=ASRHealthResponse)
async def get_asr_status():
    """
    获取ASR服务状态
    
    返回ASR服务的详细状态信息
    """
    try:
        check_asr_availability()
        
        status_info = ASRStatus(
            connected=_asr_connected,
            client_exists=_asr_client is not None,
            recognition_active=_asr_client.is_recognizing if _asr_client else False,
            web_service_active=False,
            model_name=getattr(_asr_client, 'model_name', None),
            sample_rate=getattr(_asr_client, 'sample_rate', None),
            channels=getattr(_asr_client, 'channels', None),
            connection_id=getattr(_asr_client, 'connect_id', None),
            last_activity=time.strftime('%Y-%m-%d %H:%M:%S')
        )
        
        return ASRHealthResponse(
            is_healthy=_asr_connected and _asr_client is not None,
            status=status_info
        )
        
    except Exception as e:
        logger.error(f"获取ASR状态时出错: {e}")
        return ASRHealthResponse(
            is_healthy=False,
            status=ASRStatus(
                connected=False,
                client_exists=False,
                recognition_active=False
            ),
            error=str(e)
        )


@router.get("/models", response_model=ASRModelsResponse)
async def get_available_models():
    """
    获取可用的ASR模型列表
    
    返回系统支持的所有ASR模型信息
    """
    try:
        # 预定义的模型列表（实际项目中可能从配置或API获取）
        models = [
            ASRModelInfo(
                model_id="bigmodel",
                model_name="火山引擎大模型",
                language="zh-CN",
                sample_rates=[8000, 16000, 24000, 48000],
                description="火山引擎提供的中文语音识别大模型，支持多种采样率"
            ),
            ASRModelInfo(
                model_id="standard",
                model_name="标准模型",
                language="zh-CN",
                sample_rates=[16000],
                description="标准中文语音识别模型"
            )
        ]
        
        return ASRModelsResponse(
            success=True,
            models=models,
            total_count=len(models)
        )
        
    except Exception as e:
        logger.error(f"获取ASR模型列表时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模型列表失败: {str(e)}"
        )


@router.get("/statistics", response_model=ASRStatisticsResponse)
async def get_asr_statistics():
    """
    获取ASR服务统计信息
    
    返回ASR服务的使用统计数据
    """
    try:
        uptime = time.time() - _asr_statistics["start_time"]
        
        return ASRStatisticsResponse(
            success=True,
            total_requests=_asr_statistics["total_requests"],
            successful_requests=_asr_statistics["successful_requests"],
            failed_requests=_asr_statistics["failed_requests"],
            uptime=uptime
        )
        
    except Exception as e:
        logger.error(f"获取ASR统计信息时出错: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )
