#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
语音交互 API 端点

整合ASR、AI对话和TTS功能，提供完整的语音交互服务
"""

import os
import sys
import logging
import time
import asyncio
from typing import Optional
from fastapi import APIRouter, HTTPException, status
import httpx

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
sys.path.insert(0, project_root)

from service.models.voice_models import (
    VoiceInteractionRequest, VoiceInteractionResponse, VoiceServicesStatusResponse,
    VoiceServiceStatus, VoiceConfigRequest, VoiceConfigResponse, VoiceBatchRequest,
    VoiceBatchResponse, VoiceHealthResponse, VoiceAPIResponse, VoiceErrorResponse
)
from config.config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/voice",
    tags=["Voice Interaction"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)


async def call_service_api(client: httpx.AsyncClient, method: str, url: str, **kwargs) -> dict:
    """调用服务API的通用方法"""
    try:
        if method.upper() == "GET":
            response = await client.get(url, **kwargs)
        elif method.upper() == "POST":
            response = await client.post(url, **kwargs)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail=f"服务调用失败: {response.text}"
            )
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"服务连接失败: {str(e)}"
        )


@router.post("/interact", response_model=VoiceInteractionResponse)
async def voice_interaction(request: VoiceInteractionRequest):
    """
    语音交互端点
    
    整合ASR和TTS功能，实现完整的语音对话流程：
    1. 接收用户语音输入
    2. 使用ASR转换为文本
    3. 调用AI模型生成回复
    4. 使用TTS转换回复为语音
    5. 返回文本和音频回复
    """
    start_time = time.time()
    asr_time = 0
    ai_time = 0
    tts_time = 0
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            base_url = f"http://localhost:8014{settings.API_V1_STR}"
            
            # 1. ASR处理 - 语音转文本
            asr_start = time.time()
            
            # 连接ASR服务
            await call_service_api(client, "POST", f"{base_url}/asr/connect")
            
            # 启动ASR识别
            await call_service_api(client, "POST", f"{base_url}/asr/start")
            
            # 发送音频数据
            await call_service_api(
                client, "POST", f"{base_url}/asr/send_audio",
                json={"audio_data": request.audio_data}
            )
            
            # 等待ASR处理
            await asyncio.sleep(2)
            
            # 获取识别结果
            asr_results = await call_service_api(client, "GET", f"{base_url}/asr/results")
            
            # 停止ASR识别
            await call_service_api(client, "POST", f"{base_url}/asr/stop")
            
            asr_time = time.time() - asr_start
            
            # 提取识别的文本
            recognized_text = ""
            if asr_results.get("success") and asr_results.get("results"):
                # 获取最终结果或最后一个结果
                final_results = [r for r in asr_results["results"] if r.get("is_final")]
                if final_results:
                    recognized_text = final_results[-1]["text"]
                elif asr_results["results"]:
                    recognized_text = asr_results["results"][-1]["text"]
            
            if not recognized_text:
                return VoiceInteractionResponse(
                    success=False,
                    message="语音识别失败，未能识别出文本",
                    error="ASR识别结果为空",
                    processing_time=time.time() - start_time,
                    asr_time=asr_time
                )
            
            # 2. AI处理 - 生成回复
            ai_start = time.time()
            
            ai_response = await call_service_api(
                client, "POST", f"{base_url}/doubao/simple-chat",
                json={"prompt": recognized_text}
            )
            
            ai_time = time.time() - ai_start
            response_text = ai_response.get("response_text", "抱歉，我无法理解您的问题。")
            
            # 3. TTS处理 - 文本转语音（如果需要）
            audio_data = None
            audio_format = None
            
            if request.return_audio:
                tts_start = time.time()
                
                tts_response = await call_service_api(
                    client, "POST", f"{base_url}/tts/play",
                    json={
                        "text": response_text,
                        "speaker": request.speaker,
                        "return_audio": True
                    }
                )
                
                tts_time = time.time() - tts_start
                
                if tts_response.get("success"):
                    audio_data = tts_response.get("audio_data")
                    audio_format = tts_response.get("audio_format", "mp3")
            
            total_time = time.time() - start_time
            
            logger.info(f"语音交互完成 - 识别: '{recognized_text}' -> 回复: '{response_text[:50]}...'")
            
            return VoiceInteractionResponse(
                success=True,
                recognized_text=recognized_text,
                response_text=response_text,
                audio_data=audio_data,
                audio_format=audio_format,
                processing_time=total_time,
                asr_time=asr_time,
                ai_time=ai_time,
                tts_time=tts_time,
                message="语音交互处理成功"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"语音交互处理失败: {e}")
        return VoiceInteractionResponse(
            success=False,
            message="语音交互处理失败",
            error=str(e),
            processing_time=time.time() - start_time,
            asr_time=asr_time,
            ai_time=ai_time,
            tts_time=tts_time
        )


@router.get("/status", response_model=VoiceServicesStatusResponse)
async def get_voice_services_status():
    """
    检查语音服务状态
    
    检查ASR、TTS和AI服务的可用性
    """
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            base_url = f"http://localhost:8014{settings.API_V1_STR}"
            
            # 检查ASR状态
            asr_status = VoiceServiceStatus(
                service_name="ASR",
                available=False,
                healthy=False
            )
            
            try:
                asr_response = await call_service_api(client, "GET", f"{base_url}/asr/status")
                asr_status.available = True
                asr_status.healthy = asr_response.get("is_healthy", False)
                asr_status.last_check = time.strftime('%Y-%m-%d %H:%M:%S')
            except Exception as e:
                asr_status.error = str(e)
            
            # 检查TTS状态
            tts_status = VoiceServiceStatus(
                service_name="TTS",
                available=False,
                healthy=False
            )
            
            try:
                tts_response = await call_service_api(client, "GET", f"{base_url}/tts/status")
                tts_status.available = True
                tts_status.healthy = tts_response.get("is_healthy", False)
                tts_status.last_check = time.strftime('%Y-%m-%d %H:%M:%S')
            except Exception as e:
                tts_status.error = str(e)
            
            # 检查AI状态
            ai_status = VoiceServiceStatus(
                service_name="AI",
                available=False,
                healthy=False
            )
            
            try:
                ai_response = await call_service_api(client, "GET", f"{base_url}/doubao/health")
                ai_status.available = True
                ai_status.healthy = ai_response.get("is_healthy", False)
                ai_status.last_check = time.strftime('%Y-%m-%d %H:%M:%S')
            except Exception as e:
                ai_status.error = str(e)
            
            # 语音交互可用性取决于所有服务都可用
            voice_interaction_available = (
                asr_status.healthy and 
                tts_status.healthy and 
                ai_status.healthy
            )
            
            return VoiceServicesStatusResponse(
                success=True,
                message="语音服务状态检查完成",
                asr_status=asr_status,
                tts_status=tts_status,
                ai_status=ai_status,
                voice_interaction_available=voice_interaction_available
            )
            
    except Exception as e:
        logger.error(f"检查语音服务状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"语音服务状态检查失败: {str(e)}"
        )


@router.get("/health", response_model=VoiceHealthResponse)
async def voice_health_check():
    """
    语音服务健康检查
    
    提供详细的健康状态和改进建议
    """
    try:
        services_status = await get_voice_services_status()
        
        is_healthy = services_status.voice_interaction_available
        recommendations = []
        
        if not services_status.asr_status.healthy:
            recommendations.append("ASR服务不健康，请检查语音识别服务配置")
        
        if not services_status.tts_status.healthy:
            recommendations.append("TTS服务不健康，请检查文本转语音服务配置")
        
        if not services_status.ai_status.healthy:
            recommendations.append("AI服务不健康，请检查对话模型服务配置")
        
        if is_healthy:
            recommendations.append("所有语音服务运行正常")
        
        return VoiceHealthResponse(
            is_healthy=is_healthy,
            services=services_status,
            recommendations=recommendations
        )
        
    except Exception as e:
        logger.error(f"语音服务健康检查失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"语音服务健康检查失败: {str(e)}"
        )
