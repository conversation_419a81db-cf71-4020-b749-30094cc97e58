# -*- coding: utf-8 -*-
"""
多模态任务输入API接口
支持语音、图片、文本三种输入方式的任务创建，使用多模态模型处理
"""

import logging
from fastapi import APIRouter, HTTPException, Depends, File, UploadFile, Form
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

from service.multimodal_task_input_service import MultimodalTaskInputService

logger = logging.getLogger(__name__)

router = APIRouter()


class TextTaskRequest(BaseModel):
    """文本任务输入请求模型"""
    child_id: int = Field(..., description="学生ID")
    text_content: str = Field(..., description="文本内容")


class VoiceTaskRequest(BaseModel):
    """语音任务输入请求模型"""
    child_id: int = Field(..., description="学生ID")
    voice_text: str = Field(..., description="语音转文字结果")


class TaskInputResponse(BaseModel):
    """任务输入响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    child_id: int = Field(..., description="学生ID")
    input_type: str = Field(..., description="输入类型")
    tasks: List[Dict[str, Any]] = Field(..., description="解析的任务列表")
    stored_task_ids: Optional[List[int]] = Field(None, description="存储的任务ID列表")
    total_tasks: int = Field(0, description="总任务数")
    stored_tasks: int = Field(0, description="成功存储的任务数")
    processed_at: Optional[datetime] = Field(None, description="处理时间")


def get_multimodal_task_input_service() -> MultimodalTaskInputService:
    """获取多模态任务输入服务实例"""
    return MultimodalTaskInputService()


@router.post("/text", response_model=TaskInputResponse)
async def process_multimodal_text_task_input(
    request: TextTaskRequest,
    service: MultimodalTaskInputService = Depends(get_multimodal_task_input_service)
):
    """
    处理文本输入的任务（多模态版本）
    
    将用户输入的文本内容解析为结构化的今日任务，并存储到数据库。
    """
    try:
        logger.info(f"收到学生{request.child_id}的多模态文本任务输入请求")
        
        # 验证学生ID
        if request.child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 验证文本内容
        if not request.text_content.strip():
            raise HTTPException(
                status_code=400,
                detail="文本内容不能为空"
            )
        
        # 调用多模态服务处理文本输入
        result = await service.process_text_input(
            child_id=request.child_id,
            text_content=request.text_content
        )
        
        if result["success"]:
            logger.info(f"成功处理学生{request.child_id}的多模态文本输入，解析出{result['total_tasks']}个任务")
        else:
            logger.warning(f"学生{request.child_id}的多模态文本输入处理失败: {result['message']}")
        
        return TaskInputResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理多模态文本任务输入请求时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"处理多模态文本输入失败: {str(e)}"
        )


@router.post("/voice", response_model=TaskInputResponse)
async def process_multimodal_voice_task_input(
    request: VoiceTaskRequest,
    service: MultimodalTaskInputService = Depends(get_multimodal_task_input_service)
):
    """
    处理语音输入的任务（多模态版本）
    
    将语音转文字的结果解析为结构化的今日任务，并存储到数据库。
    """
    try:
        logger.info(f"收到学生{request.child_id}的多模态语音任务输入请求")
        
        # 验证学生ID
        if request.child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 验证语音文字内容
        if not request.voice_text.strip():
            raise HTTPException(
                status_code=400,
                detail="语音文字内容不能为空"
            )
        
        # 调用多模态服务处理语音输入
        result = await service.process_voice_input(
            child_id=request.child_id,
            voice_text=request.voice_text
        )
        
        if result["success"]:
            logger.info(f"成功处理学生{request.child_id}的多模态语音输入，解析出{result['total_tasks']}个任务")
        else:
            logger.warning(f"学生{request.child_id}的多模态语音输入处理失败: {result['message']}")
        
        return TaskInputResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理多模态语音任务输入请求时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"处理多模态语音输入失败: {str(e)}"
        )


@router.post("/image", response_model=TaskInputResponse)
async def process_multimodal_image_task_input(
    child_id: int = Form(..., description="学生ID"),
    image_file: UploadFile = File(..., description="图片文件"),
    service: MultimodalTaskInputService = Depends(get_multimodal_task_input_service)
):
    """
    处理图片输入的任务（多模态版本）
    
    使用多模态模型从上传的图片中识别学习任务，解析为结构化的今日任务，并存储到数据库。
    """
    try:
        logger.info(f"收到学生{child_id}的多模态图片任务输入请求")
        
        # 验证学生ID
        if child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 验证图片文件
        if not image_file:
            raise HTTPException(
                status_code=400,
                detail="图片文件不能为空"
            )
        
        # 检查文件类型
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp"]
        if image_file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的图片格式: {image_file.content_type}，支持的格式: {', '.join(allowed_types)}"
            )
        
        # 读取图片数据
        image_data = await image_file.read()
        
        if len(image_data) == 0:
            raise HTTPException(
                status_code=400,
                detail="图片文件为空"
            )
        
        # 调用多模态服务处理图片输入
        result = await service.process_image_input(
            child_id=child_id,
            image_data=image_data,
            image_format="bytes"
        )
        
        if result["success"]:
            logger.info(f"成功处理学生{child_id}的多模态图片输入，解析出{result['total_tasks']}个任务")
        else:
            logger.warning(f"学生{child_id}的多模态图片输入处理失败: {result['message']}")
        
        return TaskInputResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理多模态图片任务输入请求时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"处理多模态图片输入失败: {str(e)}"
        )



@router.post("/imageforChange", response_model=TaskInputResponse)
async def process_multimodal_image_task_input(
    child_id: int = Form(..., description="学生ID"),
    image_file: UploadFile = File(..., description="图片文件"),
    service: MultimodalTaskInputService = Depends(get_multimodal_task_input_service)
):
    """
    处理图片输入的任务（多模态版本）
    
    使用多模态模型从上传的图片中识别学习任务，解析为结构化的今日任务，并存储到数据库。
    """
    try:
        logger.info(f"收到学生{child_id}的多模态图片任务输入请求")
        
        # 验证学生ID
        if child_id <= 0:
            raise HTTPException(
                status_code=400,
                detail="学生ID必须大于0"
            )
        
        # 验证图片文件
        if not image_file:
            raise HTTPException(
                status_code=400,
                detail="图片文件不能为空"
            )
        
        # 检查文件类型
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp"]
        if image_file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的图片格式: {image_file.content_type}，支持的格式: {', '.join(allowed_types)}"
            )
        
        # 读取图片数据
        image_data = await image_file.read()
        
        if len(image_data) == 0:
            raise HTTPException(
                status_code=400,
                detail="图片文件为空"
            )
        
        # 调用多模态服务处理图片输入
        result = await service.process_image_input_changeTask(
            child_id=child_id,
            image_data=image_data,
            image_format="bytes"
        )
        
        if result["success"]:
            logger.info(f"成功处理学生{child_id}的多模态图片输入，解析出{result['total_tasks']}个用于添加的任务")
        else:
            logger.warning(f"学生{child_id}的多模态图片输入处理失败: {result['message']}")
        
        return TaskInputResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理多模态图片任务输入请求时发生错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"处理多模态图片输入失败: {str(e)}"
        )

@router.get("/health")
async def health_check():
    """
    健康检查接口
    
    检查多模态任务输入服务的状态和依赖服务的连接情况。
    """
    try:
        service = get_multimodal_task_input_service()
        
        # 检查豆包服务连接
        doubao_status = service.doubao_service.validate_connection()
        
        # 检查今日任务服务
        daily_task_status = True  # 简单检查，可以扩展
        
        return {
            "status": "healthy" if doubao_status and daily_task_status else "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "doubao_service": "connected" if doubao_status else "disconnected",
                "daily_task_service": "connected" if daily_task_status else "disconnected"
            },
            "supported_input_types": ["text", "voice", "image"],
            "supported_image_formats": ["jpeg", "jpg", "png", "gif", "bmp"],
            "multimodal_enabled": True
        }
        
    except Exception as e:
        logger.error(f"多模态健康检查失败: {e}")
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }
