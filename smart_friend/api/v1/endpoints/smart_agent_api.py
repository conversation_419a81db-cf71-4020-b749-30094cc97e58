#!/usr/bin/env python3
"""
Smart Agent API - wxysmart-compatible endpoint with OpenManus enhancements

This API provides wxysmart-compatible voice processing with advanced OpenManus capabilities:
- Enhanced intent classification (99 categories vs 5)
- Intelligent planning and tool selection
- Advanced embedding and retrieval
- Performance optimization and caching
"""

import os
import sys
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field

# Add project root to path
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
sys.path.insert(0, project_root)

# Import OpenManus planner dynamically to avoid circular imports
def get_openmanus_planner():
    """Get OpenManus planner instance dynamically to avoid circular imports"""
    try:
        import main
        return main.get_openmanus_planner()
    except Exception as e:
        logger.error(f"Failed to get OpenManus planner: {e}")
        return None

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/smart-agent",
    tags=["Smart Agent"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)

# Request/Response Models (wxysmart-compatible)
class VoiceInputRequest(BaseModel):
    """Voice input request model - wxysmart compatible"""
    voice_text: str = Field(..., description="Voice recognition text")
    child_id: Optional[int] = Field(None, description="Student ID")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional context")
    session_id: Optional[str] = Field(None, description="Session identifier")

class SmartAgentResponse(BaseModel):
    """Smart agent response model - wxysmart compatible with OpenManus enhancements"""
    success: bool = Field(..., description="Whether processing was successful")
    message: str = Field(..., description="Response message")
    intent_info: Dict[str, Any] = Field(..., description="Intent classification information")
    execution_info: Optional[Dict[str, Any]] = Field(None, description="Execution details")
    performance_info: Dict[str, Any] = Field(..., description="Performance metrics")
    child_context: Optional[Dict[str, Any]] = Field(None, description="Child-specific context")
    timestamp: str = Field(..., description="Response timestamp")
    response_type: str = Field(..., description="Type of response")
    error: Optional[str] = Field(None, description="Error message if failed")

class IntentAnalysisRequest(BaseModel):
    """Intent analysis request model"""
    text: str = Field(..., description="Text to analyze")
    return_top_matches: Optional[int] = Field(3, description="Number of top matches to return")

class IntentAnalysisResponse(BaseModel):
    """Intent analysis response model"""
    intent: str = Field(..., description="Predicted intent")
    confidence: float = Field(..., description="Confidence score")
    text: str = Field(..., description="Input text")
    top_intents: Optional[list] = Field(None, description="Top intent matches")
    classification_method: str = Field(..., description="Classification method used")
    embedding_dimension: int = Field(..., description="Embedding dimension")
    total_categories: int = Field(..., description="Total intent categories")

class SystemStatusResponse(BaseModel):
    """System status response model"""
    openmanus_ready: bool = Field(..., description="OpenManus system status")
    system_components: Dict[str, str] = Field(..., description="Component status")
    performance_metrics: Dict[str, Any] = Field(..., description="Performance metrics")
    integration_info: Dict[str, Any] = Field(..., description="Integration information")
    available_tools: list = Field(..., description="Available tools")
    timestamp: str = Field(..., description="Status timestamp")

@router.post("/process-voice", response_model=SmartAgentResponse)
async def process_voice_input(request: VoiceInputRequest):
    """
    Process voice input with wxysmart-compatible interface and OpenManus enhancements
    
    This endpoint provides the same interface as wxysmart but with advanced capabilities:
    - 99 intent categories (vs wxysmart's 5)
    - Intelligent planning and tool selection
    - Advanced embedding and retrieval
    - Performance optimization
    """
    start_time = time.time()
    
    try:
        logger.info(f"🎤 Processing voice input: {request.voice_text}")
        
        # Get OpenManus planner
        planner = get_openmanus_planner()
        if not planner:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="OpenManus system not available"
            )
        
        # Process voice input using OpenManus with wxysmart compatibility
        result = planner.process_voice_input(
            voice_text=request.voice_text,
            child_id=request.child_id,
            context=request.context
        )
        
        processing_time = time.time() - start_time
        
        # Ensure wxysmart-compatible response format
        response = SmartAgentResponse(
            success=result.get('success', False),
            message=result.get('message', ''),
            intent_info=result.get('intent_info', {}),
            execution_info=result.get('execution_info'),
            performance_info={
                **result.get('performance_info', {}),
                'api_processing_time': processing_time
            },
            child_context=result.get('child_context'),
            timestamp=result.get('timestamp', datetime.now().isoformat()),
            response_type=result.get('response_type', 'voice_processed'),
            error=result.get('error')
        )
        
        logger.info(f"✅ Voice processing completed in {processing_time:.2f}s")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        error_time = time.time() - start_time
        logger.error(f"❌ Voice processing failed: {e}")
        
        return SmartAgentResponse(
            success=False,
            message="Voice processing failed. Please try again.",
            intent_info={
                'intent': 'error',
                'confidence': 0.0,
                'tool_used': 'openmanus_enhanced',
                'classification_level': 'error'
            },
            performance_info={
                'processing_duration': error_time,
                'error_occurred': True,
                'api_processing_time': error_time
            },
            child_context={'child_id': request.child_id} if request.child_id else None,
            timestamp=datetime.now().isoformat(),
            response_type='voice_error',
            error=str(e)
        )

@router.post("/analyze-intent", response_model=IntentAnalysisResponse)
async def analyze_intent(request: IntentAnalysisRequest):
    """
    Analyze intent using OpenManus advanced classification (99 categories)
    
    This provides enhanced intent analysis compared to wxysmart's 5 categories
    """
    try:
        logger.info(f"🎯 Analyzing intent for: {request.text}")
        
        # Get OpenManus planner
        planner = get_openmanus_planner()
        if not planner:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="OpenManus system not available"
            )
        
        # Use OpenManus intent analysis with wxysmart format
        result = planner.analyze_intent_with_wxysmart_format(request.text)
        
        response = IntentAnalysisResponse(
            intent=result.get('intent', 'daily_chat'),
            confidence=result.get('confidence', 0.0),
            text=result.get('text', request.text),
            top_intents=result.get('top_intents', [])[:request.return_top_matches],
            classification_method=result.get('classification_method', 'openmanus_jina_embeddings'),
            embedding_dimension=result.get('embedding_dimension', 384),
            total_categories=result.get('total_categories', 99)
        )
        
        logger.info(f"✅ Intent analysis completed: {response.intent} ({response.confidence:.3f})")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Intent analysis failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Intent analysis failed: {str(e)}"
        )

@router.get("/status", response_model=SystemStatusResponse)
async def get_system_status():
    """
    Get comprehensive system status including OpenManus components
    """
    try:
        # Get OpenManus planner
        planner = get_openmanus_planner()
        
        if planner:
            performance_summary = planner.get_performance_summary()
            tools = planner.get_wxysmart_compatible_tools()
            
            return SystemStatusResponse(
                openmanus_ready=True,
                system_components=performance_summary.get('system_status', {}),
                performance_metrics=performance_summary.get('openmanus_metrics', {}),
                integration_info=performance_summary.get('integration_info', {}),
                available_tools=list(tools.keys()),
                timestamp=datetime.now().isoformat()
            )
        else:
            return SystemStatusResponse(
                openmanus_ready=False,
                system_components={'openmanus': 'not_ready'},
                performance_metrics={},
                integration_info={'error': 'OpenManus not initialized'},
                available_tools=[],
                timestamp=datetime.now().isoformat()
            )
            
    except Exception as e:
        logger.error(f"❌ Status check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Status check failed: {str(e)}"
        )

@router.get("/tools")
async def get_available_tools():
    """
    Get available wxysmart-compatible tools with descriptions
    """
    try:
        planner = get_openmanus_planner()
        if not planner:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="OpenManus system not available"
            )
        
        tools = planner.get_wxysmart_compatible_tools()
        return {
            'success': True,
            'tools': tools,
            'total_tools': len(tools),
            'timestamp': datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Tools retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Tools retrieval failed: {str(e)}"
        )

@router.get("/health")
async def health_check():
    """
    Health check endpoint for monitoring
    """
    try:
        planner = get_openmanus_planner()
        openmanus_ready = planner is not None
        
        return {
            'status': 'healthy',
            'openmanus_ready': openmanus_ready,
            'wxysmart_compatible': True,
            'timestamp': datetime.now().isoformat(),
            'version': '1.0.0'
        }
        
    except Exception as e:
        logger.error(f"❌ Health check failed: {e}")
        return {
            'status': 'unhealthy',
            'openmanus_ready': False,
            'wxysmart_compatible': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }
