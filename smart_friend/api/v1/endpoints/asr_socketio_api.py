#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ASR Socket.IO API端点
基于FastAPI和Socket.IO的语音识别服务
整合了voice_thread.py的功能到FastAPI框架中
"""

import os
import sys
import time
import base64
import struct
import threading
import numpy as np
from typing import Optional
from fastapi import APIRouter
import socketio

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
sys.path.insert(0, project_root)

# 导入ASR客户端
from backend.utils.volcano_asr_client import VolcanoASRClient
from backend.utils.fast_asr_connection import FastVolcanoASRClient
from backend.utils.webrtc_vad_detector import WebRTCVADDetector, VADEnhancedAudioProcessor

# 导入日志管理模块
try:
    from utils.logging_manager import get_logger
    logger = get_logger("ASRSocketIOAPI")
except ImportError:
    import logging
    logger = logging.getLogger("ASRSocketIOAPI")

# 创建路由器
router = APIRouter(
    prefix="/asr-socketio",
    tags=["ASR Socket.IO"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)

# 全局变量
asr_client = None
asr_connected = False
recognition_active = False
is_listening = False
last_speech_time = 0
silence_timeout = 4
recognition_start_time = None
last_recognized_text = ""
asr_results = []
asr_results_lock = threading.Lock()
auto_reconnect_enabled = True  # 是否启用自动重连功能（默认启用）

# VAD检测器相关
vad_detector = None
vad_processor = None
vad_enabled = True  # 是否启用VAD检测

# ASR配置
ASR_CONFIG = {
    "app_key": "5311525929",
    "access_key": "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23",
    "model_name": "bigmodel",
    "sample_rate": 16000,
    "channels": 1
}

# 全局Socket.IO实例，用于发送识别结果
_socketio_instance = None
current_client_sid = None  # 当前客户端的session ID

def set_socketio_instance(sio):
    """设置Socket.IO实例"""
    global _socketio_instance
    _socketio_instance = sio

def init_vad_detector():
    """初始化VAD检测器"""
    global vad_detector, vad_processor

    try:
        if vad_enabled:
            # 创建WebRTC VAD检测器
            vad_detector = WebRTCVADDetector(
                detection_window=0.5,      # 检测窗口：0.5秒
                chunk_duration=20,         # chunk时长：20ms
                activation_threshold=0.4,  # 激活阈值：40%
                sample_rate=16000,         # 采样率：16kHz
                aggressiveness=2           # VAD敏感度：中等
            )

            # 创建VAD增强音频处理器
            vad_processor = VADEnhancedAudioProcessor(
                vad_detector=vad_detector,
                silence_timeout=2.0,       # 静音超时：2秒
                min_voice_duration=0.3     # 最小语音时长：0.3秒
            )

            logger.info("✅ VAD检测器初始化成功")
            return True
        else:
            logger.info("VAD检测器已禁用")
            return True

    except Exception as e:
        logger.error(f"❌ VAD检测器初始化失败: {e}")
        return False


def process_final_text_with_openmanus(text: str, client_sid: str):
    """
    Process final recognized text with OpenManus (wxysmart-compatible)
    This function integrates OpenManus processing when ASR provides final results
    """
    try:
        # Import dynamically to avoid circular imports
        import main
        get_openmanus_planner = main.get_openmanus_planner

        logger.info(f"🧠 Processing final text with OpenManus: {text}")

        # Get OpenManus planner
        planner = get_openmanus_planner()
        if not planner:
            logger.error("OpenManus planner not available")
            return

        # Process voice input using OpenManus with wxysmart compatibility
        result = planner.process_voice_input(
            voice_text=text,
            child_id=12345,  # Default child_id, can be made configurable
            context={'source': 'socketio_asr', 'client_sid': client_sid}
        )

        # Send OpenManus response back to frontend
        if _socketio_instance and result:
            def emit_openmanus_response():
                try:
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # Send wxysmart-compatible response
                    loop.run_until_complete(_socketio_instance.emit('openmanus_response', {
                        'success': result.get('success', False),
                        'message': result.get('message', ''),
                        'intent_info': result.get('intent_info', {}),
                        'performance_info': result.get('performance_info', {}),
                        'timestamp': result.get('timestamp', ''),
                        'response_type': 'openmanus_processed'
                    }, room=client_sid))

                    loop.close()
                    logger.info(f"✅ OpenManus response sent to client {client_sid}")

                except Exception as e:
                    logger.error(f"❌ Failed to send OpenManus response: {e}")

            threading.Thread(target=emit_openmanus_response, daemon=True).start()

    except Exception as e:
        logger.error(f"❌ OpenManus processing failed: {e}")

def asr_result_callback(text: str, is_final: bool):
    """ASR识别结果回调函数 - Enhanced with OpenManus integration"""
    global asr_results, last_recognized_text, _socketio_instance, current_client_sid

    timestamp = time.time()
    result = {
        "text": text,
        "is_final": is_final,
        "timestamp": timestamp
    }

    with asr_results_lock:
        asr_results.append(result)
        # 保留最近100个结果
        if len(asr_results) > 100:
            asr_results = asr_results[-100:]

    # 更新最后语音时间 - 主要依赖ASR结果而不是音频活动
    global last_speech_time
    if text and text.strip():
        # 只有当文本真正发生变化且长度增加时才更新语音时间
        if text != last_recognized_text and len(text) > len(last_recognized_text):
            last_speech_time = timestamp
            logger.debug(f"✅ 检测到新语音内容，更新语音时间: {text[:30]}...")
        else:
            logger.debug(f"⏸️ 重复内容，不更新语音时间: {text[:30]}...")
    else:
        logger.debug(f"⏸️ 空文本，不更新语音时间")

    last_recognized_text = text
    # logger.info(f"🎤 ASR识别结果: {text} (final: {is_final})")

    # Process final text with OpenManus (NEW INTEGRATION)
    if is_final and text and text.strip() and current_client_sid:
        # Only process meaningful final results
        if len(text.strip()) > 2:  # Minimum length threshold
            threading.Thread(
                target=process_final_text_with_openmanus,
                args=(text.strip(), current_client_sid),
                daemon=True
            ).start()

    # 直接emit识别结果到前端 - 简单有效的方法
    if _socketio_instance:
        try:
            import asyncio
            import threading

            def emit_result():
                """在新线程中发送识别结果"""
                try:
                    # 创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # 发送识别结果到所有连接的客户端
                    loop.run_until_complete(_socketio_instance.emit('recognition_result', {
                        'text': text,
                        'is_final': is_final,
                        'timestamp': timestamp,
                        'openmanus_processing': is_final and len(text.strip()) > 2  # Indicate if OpenManus will process
                    }))

                    loop.close()
                    # logger.info(f"✅ 已发送识别结果到前端: {text}")

                except Exception as e:
                    logger.error(f"❌ 发送识别结果失败: {e}")

            # 在新线程中发送结果，避免阻塞ASR回调
            threading.Thread(target=emit_result, daemon=True).start()

        except Exception as e:
            logger.error(f"创建发送线程失败: {e}")

def check_silence_timeout():
    """静音检测线程，支持自动重连功能"""
    global is_listening, asr_connected, recognition_active, last_speech_time, recognition_start_time, current_client_sid, auto_reconnect_enabled

    last_log_time = 0

    while True:
        try:
            time.sleep(0.5)  # 500ms检查一次，减少CPU占用

            current_time = time.time()

            # 如果正在监听且有识别开始时间
            if is_listening and recognition_start_time is not None:
                recognition_duration = current_time - recognition_start_time
                silence_duration = current_time - last_speech_time

                # 只在每10秒打印一次状态，大幅减少日志输出
                if current_time - last_log_time >= 10.0:
                    # logger.info(f"静音检测状态 - 识别时长: {recognition_duration:.1f}s, 静音时长: {silence_duration:.1f}s, 自动重连: {auto_reconnect_enabled}")
                    last_log_time = current_time

                # 检查是否需要自动停止（识别时长超过3秒且静音超过阈值）
                if recognition_duration >= 3.0 and silence_duration >= silence_timeout:
                    # logger.info(f"触发自动停止条件 - 识别时长: {recognition_duration:.1f}s >= 3.0s, 静音时长: {silence_duration:.1f}s >= {silence_timeout}s")

                    # 停止语音识别
                    if asr_client and recognition_active:
                        try:
                            asr_client.stop_recognition()
                            # 重置识别状态变量
                            recognition_active = False
                            is_listening = False
                            recognition_start_time = None
                            last_recognized_text = ""
                            # logger.info("语音识别已因静音超时自动停止，状态已重置")

                            # 通知前端识别已停止
                            if _socketio_instance and current_client_sid:
                                try:
                                    import asyncio
                                    loop = asyncio.new_event_loop()
                                    asyncio.set_event_loop(loop)
                                    loop.run_until_complete(
                                        _socketio_instance.emit('recognition_stopped', {
                                            'message': '语音识别已因静音自动停止'
                                        }, room=current_client_sid)
                                    )
                                    loop.close()
                                except Exception as emit_error:
                                    logger.error(f"发送停止通知失败: {emit_error}")

                            # 如果启用了自动重连，则在短暂延迟后自动重新启动识别
                            if auto_reconnect_enabled and current_client_sid:
                                # logger.info("自动重连已启用，将在1秒后重新启动语音识别...")
                                threading.Thread(
                                    target=auto_restart_recognition,
                                    args=(current_client_sid,),
                                    daemon=True
                                ).start()

                        except Exception as e:
                            logger.error(f"自动停止语音识别时出错: {e}")
                            # 即使出错也要重置状态
                            recognition_active = False
                            is_listening = False
                            recognition_start_time = None
                            last_recognized_text = ""

        except Exception as e:
            logger.error(f"静音检测线程出错: {e}")
            time.sleep(1)

def auto_restart_recognition(client_sid):
    """自动重启语音识别的函数"""
    global asr_client, asr_connected, recognition_active, is_listening, recognition_start_time, last_speech_time, last_recognized_text, current_client_sid

    try:
        # 等待1.5秒，确保前一次识别完全停止，并给ASR服务一些恢复时间
        time.sleep(1.5)

        # logger.info(f"🔄 开始自动重启语音识别，客户端: {client_sid}")

        # 检查是否仍然启用自动重连
        if not auto_reconnect_enabled:
            logger.info("自动重连已被禁用，取消重启")
            return

        # 检查客户端是否仍然连接
        if client_sid != current_client_sid:
            logger.info("客户端已更改，取消重启")
            return

        # 检查ASR客户端状态
        if not asr_connected or not asr_client:
            logger.error("ASR服务未连接或客户端未初始化，无法自动重启")
            return

        # 确保当前没有活跃的识别会话
        if recognition_active:
            logger.warning("检测到活跃的识别会话，先停止当前会话")
            try:
                asr_client.stop_recognition()
                time.sleep(0.5)  # 等待停止完成
            except Exception as stop_error:
                logger.error(f"停止当前识别会话失败: {stop_error}")

        # 重置状态
        recognition_active = False
        is_listening = False
        recognition_start_time = None
        last_recognized_text = ""

        # 检查ASR客户端连接状态，如果未连接则尝试重连
        max_reconnect_attempts = 3
        for attempt in range(max_reconnect_attempts):
            if not asr_client.is_connected:
                # logger.info(f"ASR客户端未连接，尝试重新连接... (尝试 {attempt + 1}/{max_reconnect_attempts})")
                if asr_client.connect():
                    # logger.info("ASR客户端重连成功")
                    break
                else:
                    logger.warning(f"ASR客户端重连失败 (尝试 {attempt + 1}/{max_reconnect_attempts})")
                    if attempt < max_reconnect_attempts - 1:
                        time.sleep(2)  # 等待2秒后重试
            else:
                # logger.info("ASR客户端已连接")
                break
        else:
            logger.error("ASR客户端重连失败，无法自动重启识别")
            return

        # 启动语音识别
        # logger.info("🎤 正在自动重启ASR语音识别...")
        success = asr_client.start_recognition(callback=asr_result_callback)
        if success:
            recognition_active = True
            is_listening = True
            current_time = time.time()
            last_speech_time = current_time
            recognition_start_time = current_time
            last_recognized_text = ""
            # logger.info("✅ 语音识别已自动重启成功")

            # 通知前端识别已重新启动
            if _socketio_instance and client_sid:
                try:
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(
                        _socketio_instance.emit('recognition_restarted', {
                            'message': '语音识别已自动重新启动',
                            'silence_timeout': silence_timeout,
                            'auto_reconnect_enabled': auto_reconnect_enabled
                        }, room=client_sid)
                    )
                    loop.close()
                    # logger.info("📡 已通知前端语音识别自动重启")
                except Exception as emit_error:
                    logger.error(f"发送重启通知失败: {emit_error}")
        else:
            logger.error("❌ 自动重启ASR语音识别失败")

    except Exception as e:
        logger.error(f"自动重启语音识别时出错: {e}")
        import traceback
        traceback.print_exc()

# 启动静音检测线程
silence_thread = threading.Thread(target=check_silence_timeout, daemon=True)
silence_thread.start()
logger.info("静音检测线程已启动")

def create_socketio_handlers(sio: socketio.AsyncServer):
    """创建Socket.IO事件处理器"""
    # 设置Socket.IO实例，用于发送识别结果
    set_socketio_instance(sio)
    
    @sio.event
    async def connect(sid, environ):
        """客户端连接事件"""
        logger.info(f"客户端已连接: {sid}")
        await sio.emit('status', {'message': '已连接到语音识别服务'}, room=sid)
    
    @sio.event
    async def disconnect(sid):
        """客户端断开连接事件"""
        logger.info(f"客户端已断开连接: {sid}")
    
    @sio.event
    async def test_connection(sid, data):
        """测试连接事件"""
        logger.info(f"收到测试连接消息: {data}")
        await sio.emit('connection_confirmed', {'message': '连接测试成功'}, room=sid)

    @sio.event
    async def enable_auto_reconnect(sid, data=None):
        """启用自动重连功能"""
        global auto_reconnect_enabled
        auto_reconnect_enabled = True
        logger.info(f"客户端 {sid} 启用了自动重连功能")
        await sio.emit('auto_reconnect_enabled', {
            'message': '自动重连功能已启用',
            'enabled': True
        }, room=sid)

    @sio.event
    async def disable_auto_reconnect(sid, data=None):
        """禁用自动重连功能"""
        global auto_reconnect_enabled
        auto_reconnect_enabled = False
        logger.info(f"客户端 {sid} 禁用了自动重连功能")
        await sio.emit('auto_reconnect_disabled', {
            'message': '自动重连功能已禁用',
            'enabled': False
        }, room=sid)

    @sio.event
    async def get_auto_reconnect_status(sid, data=None):
        """获取自动重连状态"""
        logger.info(f"客户端 {sid} 查询自动重连状态")
        await sio.emit('auto_reconnect_status', {
            'enabled': auto_reconnect_enabled,
            'silence_timeout': silence_timeout
        }, room=sid)
    
    @sio.event
    async def start_recognition(sid, data=None):
        """开始语音识别事件"""
        global asr_client, asr_connected, last_speech_time, is_listening, recognition_start_time, last_recognized_text, recognition_active, current_client_sid, auto_reconnect_enabled

        try:
            # logger.info(f"收到开始语音识别请求，客户端: {sid}")

            # 处理可选的配置参数
            if data and isinstance(data, dict):
                # 如果客户端传递了自动重连设置，则更新全局设置
                if 'auto_reconnect' in data:
                    auto_reconnect_enabled = bool(data['auto_reconnect'])
                    logger.info(f"客户端设置自动重连: {auto_reconnect_enabled}")
            else:
                # 如果没有传递参数，默认启用自动重连
                if not auto_reconnect_enabled:
                    auto_reconnect_enabled = True
                    logger.info("默认启用自动重连功能")

            if not asr_connected or not asr_client:
                logger.error("ASR服务未连接或客户端未初始化")
                await sio.emit('error', {'message': '请先连接到语音识别服务'}, room=sid)
                return

            # 检查是否已经在识别状态
            if recognition_active and asr_client.is_recognizing:
                # logger.info("语音识别已经在运行中，跳过重复启动")
                await sio.emit('recognition_started', {
                    'message': '语音识别已在运行中',
                    'silence_timeout': silence_timeout,
                    'auto_reconnect_enabled': auto_reconnect_enabled
                }, room=sid)
                return

            # 检查ASR客户端连接状态，如果未连接则尝试重连
            if not asr_client.is_connected:
                # logger.info("ASR客户端未连接，尝试重新连接...")
                if not asr_client.connect():
                    logger.error("ASR客户端重连失败")
                    await sio.emit('error', {'message': 'ASR服务连接失败，请稍后重试'}, room=sid)
                    return
                # logger.info("ASR客户端重连成功")

            # 如果之前有识别会话在运行，先停止
            if asr_client.is_recognizing:
                logger.info("停止之前的识别会话...")
                try:
                    asr_client.stop_recognition()
                    time.sleep(0.1)  # 短暂等待停止完成
                except Exception as e:
                    logger.warning(f"停止之前的识别会话时出错: {e}")

            # 启动语音识别
            # logger.info("正在启动ASR语音识别...")
            success = asr_client.start_recognition(callback=asr_result_callback)
            if success:
                recognition_active = True
                is_listening = True
                current_client_sid = sid  # 设置当前客户端ID
                current_time = time.time()
                last_speech_time = current_time
                recognition_start_time = current_time
                last_recognized_text = ""
                logger.info(f"Socket.IO录音状态已设置: {is_listening}")

                message = f'语音识别已启动，将在{silence_timeout}秒静音后自动停止'
                if auto_reconnect_enabled:
                    message += '，并自动重新启动识别'

                await sio.emit('recognition_started', {
                    'message': message,
                    'silence_timeout': silence_timeout,
                    'auto_reconnect_enabled': auto_reconnect_enabled
                }, room=sid)
                # logger.info("语音识别已成功启动")
            else:
                # logger.error("ASR语音识别启动失败")
                await sio.emit('error', {'message': '启动语音识别失败'}, room=sid)

        except Exception as e:
            logger.error(f"开始语音识别时出错: {e}")
            import traceback
            traceback.print_exc()
            await sio.emit('error', {'message': f'启动语音识别失败: {str(e)}'}, room=sid)
    
    @sio.event
    async def stop_recognition(sid, data=None):
        """停止语音识别事件"""
        global asr_client, is_listening, recognition_start_time, recognition_active, last_recognized_text, auto_reconnect_enabled

        try:
            logger.info(f"收到停止语音识别请求，客户端: {sid}")

            # 处理可选的配置参数
            disable_auto_reconnect = False
            if data and isinstance(data, dict):
                # 如果客户端明确要求禁用自动重连
                if 'disable_auto_reconnect' in data and data['disable_auto_reconnect']:
                    disable_auto_reconnect = True
                    auto_reconnect_enabled = False
                    logger.info("客户端请求禁用自动重连功能")

            if asr_client and recognition_active:
                # 停止ASR识别
                asr_client.stop_recognition()

                # 重置所有状态变量
                recognition_active = False
                is_listening = False
                recognition_start_time = None
                last_recognized_text = ""

                # logger.info(f"Socket.IO录音状态已停止: {is_listening}")

                message = '语音识别已停止'
                if disable_auto_reconnect:
                    message += '，自动重连已禁用'

                await sio.emit('recognition_stopped', {
                    'message': message,
                    'auto_reconnect_disabled': disable_auto_reconnect
                }, room=sid)
                # logger.info("语音识别已停止，状态已重置")
            else:
                logger.warning(f"停止识别请求被忽略 - ASR客户端: {bool(asr_client)}, 识别状态: {recognition_active}")
                # 即使没有活跃识别，也要重置状态确保下次可以正常启动
                recognition_active = False
                is_listening = False
                recognition_start_time = None
                last_recognized_text = ""
                await sio.emit('recognition_stopped', {'message': '语音识别已停止'}, room=sid)

        except Exception as e:
            logger.error(f"停止语音识别时出错: {e}")
            # 发生错误时也要重置状态
            recognition_active = False
            is_listening = False
            recognition_start_time = None
            last_recognized_text = ""
            await sio.emit('error', {'message': f'停止语音识别失败: {str(e)}'}, room=sid)
    
    @sio.event
    async def audio_data(sid, data):
        """处理音频数据事件 - 集成VAD检测"""
        global asr_client, last_speech_time, vad_processor
        
        try:
            if not asr_client:
                logger.warning("ASR客户端未初始化，忽略音频数据")
                return
                
            if not is_listening:
                # logger.warning("当前未在录音状态，忽略音频数据")
                return
            
            # 处理不同格式的音频数据
            audio_bytes = None
            use_vad = False

            if isinstance(data, bytes):
                # 直接的字节数据（来自测试工具）
                audio_bytes = data
                logger.debug(f"📡 收到直接字节音频数据，长度: {len(audio_bytes)} 字节")
            elif isinstance(data, dict):
                # 字典格式的数据（来自前端）
                audio_base64 = data.get('audio', '')
                use_vad = data.get('use_vad', False)  # 检查是否启用VAD

                if not audio_base64:
                    logger.warning("收到空的音频数据")
                    return
                try:
                    audio_bytes = base64.b64decode(audio_base64)
                    logger.debug(f"📡 收到base64音频数据，解码后长度: {len(audio_bytes)} 字节，VAD: {use_vad}")
                except Exception as decode_error:
                    logger.error(f"base64解码失败: {decode_error}")
                    return
            else:
                logger.warning(f"不支持的音频数据格式: {type(data)}")
                return

            if not audio_bytes:
                logger.warning("音频数据为空")
                return
            
            try:
                # 音频数据已在上面处理好了，直接发送
                
                # 禁用音频活动检测，完全依赖ASR结果来判断语音活动
                # 这样可以避免环境噪音干扰静音检测
                logger.debug(f"� 发送音频数据到ASR服务，长度: {len(audio_bytes)} 字节")
                
                # 发送音频数据到ASR服务
                asr_client.send_audio(audio_bytes)
                
            except Exception as e:
                logger.error(f"处理音频数据时出错: {e}")
                
        except Exception as e:
            logger.error(f"音频数据处理失败: {e}")

# 连接ASR服务的函数
async def connect_asr_service(use_fast_connection: bool = False):  # 暂时禁用快速连接
    """连接ASR服务"""
    global asr_client, asr_connected

    try:
        if asr_connected:
            return {"success": True, "message": "已经连接到ASR服务"}

        # 根据参数选择连接方式
        if use_fast_connection:
            logger.info("使用快速连接模式")
            asr_client = FastVolcanoASRClient(
                app_key=ASR_CONFIG["app_key"],
                access_key=ASR_CONFIG["access_key"],
                model_name=ASR_CONFIG["model_name"],
                sample_rate=ASR_CONFIG["sample_rate"],
                channels=ASR_CONFIG["channels"]
            )
        else:
            logger.info("使用标准连接模式")
            asr_client = VolcanoASRClient(
                app_key=ASR_CONFIG["app_key"],
                access_key=ASR_CONFIG["access_key"],
                model_name=ASR_CONFIG["model_name"],
                sample_rate=ASR_CONFIG["sample_rate"],
                channels=ASR_CONFIG["channels"],
                callback=asr_result_callback
            )
        
        # 连接到ASR服务
        success = asr_client.connect()
        if success:
            asr_connected = True
            connection_type = "快速连接" if use_fast_connection else "标准连接"
            # logger.info(f"成功连接到火山引擎ASR服务 ({connection_type})")

            # 初始化VAD检测器
            vad_init_success = init_vad_detector()
            if not vad_init_success:
                logger.warning("VAD检测器初始化失败，将使用传统模式")

            # 获取连接统计信息（如果支持）
            stats = {}
            if hasattr(asr_client, 'get_stats'):
                stats = asr_client.get_stats()

            return {
                "success": True,
                "message": f"成功连接到语音识别服务 ({connection_type})",
                "connection_id": getattr(asr_client, 'connect_id', None),
                "connection_type": connection_type,
                "connection_stats": stats,
                "model_info": {
                    "model_name": ASR_CONFIG["model_name"],
                    "sample_rate": ASR_CONFIG["sample_rate"],
                    "channels": ASR_CONFIG["channels"]
                }
            }
        else:
            return {"success": False, "message": "连接到语音识别服务失败"}
            
    except Exception as e:
        logger.error(f"连接ASR服务时出错: {e}")
        return {"success": False, "message": f"连接失败: {str(e)}"}

# 获取ASR客户端的函数
def get_asr_client():
    """获取ASR客户端实例"""
    return asr_client

def get_asr_status():
    """获取ASR服务状态"""
    return {
        "connected": asr_connected,
        "client_exists": asr_client is not None,
        "recognition_active": recognition_active,
        "is_listening": is_listening
    }
