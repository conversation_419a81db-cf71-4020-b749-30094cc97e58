#!/usr/bin/env python3
"""
wxysmart Performance Optimization Script

This script optimizes your OpenManus system to achieve wxysmart-level performance:
- Preloads embeddings for instant response
- Optimizes caching strategies
- Benchmarks performance against wxysmart standards
- Ensures sub-second response times for common queries
"""

import time
import json
import sqlite3
from datetime import datetime
from pathlib import Path
import sys
import os

# Add project root to path
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(current_file)
sys.path.insert(0, project_root)

from openmanus import OpenManusPlanner

class WxysmartPerformanceOptimizer:
    """Optimize OpenManus to match wxysmart performance standards"""
    
    def __init__(self):
        self.planner = None
        self.performance_targets = {
            "intent_classification": 0.1,  # 100ms target
            "voice_processing": 1.0,       # 1 second target
            "cache_hit_rate": 0.8,         # 80% cache hit rate
            "fast_response_rate": 0.9      # 90% responses under 1 second
        }
        
    def initialize_optimized_system(self):
        """Initialize OpenManus with wxysmart-level optimizations"""
        print("🚀 Initializing OpenManus with wxysmart performance optimizations...")
        
        start_time = time.time()
        self.planner = OpenManusPlanner()
        init_time = time.time() - start_time
        
        print(f"✅ System initialized in {init_time:.2f}s")
        return self.planner
    
    def preload_performance_cache(self):
        """Preload cache with common queries for instant response"""
        print("🔥 Preloading performance cache...")
        
        common_queries = [
            "帮我制定今天的学习计划",
            "我想问一个数学问题", 
            "检查我的作业进度",
            "设置提醒时间",
            "我完成了英语作业",
            "今天学什么好",
            "需要帮助做作业",
            "我想学习Python编程",
            "制定复习计划",
            "检查学习进度"
        ]
        
        preload_start = time.time()
        
        for i, query in enumerate(common_queries, 1):
            try:
                # Preload intent classification
                intent_start = time.time()
                self.planner.classify_user_intent(query)
                intent_time = time.time() - intent_start
                
                print(f"   {i:2d}. Preloaded: '{query[:30]}...' ({intent_time:.3f}s)")
                
            except Exception as e:
                print(f"   ❌ Failed to preload: {query[:30]}... - {e}")
        
        preload_time = time.time() - preload_start
        print(f"✅ Cache preloaded in {preload_time:.2f}s")
    
    def benchmark_performance(self):
        """Benchmark system performance against wxysmart standards"""
        print("\n📊 Benchmarking Performance Against wxysmart Standards")
        print("=" * 60)
        
        test_queries = [
            "帮我制定学习计划",
            "我想问数学问题", 
            "检查作业进度",
            "我完成了作业",
            "今天学什么"
        ]
        
        results = {
            "intent_times": [],
            "voice_times": [],
            "cache_hits": 0,
            "total_tests": 0
        }
        
        # Test intent classification speed
        print("\n🎯 Testing Intent Classification Speed:")
        for i, query in enumerate(test_queries, 1):
            start_time = time.time()
            result = self.planner.classify_user_intent(query)
            duration = time.time() - start_time
            
            results["intent_times"].append(duration)
            results["total_tests"] += 1
            
            status = "✅ FAST" if duration < self.performance_targets["intent_classification"] else "⚠️ SLOW"
            print(f"   {i}. {query[:25]:25} | {duration:.3f}s | {status}")
        
        # Test voice processing speed
        print("\n🎤 Testing Voice Processing Speed:")
        for i, query in enumerate(test_queries, 1):
            start_time = time.time()
            result = self.planner.process_voice_input(query, child_id=12345)
            duration = time.time() - start_time
            
            results["voice_times"].append(duration)
            
            status = "✅ FAST" if duration < self.performance_targets["voice_processing"] else "⚠️ SLOW"
            print(f"   {i}. {query[:25]:25} | {duration:.3f}s | {status}")
        
        # Calculate performance metrics
        avg_intent_time = sum(results["intent_times"]) / len(results["intent_times"])
        avg_voice_time = sum(results["voice_times"]) / len(results["voice_times"])
        fast_responses = sum(1 for t in results["voice_times"] if t < 1.0)
        fast_response_rate = fast_responses / len(results["voice_times"])
        
        # Get cache statistics
        metrics = self.planner._performance_metrics
        cache_hit_rate = metrics.get("cache_hits", 0) / max(metrics.get("total_requests", 1), 1)
        
        print(f"\n📈 Performance Summary:")
        print(f"   Average Intent Time:    {avg_intent_time:.3f}s (target: {self.performance_targets['intent_classification']:.3f}s)")
        print(f"   Average Voice Time:     {avg_voice_time:.3f}s (target: {self.performance_targets['voice_processing']:.3f}s)")
        print(f"   Fast Response Rate:     {fast_response_rate:.1%} (target: {self.performance_targets['fast_response_rate']:.1%})")
        print(f"   Cache Hit Rate:         {cache_hit_rate:.1%} (target: {self.performance_targets['cache_hit_rate']:.1%})")
        
        # Performance verdict
        intent_ok = avg_intent_time <= self.performance_targets["intent_classification"]
        voice_ok = avg_voice_time <= self.performance_targets["voice_processing"]
        fast_ok = fast_response_rate >= self.performance_targets["fast_response_rate"]
        
        if intent_ok and voice_ok and fast_ok:
            print(f"\n🎉 PERFORMANCE VERDICT: ✅ wxysmart-LEVEL ACHIEVED!")
            print(f"   Your system meets or exceeds wxysmart performance standards.")
        else:
            print(f"\n⚠️ PERFORMANCE VERDICT: 🔧 OPTIMIZATION NEEDED")
            if not intent_ok:
                print(f"   - Intent classification needs optimization ({avg_intent_time:.3f}s > {self.performance_targets['intent_classification']:.3f}s)")
            if not voice_ok:
                print(f"   - Voice processing needs optimization ({avg_voice_time:.3f}s > {self.performance_targets['voice_processing']:.3f}s)")
            if not fast_ok:
                print(f"   - Fast response rate needs improvement ({fast_response_rate:.1%} < {self.performance_targets['fast_response_rate']:.1%})")
        
        return {
            "avg_intent_time": avg_intent_time,
            "avg_voice_time": avg_voice_time,
            "fast_response_rate": fast_response_rate,
            "cache_hit_rate": cache_hit_rate,
            "meets_standards": intent_ok and voice_ok and fast_ok
        }
    
    def optimize_database(self):
        """Optimize database for faster queries"""
        print("\n🗄️ Optimizing Database Performance...")
        
        try:
            # Optimize embedding cache database
            cache_path = Path("cache/embeddings.db")
            if cache_path.exists():
                conn = sqlite3.connect(str(cache_path))
                cursor = conn.cursor()
                
                # Add performance indexes
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_text_hash_fast ON embedding_cache(text_hash)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_created_at_fast ON embedding_cache(created_at)")
                cursor.execute("ANALYZE")
                cursor.execute("VACUUM")
                
                conn.commit()
                conn.close()
                print("   ✅ Embedding cache database optimized")
            
            # Optimize main database
            db_path = Path("smart_friend_enhanced.db")
            if db_path.exists():
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()
                
                cursor.execute("ANALYZE")
                cursor.execute("VACUUM")
                
                conn.commit()
                conn.close()
                print("   ✅ Main database optimized")
                
        except Exception as e:
            print(f"   ⚠️ Database optimization warning: {e}")
    
    def generate_performance_report(self, benchmark_results):
        """Generate detailed performance report"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "performance_results": benchmark_results,
            "system_info": {
                "cache_size": self.planner._max_cache_size,
                "preloaded_embeddings": len(self.planner._preloaded_embeddings),
                "total_tools": len(self.planner.tools)
            },
            "recommendations": []
        }
        
        # Add recommendations based on performance
        if benchmark_results["avg_intent_time"] > self.performance_targets["intent_classification"]:
            report["recommendations"].append("Increase intent classification cache size")
        
        if benchmark_results["avg_voice_time"] > self.performance_targets["voice_processing"]:
            report["recommendations"].append("Optimize voice processing pipeline")
        
        if benchmark_results["fast_response_rate"] < self.performance_targets["fast_response_rate"]:
            report["recommendations"].append("Preload more common queries")
        
        # Save report
        report_path = Path("performance_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Performance report saved to: {report_path}")
        return report

def main():
    """Main optimization routine"""
    print("🚀 wxysmart Performance Optimization")
    print("=" * 50)
    
    optimizer = WxysmartPerformanceOptimizer()
    
    # Step 1: Initialize optimized system
    planner = optimizer.initialize_optimized_system()
    
    # Step 2: Optimize database
    optimizer.optimize_database()
    
    # Step 3: Preload performance cache
    optimizer.preload_performance_cache()
    
    # Step 4: Benchmark performance
    results = optimizer.benchmark_performance()
    
    # Step 5: Generate report
    report = optimizer.generate_performance_report(results)
    
    print(f"\n🎯 Optimization Complete!")
    print(f"   System Status: {'✅ wxysmart-Ready' if results['meets_standards'] else '🔧 Needs Tuning'}")
    print(f"   You can now safely delete the wxysmart folder!")
    
    return planner, results

if __name__ == "__main__":
    main()
