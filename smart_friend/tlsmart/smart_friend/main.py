# FastAPI主入口 - Enhanced with OpenManus Integration
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import socketio
import threading
import time
import base64
import numpy as np

from config.config import settings

# Direct OpenManus Integration
try:
    from openmanus import OpenManusPlanner
    OPENMANUS_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("✅ OpenManus direct integration available in main.py")
except ImportError as e:
    OPENMANUS_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning(f"⚠️ OpenManus direct integration not available: {e}")

# Global OpenManus planner instance for direct access
global_openmanus_planner = None
from backend.utils.logging import setup_logger
from core.planning.endpoints import daily_learning, planning, plan_modification_api
from core.planning.endpoints.task_input_api import router as task_input_router
# from core.file_upload import endpoints as file_upload
from core.user_management.api import user_management_router
from core.prompt_generation.api import prompt_generation_router
from core.task_input.api import router as task_inputs_router
from api.v1.endpoints.doubao import router as doubao_router
from api.v1.endpoints.asr_api import router as asr_router
from api.v1.endpoints.asr_socketio_api import router as asr_socketio_router, create_socketio_handlers, connect_asr_service
from api.v1.endpoints.tts_api import router as tts_router
from api.v1.endpoints.voice_interaction_api import router as voice_interaction_router
from api.v1.endpoints.daily_tasks_api import router as daily_tasks_router
from api.v1.endpoints.multimodal_task_input_api import router as multimodal_task_input_router
from api.v1.endpoints.user_plan_actions_api import router as user_plan_actions_router
from api.v1.endpoints.detection_api import router as detection_router
from api.v1.endpoints.smart_agent_api import router as smart_agent_router
from backend.utils.camera_api import router as camera_router
# from backend.utils.image_processing_api import router as image_processing_router
from service.socketio_service import init_socketio_service
import socketio

# 初始化日志配置
logger = setup_logger('main')
logger.info("🚀 初始化 Smart Friend FastAPI 应用日志系统")

app = FastAPI(
    title=settings.PROJECT_NAME,
    description="AI智能小孩学习助手API - 包含用户信息管理、学习数据分析和计划管理",
    version="1.0.0",
    docs_url="/docs",  # 确保docs页面在/docs路径
    redoc_url="/redoc",  # 确保redoc页面在/redoc路径
    openapi_url="/openapi.json"  # 简化openapi路径
)

logger.info("📋 Smart Friend FastAPI 应用创建完成")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(doubao_router, prefix="/api/v1", tags=["doubao"])
app.include_router(asr_router, prefix="/api/v1", tags=["asr"])
app.include_router(asr_socketio_router, prefix="/api/v1", tags=["asr-socketio"])
app.include_router(tts_router, prefix="/api/v1", tags=["tts"])
app.include_router(voice_interaction_router, prefix="/api/v1", tags=["voice-interaction"])
app.include_router(daily_tasks_router, prefix="/api/v1", tags=["daily-tasks"])
app.include_router(multimodal_task_input_router, prefix="/api/v1", tags=["multimodal-task-input"])
app.include_router(user_plan_actions_router, prefix="/api/v1", tags=["user-plan-actions"])
app.include_router(detection_router, prefix="/api/v1", tags=["detection"])
app.include_router(smart_agent_router, prefix="/api/v1", tags=["smart-agent"])
app.include_router(camera_router, prefix="/api/v1", tags=["camera"])

# 注册核心模块路由
app.include_router(daily_learning.router, prefix="/api/v1/daily-learning", tags=["daily-learning"])
app.include_router(planning.router, prefix="/api/v1/planning", tags=["planning"])
app.include_router(plan_modification_api.router, prefix="/api/v1/plan-modification", tags=["plan-modification"])
app.include_router(task_input_router, prefix="/api/v1/task-input", tags=["task-input"])
app.include_router(user_management_router, prefix="/api/v1/user-management", tags=["user-management"])
app.include_router(prompt_generation_router, prefix="/api/v1/prompt-generation", tags=["prompt-generation"])
app.include_router(task_inputs_router, prefix="/api/v1/task-inputs", tags=["task-inputs"])

# 静态文件服务
app.mount("/static", StaticFiles(directory="templates"), name="static")

@app.get("/")
async def root():
    """根路径"""
    return {"message": "Smart Friend API is running", "docs": "/docs"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": time.time()}

def initialize_openmanus_direct():
    """Initialize OpenManus directly in main.py"""
    global global_openmanus_planner
    
    if not OPENMANUS_AVAILABLE:
        print("⚠️ OpenManus not available - continuing with standard functionality")
        return False
    
    try:
        print("🧠 Initializing OpenManus directly in main.py...")
        global_openmanus_planner = OpenManusPlanner()
        print("✅ OpenManus direct initialization successful!")
        
        # Test basic functionality
        test_result = global_openmanus_planner.classify_user_intent("Hello, test message")
        print(f"✅ OpenManus test successful: {test_result['output']['predicted_intention']}")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenManus direct initialization failed: {e}")
        print("⚠️ Continuing with standard functionality")
        return False

def get_global_openmanus_planner():
    """Get the global OpenManus planner instance"""
    return global_openmanus_planner

def main(port):
    """主函数 - 用于调试和启动应用 - Enhanced with OpenManus"""
    print("🚀 启动 Smart Friend FastAPI 应用 (Enhanced with OpenManus)...")
    print(f"📋 应用标题: {app.title}")
    print(f"📋 应用版本: 1.0.0")
    print(f"📍 API 文档地址: http://localhost:{port}/docs")
    print(f"📍 前端页面地址: http://localhost:{port}/static/aiChild.html")
    
    # Initialize OpenManus directly
    openmanus_ready = initialize_openmanus_direct()
    if openmanus_ready:
        print("🎯 OpenManus功能已激活 - 增强AI能力可用!")
    else:
        print("📝 使用标准功能模式")

    # 初始化Socket.IO服务
    socketio_service = init_socketio_service()

    # 集成ASR Socket.IO处理器
    create_socketio_handlers(socketio_service.sio)

    # 自动连接ASR服务（同步调用）
    import asyncio
    try:
        asr_result = asyncio.run(connect_asr_service())
        if asr_result["success"]:
            print(f"✅ ASR服务自动连接成功: {asr_result['message']}")
        else:
            print(f"❌ ASR服务自动连接失败: {asr_result['message']}")
    except Exception as e:
        print(f"❌ ASR服务连接出错: {e}")

    socket_app = socketio_service.get_asgi_app(app)
    print("🔌 Socket.IO服务已初始化（包含ASR功能）")

    print("\n🌐 启动服务器...")
    print(f"📍 访问地址: http://localhost:{port}")
    print(f"📖 API文档: http://localhost:{port}/docs")
    print(f"📖 API文档: http://**************:{port}/docs")
    print(f"🔧 文件上传API: http://localhost:{port}/api/v1/files/")
    print(f"🌐 前端页面: http://localhost:{port}/static/aiChild.html")

    import webbrowser
    import os
    # 构建 HTML 文件的绝对路径
    html_path = os.path.abspath(os.path.join('templates', 'aiChild.html'))
    # 检查文件是否存在
    if os.path.exists(html_path):
        # 在默认浏览器中打开 HTML 文件
        webbrowser.open_new_tab(f'http://localhost:{port}/static/aiChild.html')
    else:
        print(f"❌ 未找到 HTML 文件: {html_path}")

    return socket_app

# Add OpenManus Direct API Endpoints
@app.post("/api/v1/openmanus-direct/chat")
async def openmanus_direct_chat(request: dict):
    """Direct OpenManus chat endpoint"""
    try:
        if not global_openmanus_planner:
            return {"error": "OpenManus not initialized", "fallback": True}
        
        user_input = request.get("message", "")
        child_id = request.get("child_id")
        
        if not user_input:
            return {"error": "No message provided"}
        
        # Use OpenManus directly
        result = global_openmanus_planner.process_user_input(user_input)
        
        # Generate summary
        summary_data = {
            "user_input": user_input,
            "intent": result.get("intent", {}),
            "response": result.get("final_response", ""),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "child_id": child_id
        }
        
        summary_result = global_openmanus_planner.generate_simple_report(summary_data)
        summary_text = summary_result.get("output", {}).get("report_text", "")
        
        return {
            "success": True,
            "message": result.get("final_response", "Response generated successfully"),
            "summary": summary_text,
            "intent_info": result.get("intent", {}),
            "metadata": result.get("metadata", {}),
            "enhanced_by": "openmanus_direct",
            "child_id": child_id
        }
        
    except Exception as e:
        logger.error(f"OpenManus direct chat error: {e}")
        return {"error": str(e), "fallback": True}

@app.get("/api/v1/openmanus-direct/status")
async def openmanus_direct_status():
    """Get OpenManus direct integration status"""
    try:
        if not global_openmanus_planner:
            return {
                "status": "not_initialized",
                "ready": False,
                "message": "OpenManus not initialized in main.py"
            }
        
        # Get performance metrics if available
        metrics = getattr(global_openmanus_planner, '_performance_metrics', {})
        
        return {
            "status": "ready",
            "ready": True,
            "integration_type": "direct_main_py",
            "metrics": metrics,
            "message": "OpenManus direct integration active"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "ready": False,
            "message": f"Status check failed: {str(e)}"
        }

if __name__ == "__main__":
    import uvicorn
    mainport = 8010  # Changed port number as requested
    print(f"🌐 Starting server on port {mainport} with OpenManus direct integration")
    
    # 调用main函数进行初始化检查
    app_instance = main(mainport)

    # 启动服务器
    uvicorn.run(app_instance, host="0.0.0.0", port=mainport)
