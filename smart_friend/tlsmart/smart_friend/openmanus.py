#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OpenManus - Advanced AI Planning and Reasoning System

This module provides comprehensive AI capabilities including:
- Intent classification using advanced embeddings
- Task planning and execution
- Context-aware reasoning
- Learning progress tracking
- Intelligent response generation

Author: OpenManus Team
Version: 2.0.0
"""

import json
import os
import time
import logging
import sqlite3
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import requests

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpenManusPlanner:
    """
    Advanced AI Planning and Reasoning System
    
    Provides comprehensive AI capabilities for educational assistance,
    task planning, intent classification, and intelligent response generation.
    """
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2", cache_dir: str = "cache"):
        """
        Initialize OpenManus Planner
        
        Args:
            model_name: Sentence transformer model name
            cache_dir: Directory for caching embeddings and data
        """
        self.model_name = model_name
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Initialize embedding model
        logger.info(f"Loading sentence transformer model: {model_name}")
        self.embedding_model = SentenceTransformer(model_name)
        
        # Initialize cache database
        self.cache_db_path = self.cache_dir / "embeddings.db"
        self._init_cache_db()
        
        # Performance metrics
        self._performance_metrics = {
            "total_requests": 0,
            "cache_hits": 0,
            "average_response_time": 0.0,
            "successful_plans": 0,
            "failed_plans": 0,
            "intent_cache_hits": 0,
            "embedding_cache_hits": 0,
            "fast_responses": 0
        }
        
        # Intent classification data
        self.intent_data = []
        self.intent_embeddings = None
        self.intent_labels = []
        
        # Load intent data if available
        self._load_intent_data()
        
        # Preload common embeddings for performance
        self._preload_common_embeddings()
        
        logger.info("✅ OpenManus Planner initialized successfully")
    
    def _init_cache_db(self):
        """Initialize SQLite cache database"""
        try:
            with sqlite3.connect(self.cache_db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS embeddings (
                        text_hash TEXT PRIMARY KEY,
                        text TEXT,
                        embedding BLOB,
                        model_name TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS intent_cache (
                        text_hash TEXT PRIMARY KEY,
                        text TEXT,
                        predicted_class INTEGER,
                        predicted_intention TEXT,
                        confidence REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to initialize cache database: {e}")
    
    def _load_intent_data(self):
        """Load intent classification data"""
        intent_file = Path("Intent_classification_100_data.json")
        if intent_file.exists():
            try:
                with open(intent_file, 'r', encoding='utf-8') as f:
                    self.intent_data = json.load(f)
                logger.info(f"Loaded {len(self.intent_data)} intent examples")
                self._generate_intent_embeddings()
            except Exception as e:
                logger.warning(f"Failed to load intent data: {e}")
        else:
            logger.warning("Intent classification data not found")
    
    def _generate_intent_embeddings(self):
        """Generate embeddings for intent classification data"""
        if not self.intent_data:
            return
        
        try:
            texts = [item['text'] for item in self.intent_data]
            self.intent_labels = [item['intent'] for item in self.intent_data]
            
            logger.info("Generating intent embeddings...")
            self.intent_embeddings = self.embedding_model.encode(texts)
            logger.info(f"Generated embeddings for {len(texts)} intent examples")
        except Exception as e:
            logger.error(f"Failed to generate intent embeddings: {e}")
    
    def _preload_common_embeddings(self):
        """Preload embeddings for common phrases"""
        common_phrases = [
            "I want to learn math today",
            "Help me with homework",
            "Create a study plan",
            "I need help with science",
            "What should I study?",
            "I'm having trouble with reading",
            "Can you help me practice?"
        ]
        
        print("🚀 Preloading common embeddings for optimal performance...")
        try:
            self.get_embeddings_batch(common_phrases)
            print(f"✅ Preloaded {len(common_phrases)} common embeddings")
        except Exception as e:
            logger.error(f"Failed to preload embeddings: {e}")
    
    def get_text_hash(self, text: str) -> str:
        """Generate hash for text caching"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def get_embeddings_batch(self, texts: List[str]) -> np.ndarray:
        """
        Get embeddings for a batch of texts with caching
        
        Args:
            texts: List of texts to embed
            
        Returns:
            numpy array of embeddings
        """
        embeddings = []
        texts_to_embed = []
        indices_to_embed = []
        
        # Check cache first
        for i, text in enumerate(texts):
            text_hash = self.get_text_hash(text)
            cached_embedding = self._get_cached_embedding(text_hash)
            
            if cached_embedding is not None:
                embeddings.append(cached_embedding)
                self._performance_metrics["embedding_cache_hits"] += 1
            else:
                embeddings.append(None)
                texts_to_embed.append(text)
                indices_to_embed.append(i)
        
        # Generate embeddings for uncached texts
        if texts_to_embed:
            print(f"📊 Cache stats: {self._performance_metrics['embedding_cache_hits']}/{len(texts)} cached. Generating {len(texts_to_embed)} new embeddings.")
            new_embeddings = self.embedding_model.encode(texts_to_embed)
            
            # Cache new embeddings and update results
            for j, idx in enumerate(indices_to_embed):
                embedding = new_embeddings[j]
                embeddings[idx] = embedding
                
                # Cache the embedding
                text_hash = self.get_text_hash(texts[idx])
                self._cache_embedding(text_hash, texts[idx], embedding)
        
        return np.array(embeddings)
    
    def _get_cached_embedding(self, text_hash: str) -> Optional[np.ndarray]:
        """Get cached embedding"""
        try:
            with sqlite3.connect(self.cache_db_path) as conn:
                cursor = conn.execute(
                    "SELECT embedding FROM embeddings WHERE text_hash = ? AND model_name = ?",
                    (text_hash, self.model_name)
                )
                result = cursor.fetchone()
                if result:
                    return np.frombuffer(result[0], dtype=np.float32)
        except Exception as e:
            logger.error(f"Failed to get cached embedding: {e}")
        return None
    
    def _cache_embedding(self, text_hash: str, text: str, embedding: np.ndarray):
        """Cache embedding"""
        try:
            with sqlite3.connect(self.cache_db_path) as conn:
                conn.execute(
                    "INSERT OR REPLACE INTO embeddings (text_hash, text, embedding, model_name) VALUES (?, ?, ?, ?)",
                    (text_hash, text, embedding.astype(np.float32).tobytes(), self.model_name)
                )
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to cache embedding: {e}")
    
    def classify_user_intent(self, text: str) -> Dict[str, Any]:
        """
        Classify user intent using embeddings and similarity matching
        
        Args:
            text: User input text
            
        Returns:
            Dictionary containing classification results
        """
        start_time = time.time()
        self._performance_metrics["total_requests"] += 1
        
        # Check intent cache first
        text_hash = self.get_text_hash(text)
        cached_result = self._get_cached_intent(text_hash)
        
        if cached_result:
            self._performance_metrics["intent_cache_hits"] += 1
            self._performance_metrics["cache_hits"] += 1
            return {
                "success": True,
                "output": cached_result,
                "processing_time": time.time() - start_time,
                "cached": True
            }
        
        try:
            print(f"Classifying intent for: {text[:50]}...")
            
            if not self.intent_embeddings is not None:
                logger.warning("⚠️ No intent data found. Please run generate_embeddings_for_intent_data() first.")
                # Return default classification
                default_result = {
                    "predicted_class": 0,
                    "predicted_intention": "daily_chat",
                    "confidence": 0.0,
                    "similar_examples": []
                }
                return {
                    "success": True,
                    "output": default_result,
                    "processing_time": time.time() - start_time,
                    "cached": False
                }
            
            # Get embedding for input text
            input_embedding = self.get_embeddings_batch([text])[0]
            
            # Calculate similarities
            similarities = cosine_similarity([input_embedding], self.intent_embeddings)[0]
            
            # Find best match
            best_match_idx = np.argmax(similarities)
            confidence = float(similarities[best_match_idx])
            predicted_intention = self.intent_labels[best_match_idx]
            
            # Get similar examples
            top_indices = np.argsort(similarities)[-3:][::-1]
            similar_examples = []
            for idx in top_indices:
                if idx < len(self.intent_data):
                    similar_examples.append({
                        "text": self.intent_data[idx]["text"],
                        "intent": self.intent_data[idx]["intent"],
                        "similarity": float(similarities[idx])
                    })
            
            result = {
                "predicted_class": int(best_match_idx),
                "predicted_intention": predicted_intention,
                "confidence": confidence,
                "similar_examples": similar_examples
            }
            
            # Cache the result
            self._cache_intent_result(text_hash, text, result)
            
            processing_time = time.time() - start_time
            self._update_performance_metrics(processing_time)
            
            return {
                "success": True,
                "output": result,
                "processing_time": processing_time,
                "cached": False
            }
            
        except Exception as e:
            logger.error(f"Intent classification failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "output": {
                    "predicted_class": 0,
                    "predicted_intention": "daily_chat",
                    "confidence": 0.0,
                    "similar_examples": []
                }
            }
    
    def _get_cached_intent(self, text_hash: str) -> Optional[Dict[str, Any]]:
        """Get cached intent classification result"""
        try:
            with sqlite3.connect(self.cache_db_path) as conn:
                cursor = conn.execute(
                    "SELECT predicted_class, predicted_intention, confidence FROM intent_cache WHERE text_hash = ?",
                    (text_hash,)
                )
                result = cursor.fetchone()
                if result:
                    return {
                        "predicted_class": result[0],
                        "predicted_intention": result[1],
                        "confidence": result[2],
                        "similar_examples": []
                    }
        except Exception as e:
            logger.error(f"Failed to get cached intent: {e}")
        return None
    
    def _cache_intent_result(self, text_hash: str, text: str, result: Dict[str, Any]):
        """Cache intent classification result"""
        try:
            with sqlite3.connect(self.cache_db_path) as conn:
                conn.execute(
                    "INSERT OR REPLACE INTO intent_cache (text_hash, text, predicted_class, predicted_intention, confidence) VALUES (?, ?, ?, ?, ?)",
                    (text_hash, text, result["predicted_class"], result["predicted_intention"], result["confidence"])
                )
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to cache intent result: {e}")
    
    def _update_performance_metrics(self, processing_time: float):
        """Update performance metrics"""
        self._performance_metrics["average_response_time"] = (
            (self._performance_metrics["average_response_time"] * (self._performance_metrics["total_requests"] - 1) + processing_time) /
            self._performance_metrics["total_requests"]
        )
        
        if processing_time < 1.0:  # Fast response threshold
            self._performance_metrics["fast_responses"] += 1

    def process_user_input(self, user_input: str, child_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Process user input with comprehensive AI analysis

        Args:
            user_input: User's input text
            child_id: Optional child ID for personalization

        Returns:
            Dictionary containing processed results
        """
        start_time = time.time()

        try:
            # Step 1: Intent Classification
            intent_result = self.classify_user_intent(user_input)
            intent_info = intent_result.get("output", {})

            # Step 2: Context Analysis
            context_analysis = self._analyze_context(user_input, intent_info)

            # Step 3: Generate Response
            response = self._generate_response(user_input, intent_info, context_analysis)

            # Step 4: Create metadata
            metadata = {
                "processing_time": time.time() - start_time,
                "intent_confidence": intent_info.get("confidence", 0.0),
                "context_analysis": context_analysis,
                "child_id": child_id,
                "timestamp": datetime.now().isoformat()
            }

            return {
                "success": True,
                "final_response": response,
                "intent": intent_info,
                "metadata": metadata
            }

        except Exception as e:
            logger.error(f"Failed to process user input: {e}")
            return {
                "success": False,
                "error": str(e),
                "final_response": "I'm sorry, I encountered an error processing your request.",
                "intent": {},
                "metadata": {}
            }

    def _analyze_context(self, user_input: str, intent_info: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze context of user input"""
        try:
            # Basic context analysis
            word_count = len(user_input.split())
            has_question = "?" in user_input
            has_urgency = any(word in user_input.lower() for word in ["urgent", "quickly", "asap", "now", "immediately"])

            # Educational level indicators
            complexity_indicators = {
                "basic": ["help", "simple", "easy", "basic"],
                "intermediate": ["explain", "understand", "learn", "study"],
                "advanced": ["analyze", "complex", "detailed", "comprehensive"]
            }

            detected_level = "basic"
            for level, indicators in complexity_indicators.items():
                if any(indicator in user_input.lower() for indicator in indicators):
                    detected_level = level
                    break

            return {
                "word_count": word_count,
                "has_question": has_question,
                "has_urgency": has_urgency,
                "detected_level": detected_level,
                "intent": intent_info.get("predicted_intention", "unknown")
            }

        except Exception as e:
            logger.error(f"Context analysis failed: {e}")
            return {"error": str(e)}

    def _generate_response(self, user_input: str, intent_info: Dict[str, Any], context_analysis: Dict[str, Any]) -> str:
        """Generate appropriate response based on intent and context"""
        try:
            intent = intent_info.get("predicted_intention", "daily_chat")
            confidence = intent_info.get("confidence", 0.0)

            # Response templates based on intent
            if intent == "study_create_plan":
                return self._generate_study_plan_response(user_input, context_analysis)
            elif intent == "study_modify_plan":
                return self._generate_plan_modification_response(user_input, context_analysis)
            elif intent == "study_execute_task":
                return self._generate_task_execution_response(user_input, context_analysis)
            elif intent == "study_review_progress":
                return self._generate_progress_review_response(user_input, context_analysis)
            else:  # daily_chat or unknown
                return self._generate_daily_chat_response(user_input, context_analysis)

        except Exception as e:
            logger.error(f"Response generation failed: {e}")
            return "I'm here to help you learn! What would you like to study today?"

    def _generate_study_plan_response(self, user_input: str, context: Dict[str, Any]) -> str:
        """Generate response for study plan creation"""
        level = context.get("detected_level", "basic")

        if level == "basic":
            return "I'd love to help you create a study plan! What subject would you like to focus on? We can start with something simple and fun."
        elif level == "intermediate":
            return "Great! Let's create a comprehensive study plan. What subject are you interested in, and how much time do you have available for studying?"
        else:
            return "Excellent! I can help you develop a detailed, structured study plan. Please tell me your learning objectives, available time, and any specific topics you'd like to cover."

    def _generate_plan_modification_response(self, user_input: str, context: Dict[str, Any]) -> str:
        """Generate response for plan modification"""
        return "I can help you adjust your study plan! What changes would you like to make? We can modify the schedule, add new topics, or change the difficulty level."

    def _generate_task_execution_response(self, user_input: str, context: Dict[str, Any]) -> str:
        """Generate response for task execution"""
        return "Let's work on your task together! I'm here to guide you through each step. What specific help do you need?"

    def _generate_progress_review_response(self, user_input: str, context: Dict[str, Any]) -> str:
        """Generate response for progress review"""
        return "Great job on wanting to review your progress! Let's look at what you've accomplished and plan your next steps."

    def _generate_daily_chat_response(self, user_input: str, context: Dict[str, Any]) -> str:
        """Generate response for daily chat"""
        if context.get("has_question"):
            return "That's a great question! I'm here to help you learn and explore new things. What would you like to know more about?"
        else:
            return "Hello! I'm excited to help you learn today. What subject interests you most right now?"

    def create_task_plan(self, objective: str, resources: str = "", context: str = "", child_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Create a comprehensive task plan using AI

        Args:
            objective: Learning objective
            resources: Available resources
            context: Additional context
            child_id: Optional child ID

        Returns:
            Dictionary containing the generated plan
        """
        start_time = time.time()

        try:
            # Analyze the objective
            objective_analysis = self._analyze_objective(objective)

            # Generate plan structure
            plan_structure = self._generate_plan_structure(objective, resources, context, objective_analysis)

            # Create detailed plan
            detailed_plan = self._create_detailed_plan(plan_structure, objective_analysis)

            # Add metadata
            metadata = {
                "objective": objective,
                "resources": resources,
                "context": context,
                "child_id": child_id,
                "created_at": datetime.now().isoformat(),
                "processing_time": time.time() - start_time
            }

            self._performance_metrics["successful_plans"] += 1

            return {
                "success": True,
                "plan": {
                    "objective": objective,
                    "detailed_plan": detailed_plan,
                    "context_used": f"Available resources: {resources}\nExisting context: {context}",
                    "timestamp": datetime.now().isoformat()
                },
                "status": "success",
                "metadata": metadata
            }

        except Exception as e:
            logger.error(f"Task plan creation failed: {e}")
            self._performance_metrics["failed_plans"] += 1
            return {
                "success": False,
                "error": str(e),
                "plan": {},
                "status": "failed"
            }

    def _analyze_objective(self, objective: str) -> Dict[str, Any]:
        """Analyze learning objective"""
        try:
            # Extract key information
            words = objective.lower().split()

            # Detect subject area
            subjects = {
                "math": ["math", "mathematics", "arithmetic", "algebra", "geometry", "calculus", "numbers"],
                "science": ["science", "physics", "chemistry", "biology", "experiment"],
                "language": ["reading", "writing", "grammar", "vocabulary", "literature", "english"],
                "history": ["history", "historical", "past", "ancient", "civilization"],
                "art": ["art", "drawing", "painting", "creative", "design"]
            }

            detected_subject = "general"
            for subject, keywords in subjects.items():
                if any(keyword in words for keyword in keywords):
                    detected_subject = subject
                    break

            # Detect complexity level
            complexity_keywords = {
                "basic": ["basic", "simple", "easy", "beginner", "start", "introduction"],
                "intermediate": ["intermediate", "medium", "practice", "improve"],
                "advanced": ["advanced", "complex", "master", "expert", "detailed"]
            }

            detected_complexity = "intermediate"
            for level, keywords in complexity_keywords.items():
                if any(keyword in words for keyword in keywords):
                    detected_complexity = level
                    break

            return {
                "subject": detected_subject,
                "complexity": detected_complexity,
                "word_count": len(words),
                "estimated_duration": self._estimate_duration(objective, detected_complexity)
            }

        except Exception as e:
            logger.error(f"Objective analysis failed: {e}")
            return {"subject": "general", "complexity": "intermediate", "estimated_duration": "1-2 hours"}

    def _estimate_duration(self, objective: str, complexity: str) -> str:
        """Estimate duration for learning objective"""
        base_duration = {
            "basic": "30-60 minutes",
            "intermediate": "1-2 hours",
            "advanced": "2-4 hours"
        }
        return base_duration.get(complexity, "1-2 hours")

    def _generate_plan_structure(self, objective: str, resources: str, context: str, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate basic plan structure"""
        return {
            "learning_objectives": [f"Master {objective}"],
            "estimated_time": analysis.get("estimated_duration", "1-2 hours"),
            "difficulty_level": analysis.get("complexity", "intermediate"),
            "subject_area": analysis.get("subject", "general"),
            "resources_needed": resources.split(",") if resources else ["textbook", "notebook"],
            "success_criteria": ["Complete all tasks", "Demonstrate understanding", "Apply knowledge"]
        }

    def _create_detailed_plan(self, structure: Dict[str, Any], analysis: Dict[str, Any]) -> str:
        """Create detailed learning plan"""
        subject = analysis.get("subject", "general")
        complexity = analysis.get("complexity", "intermediate")

        # Generate plan based on subject and complexity
        if subject == "math":
            return self._create_math_plan(structure, complexity)
        elif subject == "science":
            return self._create_science_plan(structure, complexity)
        elif subject == "language":
            return self._create_language_plan(structure, complexity)
        else:
            return self._create_general_plan(structure, complexity)

    def _create_math_plan(self, structure: Dict[str, Any], complexity: str) -> str:
        """Create math-specific learning plan"""
        if complexity == "basic":
            return """# Basic Math Learning Plan

## Learning Objectives
- Understand fundamental math concepts
- Practice basic operations
- Build confidence with numbers

## Step-by-Step Tasks
1. **Warm-up (10 minutes)**
   - Review basic number recognition
   - Practice counting exercises

2. **Main Learning (30 minutes)**
   - Introduction to the concept
   - Guided practice with examples
   - Interactive exercises

3. **Practice (15 minutes)**
   - Independent problem solving
   - Apply learned concepts

4. **Review (5 minutes)**
   - Summarize key points
   - Check understanding

## Success Criteria
- Complete all practice problems correctly
- Explain the concept in own words
- Apply knowledge to new problems"""

        elif complexity == "advanced":
            return """# Advanced Math Learning Plan

## Learning Objectives
- Master complex mathematical concepts
- Develop problem-solving strategies
- Apply knowledge to real-world scenarios

## Detailed Study Plan
1. **Conceptual Foundation (45 minutes)**
   - Theoretical understanding
   - Mathematical proofs and derivations
   - Connection to previous knowledge

2. **Problem-Solving Practice (60 minutes)**
   - Challenging problem sets
   - Multiple solution approaches
   - Error analysis and correction

3. **Application Projects (30 minutes)**
   - Real-world applications
   - Cross-curricular connections
   - Creative problem design

4. **Assessment and Reflection (15 minutes)**
   - Self-evaluation
   - Identify areas for improvement
   - Plan next steps

## Success Criteria
- Solve complex problems independently
- Explain mathematical reasoning clearly
- Create original problems"""

        else:  # intermediate
            return """# Intermediate Math Learning Plan

## Learning Objectives
- Build on existing math knowledge
- Develop problem-solving skills
- Gain confidence in mathematical thinking

## Learning Activities
1. **Review and Preparation (15 minutes)**
   - Review prerequisite concepts
   - Identify knowledge gaps

2. **New Concept Introduction (30 minutes)**
   - Step-by-step explanation
   - Visual aids and examples
   - Guided practice

3. **Independent Practice (30 minutes)**
   - Varied problem types
   - Progressive difficulty
   - Self-checking opportunities

4. **Application and Extension (15 minutes)**
   - Real-world connections
   - Challenge problems
   - Reflection on learning

## Success Criteria
- Demonstrate understanding through practice
- Solve problems with minimal guidance
- Connect concepts to previous learning"""

    def _create_science_plan(self, structure: Dict[str, Any], complexity: str) -> str:
        """Create science-specific learning plan"""
        return """# Science Learning Plan

## Learning Objectives
- Understand scientific concepts and principles
- Develop scientific thinking and inquiry skills
- Apply knowledge through hands-on activities

## Learning Activities
1. **Introduction and Background (20 minutes)**
   - Review related concepts
   - Introduce new vocabulary
   - Set learning goals

2. **Exploration and Discovery (40 minutes)**
   - Hands-on experiments or observations
   - Data collection and analysis
   - Discussion of findings

3. **Concept Application (20 minutes)**
   - Apply concepts to new situations
   - Problem-solving activities
   - Real-world connections

4. **Reflection and Assessment (10 minutes)**
   - Summarize key learnings
   - Assess understanding
   - Plan further exploration

## Success Criteria
- Demonstrate understanding of key concepts
- Apply scientific method appropriately
- Make connections to everyday life"""

    def _create_language_plan(self, structure: Dict[str, Any], complexity: str) -> str:
        """Create language-specific learning plan"""
        return """# Language Arts Learning Plan

## Learning Objectives
- Improve reading comprehension and vocabulary
- Develop writing and communication skills
- Enhance critical thinking through literature

## Learning Activities
1. **Reading and Comprehension (30 minutes)**
   - Read selected text or story
   - Identify main ideas and details
   - Discuss vocabulary and context

2. **Writing Practice (25 minutes)**
   - Creative or analytical writing
   - Grammar and mechanics focus
   - Peer review and feedback

3. **Discussion and Analysis (20 minutes)**
   - Share ideas and interpretations
   - Critical thinking exercises
   - Connect to personal experiences

4. **Review and Extension (15 minutes)**
   - Summarize key points
   - Plan additional reading
   - Set writing goals

## Success Criteria
- Demonstrate improved comprehension
- Produce clear, well-organized writing
- Engage in meaningful discussions"""

    def _create_general_plan(self, structure: Dict[str, Any], complexity: str) -> str:
        """Create general learning plan"""
        return """# General Learning Plan

## Learning Objectives
- Acquire new knowledge and skills
- Develop critical thinking abilities
- Apply learning to practical situations

## Learning Activities
1. **Preparation and Goal Setting (15 minutes)**
   - Review learning objectives
   - Assess current knowledge
   - Set specific goals

2. **Active Learning (45 minutes)**
   - Engage with new material
   - Practice skills and concepts
   - Ask questions and seek clarification

3. **Application and Practice (20 minutes)**
   - Apply knowledge to new situations
   - Complete practice exercises
   - Solve problems independently

4. **Reflection and Planning (10 minutes)**
   - Reflect on learning progress
   - Identify areas for improvement
   - Plan next steps

## Success Criteria
- Meet stated learning objectives
- Demonstrate skill improvement
- Apply knowledge effectively"""

    def generate_simple_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a simple learning report

        Args:
            data: Dictionary containing learning data

        Returns:
            Dictionary containing the generated report
        """
        try:
            user_input = data.get("user_input", "")
            intent = data.get("intent", {})
            response = data.get("response", "")
            timestamp = data.get("timestamp", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            child_id = data.get("child_id", "Unknown")

            # Generate summary
            summary_parts = []

            if user_input:
                summary_parts.append(f"Student Input: {user_input[:100]}...")

            if intent:
                intent_name = intent.get("predicted_intention", "unknown")
                confidence = intent.get("confidence", 0.0)
                summary_parts.append(f"Detected Intent: {intent_name} (confidence: {confidence:.2f})")

            if response:
                summary_parts.append(f"AI Response: {response[:100]}...")

            summary_parts.append(f"Session Time: {timestamp}")
            summary_parts.append(f"Student ID: {child_id}")

            report_text = "\n".join(summary_parts)

            return {
                "success": True,
                "output": {
                    "report_text": report_text,
                    "summary": f"Learning session completed at {timestamp}",
                    "metadata": {
                        "generated_at": datetime.now().isoformat(),
                        "data_points": len(summary_parts)
                    }
                }
            }

        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "output": {
                    "report_text": "Failed to generate report",
                    "summary": "Error occurred during report generation"
                }
            }

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return self._performance_metrics.copy()

    def reset_performance_metrics(self):
        """Reset performance metrics"""
        self._performance_metrics = {
            "total_requests": 0,
            "cache_hits": 0,
            "average_response_time": 0.0,
            "successful_plans": 0,
            "failed_plans": 0,
            "intent_cache_hits": 0,
            "embedding_cache_hits": 0,
            "fast_responses": 0
        }

    def generate_embeddings_for_intent_data(self):
        """Generate embeddings for intent classification data"""
        if not self.intent_data:
            logger.warning("No intent data available")
            return False

        try:
            self._generate_intent_embeddings()
            logger.info("Intent embeddings generated successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to generate intent embeddings: {e}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """Get OpenManus system status"""
        return {
            "status": "ready",
            "ready": True,
            "metrics": self.get_performance_metrics(),
            "intent_data_loaded": len(self.intent_data) > 0,
            "embeddings_ready": self.intent_embeddings is not None,
            "cache_db_path": str(self.cache_db_path),
            "model_name": self.model_name,
            "message": "OpenManus service is ready"
        }


# Utility functions for backward compatibility
def create_openmanus_planner() -> OpenManusPlanner:
    """Create and return an OpenManus planner instance"""
    return OpenManusPlanner()


# Main execution for testing
if __name__ == "__main__":
    # Test the OpenManus planner
    planner = OpenManusPlanner()

    # Test intent classification
    test_input = "I want to learn math today"
    result = planner.classify_user_intent(test_input)
    print(f"Intent classification result: {result}")

    # Test task planning
    plan_result = planner.create_task_plan(
        objective="Learn multiplication tables",
        resources="textbook, calculator",
        context="elementary student"
    )
    print(f"Task plan result: {plan_result}")

    # Test user input processing
    process_result = planner.process_user_input(test_input)
    print(f"User input processing result: {process_result}")

    # Show performance metrics
    metrics = planner.get_performance_metrics()
    print(f"Performance metrics: {metrics}")

    print("✅ OpenManus testing completed successfully!")
