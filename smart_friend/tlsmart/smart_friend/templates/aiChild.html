<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Friend - AI学习助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        .header {
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.2em;
        }
        
        .status {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status.loading {
            background: #fff3cd;
            border-color: #ffc107;
        }
        
        .status.error {
            background: #f8d7da;
            border-color: #dc3545;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .feature h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #666;
            font-size: 0.9em;
        }
        
        .api-links {
            margin-top: 30px;
        }
        
        .api-links a {
            display: inline-block;
            background: #007bff;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            margin: 5px;
            transition: background 0.3s;
        }
        
        .api-links a:hover {
            background: #0056b3;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Smart Friend</h1>
            <p>AI智能学习助手 - 让学习更有趣！</p>
        </div>
        
        <div id="status" class="status loading">
            <h3>🔄 正在初始化系统...</h3>
            <p>请稍等，我们正在启动AI学习助手</p>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🧠 智能对话</h3>
                <p>与AI助手进行自然对话，获得个性化学习建议</p>
            </div>
            <div class="feature">
                <h3>📚 学习规划</h3>
                <p>AI自动生成个性化学习计划，提高学习效率</p>
            </div>
            <div class="feature">
                <h3>🎯 意图识别</h3>
                <p>智能理解学习需求，提供精准的学习支持</p>
            </div>
            <div class="feature">
                <h3>📊 进度跟踪</h3>
                <p>实时监控学习进度，生成详细学习报告</p>
            </div>
            <div class="feature">
                <h3>🎤 语音交互</h3>
                <p>支持语音输入和输出，让学习更加便捷</p>
            </div>
            <div class="feature">
                <h3>🔧 OpenManus增强</h3>
                <p>集成先进AI规划系统，提供更智能的学习体验</p>
            </div>
        </div>
        
        <div class="api-links">
            <a href="/docs" target="_blank">📖 API文档</a>
            <a href="/api/v1/openmanus-direct/status" target="_blank">🔍 系统状态</a>
            <a href="/health" target="_blank">💚 健康检查</a>
        </div>
        
        <div class="footer">
            <p>Smart Friend v2.0.0 - 基于OpenManus AI技术</p>
            <p>© 2024 Smart Friend Team. 让每个孩子都能享受智能学习的乐趣！</p>
        </div>
    </div>

    <script>
        // Check system status
        async function checkStatus() {
            try {
                const response = await fetch('/api/v1/openmanus-direct/status');
                const data = await response.json();
                
                const statusDiv = document.getElementById('status');
                
                if (data.ready) {
                    statusDiv.className = 'status';
                    statusDiv.innerHTML = `
                        <h3>✅ 系统就绪！</h3>
                        <p>OpenManus AI学习助手已启动，准备为您提供智能学习服务</p>
                        <p><strong>集成类型:</strong> ${data.integration_type || 'direct_main_py'}</p>
                    `;
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = `
                        <h3>❌ 系统未就绪</h3>
                        <p>${data.message || '系统初始化失败'}</p>
                    `;
                }
            } catch (error) {
                const statusDiv = document.getElementById('status');
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `
                    <h3>❌ 连接失败</h3>
                    <p>无法连接到服务器，请检查网络连接</p>
                `;
            }
        }
        
        // Check status on page load
        window.addEventListener('load', checkStatus);
        
        // Refresh status every 30 seconds
        setInterval(checkStatus, 30000);
    </script>
</body>
</html>
