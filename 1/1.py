# ===== Imports =====
import json
import os
import numpy as np
import torch
from datasets import Dataset
from transformers import <PERSON><PERSON>oken<PERSON>, RobertaForSequenceClassification, TrainingArguments, Trainer
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

# ===== Load dataset from local path =====
from datasets import load_dataset

# Option 1: Direct load (if file is in same directory as script)
with open('Intent_classification_100_data.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# Option 2: Absolute path (more reliable)
file_path = os.path.join(os.getcwd(), 'Intent_classification_100_data.json')
with open(file_path, 'r', encoding='utf-8') as f:
    data = json.load(f)

print("Data loaded successfully!")
print(f"Found {len(data)} entries")

#Convert to HuggingFace Dataset =====
dataset = Dataset.from_list(data)

# ===== Label Mapping =====
intents = sorted(set(example["intent"] for example in data))
label2id = {label: i for i, label in enumerate(intents)}
id2label = {i: label for label, i in label2id.items()}
dataset = dataset.map(lambda x: {"label": label2id[x["intent"]]}, remove_columns=["intent"])

# ===== Tokenization =====
tokenizer = RobertaTokenizer.from_pretrained("roberta-base")

def tokenize_fn(example):
    return tokenizer(example["text"], padding="max_length", truncation=True, max_length=128)

dataset = dataset.map(tokenize_fn, batched=True)
dataset.set_format("torch", columns=["input_ids", "attention_mask", "label"])

# ===== Train-Test Split =====
split_data = dataset.train_test_split(test_size=0.2)
train_ds = split_data["train"]
test_ds = split_data["test"]

# ===== Load Model =====
model = RobertaForSequenceClassification.from_pretrained(
    "roberta-base",
    num_labels=len(label2id),
    id2label=id2label,
    label2id=label2id
)

# ===== Compute Metrics Function =====
def compute_metrics(eval_pred):
    logits, labels = eval_pred
    preds = np.argmax(logits, axis=-1)
    acc = accuracy_score(labels, preds)
    precision, recall, f1, _ = precision_recall_fscore_support(labels, preds, average="weighted")
    return {
        "accuracy": acc,
        "precision": precision,
        "recall": recall,
        "f1": f1
    }

# Disable Weights & Biases logging
import os
os.environ["WANDB_DISABLED"] = "true"

# ===== Training Arguments =====
training_args = TrainingArguments(
    output_dir="./intent_results",
    eval_strategy="epoch",
    save_strategy="epoch",
    per_device_train_batch_size=8,
    per_device_eval_batch_size=8,
    num_train_epochs=3,
    logging_dir='./logs',
    logging_steps=10,
    load_best_model_at_end=True
)

# Convert dataset to NumPy arrays explicitly
train_ds = train_ds.with_format("numpy")
test_ds = test_ds.with_format("numpy")

# ===== Trainer Setup =====
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_ds,
    eval_dataset=test_ds,
    tokenizer=tokenizer,
    compute_metrics=compute_metrics
)

# ===== Train =====
trainer.train()

# ===== Evaluate =====
results = trainer.evaluate()
print("\nFinal Evaluation Metrics:")
for k, v in results.items():
    print(f"{k}: {v:.4f}")

# ===== Save Model =====
model.save_pretrained("intent_classifier_roberta")
tokenizer.save_pretrained("intent_classifier_roberta")